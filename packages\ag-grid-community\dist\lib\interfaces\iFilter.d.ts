import { ColDef, ValueGetterFunc } from '../entities/colDef';
import { Column } from '../entities/column';
import { IFloatingFilterComp } from '../filter/floating/floatingFilter';
import { AgPromise } from '../utils';
import { IAfterGuiAttachedParams } from './iAfterGuiAttachedParams';
import { IComponent } from './iComponent';
import { AgGridCommon } from './iCommon';
import { IRowModel } from './iRowModel';
import { IRowNode } from './iRowNode';
export declare type IFilterType = string | {
    new (): IFilterComp;
} | boolean;
export declare type IFloatingFilterType = string | {
    new (): IFloatingFilterComp;
};
export interface IFilterDef {
    /**
     * Filter component to use for this column.
     * - Set to `true` to use the default filter.
     * - Set to the name of a provided filter: `set`, `number`, `text`, `date`.
     * - Set to a `IFilterComp`.
     */
    filter?: any;
    /** Params to be passed to the filter component specified in `filter`. */
    filterParams?: any;
    /**
     * The custom component to be used for rendering the floating filter.
     * If none is specified the default AG Grid is used.
     */
    floatingFilterComponent?: any;
    /** Params to be passed to `floatingFilterComponent`. */
    floatingFilterComponentParams?: any;
}
export interface IFilter {
    /**
     * Returns `true` if the filter is currently active, otherwise `false`.
     * If active then 1) the grid will show the filter icon in the column header
     * and 2) the filter will be included in the filtering of the data.
    */
    isFilterActive(): boolean;
    /**
     * The grid will ask each active filter, in turn, whether each row in the grid passes. If any
     * filter fails, then the row will be excluded from the final set. The method is provided a
     * params object with attributes node (the rodNode the grid creates that wraps the data) and data
     * (the data object that you provided to the grid for that row).
     */
    doesFilterPass(params: IDoesFilterPassParams): boolean;
    /**
     * Returns a model representing the current state of the filter, or `null` if the filter is
     * not active. The grid calls `getModel()` on all active filters when `gridApi.getFilterModel()` is called.
     */
    getModel(): any;
    /**
     * Sets the state of the filter using the supplied model. Providing `null` as the model will
     * de-activate the filter.
     */
    setModel(model: any): void | AgPromise<void>;
    /** Gets called when new rows are inserted into the grid. If the filter needs to change its
     state after rows are loaded, it can do it here. For example the set filters uses this
     to update the list of available values to select from (e.g. 'Ireland', 'UK' etc for
     Country filter). To get the list of available values from within this method from the
    Client Side Row Model, use `gridApi.forEachLeafNode(callback)`.
    */
    onNewRowsLoaded?(): void;
    /** Called whenever any filter is changed. */
    onAnyFilterChanged?(): void;
    /**
     * Optional method used by AG Grid when rendering floating filters and there isn't a floating filter
     * associated for this filter, this will happen if you create a custom filter and NOT a custom floating
     * filter.
     */
    getModelAsString?(model: any): string;
    /**
     * A hook to perform any necessary operation just after the GUI for this component has been rendered on the screen.
     * If a parent popup is closed and reopened (e.g. for filters), this method is called each time the component is shown.
     * This is useful for any logic that requires attachment before executing, such as putting focus on a particular DOM element.
     */
    afterGuiAttached?(params?: IAfterGuiAttachedParams): void;
    /**
     * A hook to perform any necessary operation just after the GUI for this component has been removed from the screen.
     * If a parent popup is opened and closed (e.g. for filters), this method is called each time the component is hidden.
     * This is useful for any logic to reset the UI state back to the model before the component is reopened.
     */
    afterGuiDetached?(): void;
}
export interface ProvidedFilterModel {
    filterType?: string;
}
export interface IFilterComp<TData = any> extends IComponent<IFilterParams<TData>>, IFilter {
}
export interface IDoesFilterPassParams<TData = any> {
    /** The row node in question. */
    node: IRowNode<TData>;
    /** The data part of the row node in question. */
    data: TData;
}
export interface IFilterOptionDef {
    /** A unique key that does not clash with the built-in filter keys. */
    displayKey: string;
    /** Display name for the filter. Can be replaced by a locale-specific value using a `localeTextFunc`. */
    displayName: string;
    /** Custom filter logic that returns a boolean based on the `filterValues` and `cellValue`. */
    predicate?: (filterValues: any[], cellValue: any) => boolean;
    /** Number of inputs to display for this option. Defaults to `1` if unspecified. */
    numberOfInputs?: 0 | 1 | 2;
}
/**
 * Parameters provided by the grid to the `init` method of an `IFilterComp`
 */
export interface IFilterParams<TData = any, TContext = any> extends AgGridCommon<TData, TContext> {
    /** The column this filter is for. */
    column: Column;
    /** The column definition for the column. */
    colDef: ColDef<TData>;
    /**
     * The row model, helpful for looking up data values if needed.
     * If the filter needs to know which rows are
     * a) in the table,
     * b) currently visible (i.e. not already filtered),
     * c) which groups,
     * d) what order - all of this can be read from the rowModel.
     */
    rowModel: IRowModel;
    /**
     * A function callback to be called when the filter changes. The
     * grid will then respond by filtering the grid data. The callback
     * takes one optional parameter which, if included, will get merged
     * to the FilterChangedEvent object (useful for passing additional
     * information to anyone listening to this event, however such extra
     * attributes are not used by the grid).
     */
    filterChangedCallback: (additionalEventAttributes?: any) => void;
    /**
     * A function callback, to be optionally called, when the filter UI changes.
     * The grid will respond with emitting a FilterModifiedEvent.
     * Apart from emitting the event, the grid takes no further action.
     */
    filterModifiedCallback: () => void;
    /**
     * A function callback for the filter to get cell values from provided row data. Called with a
     * `ValueGetterParams` to get the value for this filter's column for the provided row data.
     *
     * The callback takes care of selecting the right column definition and deciding whether to use
     * the column `valueGetter` or raw field etc.
     */
    valueGetter: ValueGetterFunc<TData>;
    /**
     * A function callback, call with a node to be told whether the node passes all filters except the current filter.
     * This is useful if you want to only present to the user values that this filter can filter given the status of the other filters.
     * The set filter uses this to remove from the list,
     * items that are no longer available due to the state of other filters (like Excel type filtering).
     */
    doesRowPassOtherFilter: (rowNode: IRowNode<TData>) => boolean;
}
