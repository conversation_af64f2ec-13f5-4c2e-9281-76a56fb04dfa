FROM lambo-docker-local.repo.inspur.com/lambo/nginx:latest
ARG PUBLIC_PATH=ind-ima
##维护者信息
COPY ./nginx.conf /etc/nginx/nginx.conf
COPY ./redirect.html /opt/apps/htdocs/index.html
# 在容器内部使用 sed 进行替换
RUN sed -i "s|PUBLIC_PATH_REPLACE|$PUBLIC_PATH|g" /etc/nginx/nginx.conf
RUN sed -i "s|PUBLIC_PATH_REPLACE|$PUBLIC_PATH|g" /opt/apps/htdocs/index.html
COPY ./dist /apps/$PUBLIC_PATH
EXPOSE 80
#指定挂载点
VOLUME ["/var/log/nginx"]

#docker build --build-arg SVC1="text1"
