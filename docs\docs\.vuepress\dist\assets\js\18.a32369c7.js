(window.webpackJsonp=window.webpackJsonp||[]).push([[18],{553:function(s,v,t){"use strict";t.r(v);var n=t(15),_=Object(n.a)({},(function(){var s=this,v=s._self._c;return v("ContentSlotsDistributor",{attrs:{"slot-key":s.$parent.slotKey}},[v("h2",{attrs:{id:"背景"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#背景"}},[s._v("#")]),s._v(" 背景")]),s._v(" "),v("ol",[v("li",[s._v("移动端前端要拆分多个工程。")]),s._v(" "),v("li",[s._v("移动端研发要兼容PAD端和手机端，预测PAD端的展现样式类似PC。")]),s._v(" "),v("li",[s._v("复用PC端的表单和表格配置，实现移动端的页面配置。")]),s._v(" "),v("li",[s._v("基于PC端"),v("code",[s._v("@indfnd/common")]),s._v("组件，重写了form表单和列表页面，渲染组件改为适配移动端展现的方式。")]),s._v(" "),v("li",[s._v("由于PC端配置有冗余，为了统一配置源，引入了xmlInfo配置方式。")]),s._v(" "),v("li",[s._v("对于常规列表和常规表单页面做了封装，对于非典型场景，需要个性化开发。")])]),s._v(" "),v("p",[s._v("基于以上几点原因，封装本组件库。")]),s._v(" "),v("h2",{attrs:{id:"提取到组件库的原则"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#提取到组件库的原则"}},[s._v("#")]),s._v(" 提取到组件库的原则")]),s._v(" "),v("ol",[v("li",[s._v("多个应用都需要用到")]),s._v(" "),v("li",[s._v("基于 Vue2.0 框架")])]),s._v(" "),v("h2",{attrs:{id:"工程结构"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#工程结构"}},[s._v("#")]),s._v(" 工程结构")]),s._v(" "),v("div",{staticClass:"language- extra-class"},[v("pre",{pre:!0,attrs:{class:"language-text"}},[v("code",[s._v("common                                 \n├─ src                                 \n│  ├─ assets                           \n│  │  └─ ...                           \n│  ├─ components                       \n│  │  └─ ...                           \n│  ├─ directives                       \n│  │  └─ ...                           \n│  ├─ plugins                          \n│  │  └─ ...                           \n│  ├─ router                           \n│  │  └─ ...                           \n│  ├─ store                            \n│  │  └─ ...                           \n│  ├─ styles                           \n│  │  └─ ...                           \n│  └─ index.js                         \n├─ CHANGELOG.md                        \n└─ global.d.ts                         \n")])])]),v("ul",[v("li",[s._v("assets: 存放样式里用到的图片")]),s._v(" "),v("li",[s._v("components: 提供公共组件")]),s._v(" "),v("li",[s._v("directives: 提供公共的 vue 指令")]),s._v(" "),v("li",[s._v("plugins: 提供简单的 vue 插件，包括 "),v("code",[s._v("$config")]),s._v(" 和 返回按钮的处理")]),s._v(" "),v("li",[s._v("router: 定义统一的路由及处理方法")]),s._v(" "),v("li",[s._v("store: 定义统一的状态管理")]),s._v(" "),v("li",[s._v("styles: 维护统一样式，并提供常用的样式类")]),s._v(" "),v("li",[s._v("CHANGELOG.md: 为 "),v("code",[s._v("standard-version")]),s._v(" 根据 git 的 commit-msg 自动生成的版本更新日志")]),s._v(" "),v("li",[s._v("global.d.ts: 为该工程对全局变量的一些扩展定义，用于其它应用配置 "),v("code",[s._v("jsconfig.json")]),s._v(" 或 "),v("code",[s._v("tsconfig.json")]),s._v(" 时增加")])])])}),[],!1,null,null,null);v.default=_.exports}}]);