<template>
  <KeepAlive>
    <RouterView ref="child" />
  </KeepAlive>
</template>

<script>
export default {
  name: 'IndMEmptyLayout',
  computed: {
    tagNavList() {
      return this.$store.state.app.tagNavList
    },
    notCacheName() {
      return [this.$route.meta && this.$route.meta.notCache ? this.$route.name : '']
    },
    cacheList() {
      const list = (this.tagNavList || [])
        .filter((item) => !(item.meta && item.meta.notCache))
        .map((item) => item.name)

      return ['IndMEmptyLayout', ...list]
    },
  },
}
</script>
