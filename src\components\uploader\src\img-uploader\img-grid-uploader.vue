<template>
  <div class="img-grid-uploader" :style="style">
    <div class="img-grid-uploader-item" v-for="(fileId, index) in fileIdList" :key="fileId">
      <div class="img-grid-uploader-item-box">
        <van-image-oss
          class="img-uploader-image"
          :src="fileId"
          :fit="imageFit"
          :preview="false"
          @click="imagePreview(index)"
        />
        <div class="img-uploader-del" v-if="!disabled" @click="delFile(index)">
          <van-icon class="img-uploader-del-icon" name="cross" color="white"></van-icon>
        </div>
      </div>
    </div>
    <div
      class="img-grid-uploader-item"
      v-if="!disabled && (!maxCount || fileIdList.length < maxCount)"
    >
      <div class="img-grid-uploader-item-box">
        <ImgUploader
          ref="imgUploader"
          v-model="uploadedFileId"
          :sourceType="sourceType"
          :maxSize="maxSize"
          :maxWidth="maxWidth"
          :maxHeight="maxHeight"
          :imageFit="imageFit"
        >
          <slot>
            <div class="img-uploader-image-empty">
              <van-icon class="img-uploader-image-empty-icon" name="photograph" />
            </div>
          </slot>
        </ImgUploader>
      </div>
    </div>
  </div>
</template>

<script>
import ImgUploader from './img-uploader.vue'
import { ImagePreview } from 'vant'
import { getFileURL } from '@/api/ics/common/index'

// const sourceTypeList = ['album', 'camera']
const sourceTypeList = ['album', 'camera']

export default {
  name: 'img-grid-uploader',
  components: {
    ImgUploader,
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    columnsNum: {
      type: Number, // 列数
      default: 3,
    },
    rowGap: {
      type: Number, // 行间隔，单位px
      default: 10,
    },
    columnGap: {
      type: Number, // 列间隔，单位px
      default: 10,
    },
    preview: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    maxCount: {
      type: Number,
    },
    sourceType: {
      type: Array, // 'album', 'camera'
      default: () => sourceTypeList,
    },
    maxSize: {
      type: [Number, String, Function], // number | string | (file: File) => boolean
    },
    maxWidth: {
      type: [Number],
    },
    maxHeight: {
      type: [Number],
    },
    imageFit: {
      type: String,
      default: 'cover',
    },
  },
  data() {
    return {
      fileIdList: [],
      uploadedFileId: '',
    }
  },
  computed: {
    style() {
      return `
        grid-template-columns: repeat(${this.columnsNum}, 1fr);
        gap: ${this.rowGap}px ${this.columnGap}px;`
    },
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler() {
        this.fileIdList = this.value
      },
    },
    fileIdList() {
      this.$emit('input', this.fileIdList)
    },
    uploadedFileId(val) {
      if (val) {
        this.addFile(val)
        setTimeout(() => {
          this.uploadedFileId = ''
        }, 0)
      }
    },
  },
  methods: {
    addFile(fileId) {
      this.fileIdList.push(fileId)
      this.$emit('img-changed')
    },
    delFile(index) {
      this.fileIdList.splice(index, 1)
      this.$emit('img-changed')
    },
    imagePreview(index) {
      if (this.preview) {
        ImagePreview({
          images: this.fileIdList.map((fileId) => getFileURL(fileId)),
          startPosition: index,
        })
      }
    },
  },
}
</script>

<style lang="less">
.img-grid-uploader {
  width: 100%;
  display: grid;
  .img-grid-uploader-item {
    position: relative;
    height: 0;
    padding-bottom: 100%;
    .img-grid-uploader-item-box {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      text-align: center;
      .img-uploader-image {
        width: 100%;
        height: 100%;
        clear: both;
      }
      .img-uploader-del {
        position: absolute;
        top: 0;
        right: 0;
        width: 14px;
        height: 14px;
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 0 0 0 12px;
        .img-uploader-del-icon {
          position: absolute;
          top: -2px;
          right: -2px;
          color: #fff;
          font-size: 16px;
          -webkit-transform: scale(0.5);
          transform: scale(0.5);
        }
      }

      .img-uploader-image-empty {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f7f8fa;
        .img-uploader-image-empty-icon {
          color: #dcdee0;
          font-size: 24px;
        }
      }
    }
  }
}
</style>
