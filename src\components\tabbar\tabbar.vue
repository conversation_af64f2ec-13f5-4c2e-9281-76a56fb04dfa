<template>
  <div v-show="isLogin && !isQianKun">
    <van-tabbar v-model="active" @change="onChange" :placeholder="true" :safe-area-inset-top="true">
      <van-tabbar-item
        @click="onClick(item)"
        v-for="(item, idx) in tab_menus"
        :key="'tab' + idx"
        :icon="item.icon"
      >
        {{ item.title }}
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import { getToken } from '@indfnd/utils'
import { EventBus } from '../../event-bus/event-bus'
import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper'

export default {
  name: 'LamboMTabBar',
  props: {
    tab_menus: Array,
  },
  data() {
    return {
      active: 0,
      isLogin: false,
      isQianKun: qiankunWindow.__POWERED_BY_QIANKUN__,
    }
  },
  watch: {
    $route() {
      this.updateActive()
    },
  },
  mounted() {
    console.log('this.$store.', this.$store)
    let token = getToken()
    if (token) {
      this.updateActive()
      this.isLogin = true
    }
  },
  beforeCreate() {
    let token = getToken()
    if (token) {
      this.isLogin = true
    }
  },
  updated() {
    let token = getToken()
    if (token) {
      this.isLogin = true
    }
  },
  methods: {
    updateActive() {
      const path = this.$route.path
      const index = this.tab_menus.findIndex((item) => item.uri == path)
      this.active = index > -1 ? index : 0
    },
    onChange(index) {
      this.active = index
    },
    onClick(data) {
      this.$emit('on-tabbar-change', data)
      if (data.type == 'web') EventBus.$emit('openUrl', data)
      else {
        if (this.$router.currentRoute.path != data.uri) this.$router.push({ path: data.uri })
      }
    },
  },
}
</script>
