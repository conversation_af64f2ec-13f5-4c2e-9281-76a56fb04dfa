<template>
  <div class="im-load-more">
    <div class="total-num">
      {{
        '- - 共计 ' +
        totalNum +
        ' 条' +
        ($route.path == '/ics/cust-maintain/customQuery' ? ' (含并列) ' : '') +
        ' - -'
      }}
    </div>
    <van-list
      class="paging-list"
      v-model="loading"
      :finished="finished"
      :offset="offset"
      :finished-text="'- - 共计 ' + totalNum + ' 条 - -'"
      @load="onLoad"
    >
      <!-- <template v-for="item in curData">
        <slot name="item" v-bind:item="item"></slot>
      </template> -->
      <slot></slot>
    </van-list>
  </div>
</template>

<script>
import _ from 'lodash'
import { Toast } from 'vant'

export default {
  props: {
    outSideTotal: {
      type: Number,
      default: 0,
    },
    //后端分页api
    pageDataApi: {
      type: Function,
    },
    params: {
      type: Object,
      default: function () {
        return {}
      },
    },
    // 数据列表
    datas: {
      type: Array,
      default: function () {
        return []
      },
    },
    // 滚动条与底部距离小于 offset 时触发load事件
    offset: {
      type: Number,
      default: 50,
    },
    // 一次加载的数据量
    pageLen: {
      type: Number,
      default: 10,
    },
    // finishedText: {
    //   type: String,
    //   default: '共计' + this.curData.length + '条',
    // },
  },
  data() {
    return {
      loading: false,
      curData: [],
      //分页参数 当前第几页
      curPage: 0,
      curIdx: 0,
      total: 0,
    }
  },
  computed: {
    totalNum() {
      if (this.outSideTotal) {
        return this.outSideTotal
      } else if (this.pageDataApi) {
        return this.total
      } else {
        return this.datas.length
      }
    },
    finished() {
      console.log(
        'page list finished???',
        this.pageDataApi
          ? this.curData.length == this.total
          : this.curData.length == this.datas.length,
      )
      if (this.pageDataApi) {
        return this.curData.length == this.total
      }
      return this.curData.length == this.datas.length
    },
  },
  watch: {
    params: {
      deep: true,
      handler() {
        this.initData()
      },
    },
    datas() {
      this.initData()
    },
  },
  created() {
    this.initData()
  },
  methods: {
    async initData() {
      if (this.pageDataApi) {
        this.curPage = 0
        this.loading = true
        Toast.loading({
          message: '加载中...',
          forbidClick: true,
        })

        try {
          const result = await this.pageDataApi({
            ...this.params,
            offset: this.curPage * this.pageLen,
            limit: this.pageLen,
          })
          if (result.code == '1') {
            this.$emit('getPositionCode', result.data.positionCode || '')
            this.total = result.data.total
            this.curData = result.data.rows
            if (result.data.rows.length > 0) {
              this.curPage += 1
            }
            this.$emit('getData', this.curData)
          } else {
            this.$toast(result.message)
          }
        } catch (error) {
          console.error('分页查询接口异常 error: ', error)
        }
        this.loading = false
        Toast.clear()
      } else {
        if (!!this.datas && !!this.datas.length) {
          this.curData = this.datas.slice(0, this.pageLen)
        } else {
          this.curData = []
        }

        this.$emit('getData', this.curData)
        this.curIdx = this.curData.length
      }
    },
    async onLoad() {
      console.log('onload start')
      if (this.pageDataApi) {
        this.loading = true
        Toast.loading({
          message: '加载中...',
          forbidClick: true,
        })

        try {
          const result = await this.pageDataApi({
            ...this.params,
            offset: this.pageLen * this.curPage,
            limit: this.pageLen,
          })
          if (result.code == '1') {
            //备选池分页接口新返回positionCode 需要抛出去
            this.$emit('getPositionCode', result.data.positionCode || '')
            this.curData = _.concat(this.curData, result.data.rows)
            if (result.data.rows.length > 0) {
              this.curPage += 1
            }
            this.$emit('getData', this.curData)
          } else {
            this.$toast(result.message)
          }
        } catch (error) {
          console.error('分页查询接口异常 error: ', error)
        }
        setTimeout(() => {
          this.loading = false
          Toast.clear()
        }, 200)
      } else {
        if (!!this.datas && !!this.datas.length) {
          this.loading = true
          Toast.loading({
            message: '加载中...',
            forbidClick: true,
          })
          this.curData = _.concat(
            this.curData,
            this.datas.slice(this.curIdx, this.curIdx + this.pageLen),
          )
          this.curIdx = this.curIdx + this.pageLen
          this.$emit('getData', this.curData)
          setTimeout(() => {
            this.loading = false
            Toast.clear()
          }, 200)
        }
      }
      setTimeout(() => {
        this.loading = false
        Toast.clear()
      }, 200)
    },
  },
}
</script>
<style lang="less" scoped>
.paging-list {
  position: relative;
}
.total-num {
  z-index: 1;
  top: 0;
  text-align: center;
  line-height: 30px;
  width: 100%;
  color: #969799;
  font-size: 14px;
}
/deep/.van-list__finished-text {
  position: absolute;
  z-index: 1;
  top: 0;
  text-align: center;
  line-height: 30px;
  width: 100%;
  display: none;
}
</style>
