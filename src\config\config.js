let routerBase = import.meta.env.MODE == 'development' ? 'ind-mobile' : 'PUBLIC_ROUTER_BASE'
let content = import.meta.env.MODE == 'development' ? '' : 'PUBLIC_CONTENT'

let config = {
  title: 'ind-mobile-manage',
  serverContext: '/ibp',
  // routerBase: 'ind-mobile-manage-front',
  // routerBase: 'ibp-mobile',
  routerBase: routerBase,
  loginName: 'login',
  // 登录页的路由name
  loginRouteName: 'login',
  // 根路由name
  rootRouteName: 'root',
  loginPagePath: '/login',
  // subAppName: 'ind',
  subAppName: 'ics',
  weComId: 'wwa31bb0c8c833982b',
  agentId: '1000392',
  //取不到定位默认经纬度
  defaultLocation: {
    longitude: 117.13597023313,
    latitude: 36.668022891427,
    // latitude: 30.63807,
    isGetLocationErr: true,
  },
  isQywx: false,
  baiduAk: 'hiTWNFfoSRjS2lgAjADVwbX2M7X7D24D',

  /**
   * @description icsCenterServer
   */
  icsCenterServerContext: `${content}/ics-center-server`,
  icustManageServerContext: `${content}/icust-manage-server`,
  userManageServerContext: `${content}/user-manage-server`,
  dddIcustServiceManageServer: `${content}/ddd-icustservice-manage-server`,
  indUcExtServerContent: `${content}/ind-uc-ext-server`,
  imaServerContext: `${content}/imarket-act-manage-server`,
  homeName: '首页',
  hideNavBar: false,
  navBarHeight: 46,
  // ------x1 版本配置--------//
  // appId:"1654332774161469440",//当前应用id 示例模块 ，用过滤展示应用中心菜单数据  id从后台功能资源获取   自行修改
  // type:"x1",//当前应用后台对接模式 x1，sc
  // upmsServerContext: '/ibp-upms-server',//x1后台服务  使用  x1:ibp-upms-server
  // ------x1 版本配置--------//
  // // ------sc 版本配置--------//
  appId: '5313240702000000006', //当前应用id ，用过滤展示应用中心菜单数据  id从后台功能资源获取   自行修改
  type: 'sc', //当前应用后台对接模式  sc
  upmsServerContext: `${content}/user-manage-server`, //sc认证  使用 sc:user-manage-server
  // //  ------sc 版本配置--------//
  icsServerContext: 'ics-center-server',
  homeTitle: '数字化营销平台',
}
const setConfig = function (key, value) {
  config[key] = value
}
const registerConfig = function (configMap) {
  config = Object.assign(config, configMap)
}
export default config
export { config, setConfig, registerConfig }
