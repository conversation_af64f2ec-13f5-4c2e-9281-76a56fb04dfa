<template>
  <PageView>
    <div class="tab-list" v-if="isMustComplete() && isVisit">
      <template v-for="(item, index) in mustCompleteList">
        <div class="tab-item" :key="index" @click="toPath(item)">
          <div class="tab-box" :class="isCurrentItem(item.mc04CustVisitTaskCode) ? 'selected' : ''">
            <img class="tab-icon" :src="getImg(item.visitItemIcon)" alt="" />
            <div class="tab-title">{{ item.mc04CustVisitTaskName }}</div>
          </div>
        </div>
        <img
          v-if="showDivide(index)"
          class="divide-line"
          :key="index + 'divide'"
          :src="divideLine"
          alt=""
        />
      </template>
    </div>
    <div
      ref="contentDisplay"
      :class="
        isDisplay
          ? isOverview
            ? 'page-content-isOverview'
            : 'page-content-isDisplay'
          : 'page-content'
      "
    >
      <div v-if="isVisit && !isDisplay" class="mission-title-box">
        <img class="title-bg" :src="titleBg" alt="" />
        <div class="title-text">{{ visitItemName }}</div>
      </div>
      <slot></slot>
    </div>
  </PageView>
</template>

<script>
import divideLine from './imgs/divide-line.png'
import titleBg from './imgs/title-bg.png'
import { saveFirstVisitItemRecord, updateVisitItemRecord } from '@/api/ics/visit/visitItem'
// import config from '@/config/config'
import { getImg } from '@/utils/getImg'

export default {
  props: {
    updateRecord: {
      type: Boolean,
      default: false,
    },
    isDisplay: {
      type: Boolean,
      default: false,
    },
    isOverview: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    isVisit() {
      return JSON.parse(this.$route.query.isVisit || false)
    },
    baCityOrgCode() {
      return this.$route.query.baCityOrgCode || ''
    },
    baProvOrgCode() {
      return this.$route.query.baProvOrgCode || ''
    },
    ccomCode() {
      return this.$route.query.ccomCode || ''
    },
    custCode() {
      return this.$route.query.custCode || ''
    },
    visitTaskCode() {
      return this.$route.query.visitTaskCode || ''
    },
    visitItemName() {
      return this.$route.query.visitItemName || ''
    },
    custVisitType() {
      return this.$route.query.custVisitType || '10'
    },
    custVisitRecordId() {
      return this.$route.query.custVisitRecordId || ''
    },
  },
  // props: {
  //   isVisit: {
  //     type: Boolean,
  //     default: false,
  //   },
  // },
  data() {
    return {
      divideLine,
      titleBg,
      //必做任务列表
      mustCompleteList: [],
      hasCreated: false,
    }
  },
  async created() {
    this.hasCreated = true
    this.mustCompleteList = JSON.parse(localStorage.getItem('mustCompleteList')) || []
    console.log('this.mustCompleteList created', this.mustCompleteList)
    if (this.isVisit) {
      await this.saveFirstVisitItemRecord()
    }
    if (this.isVisit && this.updateRecord) {
      this.updateVisitItemRecord()
    }
  },
  async activated() {
    if (!this.hasCreated) {
      this.mustCompleteList = JSON.parse(localStorage.getItem('mustCompleteList')) || []
      console.log('this.mustCompleteList activated', this.mustCompleteList)
      if (this.isVisit) {
        await this.saveFirstVisitItemRecord()
      }
      if (this.isVisit && this.updateRecord) {
        this.updateVisitItemRecord()
      }
    }
    this.hasCreated = false
  },
  methods: {
    scrollToBottom() {
      let scrollElem = this.$refs.contentDisplay
      console.log('doc height ', scrollElem.scrollHeight)
      scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' })
    },

    // getImg(uri) {
    //   if (config.isQywx) {
    //     console.log('geticon', config.isQywx, location.origin + '/hbwebchat' + uri)
    //     return location.origin + '/hbwebchat' + uri
    //   } else {
    //     console.log('geticon', config.isQywx, location.origin + uri)
    //     return location.origin + uri
    //   }
    // },
    getImg(uri) {
      const result = getImg(uri)
      console.log('visit-item-page-view uri', result)
      return result
    },
    async updateVisitItemRecord() {
      const result = await updateVisitItemRecord({
        baCityOrgCode: this.baCityOrgCode,
        baProvOrgCode: this.baProvOrgCode,
        bbRetailCustomerCode: this.custCode,
        mc04CustVisitTaskCode: this.visitTaskCode,
        mc04CustVisitType: this.custVisitType,
        custVisitRecordId: this.custVisitRecordId,
      })
      return result
    },
    //保存服务项记录
    async saveFirstVisitItemRecord() {
      const result = await saveFirstVisitItemRecord({
        baCityOrgCode: this.baCityOrgCode,
        baProvOrgCode: this.baProvOrgCode,
        bbRetailCustomerCode: this.custCode,
        mc04CustVisitTaskCode: this.visitTaskCode,
        mc04CustVisitType: this.custVisitType,
        custVisitRecordId: this.custVisitRecordId,
      })
      return result
    },
    back() {
      if (!this.isMustComplete()) {
        this.$router.go(-1)
      }
    },
    //是否必做项
    isMustComplete() {
      return this.$route.query.isMustComplete == '1'
    },
    toPath(item) {
      this.$router.replace({
        path: item.visitItemUrl,
        query: {
          isVisit: 'true',
          ccomCode: this.ccomCode,
          custCode: this.custCode,
          visitTaskCode: item.mc04CustVisitTaskCode || '',
          visitItemName: item.mc04CustVisitTaskName,
          isMustComplete: '1',
          baCityOrgCode: this.baCityOrgCode,
          baProvOrgCode: this.baProvOrgCode,
          custVisitType: this.custVisitType,
          custVisitRecordId: this.custVisitRecordId,
          custTypeCode: this.$route.query.custTypeCode,
        },
      })
    },
    //判断图标右侧是否展示分隔线
    showDivide(index) {
      if (this.mustCompleteList.length <= 1) {
        return false
      } else if (index < this.mustCompleteList.length - 1) {
        return true
      }
      return false
    },
    isCurrentItem(visitTaskCode) {
      return this.visitTaskCode == visitTaskCode
    },
  },
}
</script>

<style lang="less" scoped>
.tab-list {
  z-index: 2;
  width: 100vw;
  position: sticky;
  top: 46px;
  display: flex;
  justify-content: space-around;
  background-color: #ffffff;
  font-size: 10px;
  color: #333333;
  overflow-x: scroll;
  .tab-item {
    display: flex;
    // align-items: center;
    .tab-box {
      padding: 10px;
      text-align: center;
      &.selected {
        background: linear-gradient(0deg, #f1f7ff 0%, #ffffff 100%);
        font-weight: bold;
        color: #2d8cf0;
        border-bottom: 1px solid #cae2fc;
      }
      .tab-icon {
        height: 31px;
        width: 31px;
      }
      .tab-title {
        margin-top: 7px;
      }
    }
  }

  .divide-line {
    width: 18px;
    height: 4px;
    margin-top: 26px;
  }
}
.page-content {
  // margin-top: 74px;
  .mission-title-box {
    position: relative;
    height: 63px;
    width: 100vw;
    .title-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100vw;
      height: 104px;
    }
    .title-text {
      position: absolute;
      top: 21px;
      left: 21px;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
    }
  }
}
.page-content-isDisplay {
  position: relative;
  overflow: scroll;
  height: calc(100vh - 185px);
  // margin-top: 74px;
  .mission-title-box {
    position: relative;
    height: 63px;
    width: 100vw;
    .title-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100vw;
      height: 104px;
    }
    .title-text {
      position: absolute;
      top: 21px;
      left: 21px;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
    }
  }
}
.page-content-isOverview {
  position: relative;
  overflow: scroll;
  // margin-top: 74px;
  .mission-title-box {
    position: relative;
    height: 63px;
    width: 100vw;
    .title-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100vw;
      height: 104px;
    }
    .title-text {
      position: absolute;
      top: 21px;
      left: 21px;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
    }
  }
}
</style>
