(window.webpackJsonp=window.webpackJsonp||[]).push([[23],{557:function(t,v,_){"use strict";_.r(v);var d=_(15),e=Object(d.a)({},(function(){var t=this,v=t._self._c;return v("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[v("h2",{attrs:{id:"属性"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#属性"}},[t._v("#")]),t._v(" 属性")]),t._v(" "),v("table",[v("thead",[v("tr",[v("th",[t._v("属性")]),t._v(" "),v("th",[t._v("类型")]),t._v(" "),v("th",[t._v("默认值")]),t._v(" "),v("th",[t._v("说明")])])]),t._v(" "),v("tbody",[v("tr",[v("td",[v("code",[t._v("数据配置")])]),t._v(" "),v("td"),t._v(" "),v("td"),t._v(" "),v("td")]),t._v(" "),v("tr",[v("td",[t._v("value")]),t._v(" "),v("td",[t._v("Array")]),t._v(" "),v("td",[t._v("[]")]),t._v(" "),v("td",[t._v("表格的数据定义，推荐使用v-model")])]),t._v(" "),v("tr",[v("td",[t._v("dataApi")]),t._v(" "),v("td",[t._v("Function")]),t._v(" "),v("td",[t._v("null")]),t._v(" "),v("td",[t._v("表格的数据源定义，其定义就是api目录暴露的接口服务")])]),t._v(" "),v("tr",[v("td",[t._v("renderDataFn")]),t._v(" "),v("td",[t._v("Function")]),t._v(" "),v("td",[t._v("null")]),t._v(" "),v("td",[t._v("用于处理dataApi返回的数据，返回新数据")])]),t._v(" "),v("tr",[v("td",[t._v("searchParams")]),t._v(" "),v("td",[t._v("Object")]),t._v(" "),v("td",[t._v("{}")]),t._v(" "),v("td",[t._v("与dataApi搭配使用，接口请求的参数")])]),t._v(" "),v("tr",[v("td",[v("code",[t._v("功能项")])]),t._v(" "),v("td"),t._v(" "),v("td"),t._v(" "),v("td")]),t._v(" "),v("tr",[v("td",[t._v("columns")]),t._v(" "),v("td",[t._v("Array")]),t._v(" "),v("td",[t._v("[]")]),t._v(" "),v("td",[t._v("表格列定义，可用属性见列属性定义")])]),t._v(" "),v("tr",[v("td",[t._v("loading")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("是否展现表格加载中的蒙版")])]),t._v(" "),v("tr",[v("td",[t._v("disablePage")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("传true禁用分页，默认分页")])]),t._v(" "),v("tr",[v("td",[t._v("autoRefresh")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("是否自动刷新表格，即从编辑页面，明细页面返回时自动重新请求数据")])]),t._v(" "),v("tr",[v("td",[t._v("showTableOption")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("true")]),t._v(" "),v("td",[t._v("是否展现表格快捷操作区，默认展示")])]),t._v(" "),v("tr",[v("td",[t._v("revertBtn")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("表格快捷操作区是否展现转置按钮，默认不展示")])]),t._v(" "),v("tr",[v("td",[t._v("pinnedTopRowData")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("true")]),t._v(" "),v("td",[t._v("表格头部固定行数据")])]),t._v(" "),v("tr",[v("td",[t._v("pinnedBottomRowData")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("true")]),t._v(" "),v("td",[t._v("表格底部固定行数据")])]),t._v(" "),v("tr",[v("td",[t._v("rowKey")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("id")]),t._v(" "),v("td",[t._v("用于选择数据时，选择选中的dom元素，调用setSelection时必填")])]),t._v(" "),v("tr",[v("td",[t._v("noSortLine")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("-1")]),t._v(" "),v("td",[t._v("不参与排序的行数")])]),t._v(" "),v("tr",[v("td",[v("code",[t._v("样式配置")])]),t._v(" "),v("td"),t._v(" "),v("td"),t._v(" "),v("td")]),t._v(" "),v("tr",[v("td",[t._v("height")]),t._v(" "),v("td",[t._v("[Number, String]")]),t._v(" "),v("td",[t._v("null")]),t._v(" "),v("td",[t._v("单位px，不传默认高度100%，依赖父容器样式，如果父容器未设置高度，则表格无高度")])]),t._v(" "),v("tr",[v("td",[t._v("domLayout")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("设置为autoHeight，表格高度自动扩充")])]),t._v(" "),v("tr",[v("td",[t._v("getRowClass")]),t._v(" "),v("td",[t._v("Function")]),t._v(" "),v("td",[t._v("null")]),t._v(" "),v("td",[t._v("行class定义函数，根据行数据返回classname")])]),t._v(" "),v("tr",[v("td",[t._v("headerAutoHeight")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("true")]),t._v(" "),v("td",[t._v("表头高度自动扩充，设置为false，表头高度固定，展示不全显示省略号")])]),t._v(" "),v("tr",[v("td",[v("code",[t._v("单位切换配置")])]),t._v(" "),v("td"),t._v(" "),v("td"),t._v(" "),v("td")]),t._v(" "),v("tr",[v("td",[t._v("isShowWX")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("单位切换是否含万箱的选项")])]),t._v(" "),v("tr",[v("td",[t._v("isShowJ")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("单位切换是否含件的选项")])]),t._v(" "),v("tr",[v("td",[t._v("defaultUnitType")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("'X'")]),t._v(" "),v("td",[t._v("表格默认的单位，用于使用了单位切换场景")])]),t._v(" "),v("tr",[v("td",[t._v("xScale")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("2")]),t._v(" "),v("td",[t._v("单位为箱时，保留的小数位，默认2，用于使用了单位切换场景")])]),t._v(" "),v("tr",[v("td",[t._v("wzScale")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("4")]),t._v(" "),v("td",[t._v("单位为万支时，保留的小数位，默认4，用于使用了单位切换场景")])]),t._v(" "),v("tr",[v("td",[v("code",[t._v("导出EXCEL配置")])]),t._v(" "),v("td"),t._v(" "),v("td"),t._v(" "),v("td")]),t._v(" "),v("tr",[v("td",[t._v("paramLeft")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("导出excle时，表格左上角说明文字 -- 导出xlsx")])]),t._v(" "),v("tr",[v("td",[t._v("paramRight")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("导出excle时，表格右上角说明文字 -- 导出xlsx")])]),t._v(" "),v("tr",[v("td",[t._v("exportFileName")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("导出excel名默认值 -- 导出xlsx")])]),t._v(" "),v("tr",[v("td",[t._v("dataChildren")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("是否导出子数据(里面嵌套children) -- 导出xlsx")])]),t._v(" "),v("tr",[v("td",[t._v("rowSpanIndexCol")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("表格含自动合并单元格数据时，合并规则的主属性 -- 导出xlsx")])])])]),t._v(" "),v("h2",{attrs:{id:"列定义"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#列定义"}},[t._v("#")]),t._v(" 列定义")]),t._v(" "),v("table",[v("thead",[v("tr",[v("th",[t._v("属性")]),t._v(" "),v("th",[t._v("类型")]),t._v(" "),v("th",[t._v("默认值")]),t._v(" "),v("th",[t._v("说明")])])]),t._v(" "),v("tbody",[v("tr",[v("td",[t._v("headerName | title")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("列名，用headerName或者title均可(title是view-design的写法，是兼容的写法)")])]),t._v(" "),v("tr",[v("td",[t._v("field | key")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("字段名，用field或者key均可(key是view-design的写法，是兼容的写法)")])]),t._v(" "),v("tr",[v("td",[t._v("width")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("单元格默认的宽度，单位像素")])]),t._v(" "),v("tr",[v("td",[t._v("minWidth")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("单元格可拖拽的最小宽度，单位像素")])]),t._v(" "),v("tr",[v("td",[t._v("maxWidth")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("单元格可拖拽的最大宽度，单位像素")])]),t._v(" "),v("tr",[v("td",[t._v("flex")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("当前列弹性布局的权重，用于使表格铺满全屏")])]),t._v(" "),v("tr",[v("td",[t._v("cellClass")]),t._v(" "),v("td",[t._v("[String, Array]")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("设置列的样式，传classname或classname数组")])]),t._v(" "),v("tr",[v("td",[t._v("cellClassRules")]),t._v(" "),v("td",[t._v("Object")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("根据行数据，返回当前单元格的classname")])]),t._v(" "),v("tr",[v("td",[t._v("pinned")]),t._v(" "),v("td",[t._v("['left', 'right']")]),t._v(" "),v("td",[t._v("'left'")]),t._v(" "),v("td",[t._v("锁定列到左侧或者右侧")])]),t._v(" "),v("tr",[v("td",[t._v("cellDataType")]),t._v(" "),v("td",[t._v("['text', 'number']")]),t._v(" "),v("td",[t._v("'text'")]),t._v(" "),v("td",[t._v("单元格数据类型，一般情况不需要设置")])]),t._v(" "),v("tr",[v("td",[t._v("hide")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("是否隐藏列")])]),t._v(" "),v("tr",[v("td",[t._v("headerTooltip")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("表头鼠标悬浮展示的内容，可以放置指标定义说明")])]),t._v(" "),v("tr",[v("td",[t._v("headerTooltip")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("表头鼠标悬浮展示的内容，可以放置指标定义说明")])]),t._v(" "),v("tr",[v("td",[t._v("filter")]),t._v(" "),v("td",[v("a",{attrs:{href:"https://www.ag-grid.com/vue-data-grid/filtering/#column-filter-types",target:"_blank",rel:"noopener noreferrer"}},[t._v("any"),v("OutboundLink")],1)]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("表格列筛选的设置，可以做到文本模糊查询，数字区间查询，值分组筛选等功能")])]),t._v(" "),v("tr",[v("td",[t._v("filterParams")]),t._v(" "),v("td",[v("a",{attrs:{href:"https://www.ag-grid.com/vue-data-grid/filtering/#column-filter-types",target:"_blank",rel:"noopener noreferrer"}},[t._v("any"),v("OutboundLink")],1)]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("filter组件用到的参数")])]),t._v(" "),v("tr",[v("td",[t._v("wrapText")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("true")]),t._v(" "),v("td",[t._v("单元格内容自动换行")])]),t._v(" "),v("tr",[v("td",[t._v("autoHeight")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("true")]),t._v(" "),v("td",[t._v("单元格高度根据内容动态计算")])]),t._v(" "),v("tr",[v("td",[t._v("initialSort")]),t._v(" "),v("td",[t._v("'asc' | 'desc' | null")]),t._v(" "),v("td",[t._v("null")]),t._v(" "),v("td",[t._v("默认排序，表格需要指定默认排序，使用该字段")])]),t._v(" "),v("tr",[v("td",[t._v("autoMergeRow")]),t._v(" "),v("td",[t._v("[Boolean,String]")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("当前列根据单元格内容自动合并，传字符串则按照所传字符串列作为跨行计算的主属性")])]),t._v(" "),v("tr",[v("td",[t._v("rowSpan")]),t._v(" "),v("td",[v("a",{attrs:{href:"https://www.ag-grid.com/vue-data-grid/row-spanning/",target:"_blank",rel:"noopener noreferrer"}},[t._v("Function"),v("OutboundLink")],1)]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("单元格跨行，简单的单元格跨行，建议使用autoMergeRow")])]),t._v(" "),v("tr",[v("td",[t._v("colSpan")]),t._v(" "),v("td",[v("a",{attrs:{href:"https://www.ag-grid.com/vue-data-grid/column-spanning/",target:"_blank",rel:"noopener noreferrer"}},[t._v("Function"),v("OutboundLink")],1)]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("单元格跨列")])]),t._v(" "),v("tr",[v("td",[t._v("sortable")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("是否可排序，传dataApi使用服务端排序，否则使用前端排序")])]),t._v(" "),v("tr",[v("td",[t._v("align")]),t._v(" "),v("td",[t._v("'left' | 'center' | 'right'")]),t._v(" "),v("td",[t._v("left")]),t._v(" "),v("td",[t._v("当前列对齐方式")])]),t._v(" "),v("tr",[v("td",[t._v("type")]),t._v(" "),v("td",[t._v("['index', 'checkbox', 'radio', 'tooltip', 'input', 'select', 'datepicker', 'treeSelect', 'button', 'link', 'enum' ]")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("当前列的渲染类型")])]),t._v(" "),v("tr",[v("td",[t._v("cellRenderer")]),t._v(" "),v("td",[v("a",{attrs:{href:"https://www.ag-grid.com/vue-data-grid/cell-rendering/",target:"_blank",rel:"noopener noreferrer"}},[t._v("Function"),v("OutboundLink")],1)]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("一般场景不建议使用")])]),t._v(" "),v("tr",[v("td",[t._v("valueFormatter")]),t._v(" "),v("td",[v("a",{attrs:{href:"https://www.ag-grid.com/vue-data-grid/value-formatters/",target:"_blank",rel:"noopener noreferrer"}},[t._v("String, Function"),v("OutboundLink")],1)]),t._v(" "),v("td",[t._v("null")]),t._v(" "),v("td",[t._v("格式化渲染单元格文本")])]),t._v(" "),v("tr",[v("td",[t._v("valueSetter")]),t._v(" "),v("td",[v("a",{attrs:{href:"https://www.ag-grid.com/vue-data-grid/value-setters/",target:"_blank",rel:"noopener noreferrer"}},[t._v("Function"),v("OutboundLink")],1)]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("当前单元格值发生变化后的钩子函数，一般用于计算合计、小计")])]),t._v(" "),v("tr",[v("td",[t._v("isSwitchUnit")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("是否参与单位切换，任一列添加该属性，则表格展现切换单位的操作")])]),t._v(" "),v("tr",[v("td",[t._v("isSum")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("是否参与合计，任一列添加该属性，则展现合计行")])]),t._v(" "),v("tr",[v("td",[t._v("cellRendererParams")]),t._v(" "),v("td",[t._v("Object")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("type属性所使用的自定义渲染组件的参数，支持传props，即渲染的自定义组件的属性，同时增加了"),v("code",[t._v("scale: Number")]),t._v("属性用于保留小数位数(0是取整)，"),v("code",[t._v("dateFormat: Boolean")]),t._v("属性用于格式化日期")])]),t._v(" "),v("tr",[v("td",[t._v("calcRule")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("为该列的值设置自动计算公式，公式的每一项为字段名或者数字常量，支持加减乘除，例如'qtySplita+qtySplitb'，在检测到qtySplita qtySplitb值变更后，将自动计算添加了该属性的字段的值")])])])]),t._v(" "),v("h2",{attrs:{id:"slots"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#slots"}},[t._v("#")]),t._v(" slots")]),t._v(" "),v("table",[v("thead",[v("tr",[v("th",[t._v("名称")]),t._v(" "),v("th",[t._v("说明")])])]),t._v(" "),v("tbody",[v("tr",[v("td",[t._v("search")]),t._v(" "),v("td",[t._v("查询条件区域")])]),t._v(" "),v("tr",[v("td",[t._v("tip")]),t._v(" "),v("td",[t._v("补充说明内容")])]),t._v(" "),v("tr",[v("td",[t._v("buttons")]),t._v(" "),v("td",[t._v("左上角按钮区域")])])])]),t._v(" "),v("h2",{attrs:{id:"api"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#api"}},[t._v("#")]),t._v(" Api")]),t._v(" "),v("ul",[v("li",[v("code",[t._v("getXData")]),t._v(" 用于获取以箱为单位的数据，供单位切换的表格实例使用")]),t._v(" "),v("li",[v("code",[t._v("getWZData")]),t._v(" 用于获取以万支为单位的数据，供单位切换的表格实例使用")]),t._v(" "),v("li",[v("code",[t._v("setSelection")]),t._v(" 设置选中行数据，表格必须设置属性"),v("code",[t._v("rowKey")]),t._v("，否则将不能按照所传参数找到数据行")]),t._v(" "),v("li",[v("code",[t._v("getSelection")]),t._v(" 获取选中")]),t._v(" "),v("li",[v("code",[t._v("clearSelection")]),t._v(" 清除选中")]),t._v(" "),v("li",[v("code",[t._v("updateTableData")]),t._v(" 用于更新表格数据")]),t._v(" "),v("li",[v("code",[t._v("toggleUnitIfNeeded")]),t._v(" 通过v-model传递数据，如果有切换单位的列，需要重新获取数据之后，调用toggleUnitIfNeeded，否则数据将匹配不上")])])])}),[],!1,null,null,null);v.default=e.exports}}]);