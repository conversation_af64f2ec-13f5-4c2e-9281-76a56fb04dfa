(window.webpackJsonp=window.webpackJsonp||[]).push([[62],{598:function(t,s,a){"use strict";a.r(s);var n=a(15),r=Object(n.a)({},(function(){var t=this,s=t._self._c;return s("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[s("h2",{attrs:{id:"颜色"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#颜色"}},[t._v("#")]),t._v(" 颜色")]),t._v(" "),s("h3",{attrs:{id:"字体颜色"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#字体颜色"}},[t._v("#")]),t._v(" 字体颜色")]),t._v(" "),s("table",[s("thead",[s("tr",[s("th",[t._v("类")]),t._v(" "),s("th",[t._v("值")]),t._v(" "),s("th",[t._v("说明")])])]),t._v(" "),s("tbody",[s("tr",[s("td",[t._v("ind-primary")]),t._v(" "),s("td",[s("span",{staticClass:"ind-primary"},[t._v("var(--ind-primary-color)")])]),t._v(" "),s("td",[t._v("主颜色，一般用于可点击元素")])]),t._v(" "),s("tr",[s("td",[t._v("ind-red")]),t._v(" "),s("td",[s("span",{staticClass:"ind-red"},[t._v("var(--ind-red)")])]),t._v(" "),s("td",[t._v("红色，指标的增幅等请不要用这个类，用下方的")])]),t._v(" "),s("tr",[s("td",[t._v("ind-down")]),t._v(" "),s("td",[s("span",{staticClass:"ind-down"},[t._v("var(--ind-red)")])]),t._v(" "),s("td",[t._v("指标值下降的颜色，如销量增幅")])]),t._v(" "),s("tr",[s("td",[t._v("ind-up reverse")]),t._v(" "),s("td",[s("span",{staticClass:"ind-up reverse"},[t._v("var(--ind-red)")])]),t._v(" "),s("td",[t._v("反向值指标上升的颜色，如库存增幅")])]),t._v(" "),s("tr",[s("td",[t._v("ind-green")]),t._v(" "),s("td",[s("span",{staticClass:"ind-green"},[t._v("var(--ind-green)")])]),t._v(" "),s("td",[t._v("绿色，指标的增幅等请不要用这个类，用下方的")])]),t._v(" "),s("tr",[s("td",[t._v("ind-up")]),t._v(" "),s("td",[s("span",{staticClass:"ind-up"},[t._v("var(--ind-green)")])]),t._v(" "),s("td",[t._v("指标值上升的颜色，如销量增幅")])]),t._v(" "),s("tr",[s("td",[t._v("ind-down reverse")]),t._v(" "),s("td",[s("span",{staticClass:"ind-down reverse"},[t._v("var(--ind-green)")])]),t._v(" "),s("td",[t._v("反向值指标下降的颜色，如库存增幅")])]),t._v(" "),s("tr",[s("td",[t._v("ind-orange")]),t._v(" "),s("td",[s("span",{staticClass:"ind-orange"},[t._v("var(--ind-orange)")])]),t._v(" "),s("td",[t._v("橙色，一般用于高亮元素")])]),t._v(" "),s("tr",[s("td",[t._v("ind-yellow")]),t._v(" "),s("td",[s("span",{staticClass:"ind-yellow"},[t._v("#fff600")])]),t._v(" "),s("td",[t._v("黄色")])]),t._v(" "),s("tr",[s("td",[t._v("ind-blue")]),t._v(" "),s("td",[s("span",{staticClass:"ind-blue"},[t._v("var(--ind-blue)")])]),t._v(" "),s("td",[t._v("蓝色")])])])]),t._v(" "),s("h3",{attrs:{id:"背景颜色"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#背景颜色"}},[t._v("#")]),t._v(" 背景颜色")]),t._v(" "),s("table",[s("thead",[s("tr",[s("th",[t._v("类")]),t._v(" "),s("th",[t._v("值")]),t._v(" "),s("th",[t._v("说明")])])]),t._v(" "),s("tbody",[s("tr",[s("td",[t._v("ind-bg-primary")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-primary"},[t._v("var(--ind-primary-color)")])]),t._v(" "),s("td",[t._v("主颜色，一般用于可点击元素")])]),t._v(" "),s("tr",[s("td",[t._v("ind-bg-red")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-red"},[t._v("var(--ind-red)")])]),t._v(" "),s("td",[t._v("红色，指标的增幅等请不要用这个类，用下方的")])]),t._v(" "),s("tr",[s("td",[t._v("ind-bg-down")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-down"},[t._v("var(--ind-red)")])]),t._v(" "),s("td",[t._v("指标值下降的颜色，如销量增幅")])]),t._v(" "),s("tr",[s("td",[t._v("ind-bg-up reverse")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-up reverse"},[t._v("var(--ind-red)")])]),t._v(" "),s("td",[t._v("反向值指标上升的颜色，如库存增幅")])]),t._v(" "),s("tr",[s("td",[t._v("ind-bg-green")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-green"},[t._v("var(--ind-green)")])]),t._v(" "),s("td",[t._v("绿色，指标的增幅等请不要用这个类，用下方的")])]),t._v(" "),s("tr",[s("td",[t._v("ind-bg-up")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-up"},[t._v("var(--ind-green)")])]),t._v(" "),s("td",[t._v("指标值上升的颜色，如销量增幅")])]),t._v(" "),s("tr",[s("td",[t._v("ind-bg-down reverse")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-down reverse"},[t._v("var(--ind-green)")])]),t._v(" "),s("td",[t._v("反向值指标下降的颜色，如库存增幅")])]),t._v(" "),s("tr",[s("td",[t._v("ind-bg-orange")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-orange"},[t._v("var(--ind-orange)")])]),t._v(" "),s("td",[t._v("橙色，一般用于高亮元素")])]),t._v(" "),s("tr",[s("td",[t._v("ind-bg-yellow")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-yellow"},[t._v("#fff600")])]),t._v(" "),s("td",[t._v("黄色")])]),t._v(" "),s("tr",[s("td",[t._v("ind-bg-blue")]),t._v(" "),s("td",[s("span",{staticClass:"ind-bg-blue"},[t._v("var(--ind-blue)")])]),t._v(" "),s("td",[t._v("蓝色")])])])]),t._v(" "),s("h2",{attrs:{id:"字体"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#字体"}},[t._v("#")]),t._v(" 字体")]),t._v(" "),s("h3",{attrs:{id:"字体大小"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#字体大小"}},[t._v("#")]),t._v(" 字体大小")]),t._v(" "),s("table",[s("thead",[s("tr",[s("th",[t._v("类")]),t._v(" "),s("th",[t._v("值")]),t._v(" "),s("th",[t._v("说明")])])]),t._v(" "),s("tbody",[s("tr",[s("td",[t._v("ind-sm")]),t._v(" "),s("td",[s("span",{staticClass:"ind-sm"},[t._v("var(--ind-font-size-sm)")])]),t._v(" "),s("td",[t._v("12px，浏览器默认的最小字号")])]),t._v(" "),s("tr",[s("td",[t._v("ind-lg")]),t._v(" "),s("td",[s("span",{staticClass:"ind-lg"},[t._v("var(--ind-font-size-lg)")])]),t._v(" "),s("td",[t._v("16px")])]),t._v(" "),s("tr",[s("td",[t._v("ind-xl")]),t._v(" "),s("td",[s("span",{staticClass:"ind-xl"},[t._v("var(--ind-font-size-xl)")])]),t._v(" "),s("td",[t._v("18px")])]),t._v(" "),s("tr",[s("td",[t._v("ind-xxl")]),t._v(" "),s("td",[s("span",{staticClass:"ind-xxl"},[t._v("var(--ind-font-size-xxl)")])]),t._v(" "),s("td",[t._v("20px")])])])]),t._v(" "),s("h3",{attrs:{id:"字体粗细"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#字体粗细"}},[t._v("#")]),t._v(" 字体粗细")]),t._v(" "),s("table",[s("thead",[s("tr",[s("th",[t._v("类")]),t._v(" "),s("th",[t._v("值")]),t._v(" "),s("th",[t._v("说明")])])]),t._v(" "),s("tbody",[s("tr",[s("td",[t._v("ind-font-bold")]),t._v(" "),s("td",[s("span",{staticClass:"ind-font-bold"},[t._v("var(--ind-font-weight-bold)")])]),t._v(" "),s("td")]),t._v(" "),s("tr",[s("td",[t._v("ind-font-bolder")]),t._v(" "),s("td",[s("span",{staticClass:"ind-font-bolder"},[t._v("var(--ind-font-weight-bolder)")])]),t._v(" "),s("td")])])]),t._v(" "),s("h2",{attrs:{id:"布局"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#布局"}},[t._v("#")]),t._v(" 布局")]),t._v(" "),s("h3",{attrs:{id:"面板块"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#面板块"}},[t._v("#")]),t._v(" 面板块")]),t._v(" "),s("div",{staticClass:"language-less extra-class"},[s("pre",{pre:!0,attrs:{class:"language-less"}},[s("code",[s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-panel")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("padding")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("ind"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("padding"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("xs"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("ind"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("padding"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("sm"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("background-color")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("ind"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("bg"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("color"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("light"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("border-radius")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("ind"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("border"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("radius"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("sm"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n")])])]),s("h3",{attrs:{id:"宽度自适应"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#宽度自适应"}},[t._v("#")]),t._v(" 宽度自适应")]),t._v(" "),s("p",[t._v("用于横向上，有一些元素宽度固定，剩下的元素自动铺满剩余宽度，如页面的左侧侧边栏+右侧内容区域。")]),t._v(" "),s("div",{staticClass:"language-less extra-class"},[s("pre",{pre:!0,attrs:{class:"language-less"}},[s("code",[s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-flex")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token comment"}},[t._v("// 父元素")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("display")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" flex"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v("&.center")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n    "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("align-items")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" center"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v("&.baseline")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n    "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("align-items")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" baseline"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v("&.end")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n    "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("align-items")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" flex"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("end"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n\n"),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-flex-no-shrink")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token comment"}},[t._v("// 宽度固定的元素")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("flex-shrink")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" 0"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n\n"),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-flex-grow")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token comment"}},[t._v("// 宽度要铺满的元素")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("flex-grow")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" 1 "),s("span",{pre:!0,attrs:{class:"token important"}},[t._v("!important")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n")])])]),s("h3",{attrs:{id:"高度自适应"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#高度自适应"}},[t._v("#")]),t._v(" 高度自适应")]),t._v(" "),s("p",[t._v("用于纵向上，有一些元素高度固定，剩下的元素自动铺满剩余高度，如页面的顶部菜单栏+下方内容区域。")]),t._v(" "),s("div",{staticClass:"language-less extra-class"},[s("pre",{pre:!0,attrs:{class:"language-less"}},[s("code",[s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-flex-column")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token comment"}},[t._v("// 父元素")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("display")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" flex"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n\n"),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-flex-no-shrink")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token comment"}},[t._v("// 高度固定的元素")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("flex-shrink")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" 0"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n\n"),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-flex-grow")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token comment"}},[t._v("// 高度要铺满的元素")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("flex-grow")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" 1 "),s("span",{pre:!0,attrs:{class:"token important"}},[t._v("!important")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n")])])]),s("h3",{attrs:{id:"上下左右边距"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#上下左右边距"}},[t._v("#")]),t._v(" 上下左右边距")]),t._v(" "),s("div",{staticClass:"language-less extra-class"},[s("pre",{pre:!0,attrs:{class:"language-less"}},[s("code",[s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-mt")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("margin-top")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("ind"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("padding"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("sm"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-mb")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("margin-top")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("ind"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("padding"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("sm"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-ml")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("margin-left")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("ind"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("padding"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("sm"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ind-mr")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("margin-right")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("ind"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("padding"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("sm"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n")])])]),s("h3",{attrs:{id:"modal、drawer中的form"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#modal、drawer中的form"}},[t._v("#")]),t._v(" Modal、Drawer中的form")]),t._v(" "),s("div",{staticClass:"language-less extra-class"},[s("pre",{pre:!0,attrs:{class:"language-less"}},[s("code",[s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ivu-biz-form-row")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token selector"}},[t._v(".ivu-form")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n    "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("border-top")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" 1px solid "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("border"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("color"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("base"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n    "),s("span",{pre:!0,attrs:{class:"token property"}},[t._v("border-left")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(":")]),t._v(" 1px solid "),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("var")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("border"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("color"),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("-")]),t._v("base"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n  "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n  #"),s("span",{pre:!0,attrs:{class:"token function"}},[t._v("ind-detail-form")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n")])])])])}),[],!1,null,null,null);s.default=r.exports}}]);