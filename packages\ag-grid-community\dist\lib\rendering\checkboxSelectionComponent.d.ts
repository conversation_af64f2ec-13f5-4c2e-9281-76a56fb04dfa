import { Column } from '../entities/column';
import { Component } from '../widgets/component';
import { RowNode } from '../entities/rowNode';
import { CheckboxSelectionCallback } from '../entities/colDef';
import { GroupCheckboxSelectionCallback } from './cellRenderers/groupCellRendererCtrl';
export declare class CheckboxSelectionComponent extends Component {
    private eCheckbox;
    private rowNode;
    private column;
    private overrides?;
    constructor();
    private postConstruct;
    getCheckboxId(): string;
    private onDataChanged;
    private onSelectableChanged;
    private onSelectionChanged;
    private onClicked;
    init(params: {
        rowNode: RowNode;
        column?: Column;
        overrides?: {
            isVisible: boolean | CheckboxSelectionCallback | GroupCheckboxSelectionCallback | undefined;
            callbackParams: any;
            removeHidden: boolean;
        };
    }): void;
    private shouldHandleIndeterminateState;
    private showOrHideSelect;
    private getIsVisible;
}
