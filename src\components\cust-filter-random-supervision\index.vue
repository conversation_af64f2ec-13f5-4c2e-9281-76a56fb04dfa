<template>
  <van-sticky offset-top="46">
    <div class="cust-filter">
      <div class="search-title-box">
        <form class="form" :class="!showMode ? 'wide' : ''" action="/">
          <van-search
            v-model="custSearchText"
            show-action
            placeholder="请输入客户名称或许可证号"
            @search="onFilter"
          >
            <template #action>
              <div class="search" @click="onFilter">搜索</div>
            </template>
          </van-search>
        </form>
        <img
          v-if="showMode"
          class="mode-icon"
          :src="mode == '1' ? listModeIcon : mapModeIcon"
          alt=""
          @click="changeMode"
        />
      </div>
      <van-dropdown-menu>
        <van-dropdown-item
          ref="dropdownItem"
          :title="item.mc04CustFilterGroupName"
          v-for="(item, index) in filterList"
          :key="index"
        >
          <div class="filter-item" v-for="(item2, index2) in item.children" :key="index2">
            <div class="filter-item-title-box">
              <img class="filter-item-title-icon" :src="filterIcon" alt="" />
              <div class="filter-item-title-text">{{ item2.mc04CustFilterName }}</div>
            </div>
            <div class="filter-item-content-box">
              <div
                class="filter-item-content-item"
                :class="isSelected(item2, dictItem.K) ? 'selected' : ''"
                v-for="(dictItem, dictIndex) in item2.dictList"
                :key="dictIndex"
                @click="changeSingleFilterTagStatus(item2, dictItem.K, true)"
              >
                {{ dictItem.V }}
              </div>
            </div>
          </div>

          <div class="btn-box">
            <div class="reset-btn" @click="reset(defaultSearchParams)">重置</div>
            <div class="query-btn" @click="submit">确定</div>
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
    </div>
  </van-sticky>
</template>
<script>
import filterIcon from './imgs/filter-title-icon.png'
import mapModeIcon from './imgs/map-mode-icon.png'
import listModeIcon from './imgs/list-mode-icon.png'
import _ from 'lodash'
export default {
  props: {
    filterList: {
      type: Array,
      default: () => {
        return []
      },
    },
    showMode: {
      type: Boolean,
      default: true,
    },
  },
  watch: {},
  data() {
    return {
      type: 'month-day',
      sliderValue: [0, 100],
      filterIcon,
      mapModeIcon,
      listModeIcon,
      mode: 1, // 0：列表 1：地图
      custSearchText: '',
      searchParams: {
        filterConditionList: [],
      },
      defaultSearchParams: {},
    }
  },
  async activated() {},
  async created() {},
  methods: {
    submit() {
      this.$refs.dropdownItem[0].toggle(false)
      this.onSearch()
    },
    changeMode() {
      if (this.mode == '0') {
        this.mode = '1'
      } else {
        this.mode = '0'
      }
      this.$emit('changeMode', this.mode)
    },
    setDefaultSearchParams(params) {
      this.searchParams = _.cloneDeep(params)
      this.defaultSearchParams = _.cloneDeep(params)
    },
    reset(params) {
      this.searchParams = _.cloneDeep(params)
    },
    onSearch() {
      this.$emit('on-search', {
        filtersConditionRequest: this.searchParams.filterConditionList,
      })
    },
    onFilter() {
      this.$emit('on-filter', this.custSearchText)
    },
    isSelected(item, key) {
      // 寻找当前标签对应的选中的数据
      const filter = this.searchParams.filterConditionList.find(
        (e) => e.filterCode == item.mc04CustFilterCode && e.filterType == item.mc04CustFilterType,
      )
      if (filter && filter.filterKeys.includes(key)) {
        return true
      }
      return false
    },
    changeSingleFilterTagStatus(item, key, required) {
      const filter = this.searchParams.filterConditionList.find(
        (e) => e.filterCode === item.mc04CustFilterCode && e.filterType === item.mc04CustFilterType,
      )
      if (filter) {
        const index = filter.filterKeys.indexOf(key)
        if (index > -1) {
          if (!required) {
            // 如果不必须,可去掉
            filter.filterKeys.splice(index, 1)
          }
        } else {
          filter.filterKeys = [key]
          filter.filterCode = item.mc04CustFilterCode
          filter.filterType = item.mc04CustFilterType
        }
      } else {
        this.searchParams.filterConditionList.push({
          filterCode: item.mc04CustFilterCode,
          filterType: item.mc04CustFilterType,
          filterKeys: [key],
        })
      }
    },
  },
}
</script>
<style lang="less" scoped>
.cust-filter {
  position: sticky;
  top: 46px;
  // z-index: 9999;
  z-index: 2;
  background-color: #ffffff;
  .search-title-box {
    display: flex;
    align-items: center;
    .form {
      flex: 1;
      margin-left: 15px;
      &.wide {
        margin-right: 15px;
      }
      .search {
        font-weight: 400;
        font-size: 13px;
        color: #2d8cf0;
        padding-left: 10px;
        border-left: 1px solid #2d8cf0;
        line-height: 13px;
      }
    }
    .mode-icon {
      width: 44px;
      height: 33px;
      margin: 0 10px;
    }
  }
  .filter-item {
    background-color: #ffffff;
    padding: 20px;
    .filter-item-title-box {
      display: flex;
      align-items: center;
      .filter-item-title-icon {
        width: 10px;
        height: 13px;
      }
      .filter-item-title-text {
        margin-left: 6px;
        font-weight: bold;
        font-size: 15px;
        color: #333333;
      }
    }
    .filter-item-content-box {
      margin-top: 17px;
      // display: flex;
      // flex-wrap: wrap;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 6px;
      .filter-item-content-item {
        text-align: center;
        padding: 10px 20px;
        background: #f6f6f6;
        border-radius: 6px;
        font-weight: 400;
        font-size: 13px;
        line-height: 13px;
        color: #333333;
        &.selected {
          color: #2d8cf0;
          background: #e7f4fd;
          border: 1px solid #b2d7fe;
        }
      }
      .filter-slider {
        width: 80vw;
        .custom-button {
          width: 26px;
          color: #fff;
          font-size: 10px;
          line-height: 18px;
          text-align: center;
          background-color: #2d8cf0;
          border-radius: 100px;
        }
      }
    }
    .filter-item-date-box {
      display: grid;
      gap: 6px;
      .filter-item-date {
        text-align: center;
        padding: 10px 10px;
      }
    }
  }
  .btn-box {
    box-shadow: 0px -1px 7px 0px rgba(0, 0, 0, 0.05);
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
    .reset-btn {
      background: #e7f4fd;
      border-radius: 6px 0px 0px 6px;
      font-weight: 400;
      font-size: 15px;
      color: #2d8cf0;
      padding: 12px 74px;
      // line-height: 6px;
    }
    .query-btn {
      background: linear-gradient(90deg, #67b6fb, #2d8cf0);
      border-radius: 0px 6px 6px 0px;
      font-weight: 400;
      font-size: 15px;
      color: #ffffff;
      padding: 12px 74px;
      // line-height: 6px;
    }
  }
  .situation-list {
    .situation-item {
      padding: 15px 0;
      margin: 0 20px;
      font-weight: 400;
      font-size: 13px;
      color: #333333;
      line-height: 6px;
      border-bottom: solid 1px #f3f3f3;
      &.selected {
        font-weight: bold;
        color: #2d8cf0;
      }
    }
  }
}
/deep/.van-dropdown-menu__bar--opened {
  z-index: 10;
}
/deep/.van-dropdown-menu__title--active {
  color: #2d8cf0;
}

.van-search--show-action {
  // width: calc(100% - 65px);
  padding: 0px !important;
  border-radius: 3px;
  background: #f6f6f6 !important;
  font-weight: 400;
  font-size: 13px !important;
  color: #2d8cf0 !important;

  ::placeholder {
    color: #cccccc;
    font-size: 13px;
  }
}
</style>
