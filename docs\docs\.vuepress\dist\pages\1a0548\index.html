<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>单位切换 | @indfnd/common-mobile</title>
    <meta name="generator" content="VuePress 1.9.9">
    <link rel="icon" href="/ind-common-mobile-doc/img/favicon.ico">
    <meta name="description" content="工业营销一体化前端PC组件库">
    <meta name="theme-color" content="#11a8cd">
    
    <link rel="preload" href="/ind-common-mobile-doc/assets/css/0.styles.5e30afbe.css" as="style"><link rel="preload" href="/ind-common-mobile-doc/assets/js/app.ea3e7bd7.js" as="script"><link rel="preload" href="/ind-common-mobile-doc/assets/js/5.497b83b0.js" as="script"><link rel="preload" href="/ind-common-mobile-doc/assets/js/41.7476f9d7.js" as="script"><link rel="preload" href="/ind-common-mobile-doc/assets/js/9.b8b68c9a.js" as="script"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/1.f887c01b.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/10.c2f7dbc6.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/11.2d8ced3b.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/12.be193899.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/13.1adaeb18.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/14.a59f9f15.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/15.fdb4db63.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/16.c03d7b33.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/17.d218c707.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/18.a32369c7.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/19.e29739a2.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/2.f2d3b607.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/20.d8e8e499.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/21.9b7087d5.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/22.91c48ec9.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/23.e3a0c63b.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/24.6c1647d6.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/25.ec1a045d.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/26.03e2f49e.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/27.ec2b7445.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/28.adf304b5.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/29.40926cb5.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/3.ec1afd9a.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/30.227e65be.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/31.adc66a4a.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/32.ebdd110b.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/33.fa0b77dd.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/34.d71fceff.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/35.6fab15bf.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/36.0ea76172.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/37.6f6d7fd4.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/38.e28d96d5.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/39.5df428e7.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/40.1bb464e8.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/42.afe7eef2.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/43.addd5047.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/44.5a95c474.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/45.eb1c19ea.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/46.d2be3b44.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/47.6ceb750c.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/48.547ac81e.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/49.ab594200.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/50.267ced76.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/51.02715180.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/52.8f251bd7.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/53.9d3cf6b2.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/54.67594984.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/55.45babe73.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/56.90566112.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/57.d94f0e39.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/58.5b35ce87.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/59.f6bb8412.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/6.3d2394bd.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/60.266896d0.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/61.9f8cab45.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/62.5498d7bd.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/63.e112b97d.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/64.0e09ce0d.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/65.735c216a.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/7.76049480.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/8.d79108b0.js">
    <link rel="stylesheet" href="/ind-common-mobile-doc/assets/css/0.styles.5e30afbe.css">
  </head>
  <body class="theme-mode-light">
    <div id="app" data-server-rendered="true"><div class="theme-container sidebar-open have-rightmenu"><header class="navbar blur"><div title="目录" class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/ind-common-mobile-doc/" class="home-link router-link-active"><img src="/ind-common-mobile-doc/img/logo.png" alt="@indfnd/common-mobile" class="logo"> <span class="site-name can-hide">@indfnd/common-mobile</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/ind-common-mobile-doc/pages/a42973/" class="nav-link">概述</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/700a0d/" class="nav-link">组件</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/4212d3/" class="nav-link">指令</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/2ed36d/" class="nav-link">插件</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/fc57ff/" class="nav-link">样式</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/release/" class="nav-link">更新日志</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="其它文档" class="dropdown-title"><!----> <span class="title" style="display:;">其它文档</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="http://10.110.23.208/big-front-end/notes/ind-x1/front.html" target="_blank" rel="noopener noreferrer" class="nav-link external">
  山东一体化开发必读
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="http://10.110.23.208/ind-utils-doc/pages/index/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  工具库
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="/ind-common-mobile-doc/pages/1a0548/404" class="nav-link">移动端组件库(规划中)</a></li></ul></div></div><div class="nav-item"><a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front.git" target="_blank" rel="noopener noreferrer" class="nav-link external">
  仓库地址
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></div> <a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front/-" target="_blank" rel="noopener noreferrer" class="repo-link">
    Source
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <div class="sidebar-hover-trigger"></div> <aside class="sidebar" style="display:none;"><!----> <nav class="nav-links"><div class="nav-item"><a href="/ind-common-mobile-doc/pages/a42973/" class="nav-link">概述</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/700a0d/" class="nav-link">组件</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/4212d3/" class="nav-link">指令</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/2ed36d/" class="nav-link">插件</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/fc57ff/" class="nav-link">样式</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/release/" class="nav-link">更新日志</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="其它文档" class="dropdown-title"><!----> <span class="title" style="display:;">其它文档</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="http://10.110.23.208/big-front-end/notes/ind-x1/front.html" target="_blank" rel="noopener noreferrer" class="nav-link external">
  山东一体化开发必读
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="http://10.110.23.208/ind-utils-doc/pages/index/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  工具库
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="/ind-common-mobile-doc/pages/1a0548/404" class="nav-link">移动端组件库(规划中)</a></li></ul></div></div><div class="nav-item"><a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front.git" target="_blank" rel="noopener noreferrer" class="nav-link external">
  仓库地址
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></div> <a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front/-" target="_blank" rel="noopener noreferrer" class="repo-link">
    Source
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><section class="sidebar-group depth-0"><p class="sidebar-heading"><span>布局</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/700a0d/" class="sidebar-link">查询页 - IndPageView</a></li><li><a href="/ind-common-mobile-doc/pages/b4f8ff/" class="sidebar-link">明细页 - IndDetailView</a></li><li><a href="/ind-common-mobile-doc/pages/766a55/" class="sidebar-link">弹出页 - IndMPopupView</a></li></ul></section></li><li><section class="sidebar-group depth-0"><p class="sidebar-heading open"><span>表格 - IndTable</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/7eaa51/" class="sidebar-link">基础</a></li><li><a href="/ind-common-mobile-doc/pages/53aeec/" class="sidebar-link">锁列</a></li><li><a href="/ind-common-mobile-doc/pages/67d86f/" class="sidebar-link">锁行</a></li><li><a href="/ind-common-mobile-doc/pages/1044e4/" class="sidebar-link">可编辑的列</a></li><li><a href="/ind-common-mobile-doc/pages/1ffe3e/" class="sidebar-link">树形表格</a></li><li><a href="/ind-common-mobile-doc/pages/771469/" class="sidebar-link">选中行</a></li><li><a href="/ind-common-mobile-doc/pages/09c3f0/" class="sidebar-link">表格的高度</a></li><li><a href="/ind-common-mobile-doc/pages/9062de/" class="sidebar-link">导出excel</a></li><li><a href="/ind-common-mobile-doc/pages/dde6ff/" class="sidebar-link">表格的数据类型</a></li><li><a href="/ind-common-mobile-doc/pages/aafd9b/" class="sidebar-link">表格的事件</a></li><li><a href="/ind-common-mobile-doc/pages/9bcc84/" class="sidebar-link">单元格的自定义样式</a></li><li><a href="/ind-common-mobile-doc/pages/cad969/" class="sidebar-link">单元格的格式化</a></li><li><a href="/ind-common-mobile-doc/pages/71bafb/" class="sidebar-link">单元格的宽度</a></li><li><a href="/ind-common-mobile-doc/pages/8bf102/" class="sidebar-link">单元格的对齐方式</a></li><li><a href="/ind-common-mobile-doc/pages/b1213e/" class="sidebar-link">表格排序</a></li><li><a href="/ind-common-mobile-doc/pages/cbb7d2/" class="sidebar-link">数据分组</a></li><li><a href="/ind-common-mobile-doc/pages/95980f/" class="sidebar-link">数据筛选</a></li><li><a href="/ind-common-mobile-doc/pages/0c4049/" class="sidebar-link">预置列定义</a></li><li><a href="/ind-common-mobile-doc/pages/1a0548/" aria-current="page" class="active sidebar-link">单位切换</a><ul class="sidebar-sub-headers"><li class="sidebar-sub-header level2"><a href="/ind-common-mobile-doc/pages/1a0548/#props" class="sidebar-link">props</a></li><li class="sidebar-sub-header level2"><a href="/ind-common-mobile-doc/pages/1a0548/#api" class="sidebar-link">api</a><ul class="sidebar-sub-headers"><li class="sidebar-sub-header level3"><a href="/ind-common-mobile-doc/pages/1a0548/#getxdata" class="sidebar-link">getXData</a></li><li class="sidebar-sub-header level3"><a href="/ind-common-mobile-doc/pages/1a0548/#getwzdata" class="sidebar-link">getWZData</a></li></ul></li><li class="sidebar-sub-header level2"><a href="/ind-common-mobile-doc/pages/1a0548/#列定义" class="sidebar-link">列定义</a><ul class="sidebar-sub-headers"><li class="sidebar-sub-header level3"><a href="/ind-common-mobile-doc/pages/1a0548/#isswitchunit" class="sidebar-link">isSwitchUnit</a></li></ul></li></ul></li><li><a href="/ind-common-mobile-doc/pages/9919e0/" class="sidebar-link">合计行</a></li></ul></section></li><li><section class="sidebar-group depth-0"><p class="sidebar-heading"><span>表单 - IndFormWrap</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/2b4076/" class="sidebar-link">基础</a></li><li><a href="/ind-common-mobile-doc/pages/1410c2/" class="sidebar-link">查询条件表单与业务表单</a></li><li><a href="/ind-common-mobile-doc/pages/69fadf/" class="sidebar-link">有联动的表单</a></li><li><a href="/ind-common-mobile-doc/pages/28cf9c/" class="sidebar-link">表单的校验</a></li><li><a href="/ind-common-mobile-doc/pages/866471/" class="sidebar-link">表单的布局</a></li><li><a href="/ind-common-mobile-doc/pages/fff3ca/" class="sidebar-link">获取表单的元素实例对象</a></li><li><a href="/ind-common-mobile-doc/pages/cc5e41/" class="sidebar-link">自定义渲染的表单元素</a></li></ul></section></li><li><section class="sidebar-group depth-0"><p class="sidebar-heading"><span>树 - IndTree</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/fda86b/" class="sidebar-link">基础</a></li><li><a href="/ind-common-mobile-doc/pages/2a5147/" class="sidebar-link">单选树与多选树</a></li><li><a href="/ind-common-mobile-doc/pages/74aafd/" class="sidebar-link">默认展开级别</a></li><li><a href="/ind-common-mobile-doc/pages/da923e/" class="sidebar-link">禁止点击的节点</a></li><li><a href="/ind-common-mobile-doc/pages/781c8c/" class="sidebar-link">树的事件</a></li><li><a href="/ind-common-mobile-doc/pages/19d553/" class="sidebar-link">获取选中的节点</a></li></ul></section></li><li><section class="sidebar-group depth-0"><p class="sidebar-heading"><span>业务组件</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/ef4ce7/" class="sidebar-link">单位切换</a></li></ul></section></li></ul> </aside> <div><main class="page"><div class="theme-vdoing-wrapper "><div class="articleInfo-wrap" data-v-06225672><div class="articleInfo" data-v-06225672><ul class="breadcrumbs" data-v-06225672><li data-v-06225672><a href="/ind-common-mobile-doc/" title="首页" class="iconfont icon-home router-link-active" data-v-06225672></a></li> <li data-v-06225672><span data-v-06225672>组件</span></li><li data-v-06225672><span data-v-06225672>表格 - IndTable</span></li></ul> <div class="info" data-v-06225672><div title="作者" class="author iconfont icon-touxiang" data-v-06225672><a href="javascript:;" data-v-06225672>工业前端团队</a></div> <div title="创建时间" class="date iconfont icon-riqi" data-v-06225672><a href="javascript:;" data-v-06225672>2024-01-13</a></div> <!----></div></div></div> <!----> <div class="content-wrapper"><div class="right-menu-wrapper"><div class="right-menu-margin"><div class="right-menu-title">目录</div> <div class="right-menu-content"></div></div></div> <h1><!---->单位切换<!----></h1> <!----> <div class="theme-vdoing-content content__default"><div class="demo-wrap"><div class="demo-main"><!----></div> <div class="demo-nav"><i class="demo-nav-btn"><svg t="1572515960134" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1097" width="20" height="20" class="icon"><path d="M888 64H136q-30.016 0-51.008 20.992T64 136v752.992q0 28.992 20.992 50.496t51.008 21.504h752.992q28.992 0 50.496-21.504t21.504-50.496V136q0-30.016-21.504-51.008T888 64zM228.992 548.992q-15.008 0-25.504-10.496t-10.496-25.504 10.016-26.016l115.008-115.008-115.008-116.992q-10.016-11.008-10.016-25.504t10.496-24.992 25.504-10.496 24.992 10.016l140.992 142.016q10.016 11.008 10.016 26.016t-11.008 24.992l-140 140.992q-10.016 11.008-24.992 11.008z m389.024 0l-199.008-0.992q-15.008 0-25.504-10.496T383.008 512t10.496-25.504 25.504-10.496l199.008 0.992q15.008 0 25.504 10.496t10.496 25.504-11.008 25.504-24.992 10.496z" p-id="1098"></path></svg></i> <span class="demo-nav-span">隐藏代码</span> <i class="demo-icon-arrow active"><svg t="1572587847226" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3297" width="16" height="16" class="icon"><path d="M830.687738 603.071182c0 9.614985-3.933589 17.949814-11.799744 25.007557-7.867178 7.05672-17.222243 10.56052-28.065196 10.512425L232.716714 638.591163c-10.789741 0-20.144806-3.5038-28.064172-10.512425-7.919367-7.009647-11.852956-15.344476-11.799744-25.007557 0.053212-9.660011 3.986801-17.996886 11.799744-25.00551l279.05253-248.641917c7.867178-7.009647 17.22122-10.513448 28.065196-10.513448 10.842952 0 20.196994 3.504824 28.064172 10.513448l279.05253 248.641917C826.754149 585.074296 830.687738 593.411171 830.687738 603.071182z" p-id="3298"></path></svg></i></div> <div class="demo-code"><div class="demo-code-nav"><button class="demo-code-btn active">
          Template
        </button><button class="demo-code-btn">
          Script
        </button></div> <div class="demo-code-content"><div class="demo-code-item"><div class="language-vue extra-class"><pre class="language-vue"><code><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>template</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>IndPageView</span> <span class="token special-attr"><span class="token attr-name">style</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span><span class="token value css language-css"><span class="token property">height</span><span class="token punctuation">:</span> 700px</span><span class="token punctuation">&quot;</span></span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>IndTable</span>
      <span class="token attr-name">v-model</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>tableData<span class="token punctuation">&quot;</span></span>
      <span class="token attr-name">ref</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>table<span class="token punctuation">&quot;</span></span>
      <span class="token attr-name">:showTableOption</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>false<span class="token punctuation">&quot;</span></span>
      <span class="token attr-name">:disablePage</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>true<span class="token punctuation">&quot;</span></span>
      <span class="token attr-name">:columns</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>columns<span class="token punctuation">&quot;</span></span>
      <span class="token attr-name">:height</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>500<span class="token punctuation">&quot;</span></span>
      <span class="token attr-name">:headerAutoHeight</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>false<span class="token punctuation">&quot;</span></span>
      <span class="token attr-name">:loading</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>tableLoading<span class="token punctuation">&quot;</span></span>
    <span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>template</span> <span class="token attr-name">#buttons</span><span class="token punctuation">&gt;</span></span> <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>template</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>IndFormWrap</span>
        <span class="token attr-name">slot</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>search<span class="token punctuation">&quot;</span></span>
        <span class="token attr-name">formType</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>search<span class="token punctuation">&quot;</span></span>
        <span class="token attr-name">v-model</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>searchForm<span class="token punctuation">&quot;</span></span>
        <span class="token attr-name">:fieldList</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>searchFieldList<span class="token punctuation">&quot;</span></span>
      <span class="token punctuation">/&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>IndTable</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>IndPageView</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>template</span><span class="token punctuation">&gt;</span></span></code></pre></div></div><div class="demo-code-item" style="display:none;"><div class="language-vue extra-class"><pre class="language-vue"><code><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span><span class="token punctuation">&gt;</span></span><span class="token script"><span class="token language-javascript">
<span class="token keyword">import</span> _ <span class="token keyword">from</span> <span class="token string">'lodash'</span>
<span class="token keyword">import</span> <span class="token punctuation">{</span> searchFieldList<span class="token punctuation">,</span> columnList<span class="token punctuation">,</span> scale <span class="token punctuation">}</span> <span class="token keyword">from</span> <span class="token string">'./utils'</span>
<span class="token keyword">import</span> <span class="token punctuation">{</span> getPreparationList <span class="token punctuation">}</span> <span class="token keyword">from</span> <span class="token string">'./mock'</span>

<span class="token keyword">export</span> <span class="token keyword">default</span> <span class="token punctuation">{</span>
  <span class="token function">data</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword">return</span> <span class="token punctuation">{</span>
      <span class="token literal-property property">searchFieldList</span><span class="token operator">:</span> _<span class="token punctuation">.</span><span class="token function">cloneDeep</span><span class="token punctuation">(</span>searchFieldList<span class="token punctuation">)</span><span class="token punctuation">,</span>
      <span class="token literal-property property">searchForm</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">coms</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
        <span class="token literal-property property">items</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
        <span class="token literal-property property">contDelivWhseId</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
      <span class="token punctuation">}</span><span class="token punctuation">,</span>
      <span class="token literal-property property">tableLoading</span><span class="token operator">:</span> <span class="token boolean">false</span><span class="token punctuation">,</span>

      <span class="token literal-property property">itemList</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token comment">// 卷烟定义</span>
      <span class="token literal-property property">tableData</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span> <span class="token comment">// 数据</span>

      <span class="token literal-property property">scale</span><span class="token operator">:</span> scale<span class="token punctuation">,</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">computed</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token function-variable function">columns</span><span class="token operator">:</span> <span class="token keyword">function</span> <span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword">let</span> leftColumns <span class="token operator">=</span> _<span class="token punctuation">.</span><span class="token function">cloneDeep</span><span class="token punctuation">(</span>columnList<span class="token punctuation">)</span>
      <span class="token keyword">return</span> <span class="token punctuation">[</span>
        <span class="token operator">...</span>leftColumns<span class="token punctuation">,</span>
        <span class="token operator">...</span><span class="token keyword">this</span><span class="token punctuation">.</span>itemList<span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token parameter">d</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
          <span class="token keyword">return</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">title</span><span class="token operator">:</span> d<span class="token punctuation">.</span>itemName<span class="token punctuation">,</span>
            <span class="token literal-property property">children</span><span class="token operator">:</span> <span class="token punctuation">[</span>
              <span class="token punctuation">{</span>
                <span class="token literal-property property">title</span><span class="token operator">:</span> <span class="token string">'计划量'</span><span class="token punctuation">,</span>
                <span class="token literal-property property">field</span><span class="token operator">:</span> d<span class="token punctuation">.</span>itemCode <span class="token operator">+</span> <span class="token string">'_itemDayPlanQty'</span><span class="token punctuation">,</span>
                <span class="token literal-property property">width</span><span class="token operator">:</span> <span class="token number">100</span><span class="token punctuation">,</span>
                <span class="token literal-property property">align</span><span class="token operator">:</span> <span class="token string">'right'</span><span class="token punctuation">,</span>
                <span class="token literal-property property">isSum</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
                <span class="token literal-property property">isSwitchUnit</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
                <span class="token literal-property property">cellRendererParams</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token literal-property property">scale</span><span class="token operator">:</span> <span class="token keyword">this</span><span class="token punctuation">.</span>scale <span class="token punctuation">}</span><span class="token punctuation">,</span>
              <span class="token punctuation">}</span><span class="token punctuation">,</span>
              <span class="token punctuation">{</span>
                <span class="token literal-property property">title</span><span class="token operator">:</span> <span class="token string">'剩余量'</span><span class="token punctuation">,</span>
                <span class="token literal-property property">field</span><span class="token operator">:</span> d<span class="token punctuation">.</span>itemCode <span class="token operator">+</span> <span class="token string">'_sumQty'</span><span class="token punctuation">,</span>
                <span class="token literal-property property">width</span><span class="token operator">:</span> <span class="token number">100</span><span class="token punctuation">,</span>
                <span class="token literal-property property">align</span><span class="token operator">:</span> <span class="token string">'right'</span><span class="token punctuation">,</span>
                <span class="token literal-property property">isSum</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
                <span class="token literal-property property">isSwitchUnit</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
                <span class="token literal-property property">cellRendererParams</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token literal-property property">scale</span><span class="token operator">:</span> <span class="token keyword">this</span><span class="token punctuation">.</span>scale <span class="token punctuation">}</span><span class="token punctuation">,</span>
              <span class="token punctuation">}</span><span class="token punctuation">,</span>
              <span class="token punctuation">{</span>
                <span class="token literal-property property">title</span><span class="token operator">:</span> <span class="token string">'存销比'</span><span class="token punctuation">,</span>
                <span class="token literal-property property">field</span><span class="token operator">:</span> d<span class="token punctuation">.</span>itemCode <span class="token operator">+</span> <span class="token string">'_rateDay'</span><span class="token punctuation">,</span>
                <span class="token literal-property property">width</span><span class="token operator">:</span> <span class="token number">100</span><span class="token punctuation">,</span>
                <span class="token literal-property property">align</span><span class="token operator">:</span> <span class="token string">'right'</span><span class="token punctuation">,</span>
                <span class="token literal-property property">isSwitchUnit</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
                <span class="token literal-property property">cellRendererParams</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token literal-property property">scale</span><span class="token operator">:</span> <span class="token number">2</span> <span class="token punctuation">}</span><span class="token punctuation">,</span>
              <span class="token punctuation">}</span><span class="token punctuation">,</span>
            <span class="token punctuation">]</span><span class="token punctuation">,</span>
          <span class="token punctuation">}</span>
        <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">,</span>
      <span class="token punctuation">]</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token function">mounted</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token function">getData</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">methods</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token keyword">async</span> <span class="token function">getData</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword">this</span><span class="token punctuation">.</span>loading <span class="token operator">=</span> <span class="token boolean">true</span>
      <span class="token keyword">try</span> <span class="token punctuation">{</span>
        <span class="token keyword">const</span> <span class="token punctuation">{</span> data <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword">await</span> <span class="token function">getPreparationList</span><span class="token punctuation">(</span><span class="token keyword">this</span><span class="token punctuation">.</span>searchForm<span class="token punctuation">)</span>
        <span class="token keyword">const</span> <span class="token punctuation">{</span> contractList<span class="token punctuation">,</span> itemList <span class="token punctuation">}</span> <span class="token operator">=</span> data
        contractList<span class="token punctuation">.</span><span class="token function">forEach</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token parameter">d</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
          d<span class="token punctuation">.</span>itemList<span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">(</span><span class="token punctuation">(</span><span class="token parameter">dd</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> <span class="token punctuation">{</span>
            d<span class="token punctuation">[</span>dd<span class="token punctuation">.</span>itemCode <span class="token operator">+</span> <span class="token string">'_itemDayPlanQty'</span><span class="token punctuation">]</span> <span class="token operator">=</span> dd<span class="token punctuation">.</span>itemDayPlanQty
            d<span class="token punctuation">[</span>dd<span class="token punctuation">.</span>itemCode <span class="token operator">+</span> <span class="token string">'_sumQty'</span><span class="token punctuation">]</span> <span class="token operator">=</span> dd<span class="token punctuation">.</span>sumQty
            d<span class="token punctuation">[</span>dd<span class="token punctuation">.</span>itemCode <span class="token operator">+</span> <span class="token string">'_rateDay'</span><span class="token punctuation">]</span> <span class="token operator">=</span> dd<span class="token punctuation">.</span>rateDay
          <span class="token punctuation">}</span><span class="token punctuation">)</span>
        <span class="token punctuation">}</span><span class="token punctuation">)</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span>tableData <span class="token operator">=</span> contractList
        <span class="token keyword">this</span><span class="token punctuation">.</span>itemList <span class="token operator">=</span> itemList
        console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token string">'this.tableData'</span><span class="token punctuation">,</span> <span class="token keyword">this</span><span class="token punctuation">.</span>tableData<span class="token punctuation">,</span> <span class="token string">'this.itemList'</span><span class="token punctuation">,</span> <span class="token keyword">this</span><span class="token punctuation">.</span>itemList<span class="token punctuation">)</span>
      <span class="token punctuation">}</span> <span class="token keyword">catch</span> <span class="token punctuation">(</span>e<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token string">'getPreparationList e'</span><span class="token punctuation">,</span> e<span class="token punctuation">)</span>
      <span class="token punctuation">}</span>
      <span class="token keyword">this</span><span class="token punctuation">.</span>loading <span class="token operator">=</span> <span class="token boolean">false</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token function">doBusi</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword">let</span> rows <span class="token operator">=</span> <span class="token keyword">this</span><span class="token punctuation">.</span>$refs<span class="token punctuation">.</span>table<span class="token punctuation">.</span><span class="token function">getSelection</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
      <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>rows <span class="token operator">||</span> <span class="token operator">!</span>rows<span class="token punctuation">.</span>length<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span>$Message<span class="token punctuation">.</span><span class="token function">warning</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
          <span class="token literal-property property">content</span><span class="token operator">:</span> <span class="token string">'请至少选择一条合同'</span><span class="token punctuation">,</span>
        <span class="token punctuation">}</span><span class="token punctuation">)</span>
      <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{</span>
        console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token string">'rows is'</span><span class="token punctuation">,</span> rows<span class="token punctuation">)</span>
        <span class="token keyword">this</span><span class="token punctuation">.</span>$router<span class="token punctuation">.</span><span class="token function">push</span><span class="token punctuation">(</span><span class="token string">'/ism/am/contract/cont-preparation/preparation'</span><span class="token punctuation">)</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
<span class="token punctuation">}</span>
</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span></code></pre></div></div></div></div></div> <h2 id="props"><a href="#props" class="header-anchor">#</a> props</h2> <div class="language-js extra-class"><pre class="language-js"><code><span class="token literal-property property">isShowWX</span><span class="token operator">:</span> Boolean<span class="token punctuation">,</span> <span class="token comment">// 万箱，当前未遇到场景，有需求再加</span>
<span class="token literal-property property">isShowJ</span><span class="token operator">:</span> Boolean<span class="token punctuation">,</span> <span class="token comment">// 件，当前未遇到场景，有需求再加</span>
<span class="token literal-property property">defaultUnitType</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token comment">// 表格默认的单位，与传入表格的数据必须对应</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> String<span class="token punctuation">,</span>
  <span class="token keyword">default</span><span class="token operator">:</span> <span class="token string">'X'</span><span class="token punctuation">,</span>
<span class="token punctuation">}</span><span class="token punctuation">,</span>
<span class="token literal-property property">xScale</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token comment">// 箱保留位数，默认两位</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> Number<span class="token punctuation">,</span>
  <span class="token keyword">default</span><span class="token operator">:</span> <span class="token number">2</span><span class="token punctuation">,</span>
<span class="token punctuation">}</span><span class="token punctuation">,</span>
<span class="token literal-property property">wzScale</span><span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token comment">// 万支保留小数位数，默认四位</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> Number<span class="token punctuation">,</span>
  <span class="token keyword">default</span><span class="token operator">:</span> <span class="token number">4</span><span class="token punctuation">,</span>
<span class="token punctuation">}</span><span class="token punctuation">,</span>
</code></pre></div><h2 id="api"><a href="#api" class="header-anchor">#</a> api</h2> <p>表格组件增加api，因为业务代码不知道当前选中的单位，必须调用api获取想要的数据</p> <h3 id="getxdata"><a href="#getxdata" class="header-anchor">#</a> getXData</h3> <p><code>getXData(): Array&lt;any&gt;</code></p> <p>获取箱单位的数据，如果当前用户选择箱直接返回数据，如果当前选中万支，返回以箱为单位的数据</p> <h3 id="getwzdata"><a href="#getwzdata" class="header-anchor">#</a> getWZData</h3> <p><code>getWZData(): Array&lt;any&gt;</code></p> <p>获取万支单位的数据，如果当前用户选择万支直接返回数据，如果当前选中箱，返回以万支为单位的数据</p> <h2 id="列定义"><a href="#列定义" class="header-anchor">#</a> 列定义</h2> <h3 id="isswitchunit"><a href="#isswitchunit" class="header-anchor">#</a> isSwitchUnit</h3> <ul><li>类型: <code>Boolean</code></li></ul> <p>为 <code>true</code> 标志当前列参与单位切换，如果没有设置该属性的列，则不展现切换单位</p></div></div> <!----> <div class="page-edit"><div class="edit-link"><a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front/-/edit/main/docs/docs/02.组件/02.表格 - IndTable/19.单位切换.md" target="_blank" rel="noopener noreferrer">编辑文档</a> <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></div> <!----> <div class="last-updated"><span class="prefix">上次更新:</span> <span class="time">2024-02-22 14:22:36</span></div></div> <div class="page-nav-wapper"><div class="page-nav-centre-wrap"><a href="/ind-common-mobile-doc/pages/0c4049/" class="page-nav-centre page-nav-centre-prev"><div class="tooltip">预置列定义</div></a> <a href="/ind-common-mobile-doc/pages/9919e0/" class="page-nav-centre page-nav-centre-next"><div class="tooltip">合计行</div></a></div> <div class="page-nav"><p class="inner"><span class="prev">
        ←
        <a href="/ind-common-mobile-doc/pages/0c4049/" class="prev">预置列定义</a></span> <span class="next"><a href="/ind-common-mobile-doc/pages/9919e0/">合计行</a>→
      </span></p></div></div></div> <!----></main></div> <div class="footer"><div class="icons"></div> 
  Theme by
  <a href="https://github.com/xugaoyi/vuepress-theme-vdoing" target="_blank" title="本站主题">Vdoing</a> <!----></div> <div class="buttons"><div title="返回顶部" class="button blur go-to-top iconfont icon-fanhuidingbu" style="display:none;"></div> <div title="去评论" class="button blur go-to-comment iconfont icon-pinglun" style="display:none;"></div> <div title="主题模式" class="button blur theme-mode-but iconfont icon-zhuti"><ul class="select-box" style="display:none;"><li class="iconfont icon-zidong">
          跟随系统
        </li><li class="iconfont icon-rijianmoshi">
          浅色模式
        </li><li class="iconfont icon-yejianmoshi">
          深色模式
        </li><li class="iconfont icon-yuedu">
          阅读模式
        </li></ul></div></div> <!----> <!----> <!----></div><div class="global-ui"></div></div>
    <script src="/ind-common-mobile-doc/assets/js/app.ea3e7bd7.js" defer></script><script src="/ind-common-mobile-doc/assets/js/5.497b83b0.js" defer></script><script src="/ind-common-mobile-doc/assets/js/41.7476f9d7.js" defer></script><script src="/ind-common-mobile-doc/assets/js/9.b8b68c9a.js" defer></script>
  </body>
</html>
