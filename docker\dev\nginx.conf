user  nginx;
worker_processes  4;
worker_cpu_affinity 0001 0010 0100 1000 ;
error_log  /var/log/nginx/error.log warn;
worker_rlimit_nofile 65535;
pid        /var/run/nginx.pid;

events {
    use epoll;
    worker_connections  40960;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main '$request_time $upstream_response_time $remote_addr $remote_user [$time_local] "$request" $http_host $status $upstream_status $body_bytes_sent "$http_referer" $ssl_protocol $ssl_cipher $upstream_addr "$http_user_agent"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    gzip on;
    gzip_min_length 1k;
    gzip_buffers    4 16k;
    gzip_http_version 1.0;
    gzip_comp_level 6;
    gzip_types text/htm text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;

    server {
        listen       80;
        server_name  localhost;
        client_max_body_size 50M;
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;

        #charset koi8-r;
        #access_log  /var/log/nginx/host.access.log  main;
        location = /ind-mobile-ima {
            root   /apps;
            try_files /ind-mobile-ima/index.html =404;
            if (!-e $request_filename){
                break;
            }
        }

        location ^~ /ind-mobile-ima {
            root   /apps;
            index  index.html;
            if (!-e $request_filename){
                rewrite ^/(.*) https://$host/ind-mobile-ima/index.html? permanent;
                break;
            }
        }

        location /status {
            #check_status;
            stub_status on;
            access_log   off;
            allow all;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }

        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
	    #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
	    #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
	    #location ~ /\.ht {
        #    deny  all;
        #}
    }
}
