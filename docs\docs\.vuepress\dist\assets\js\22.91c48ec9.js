(window.webpackJsonp=window.webpackJsonp||[]).push([[22],{555:function(t,v,_){"use strict";_.r(v);var s=_(15),a=Object(s.a)({},(function(){var t=this,v=t._self._c;return v("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[v("h2",{attrs:{id:"基础用法"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#基础用法"}},[t._v("#")]),t._v(" 基础用法")]),t._v(" "),v("ul",[v("li",[t._v("在最顶部为标题区域，支持传title属性修改标题，默认为功能权限设置的标题")]),t._v(" "),v("li",[t._v("默认slot为展现内容区域")]),t._v(" "),v("li",[t._v("该组件用于全屏弹窗展现信息")])]),t._v(" "),v("h2",{attrs:{id:"props"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#props"}},[t._v("#")]),t._v(" props")]),t._v(" "),v("table",[v("thead",[v("tr",[v("th",[t._v("属性名")]),t._v(" "),v("th",[t._v("类型")]),t._v(" "),v("th",[t._v("默认值")]),t._v(" "),v("th",[t._v("说明")])])]),t._v(" "),v("tbody",[v("tr",[v("td",[t._v("value")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td"),t._v(" "),v("td",[t._v("控制展现隐藏本组件")])]),t._v(" "),v("tr",[v("td",[t._v("title")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td"),t._v(" "),v("td",[t._v("自定义标题内容")])])])]),t._v(" "),v("h2",{attrs:{id:"slots"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#slots"}},[t._v("#")]),t._v(" slots")]),t._v(" "),v("table",[v("thead",[v("tr",[v("th",[t._v("名称")]),t._v(" "),v("th",[t._v("说明")])])]),t._v(" "),v("tbody",[v("tr",[v("td",[t._v("default")]),t._v(" "),v("td",[t._v("组件内容展示区域")])])])])])}),[],!1,null,null,null);v.default=a.exports}}]);