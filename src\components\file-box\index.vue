<template>
  <div class="file-box">
    <div class="file-box-title" v-if="title">{{ title }}</div>
    <div
      class="file-item-box"
      v-for="(item, index) in fileList"
      :key="index"
      @click="clickFile(item)"
    >
      <van-icon class="file-item-icon" color="#027efe" name="link-o" />
      <div class="file-item-title">{{ item.fileName || '' }}</div>
    </div>
    <PreviewBox v-model="previewShow" :id="id" :fileId="fileId"></PreviewBox>
  </div>
</template>

<script>
import PreviewBox from '@/components/preview-box/index'
export default {
  name: 'lamboMFileBox',
  components: {
    PreviewBox,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    fileList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      previewShow: false,
      id: '',
      fileId: '',
    }
  },
  methods: {
    clickFile(file) {
      this.fileId = file.fileId
      this.id = file.fileId
      this.previewShow = true
    },
  },
}
</script>

<style lang="less" scoped>
.file-box {
  margin: 12px;
  background-color: #ffffff;
  padding: 12px;
  border-radius: 10px;
  .file-box-title {
    font-weight: 700 !important;
    line-height: 16px;
    font-size: 16px;
    padding: 5px 3px;
    color: #333 !important;
    margin-bottom: 10px;
  }
  .file-item-box {
    margin-bottom: 5px;
    align-items: center;
    display: flex;
    color: #027efe;
    font-size: 14px;
    .file-item-title {
      margin-left: 4px;
    }
  }
}
</style>
