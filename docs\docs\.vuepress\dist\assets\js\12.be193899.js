(window.webpackJsonp=window.webpackJsonp||[]).push([[12],{411:function(t,e,n){},542:function(t,e,n){"use strict";n(411)},601:function(t,e,n){"use strict";n.r(e);var i={functional:!0,props:{type:{type:String,default:"tip"},text:String,vertical:{type:String,default:"top"}},render:(t,{props:e,slots:n})=>t("span",{class:["badge",e.type],style:{verticalAlign:e.vertical}},e.text||n().default)},a=(n(542),n(15)),p=Object(a.a)(i,void 0,void 0,!1,null,"d5affa18",null);e.default=p.exports}}]);