<template>
  <div class="im-load-more">
    <van-list
      v-model="loading"
      :finished="finished"
      :offset="offset"
      :finished-text="finishedText"
      @load="onLoad"
    >
      <template v-for="item in curData">
        <slot name="item" v-bind:item="item"> </slot>
      </template>
    </van-list>
  </div>
</template>

<script>
import _ from 'lodash'

export default {
  data() {
    return {
      loading: false,
      curIdx: 0,
      curData: [],
    }
  },
  props: {
    // 数据列表
    datas: {
      type: Array,
      default: function () {
        return []
      },
    },
    // 滚动条与底部距离小于 offset 时触发load事件
    offset: {
      type: Number,
      default: 50,
    },
    // 一次加载的数据量
    pageLen: {
      type: Number,
      default: 10,
    },
    finishedText: {
      type: String,
      default: '没有更多了',
    },
  },
  computed: {
    finished() {
      return this.curData.length == this.datas.length
    },
  },
  watch: {
    datas: function () {
      this.curData = this.datas.slice(0, this.pageLen)
      this.curIdx = this.curData.length
    },
  },
  methods: {
    onLoad() {
      this.loading = true
      this.$toast.loading('加载中')
      this.curData = _.concat(
        this.curData,
        this.datas.slice(this.curIdx, this.curIdx + this.pageLen),
      )
      this.curIdx = this.curIdx + this.pageLen
      setTimeout(() => {
        this.loading = false
        this.$toast.clear()
      }, 200)
    },
  },
}
</script>

<style lang="less"></style>
