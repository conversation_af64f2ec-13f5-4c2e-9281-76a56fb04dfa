<template>
  <van-popup class="preview-box" v-model="boxShow" closeable @close="closePopup">
    <div v-if="boxShow" :id="id" class="preview-content"></div>
  </van-popup>
</template>

<script>
import { axios } from '@/utils/request/index'
import { config } from '@/config/config'

export default {
  props: {
    id: { type: String, require: true },
    value: Boolean,
    fileId: { type: String },
  },
  watch: {
    value: {
      immediate: true,
      handler() {
        console.log('1206test v-model value  this.boxshow', this.value, this.boxShow)
        if (this.value) {
          this.boxShow = true
          console.log('preview-modal boxshow = true')
          this.openPreview()
        }
        // else {
        //   console.log('preview-modal boxshow = false')
        //   this.boxShow = false
        // }
      },
    },
  },
  data() {
    return {
      boxShow: this.value,
    }
  },
  methods: {
    closePopup() {
      this.boxShow = false
      this.$emit('input', false)
    },
    getPreviewUrlApi(fileId) {
      return axios.get(
        `${location.protocol}//${location.host}${config.indUcExtServerContent}/api/wps/getPreviewUrl/${fileId}?previewMode=high_definition`,
      )
    },
    async openPreview() {
      try {
        const result = await this.getPreviewUrlApi(this.fileId)
        console.log('wps preview url is', result)
        var tempUrl = ''
        if (result.code == '1') {
          tempUrl = result.data
        } else {
          this.boxShow = false
          this.$emit('input', false)
        }
        console.log('tempUrl is', tempUrl)
        // eslint-disable-next-line no-undef
        const instance = OpenSDK.config({
          url: tempUrl,
          mount: document.getElementById(this.id),
        })
        const token = localStorage['v8-token']
        instance.setToken({
          token: token, // 根据自身的业务需求，通过异步请求或者模板输出的方式，取得 token
          timeout: 10 * 60 * 1000, // token 超时时间，可配合 refreshToken 配置函数使用，在超时前刷新 token
        })
        console.log('wps instance', instance)
      } catch (e) {
        this.boxShow = false
        this.$emit('input', false)
        console.log('openpreveiw err', e)
      }
    },
    onCancel() {
      this.$emit('input', false)
      this.boxShow = false
      this.$emit('input', false)
    },
  },
}
</script>

<style lang="less" scoped>
.preview-box {
  border-radius: 5px;
  overflow: hidden;
}
.preview-content {
  width: 80vw;
  height: 85vh;
}
</style>
