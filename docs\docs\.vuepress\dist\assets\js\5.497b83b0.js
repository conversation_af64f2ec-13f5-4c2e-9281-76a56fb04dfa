(window.webpackJsonp=window.webpackJsonp||[]).push([[5],{285:function(t,e,s){"use strict";var i=s(116),o=s(23);t.exports=function(t,e,s){return s.get&&i(s.get,e,{getter:!0}),s.set&&i(s.set,e,{setter:!0}),o.f(t,e,s)}},286:function(t,e,s){"use strict";var i=s(307),o=s(58),n=s(38),r=s(37),a=function(t){var e=1===t;return function(s,a,l){for(var h,c=n(s),u=o(c),p=r(u),g=i(a,l);p-- >0;)if(g(h=u[p],p,c))switch(t){case 0:return h;case 1:return p}return e?-1:void 0}};t.exports={findLast:a(0),findLastIndex:a(1)}},289:function(t,e,s){"use strict";var i=TypeError;t.exports=function(t,e){if(t<e)throw new i("Not enough arguments");return t}},290:function(t,e,s){"use strict";var i=s(29),o=s(309),n=s(23).f,r=i("unscopables"),a=Array.prototype;void 0===a[r]&&n(a,r,{configurable:!0,value:o(null)}),t.exports=function(t){a[r][t]=!0}},303:function(t,e,s){"use strict";var i=s(110),o=s(9),n=s(111),r=s(289),a=URLSearchParams,l=a.prototype,h=o(l.append),c=o(l.delete),u=o(l.forEach),p=o([].push),g=new a("a=1&a=2&b=3");g.delete("a",1),g.delete("b",void 0),g+""!="a=2"&&i(l,"delete",(function(t){var e=arguments.length,s=e<2?void 0:arguments[1];if(e&&void 0===s)return c(this,t);var i=[];u(this,(function(t,e){p(i,{key:e,value:t})})),r(e,1);for(var o,a=n(t),l=n(s),g=0,d=0,f=!1,m=i.length;g<m;)o=i[g++],f||o.key===a?(f=!0,c(this,o.key)):d++;for(;d<m;)(o=i[d++]).key===a&&o.value===l||h(this,o.key,o.value)}),{enumerable:!0,unsafe:!0})},304:function(t,e,s){"use strict";var i=s(110),o=s(9),n=s(111),r=s(289),a=URLSearchParams,l=a.prototype,h=o(l.getAll),c=o(l.has),u=new a("a=1");!u.has("a",2)&&u.has("a",void 0)||i(l,"has",(function(t){var e=arguments.length,s=e<2?void 0:arguments[1];if(e&&void 0===s)return c(this,t);var i=h(this,t);r(e,1);for(var o=n(s),a=0;a<i.length;)if(i[a++]===o)return!0;return!1}),{enumerable:!0,unsafe:!0})},305:function(t,e,s){"use strict";var i=s(11),o=s(9),n=s(285),r=URLSearchParams.prototype,a=o(r.forEach);i&&!("size"in r)&&n(r,"size",{get:function(){var t=0;return a(this,(function(){t++})),t},configurable:!0,enumerable:!0})},306:function(t,e,s){"use strict";var i=s(28),o=s(286).findLastIndex,n=s(290);i({target:"Array",proto:!0},{findLastIndex:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}}),n("findLastIndex")},307:function(t,e,s){"use strict";var i=s(308),o=s(40),n=s(41),r=i(i.bind);t.exports=function(t,e){return o(t),void 0===e?t:n?r(t,e):function(){return t.apply(e,arguments)}}},308:function(t,e,s){"use strict";var i=s(30),o=s(9);t.exports=function(t){if("Function"===i(t))return o(t)}},309:function(t,e,s){"use strict";var i,o=s(39),n=s(310),r=s(112),a=s(59),l=s(312),h=s(114),c=s(113),u=c("IE_PROTO"),p=function(){},g=function(t){return"<script>"+t+"<\/script>"},d=function(t){t.write(g("")),t.close();var e=t.parentWindow.Object;return t=null,e},f=function(){try{i=new ActiveXObject("htmlfile")}catch(t){}var t,e;f="undefined"!=typeof document?document.domain&&i?d(i):((e=h("iframe")).style.display="none",l.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(g("document.F=Object")),t.close(),t.F):d(i);for(var s=r.length;s--;)delete f.prototype[r[s]];return f()};a[u]=!0,t.exports=Object.create||function(t,e){var s;return null!==t?(p.prototype=o(t),s=new p,p.prototype=null,s[u]=t):s=f(),void 0===e?s:n.f(s,e)}},310:function(t,e,s){"use strict";var i=s(11),o=s(115),n=s(23),r=s(39),a=s(42),l=s(311);e.f=i&&!o?Object.defineProperties:function(t,e){r(t);for(var s,i=a(e),o=l(e),h=o.length,c=0;h>c;)n.f(t,s=o[c++],i[s]);return t}},311:function(t,e,s){"use strict";var i=s(117),o=s(112);t.exports=Object.keys||function(t){return i(t,o)}},312:function(t,e,s){"use strict";var i=s(43);t.exports=i("document","documentElement")},335:function(t,e,s){},336:function(t,e,s){},337:function(t,e,s){},338:function(t,e,s){},339:function(t,e,s){},340:function(t,e,s){},341:function(t,e,s){},342:function(t,e,s){},343:function(t,e,s){},344:function(t,e,s){},345:function(t,e,s){},346:function(t,e,s){},347:function(t,e,s){},348:function(t,e,s){},349:function(t,e){t.exports=function(t){return null==t}},350:function(t,e,s){},351:function(t,e,s){},352:function(t,e,s){},353:function(t,e,s){},354:function(t,e,s){},355:function(t,e,s){},356:function(t,e,s){},357:function(t,e,s){},358:function(t,e,s){},359:function(t,e,s){},360:function(t,e,s){},361:function(t,e,s){},362:function(t,e,s){},363:function(t,e,s){},364:function(t,e,s){},365:function(t,e,s){},412:function(t,e,s){"use strict";s.r(e);var i=s(21),o={name:"SidebarGroup",props:["item","open","collapsable","depth"],components:{DropdownTransition:s(413).a},beforeCreate(){this.$options.components.SidebarLinks=s(412).default},methods:{isActive:i.f}},n=(s(440),s(15)),r=Object(n.a)(o,(function(){var t=this,e=t._self._c;return e("section",{staticClass:"sidebar-group",class:[{collapsable:t.collapsable,"is-sub-group":0!==t.depth},"depth-"+t.depth]},[t.item.path?e("router-link",{staticClass:"sidebar-heading clickable",class:{open:t.open,active:t.isActive(t.$route,t.item.path)},attrs:{to:t.item.path},nativeOn:{click:function(e){return t.$emit("toggle")}}},[e("span",[t._v(t._s(t.item.title))]),t._v(" "),t.collapsable?e("span",{staticClass:"arrow",class:t.open?"down":"right"}):t._e()]):e("p",{staticClass:"sidebar-heading",class:{open:t.open},on:{click:function(e){return t.$emit("toggle")}}},[e("span",[t._v(t._s(t.item.title))]),t._v(" "),t.collapsable?e("span",{staticClass:"arrow",class:t.open?"down":"right"}):t._e()]),t._v(" "),e("DropdownTransition",[t.open||!t.collapsable?e("SidebarLinks",{staticClass:"sidebar-group-items",attrs:{items:t.item.children,"sidebar-depth":t.item.sidebarDepth,"initial-open-group-index":t.item.initialOpenGroupIndex,depth:t.depth+1}}):t._e()],1)],1)}),[],!1,null,null,null).exports;function a(t,e,s,i){return t("router-link",{props:{to:e,activeClass:"",exactActiveClass:""},class:{active:i,"sidebar-link":!0}},s)}function l(t,e,s,o,n,r=1){return!e||r>n?null:t("ul",{class:"sidebar-sub-headers"},e.map(e=>{const h=Object(i.f)(o,s+"#"+e.slug);return t("li",{class:"sidebar-sub-header level"+e.level},[a(t,s+"#"+e.slug,e.title,h),l(t,e.children,s,o,n,r+1)])}))}var h={functional:!0,props:["item","sidebarDepth"],render(t,{parent:{$page:e,$site:s,$route:o,$themeConfig:n,$themeLocaleConfig:r},props:{item:h,sidebarDepth:c}}){const u=Object(i.f)(o,h.path),p="auto"===h.type?u||h.children.some(t=>Object(i.f)(o,h.basePath+"#"+t.slug)):u,g="external"===h.type?function(t,e,s){return t("a",{attrs:{href:e,target:"_blank",rel:"noopener noreferrer"},class:{"sidebar-link":!0}},[s,t("OutboundLink")])}(t,h.path,h.title||h.path):a(t,h.path,h.title||h.path,p),d=[e.frontmatter.sidebarDepth,c,r.sidebarDepth,n.sidebarDepth,1].find(t=>void 0!==t),f=r.displayAllHeaders||n.displayAllHeaders;if("auto"===h.type)return[g,l(t,h.children,h.basePath,o,d)];if((p||f)&&h.headers&&!i.e.test(h.path)){return[g,l(t,Object(i.d)(h.headers),h.path,o,d)]}return g}};s(441);function c(t,e){return"group"===e.type&&e.children.some(e=>"group"===e.type?c(t,e):"page"===e.type&&Object(i.f)(t,e.path))}var u={name:"SidebarLinks",components:{SidebarGroup:r,SidebarLink:Object(n.a)(h,void 0,void 0,!1,null,null,null).exports},props:["items","depth","sidebarDepth","initialOpenGroupIndex"],data(){return{openGroupIndex:this.initialOpenGroupIndex||0}},created(){this.refreshIndex()},watch:{$route(){this.refreshIndex()}},methods:{refreshIndex(){const t=function(t,e){for(let s=0;s<e.length;s++){const i=e[s];if(c(t,i))return s}return-1}(this.$route,this.items);t>-1&&(this.openGroupIndex=t)},toggleGroup(t){this.openGroupIndex=t===this.openGroupIndex?-1:t},isActive(t){return Object(i.f)(this.$route,t.regularPath)}}},p=Object(n.a)(u,(function(){var t=this,e=t._self._c;return t.items.length?e("ul",{staticClass:"sidebar-links"},t._l(t.items,(function(s,i){return e("li",{key:i},["group"===s.type?e("SidebarGroup",{attrs:{item:s,open:i===t.openGroupIndex,collapsable:s.collapsable||s.collapsible,depth:t.depth},on:{toggle:function(e){return t.toggleGroup(i)}}}):e("SidebarLink",{attrs:{sidebarDepth:t.sidebarDepth,item:s}})],1)})),0):t._e()}),[],!1,null,null,null);e.default=p.exports},413:function(t,e,s){"use strict";var i={name:"DropdownTransition",methods:{setHeight(t){t.style.height=t.scrollHeight+"px"},unsetHeight(t){t.style.height=""}}},o=(s(426),s(15)),n=Object(o.a)(i,(function(){return(0,this._self._c)("transition",{attrs:{name:"dropdown"},on:{enter:this.setHeight,"after-enter":this.unsetHeight,"before-leave":this.setHeight}},[this._t("default")],2)}),[],!1,null,null,null);e.a=n.exports},415:function(t,e,s){"use strict";s(335)},416:function(t,e,s){"use strict";s(336)},417:function(t,e,s){"use strict";s(337)},418:function(t,e,s){"use strict";s(338)},419:function(t,e,s){"use strict";s(339)},420:function(t,e,s){"use strict";s(340)},421:function(t,e,s){"use strict";s(341)},422:function(t,e,s){"use strict";s(342)},423:function(t,e,s){"use strict";!function(e,s,i){let o;(o=i.define)&&o.amd?o([],(function(){return s})):(o=i.modules)?o["FlexSearch".toLowerCase()]=s:t.exports=s}(0,function t(e){function s(t,e){const s=e?e.id:t&&t.id;this.id=s||0===s?s:B++,this.init(t,e),r(this,"index",(function(){return this.a?Object.keys(this.a.index[this.a.keys[0]].c):Object.keys(this.c)})),r(this,"length",(function(){return this.index.length}))}function i(t,e,s,i){return this.u!==this.g&&(this.o=this.o.concat(s),this.u++,i&&this.o.length>=i&&(this.u=this.g),this.u===this.g&&(this.cache&&this.j.set(e,this.o),this.F&&this.F(this.o))),this}function o(t,e){const s=t.length,i=b(e),o=[];for(let n=0,r=0;n<s;n++){const s=t[n];(i&&e(s)||!i&&!e[s])&&(o[r++]=s)}return o}function n(t,e,s,i,o,n,r,a,l,h){let c;if(s=m(s,r?0:o,a,n,e,l,h),a&&(a=s.page,c=s.next,s=s.result),r)e=this.where(r,null,o,s);else{for(e=s,s=this.l,o=e.length,n=Array(o),r=0;r<o;r++)n[r]=s[e[r]];e=n}return s=e,i&&(b(i)||(O=i.split(":"),1<O.length?i=d:(O=O[0],i=g)),s.sort(i)),s=f(a,c,s),this.cache&&this.j.set(t,s),s}function r(t,e,s){Object.defineProperty(t,e,{get:s})}function a(t){return new RegExp(t,"g")}function l(t,e){for(let s=0;s<e.length;s+=2)t=t.replace(e[s],e[s+1]);return t}function h(t,e,s,i,o,n,r,a){return e[s]?e[s]:(o=o?(a-(r||a/1.5))*n+(r||a/1.5)*o:n,e[s]=o,o>=r&&((t=(t=t[a-(o+.5>>0)])[s]||(t[s]=[]))[t.length]=i),o)}function c(t,e){if(t){const s=Object.keys(t);for(let i=0,o=s.length;i<o;i++){const o=s[i],n=t[o];if(n)for(let s=0,i=n.length;s<i;s++){if(n[s]===e){1===i?delete t[o]:n.splice(s,1);break}k(n[s])&&c(n[s],e)}}}}function u(t){let e="",s="";var i="";for(let o=0;o<t.length;o++){const n=t[o];n!==s&&(o&&"h"===n?(i="a"===i||"e"===i||"i"===i||"o"===i||"u"===i||"y"===i,(("a"===s||"e"===s||"i"===s||"o"===s||"u"===s||"y"===s)&&i||" "===s)&&(e+=n)):e+=n),i=o===t.length-1?"":t[o+1],s=n}return e}function p(t,e){return 0>(t=t.length-e.length)?1:t?-1:0}function g(t,e){return(t=t[O])<(e=e[O])?-1:t>e?1:0}function d(t,e){const s=O.length;for(let i=0;i<s;i++)t=t[O[i]],e=e[O[i]];return t<e?-1:t>e?1:0}function f(t,e,s){return t?{page:t,next:e?""+e:null,result:s}:s}function m(t,e,s,i,o,n,r){let a,l=[];if(!0===s){s="0";var h=""}else h=s&&s.split(":");const c=t.length;if(1<c){const b=T(),k=[];let w,C;var u,p=0;let _;var g=!0;let x,S,L,B,M,A,O=0;if(h&&(2===h.length?(B=h,h=!1):h=M=parseInt(h[0],10)),r){for(w=T();p<c;p++)if("not"===o[p])for(C=t[p],_=C.length,u=0;u<_;u++)w["@"+C[u]]=1;else L=p+1;if(P(L))return f(s,a,l);p=0}else S=v(o)&&o;for(;p<c;p++){const v=p===(L||c)-1;if(!S||!p)if((u=S||o&&o[p])&&"and"!==u){if("or"!==u)continue;A=!1}else A=n=!0;if(C=t[p],_=C.length){if(g){if(!x){x=C;continue}var d=x.length;for(u=0;u<d;u++){var m="@"+(g=x[u]);r&&w[m]||(b[m]=1,n||(l[O++]=g))}x=null,g=!1}for(m=!1,u=0;u<_;u++){var y="@"+(d=C[u]);const t=n?b[y]||0:p;if(!(!t&&!i||r&&w[y]||!n&&b[y]))if(t===p){if(v){if((!M||--M<O)&&(l[O++]=d,e&&O===e))return f(s,O+(h||0),l)}else b[y]=p+1;m=!0}else i&&((y=k[t]||(k[t]=[]))[y.length]=d)}if(A&&!m&&!i)break}else if(A&&!i)return f(s,a,C)}if(x)if(p=x.length,r)for(u=h?parseInt(h,10):0;u<p;u++)w["@"+(t=x[u])]||(l[O++]=t);else l=x;if(i)for(O=l.length,B?(p=parseInt(B[0],10)+1,u=parseInt(B[1],10)+1):(p=k.length,u=0);p--;)if(d=k[p]){for(_=d.length;u<_;u++)if(i=d[u],(!r||!w["@"+i])&&(l[O++]=i,e&&O===e))return f(s,p+":"+u,l);u=0}}else!c||o&&"not"===o[0]||(l=t[0],h&&(h=parseInt(h[0],10)));return e&&(r=l.length,h&&h>r&&(h=0),a=(h=h||0)+e,a<r?l=l.slice(h,a):(a=0,h&&(l=l.slice(h)))),f(s,a,l)}function v(t){return"string"==typeof t}function y(t){return t.constructor===Array}function b(t){return"function"==typeof t}function k(t){return"object"==typeof t}function P(t){return void 0===t}function w(t){const e=Array(t);for(let s=0;s<t;s++)e[s]=T();return e}function T(){return Object.create(null)}function C(){let t,e;self.onmessage=function(s){if(s=s.data)if(s.search){const i=e.search(s.content,s.threshold?{limit:s.limit,threshold:s.threshold,where:s.where}:s.limit);self.postMessage({id:t,content:s.content,limit:s.limit,result:i})}else s.add?e.add(s.id,s.content):s.update?e.update(s.id,s.content):s.remove?e.remove(s.id):s.clear?e.clear():s.info?((s=e.info()).worker=t,console.log(s)):s.register&&(t=s.id,s.options.cache=!1,s.options.async=!1,s.options.worker=!1,e=new Function(s.register.substring(s.register.indexOf("{")+1,s.register.lastIndexOf("}")))(),e=new e(s.options))}}function _(s,i,o,n){s=e("flexsearch","id"+s,C,(function(t){(t=t.data)&&t.result&&n(t.id,t.content,t.result,t.limit,t.where,t.cursor,t.suggest)}),i);const r=t.toString();return o.id=i,s.postMessage({register:r,options:o,id:i}),s}const x={encode:"icase",f:"forward",split:/\W+/,cache:!1,async:!1,g:!1,D:!1,a:!1,b:9,threshold:0,depth:0},S={memory:{encode:"extra",f:"strict",threshold:0,b:1},speed:{encode:"icase",f:"strict",threshold:1,b:3,depth:2},match:{encode:"extra",f:"full",threshold:1,b:3},score:{encode:"extra",f:"strict",threshold:1,b:9,depth:4},balance:{encode:"balance",f:"strict",threshold:0,b:3,depth:3},fast:{encode:"icase",f:"strict",threshold:8,b:9,depth:1}},L=[];let B=0;const M={},A={};let O;s.create=function(t,e){return new s(t,e)},s.registerMatcher=function(t){for(const e in t)t.hasOwnProperty(e)&&L.push(a(e),t[e]);return this},s.registerEncoder=function(t,e){return I[t]=e.bind(I),this},s.registerLanguage=function(t,e){return M[t]=e.filter,A[t]=e.stemmer,this},s.encode=function(t,e){return I[t](e)},s.prototype.init=function(t,e){if(this.v=[],e){var o=e.preset;t=e}else t||(t=x),o=t.preset;if(e={},v(t)?(e=S[t],t={}):o&&(e=S[o]),o=t.worker)if("undefined"==typeof Worker)t.worker=!1,this.m=null;else{var n=parseInt(o,10)||4;this.C=-1,this.u=0,this.o=[],this.F=null,this.m=Array(n);for(var r=0;r<n;r++)this.m[r]=_(this.id,r,t,i.bind(this))}if(this.f=t.tokenize||e.f||this.f||x.f,this.split=P(o=t.split)?this.split||x.split:v(o)?a(o):o,this.D=t.rtl||this.D||x.D,this.async="undefined"==typeof Promise||P(o=t.async)?this.async||x.async:o,this.g=P(o=t.worker)?this.g||x.g:o,this.threshold=P(o=t.threshold)?e.threshold||this.threshold||x.threshold:o,this.b=P(o=t.resolution)?o=e.b||this.b||x.b:o,o<=this.threshold&&(this.b=this.threshold+1),this.depth="strict"!==this.f||P(o=t.depth)?e.depth||this.depth||x.depth:o,this.w=(o=P(o=t.encode)?e.encode||x.encode:o)&&I[o]&&I[o].bind(I)||(b(o)?o:this.w||!1),(o=t.matcher)&&this.addMatcher(o),o=(e=t.lang)||t.filter){if(v(o)&&(o=M[o]),y(o)){n=this.w,r=T();for(var l=0;l<o.length;l++){var h=n?n(o[l]):o[l];r[h]=1}o=r}this.filter=o}if(o=e||t.stemmer){var c;for(c in e=v(o)?A[o]:o,n=this.w,r=[],e)e.hasOwnProperty(c)&&(l=n?n(c):c,r.push(a(l+"($|\\W)"),n?n(e[c]):e[c]));this.stemmer=c=r}if(this.a=r=(o=t.doc)?function t(e){const s=T();for(const i in e)if(e.hasOwnProperty(i)){const o=e[i];y(o)?s[i]=o.slice(0):k(o)?s[i]=t(o):s[i]=o}return s}(o):this.a||x.a,this.i=w(this.b-(this.threshold||0)),this.h=T(),this.c=T(),r){if(this.l=T(),t.doc=null,c=r.index={},e=r.keys=[],n=r.field,l=r.tag,h=r.store,y(r.id)||(r.id=r.id.split(":")),h){var u=T();if(v(h))u[h]=1;else if(y(h))for(let t=0;t<h.length;t++)u[h[t]]=1;else k(h)&&(u=h);r.store=u}if(l){if(this.G=T(),h=T(),n)if(v(n))h[n]=t;else if(y(n))for(u=0;u<n.length;u++)h[n[u]]=t;else k(n)&&(h=n);for(y(l)||(r.tag=l=[l]),n=0;n<l.length;n++)this.G[l[n]]=T();this.I=l,n=h}if(n){let i;for(y(n)||(k(n)?(i=n,r.field=n=Object.keys(n)):r.field=n=[n]),r=0;r<n.length;r++)y(l=n[r])||(i&&(t=i[l]),e[r]=l,n[r]=l.split(":")),c[l]=new s(t)}t.doc=o}return this.B=!0,this.j=!!(this.cache=o=P(o=t.cache)?this.cache||x.cache:o)&&new D(o),this},s.prototype.encode=function(t){return t&&(L.length&&(t=l(t,L)),this.v.length&&(t=l(t,this.v)),this.w&&(t=this.w(t)),this.stemmer&&(t=l(t,this.stemmer))),t},s.prototype.addMatcher=function(t){const e=this.v;for(const s in t)t.hasOwnProperty(s)&&e.push(a(s),t[s]);return this},s.prototype.add=function(t,e,s,i,n){if(this.a&&k(t))return this.A("add",t,e);if(e&&v(e)&&(t||0===t)){var r="@"+t;if(this.c[r]&&!i)return this.update(t,e);if(this.g)return++this.C>=this.m.length&&(this.C=0),this.m[this.C].postMessage({add:!0,id:t,content:e}),this.c[r]=""+this.C,s&&s(),this;if(!n){if(this.async&&"function"!=typeof importScripts){let o=this;return r=new Promise((function(s){setTimeout((function(){o.add(t,e,null,i,!0),o=null,s()}))})),s?(r.then(s),this):r}if(s)return this.add(t,e,null,i,!0),s(),this}if(!(e=this.encode(e)).length)return this;n=b(s=this.f)?s(e):e.split(this.split),this.filter&&(n=o(n,this.filter));const g=T();g._ctx=T();const d=n.length,f=this.threshold,m=this.depth,v=this.b,y=this.i,k=this.D;for(let e=0;e<d;e++){var a=n[e];if(a){var l=a.length,c=(k?e+1:d-e)/d,u="";switch(s){case"reverse":case"both":for(var p=l;--p;)h(y,g,u=a[p]+u,t,k?1:(l-p)/l,c,f,v-1);u="";case"forward":for(p=0;p<l;p++)h(y,g,u+=a[p],t,k?(p+1)/l:1,c,f,v-1);break;case"full":for(p=0;p<l;p++){const e=(k?p+1:l-p)/l;for(let s=l;s>p;s--)h(y,g,u=a.substring(p,s),t,e,c,f,v-1)}break;default:if(l=h(y,g,a,t,1,c,f,v-1),m&&1<d&&l>=f)for(l=g._ctx[a]||(g._ctx[a]=T()),a=this.h[a]||(this.h[a]=w(v-(f||0))),0>(c=e-m)&&(c=0),(u=e+m+1)>d&&(u=d);c<u;c++)c!==e&&h(a,l,n[c],t,0,v-(c<e?e-c:c-e),f,v-1)}}}this.c[r]=1,this.B=!1}return this},s.prototype.A=function(t,e,s){if(y(e)){var i=e.length;if(i--){for(var o=0;o<i;o++)this.A(t,e[o]);return this.A(t,e[i],s)}}else{var n,r=this.a.index,a=this.a.keys,l=this.a.tag;o=this.a.store;var h=this.a.id;i=e;for(var c=0;c<h.length;c++)i=i[h[c]];if("remove"===t&&(delete this.l[i],h=a.length,h--)){for(e=0;e<h;e++)r[a[e]].remove(i);return r[a[h]].remove(i,s)}if(l){for(n=0;n<l.length;n++){var u=l[n],p=e;for(h=u.split(":"),c=0;c<h.length;c++)p=p[h[c]];p="@"+p}n=(n=this.G[u])[p]||(n[p]=[])}for(let o=0,n=(h=this.a.field).length;o<n;o++){for(u=h[o],l=e,p=0;p<u.length;p++)l=l[u[p]];u=r[a[o]],p="add"===t?u.add:u.update,o===n-1?p.call(u,i,l,s):p.call(u,i,l)}if(o){for(s=Object.keys(o),t=T(),r=0;r<s.length;r++)if(o[a=s[r]]){let s,i;for(a=a.split(":"),h=0;h<a.length;h++)s=(s||e)[l=a[h]],i=(i||t)[l]=s}e=t}n&&(n[n.length]=e),this.l[i]=e}return this},s.prototype.update=function(t,e,s){return this.a&&k(t)?this.A("update",t,e):(this.c["@"+t]&&v(e)&&(this.remove(t),this.add(t,e,s,!0)),this)},s.prototype.remove=function(t,e,s){if(this.a&&k(t))return this.A("remove",t,e);var i="@"+t;if(this.c[i]){if(this.g)return this.m[this.c[i]].postMessage({remove:!0,id:t}),delete this.c[i],e&&e(),this;if(!s){if(this.async&&"function"!=typeof importScripts){let s=this;return i=new Promise((function(e){setTimeout((function(){s.remove(t,null,!0),s=null,e()}))})),e?(i.then(e),this):i}if(e)return this.remove(t,null,!0),e(),this}for(e=0;e<this.b-(this.threshold||0);e++)c(this.i[e],t);this.depth&&c(this.h,t),delete this.c[i],this.B=!1}return this},s.prototype.search=function(t,e,s,i){if(k(e)){if(y(e))for(var r=0;r<e.length;r++)e[r].query=t;else e.query=t;t=e,e=1e3}else e&&b(e)?(s=e,e=1e3):e||0===e||(e=1e3);if(!this.g){var a=[],l=t;if(k(t)&&!y(t)){s||(s=t.callback)&&(l.callback=null);var h=t.sort,c=t.page;e=t.limit,_=t.threshold;var u=t.suggest;t=t.query}if(this.a){_=this.a.index;const o=l.where;var g=l.bool||"or",d=l.field;let p,m,b=g;if(d)y(d)||(d=[d]);else if(y(l)){var f=l;d=[],b=[];for(var P=0;P<l.length;P++)r=(i=l[P]).bool||g,d[P]=i.field,b[P]=r,"not"===r?p=!0:"and"===r&&(m=!0)}else d=this.a.keys;for(g=d.length,P=0;P<g;P++)f&&(l=f[P]),c&&!v(l)&&(l.page=null,l.limit=0),a[P]=_[d[P]].search(l,0);if(s)return s(n.call(this,t,b,a,h,e,u,o,c,m,p));if(this.async){const s=this;return new Promise((function(i){Promise.all(a).then((function(r){i(n.call(s,t,b,r,h,e,u,o,c,m,p))}))}))}return n.call(this,t,b,a,h,e,u,o,c,m,p)}if(_||(_=this.threshold||0),!i){if(this.async&&"function"!=typeof importScripts){let t=this;return _=new Promise((function(s){setTimeout((function(){s(t.search(l,e,null,!0)),t=null}))})),s?(_.then(s),this):_}if(s)return s(this.search(l,e,null,!0)),this}if(!t||!v(t))return a;if(l=t,this.cache)if(this.B){if(s=this.j.get(t))return s}else this.j.clear(),this.B=!0;if(!(l=this.encode(l)).length)return a;s=b(s=this.f)?s(l):l.split(this.split),this.filter&&(s=o(s,this.filter)),f=s.length,i=!0,r=[];var w=T(),C=0;if(1<f&&(this.depth&&"strict"===this.f?g=!0:s.sort(p)),!g||(P=this.h)){const t=this.b;for(;C<f;C++){let e=s[C];if(e){if(g){if(!d)if(P[e])d=e,w[e]=1;else if(!u)return a;if(u&&C===f-1&&!r.length)g=!1,e=d||e,w[e]=0;else if(!d)continue}if(!w[e]){const s=[];let o=!1,n=0;const a=g?P[d]:this.i;if(a){let i;for(let r=0;r<t-_;r++)(i=a[r]&&a[r][e])&&(s[n++]=i,o=!0)}if(o)d=e,r[r.length]=1<n?s.concat.apply([],s):s[0];else if(!u){i=!1;break}w[e]=1}}}}else i=!1;return i&&(a=m(r,e,c,u)),this.cache&&this.j.set(t,a),a}this.F=s,this.u=0,this.o=[];for(var _=0;_<this.g;_++)this.m[_].postMessage({search:!0,limit:e,content:t})},s.prototype.find=function(t,e){return this.where(t,e,1)[0]||null},s.prototype.where=function(t,e,s,i){const o=this.l,n=[];let r,a=0;var l;let h;if(k(t)){s||(s=e);var c=Object.keys(t),u=c.length;if(r=!1,1===u&&"id"===c[0])return[o[t.id]];if((l=this.I)&&!i)for(var p=0;p<l.length;p++){var g=l[p],d=t[g];if(!P(d)){if(h=this.G[g]["@"+d],0==--u)return h;c.splice(c.indexOf(g),1),delete t[g];break}}for(l=Array(u),p=0;p<u;p++)l[p]=c[p].split(":")}else{if(b(t)){for(s=(e=i||Object.keys(o)).length,c=0;c<s;c++)t(u=o[e[c]])&&(n[a++]=u);return n}if(P(e))return[o[t]];if("id"===t)return[o[e]];c=[t],u=1,l=[t.split(":")],r=!0}for(p=(i=h||i||Object.keys(o)).length,g=0;g<p;g++){d=h?i[g]:o[i[g]];let p=!0;for(let s=0;s<u;s++){r||(e=t[c[s]]);const i=l[s],o=i.length;let n=d;if(1<o)for(let t=0;t<o;t++)n=n[i[t]];else n=n[i[0]];if(n!==e){p=!1;break}}if(p&&(n[a++]=d,s&&a===s))break}return n},s.prototype.info=function(){if(!this.g)return{id:this.id,items:this.length,cache:!(!this.cache||!this.cache.s)&&this.cache.s.length,matcher:L.length+(this.v?this.v.length:0),worker:this.g,threshold:this.threshold,depth:this.depth,resolution:this.b,contextual:this.depth&&"strict"===this.f};for(let t=0;t<this.g;t++)this.m[t].postMessage({info:!0,id:this.id})},s.prototype.clear=function(){return this.destroy().init()},s.prototype.destroy=function(){if(this.cache&&(this.j.clear(),this.j=null),this.i=this.h=this.c=null,this.a){const t=this.a.keys;for(let e=0;e<t.length;e++)this.a.index[t[e]].destroy();this.a=this.l=null}return this},s.prototype.export=function(t){const e=!t||P(t.serialize)||t.serialize;if(this.a){const e=!t||P(t.doc)||t.doc;var s=!t||P(t.index)||t.index;t=[];let i=0;if(s)for(s=this.a.keys;i<s.length;i++){const e=this.a.index[s[i]];t[i]=[e.i,e.h,Object.keys(e.c)]}e&&(t[i]=this.l)}else t=[this.i,this.h,Object.keys(this.c)];return e&&(t=JSON.stringify(t)),t},s.prototype.import=function(t,e){(!e||P(e.serialize)||e.serialize)&&(t=JSON.parse(t));const s=T();if(this.a){var i=!e||P(e.doc)||e.doc,o=0;if(!e||P(e.index)||e.index){const i=(e=this.a.keys).length;for(var n=t[0][2];o<n.length;o++)s[n[o]]=1;for(o=0;o<i;o++){n=this.a.index[e[o]];const i=t[o];i&&(n.i=i[0],n.h=i[1],n.c=s)}}i&&(this.l=k(i)?i:t[o])}else{for(i=t[2],o=0;o<i.length;o++)s[i[o]]=1;this.i=t[0],this.h=t[1],this.c=s}};const E=function(){const t=a("\\s+"),e=a("[^a-z0-9 ]"),s=[a("[-/]")," ",e,"",t," "];return function(t){return u(l(t.toLowerCase(),s))}}(),I={icase:function(t){return t.toLowerCase()},simple:function(){const t=a("\\s+"),e=a("[^a-z0-9 ]"),s=a("[-/]"),i=[a("[àáâãäå]"),"a",a("[èéêë]"),"e",a("[ìíîï]"),"i",a("[òóôõöő]"),"o",a("[ùúûüű]"),"u",a("[ýŷÿ]"),"y",a("ñ"),"n",a("[çc]"),"k",a("ß"),"s",a(" & ")," and ",s," ",e,"",t," "];return function(t){return" "===(t=l(t.toLowerCase(),i))?"":t}}(),advanced:function(){const t=a("ae"),e=a("ai"),s=a("ay"),i=a("ey"),o=a("oe"),n=a("ue"),r=a("ie"),h=a("sz"),c=a("zs"),p=a("ck"),g=a("cc"),d=[t,"a",e,"ei",s,"ei",i,"ei",o,"o",n,"u",r,"i",h,"s",c,"s",a("sh"),"s",p,"k",g,"k",a("th"),"t",a("dt"),"t",a("ph"),"f",a("pf"),"f",a("ou"),"o",a("uo"),"u"];return function(t,e){return t?(2<(t=this.simple(t)).length&&(t=l(t,d)),e||1<t.length&&(t=u(t)),t):t}}(),extra:function(){const t=[a("p"),"b",a("z"),"s",a("[cgq]"),"k",a("n"),"m",a("d"),"t",a("[vw]"),"f",a("[aeiouy]"),""];return function(e){if(!e)return e;if(1<(e=this.advanced(e,!0)).length){e=e.split(" ");for(let s=0;s<e.length;s++){const i=e[s];1<i.length&&(e[s]=i[0]+l(i.substring(1),t))}e=u(e=e.join(" "))}return e}}(),balance:E},D=function(){function t(t){this.clear(),this.H=!0!==t&&t}return t.prototype.clear=function(){this.cache=T(),this.count=T(),this.index=T(),this.s=[]},t.prototype.set=function(t,e){if(this.H&&P(this.cache[t])){let s=this.s.length;if(s===this.H){s--;const t=this.s[s];delete this.cache[t],delete this.count[t],delete this.index[t]}this.index[t]=s,this.s[s]=t,this.count[t]=-1,this.cache[t]=e,this.get(t)}else this.cache[t]=e},t.prototype.get=function(t){const e=this.cache[t];if(this.H&&e){var s=++this.count[t];const e=this.index;let o=e[t];if(0<o){const n=this.s;for(var i=o;this.count[n[--o]]<=s&&-1!==o;);if(o++,o!==i){for(s=i;s>o;s--)i=n[s-1],n[s]=i,e[i]=s;n[o]=t,e[t]=o}}}return e},t}();return s}(function(){const t={},e="undefined"!=typeof Blob&&"undefined"!=typeof URL&&URL.createObjectURL;return function(s,i,o,n,r){return o=e?URL.createObjectURL(new Blob(["("+o.toString()+")()"],{type:"text/javascript"})):s+".min.js",t[s+="-"+i]||(t[s]=[]),t[s][r]=new Worker(o),t[s][r].onmessage=n,t[s][r]}}()),this)},424:function(t,e,s){"use strict";s(343)},425:function(t,e,s){"use strict";s(344)},426:function(t,e,s){"use strict";s(345)},427:function(t,e,s){"use strict";s(346)},428:function(t,e,s){"use strict";s(347)},429:function(t,e,s){"use strict";s(348)},430:function(t,e,s){"use strict";s(350)},431:function(t,e,s){var i=s(25),o=s(13),n=s(22);t.exports=function(t){return"string"==typeof t||!o(t)&&n(t)&&"[object String]"==i(t)}},432:function(t,e,s){"use strict";s(351)},433:function(t,e,s){"use strict";s(352)},434:function(t,e,s){"use strict";s(353)},435:function(t,e,s){"use strict";s(354)},436:function(t,e,s){"use strict";s(355)},437:function(t,e,s){"use strict";s(356)},438:function(t,e,s){"use strict";s(357)},439:function(t,e,s){"use strict";s(358)},440:function(t,e,s){"use strict";s(359)},441:function(t,e,s){"use strict";s(360)},442:function(t,e,s){"use strict";s(361)},443:function(t,e,s){"use strict";s(362)},444:function(t,e,s){"use strict";s(363)},445:function(t,e,s){"use strict";s(364)},446:function(t,e,s){"use strict";s(365)},545:function(t,e,s){"use strict";s.r(e);var i=s(21),o={props:{item:{required:!0}},computed:{link(){return Object(i.c)(this.item.link)},exact(){return this.$site.locales?Object.keys(this.$site.locales).some(t=>t===this.link):"/"===this.link}},methods:{isExternal:i.g,isMailto:i.h,isTel:i.i,focusoutAction(){this.$emit("focusout")}}},n=s(15),r=Object(n.a)(o,(function(){var t=this,e=t._self._c;return t.isExternal(t.link)?e("a",{staticClass:"nav-link external",attrs:{href:t.link,target:t.isMailto(t.link)||t.isTel(t.link)?null:"_blank",rel:t.isMailto(t.link)||t.isTel(t.link)?null:"noopener noreferrer"},on:{focusout:t.focusoutAction}},[t._v("\n  "+t._s(t.item.text)+"\n  "),e("OutboundLink")],1):e("router-link",{staticClass:"nav-link",attrs:{to:t.link,exact:t.exact},nativeOn:{focusout:function(e){return t.focusoutAction.apply(null,arguments)}}},[t._v(t._s(t.item.text))])}),[],!1,null,null,null).exports,a=function(t,e){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s])})(t,e)};function l(t,e){function s(){this.constructor=t}a(t,e),t.prototype=null===e?Object.create(e):(s.prototype=e.prototype,new s)}var h=function(){return(h=Object.assign||function(t){for(var e,s=1,i=arguments.length;s<i;s++)for(var o in e=arguments[s])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};function c(){for(var t=0,e=0,s=arguments.length;e<s;e++)t+=arguments[e].length;var i=Array(t),o=0;for(e=0;e<s;e++)for(var n=arguments[e],r=0,a=n.length;r<a;r++,o++)i[o]=n[r];return i}var u=[{sourceKey:"scroller.scrollBehaviorX.currentPos",key:"x"},{sourceKey:"scroller.scrollBehaviorY.currentPos",key:"y"},{sourceKey:"scroller.scrollBehaviorX.hasScroll",key:"hasHorizontalScroll"},{sourceKey:"scroller.scrollBehaviorY.hasScroll",key:"hasVerticalScroll"},{sourceKey:"scroller.scrollBehaviorX.contentSize",key:"scrollerWidth"},{sourceKey:"scroller.scrollBehaviorY.contentSize",key:"scrollerHeight"},{sourceKey:"scroller.scrollBehaviorX.maxScrollPos",key:"maxScrollX"},{sourceKey:"scroller.scrollBehaviorY.maxScrollPos",key:"maxScrollY"},{sourceKey:"scroller.scrollBehaviorX.minScrollPos",key:"minScrollX"},{sourceKey:"scroller.scrollBehaviorY.minScrollPos",key:"minScrollY"},{sourceKey:"scroller.scrollBehaviorX.movingDirection",key:"movingDirectionX"},{sourceKey:"scroller.scrollBehaviorY.movingDirection",key:"movingDirectionY"},{sourceKey:"scroller.scrollBehaviorX.direction",key:"directionX"},{sourceKey:"scroller.scrollBehaviorY.direction",key:"directionY"},{sourceKey:"scroller.actions.enabled",key:"enabled"},{sourceKey:"scroller.animater.pending",key:"pending"},{sourceKey:"scroller.animater.stop",key:"stop"},{sourceKey:"scroller.scrollTo",key:"scrollTo"},{sourceKey:"scroller.scrollBy",key:"scrollBy"},{sourceKey:"scroller.scrollToElement",key:"scrollToElement"},{sourceKey:"scroller.resetPosition",key:"resetPosition"}];function p(t){console.error("[BScroll warn]: "+t)}var g="undefined"!=typeof window,d=g&&navigator.userAgent.toLowerCase(),f=!(!d||!/wechatdevtools/.test(d)),m=d&&d.indexOf("android")>0,v=function(){if("string"==typeof d){var t=/os (\d\d?_\d(_\d)?)/.exec(d);if(!t)return!1;var e=t[1].split("_").map((function(t){return parseInt(t,10)}));return!!(13===e[0]&&e[1]>=4)}return!1}(),y=!1;if(g){try{var b={};Object.defineProperty(b,"passive",{get:function(){y=!0}}),window.addEventListener("test-passive",(function(){}),b)}catch(t){}}function k(){return window.performance&&window.performance.now&&window.performance.timing?window.performance.now()+window.performance.timing.navigationStart:+new Date}var P=function(t,e){for(var s in e)t[s]=e[s];return t};function w(t){return null==t}function T(t,e,s){return t<e?e:t>s?s:t}var C=g&&document.createElement("div").style,_=function(){if(!g)return!1;for(var t=0,e=[{key:"standard",value:"transform"},{key:"webkit",value:"webkitTransform"},{key:"Moz",value:"MozTransform"},{key:"O",value:"OTransform"},{key:"ms",value:"msTransform"}];t<e.length;t++){var s=e[t];if(void 0!==C[s.value])return s.key}return!1}();function x(t){return!1===_?t:"standard"===_?"transitionEnd"===t?"transitionend":t:_+t.charAt(0).toUpperCase()+t.substr(1)}function S(t){return"string"==typeof t?document.querySelector(t):t}function L(t,e,s,i){var o=y?{passive:!1,capture:!!i}:!!i;t.addEventListener(e,s,o)}function B(t,e,s,i){t.removeEventListener(e,s,{capture:!!i})}function M(t){t.cancelable&&t.preventDefault()}function A(t){for(var e=0,s=0;t;)e-=t.offsetLeft,s-=t.offsetTop,t=t.offsetParent;return{left:e,top:s}}_&&"standard"!==_&&_.toLowerCase();var O=x("transform"),E=x("transition"),I=g&&x("perspective")in C,D=g&&("ontouchstart"in window||f),$=g&&E in C,Y={transform:O,transition:E,transitionTimingFunction:x("transitionTimingFunction"),transitionDuration:x("transitionDuration"),transitionDelay:x("transitionDelay"),transformOrigin:x("transformOrigin"),transitionEnd:x("transitionEnd"),transitionProperty:x("transitionProperty")},X={touchstart:1,touchmove:1,touchend:1,touchcancel:1,mousedown:2,mousemove:2,mouseup:2};function N(t){if(t instanceof window.SVGElement){var e=t.getBoundingClientRect();return{top:e.top,left:e.left,width:e.width,height:e.height}}return{top:t.offsetTop,left:t.offsetLeft,width:t.offsetWidth,height:t.offsetHeight}}function j(t,e){for(var s in e)if(e[s].test(t[s]))return!0;return!1}var H=j;function F(t,e){var s;void 0===e&&(e="click"),"mouseup"===t.type?s=t:"touchend"!==t.type&&"touchcancel"!==t.type||(s=t.changedTouches[0]);var i,o={};s&&(o.screenX=s.screenX||0,o.screenY=s.screenY||0,o.clientX=s.clientX||0,o.clientY=s.clientY||0);var n={ctrlKey:t.ctrlKey,shiftKey:t.shiftKey,altKey:t.altKey,metaKey:t.metaKey};if("undefined"!=typeof MouseEvent)try{i=new MouseEvent(e,P(h({bubbles:!0,cancelable:!0},n),o))}catch(t){r()}else r();function r(){(i=document.createEvent("Event")).initEvent(e,!0,!0),P(i,o)}i.forwardedTouchEvent=!0,i._constructed=!0,t.target.dispatchEvent(i)}var U={swipe:{style:"cubic-bezier(0.23, 1, 0.32, 1)",fn:function(t){return 1+--t*t*t*t*t}},swipeBounce:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(t){return t*(2-t)}},bounce:{style:"cubic-bezier(0.165, 0.84, 0.44, 1)",fn:function(t){return 1- --t*t*t*t}}},z=g&&window;function R(){}var q=g?z.requestAnimationFrame||z.webkitRequestAnimationFrame||z.mozRequestAnimationFrame||z.oRequestAnimationFrame||function(t){return window.setTimeout(t,t.interval||1e3/60)}:R,W=g?z.cancelAnimationFrame||z.webkitCancelAnimationFrame||z.mozCancelAnimationFrame||z.oCancelAnimationFrame||function(t){window.clearTimeout(t)}:R,G=function(t){},K={enumerable:!0,configurable:!0,get:G,set:G};var Q=function(){function t(t){this.events={},this.eventTypes={},this.registerType(t)}return t.prototype.on=function(t,e,s){return void 0===s&&(s=this),this.hasType(t),this.events[t]||(this.events[t]=[]),this.events[t].push([e,s]),this},t.prototype.once=function(t,e,s){var i=this;void 0===s&&(s=this),this.hasType(t);var o=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];i.off(t,o);var a=e.apply(s,n);if(!0===a)return a};return o.fn=e,this.on(t,o),this},t.prototype.off=function(t,e){if(!t&&!e)return this.events={},this;if(t){if(this.hasType(t),!e)return this.events[t]=[],this;var s=this.events[t];if(!s)return this;for(var i=s.length;i--;)(s[i][0]===e||s[i][0]&&s[i][0].fn===e)&&s.splice(i,1);return this}},t.prototype.trigger=function(t){for(var e=[],s=1;s<arguments.length;s++)e[s-1]=arguments[s];this.hasType(t);var i=this.events[t];if(i)for(var o,n=i.length,r=c(i),a=0;a<n;a++){var l=r[a],h=l[0],u=l[1];if(h&&!0===(o=h.apply(u,e)))return o}},t.prototype.registerType=function(t){var e=this;t.forEach((function(t){e.eventTypes[t]=t}))},t.prototype.destroy=function(){this.events={},this.eventTypes={}},t.prototype.hasType=function(t){var e=this.eventTypes;e[t]===t||p('EventEmitter has used unknown event type: "'+t+'", should be oneof ['+Object.keys(e).map((function(t){return JSON.stringify(t)}))+"]")},t}(),J=function(){function t(t,e){this.wrapper=t,this.events=e,this.addDOMEvents()}return t.prototype.destroy=function(){this.removeDOMEvents(),this.events=[]},t.prototype.addDOMEvents=function(){this.handleDOMEvents(L)},t.prototype.removeDOMEvents=function(){this.handleDOMEvents(B)},t.prototype.handleDOMEvents=function(t){var e=this,s=this.wrapper;this.events.forEach((function(i){t(s,i.name,e,!!i.capture)}))},t.prototype.handleEvent=function(t){var e=t.type;this.events.some((function(s){return s.name===e&&(s.handler(t),!0)}))},t}(),V=function(t){function e(){var e=t.call(this)||this;return e.startX=0,e.startY=0,e.scrollX=!1,e.scrollY=!0,e.freeScroll=!1,e.directionLockThreshold=0,e.eventPassthrough="",e.click=!1,e.dblclick=!1,e.tap="",e.bounce={top:!0,bottom:!0,left:!0,right:!0},e.bounceTime=800,e.momentum=!0,e.momentumLimitTime=300,e.momentumLimitDistance=15,e.swipeTime=2500,e.swipeBounceTime=500,e.deceleration=.0015,e.flickLimitTime=200,e.flickLimitDistance=100,e.resizePolling=60,e.probeType=0,e.stopPropagation=!1,e.preventDefault=!0,e.preventDefaultException={tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT|AUDIO)$/},e.tagException={tagName:/^TEXTAREA$/},e.HWCompositing=!0,e.useTransition=!0,e.bindToWrapper=!1,e.bindToTarget=!1,e.disableMouse=D,e.disableTouch=!D,e.autoBlur=!0,e.autoEndDistance=5,e.outOfBoundaryDampingFactor=1/3,e.specifiedIndexAsContent=0,e.quadrant=1,e}return l(e,t),e.prototype.merge=function(t){if(!t)return this;for(var e in t)"bounce"!==e?this[e]=t[e]:this.bounce=this.resolveBounce(t[e]);return this},e.prototype.process=function(){return this.translateZ=this.HWCompositing&&I?" translateZ(1px)":"",this.useTransition=this.useTransition&&$,this.preventDefault=!this.eventPassthrough&&this.preventDefault,this.scrollX="horizontal"!==this.eventPassthrough&&this.scrollX,this.scrollY="vertical"!==this.eventPassthrough&&this.scrollY,this.freeScroll=this.freeScroll&&!this.eventPassthrough,this.scrollX=!!this.freeScroll||this.scrollX,this.scrollY=!!this.freeScroll||this.scrollY,this.directionLockThreshold=this.eventPassthrough?0:this.directionLockThreshold,this},e.prototype.resolveBounce=function(t){var e={top:!0,right:!0,bottom:!0,left:!0};return"object"==typeof t?P(e,t):t?e:{top:!1,right:!1,bottom:!1,left:!1}},e}((function(){})),Z=function(){function t(t,e){this.wrapper=t,this.options=e,this.hooks=new Q(["beforeStart","start","move","end","click"]),this.handleDOMEvents()}return t.prototype.handleDOMEvents=function(){var t=this.options,e=t.bindToWrapper,s=t.disableMouse,i=t.disableTouch,o=t.click,n=this.wrapper,r=e?n:window,a=[],l=[],h=!i,c=!s;o&&a.push({name:"click",handler:this.click.bind(this),capture:!0}),h&&(a.push({name:"touchstart",handler:this.start.bind(this)}),l.push({name:"touchmove",handler:this.move.bind(this)},{name:"touchend",handler:this.end.bind(this)},{name:"touchcancel",handler:this.end.bind(this)})),c&&(a.push({name:"mousedown",handler:this.start.bind(this)}),l.push({name:"mousemove",handler:this.move.bind(this)},{name:"mouseup",handler:this.end.bind(this)})),this.wrapperEventRegister=new J(n,a),this.targetEventRegister=new J(r,l)},t.prototype.beforeHandler=function(t,e){var s=this.options,i=s.preventDefault,o=s.stopPropagation,n=s.preventDefaultException;({start:function(){return i&&!j(t.target,n)},end:function(){return i&&!j(t.target,n)},move:function(){return i}})[e]()&&t.preventDefault(),o&&t.stopPropagation()},t.prototype.setInitiated=function(t){void 0===t&&(t=0),this.initiated=t},t.prototype.start=function(t){var e=X[t.type];if(!this.initiated||this.initiated===e)if(this.setInitiated(e),H(t.target,this.options.tagException))this.setInitiated();else if((2!==e||0===t.button)&&!this.hooks.trigger(this.hooks.eventTypes.beforeStart,t)){this.beforeHandler(t,"start");var s=t.touches?t.touches[0]:t;this.pointX=s.pageX,this.pointY=s.pageY,this.hooks.trigger(this.hooks.eventTypes.start,t)}},t.prototype.move=function(t){if(X[t.type]===this.initiated){this.beforeHandler(t,"move");var e=t.touches?t.touches[0]:t,s=e.pageX-this.pointX,i=e.pageY-this.pointY;if(this.pointX=e.pageX,this.pointY=e.pageY,!this.hooks.trigger(this.hooks.eventTypes.move,{deltaX:s,deltaY:i,e:t})){var o=document.documentElement.scrollLeft||window.pageXOffset||document.body.scrollLeft,n=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop,r=this.pointX-o,a=this.pointY-n,l=this.options.autoEndDistance;(r>document.documentElement.clientWidth-l||a>document.documentElement.clientHeight-l||r<l||a<l)&&this.end(t)}}},t.prototype.end=function(t){X[t.type]===this.initiated&&(this.setInitiated(),this.beforeHandler(t,"end"),this.hooks.trigger(this.hooks.eventTypes.end,t))},t.prototype.click=function(t){this.hooks.trigger(this.hooks.eventTypes.click,t)},t.prototype.setContent=function(t){t!==this.wrapper&&(this.wrapper=t,this.rebindDOMEvents())},t.prototype.rebindDOMEvents=function(){this.wrapperEventRegister.destroy(),this.targetEventRegister.destroy(),this.handleDOMEvents()},t.prototype.destroy=function(){this.wrapperEventRegister.destroy(),this.targetEventRegister.destroy(),this.hooks.destroy()},t}(),tt={x:["translateX","px"],y:["translateY","px"]},et=function(){function t(t){this.setContent(t),this.hooks=new Q(["beforeTranslate","translate"])}return t.prototype.getComputedPosition=function(){var t=window.getComputedStyle(this.content,null)[Y.transform].split(")")[0].split(", ");return{x:+(t[12]||t[4])||0,y:+(t[13]||t[5])||0}},t.prototype.translate=function(t){var e=[];Object.keys(t).forEach((function(s){if(tt[s]){var i=tt[s][0];if(i){var o=tt[s][1],n=t[s];e.push(i+"("+n+o+")")}}})),this.hooks.trigger(this.hooks.eventTypes.beforeTranslate,e,t),this.style[Y.transform]=e.join(" "),this.hooks.trigger(this.hooks.eventTypes.translate,t)},t.prototype.setContent=function(t){this.content!==t&&(this.content=t,this.style=t.style)},t.prototype.destroy=function(){this.hooks.destroy()},t}(),st=function(){function t(t,e,s){this.translater=e,this.options=s,this.timer=0,this.hooks=new Q(["move","end","beforeForceStop","forceStop","callStop","time","timeFunction"]),this.setContent(t)}return t.prototype.translate=function(t){this.translater.translate(t)},t.prototype.setPending=function(t){this.pending=t},t.prototype.setForceStopped=function(t){this.forceStopped=t},t.prototype.setCallStop=function(t){this.callStopWhenPending=t},t.prototype.setContent=function(t){this.content!==t&&(this.content=t,this.style=t.style,this.stop())},t.prototype.clearTimer=function(){this.timer&&(W(this.timer),this.timer=0)},t.prototype.destroy=function(){this.hooks.destroy(),W(this.timer)},t}(),it=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return l(e,t),e.prototype.startProbe=function(t,e){var s=this,i=t,o=function(){var n=s.translater.getComputedPosition();(function(t,e,s,i){var o=function(t,e){var s=t-e;return s>0?-1:s<0?1:0},n=o(e.x,t.x),r=o(e.y,t.y),a=s.x-i.x,l=s.y-i.y;return n*a<=0&&r*l<=0})(t,e,n,i)&&s.hooks.trigger(s.hooks.eventTypes.move,n),s.pending||(s.callStopWhenPending?s.callStopWhenPending=!1:s.hooks.trigger(s.hooks.eventTypes.end,n)),i=n,s.pending&&(s.timer=q(o))};this.callStopWhenPending&&this.setCallStop(!1),W(this.timer),o()},e.prototype.transitionTime=function(t){void 0===t&&(t=0),this.style[Y.transitionDuration]=t+"ms",this.hooks.trigger(this.hooks.eventTypes.time,t)},e.prototype.transitionTimingFunction=function(t){this.style[Y.transitionTimingFunction]=t,this.hooks.trigger(this.hooks.eventTypes.timeFunction,t)},e.prototype.transitionProperty=function(){this.style[Y.transitionProperty]=Y.transform},e.prototype.move=function(t,e,s,i){this.setPending(s>0),this.transitionTimingFunction(i),this.transitionProperty(),this.transitionTime(s),this.translate(e);var o=3===this.options.probeType;s&&o&&this.startProbe(t,e),s||(this._reflow=this.content.offsetHeight,o&&this.hooks.trigger(this.hooks.eventTypes.move,e),this.hooks.trigger(this.hooks.eventTypes.end,e))},e.prototype.doStop=function(){var t=this.pending;if(this.setForceStopped(!1),this.setCallStop(!1),t){this.setPending(!1),W(this.timer);var e=this.translater.getComputedPosition(),s=e.x,i=e.y;this.transitionTime(),this.translate({x:s,y:i}),this.setForceStopped(!0),this.setCallStop(!0),this.hooks.trigger(this.hooks.eventTypes.forceStop,{x:s,y:i})}return t},e.prototype.stop=function(){this.doStop()&&this.hooks.trigger(this.hooks.eventTypes.callStop)},e}(st),ot=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return l(e,t),e.prototype.move=function(t,e,s,i){if(!s)return this.translate(e),3===this.options.probeType&&this.hooks.trigger(this.hooks.eventTypes.move,e),void this.hooks.trigger(this.hooks.eventTypes.end,e);this.animate(t,e,s,i)},e.prototype.animate=function(t,e,s,i){var o=this,n=k(),r=n+s,a=3===this.options.probeType,l=function(){var h=k();if(h>=r)return o.translate(e),a&&o.hooks.trigger(o.hooks.eventTypes.move,e),void o.hooks.trigger(o.hooks.eventTypes.end,e);var c=i(h=(h-n)/s),u={};Object.keys(e).forEach((function(s){var i=t[s],o=e[s];u[s]=(o-i)*c+i})),o.translate(u),a&&o.hooks.trigger(o.hooks.eventTypes.move,u),o.pending&&(o.timer=q(l)),o.pending||(o.callStopWhenPending?o.callStopWhenPending=!1:o.hooks.trigger(o.hooks.eventTypes.end,e))};this.setPending(!0),this.callStopWhenPending&&this.setCallStop(!1),W(this.timer),l()},e.prototype.doStop=function(){var t=this.pending;if(this.setForceStopped(!1),this.setCallStop(!1),t){this.setPending(!1),W(this.timer);var e=this.translater.getComputedPosition();this.setForceStopped(!0),this.setCallStop(!0),this.hooks.trigger(this.hooks.eventTypes.forceStop,e)}return t},e.prototype.stop=function(){this.doStop()&&this.hooks.trigger(this.hooks.eventTypes.callStop)},e}(st);var nt,rt,at,lt,ht=function(){function t(t,e,s){this.wrapper=t,this.options=s,this.hooks=new Q(["beforeComputeBoundary","computeBoundary","momentum","end","ignoreHasScroll"]),this.refresh(e)}return t.prototype.start=function(){this.dist=0,this.setMovingDirection(0),this.setDirection(0)},t.prototype.move=function(t){return t=this.hasScroll?t:0,this.setMovingDirection(t),this.performDampingAlgorithm(t,this.options.outOfBoundaryDampingFactor)},t.prototype.setMovingDirection=function(t){this.movingDirection=t>0?-1:t<0?1:0},t.prototype.setDirection=function(t){this.direction=t>0?-1:t<0?1:0},t.prototype.performDampingAlgorithm=function(t,e){var s=this.currentPos+t;return(s>this.minScrollPos||s<this.maxScrollPos)&&(s=s>this.minScrollPos&&this.options.bounces[0]||s<this.maxScrollPos&&this.options.bounces[1]?this.currentPos+t*e:s>this.minScrollPos?this.minScrollPos:this.maxScrollPos),s},t.prototype.end=function(t){var e={duration:0},s=Math.abs(this.currentPos-this.startPos);if(this.options.momentum&&t<this.options.momentumLimitTime&&s>this.options.momentumLimitDistance){var i=-1===this.direction&&this.options.bounces[0]||1===this.direction&&this.options.bounces[1]?this.wrapperSize:0;e=this.hasScroll?this.momentum(this.currentPos,this.startPos,t,this.maxScrollPos,this.minScrollPos,i,this.options):{destination:this.currentPos,duration:0}}else this.hooks.trigger(this.hooks.eventTypes.end,e);return e},t.prototype.momentum=function(t,e,s,i,o,n,r){void 0===r&&(r=this.options);var a=t-e,l=Math.abs(a)/s,h=r.deceleration,c=r.swipeBounceTime,u=r.swipeTime,p={destination:t+l*l/h*(a<0?-1:1),duration:Math.min(u,2*l/h),rate:15};return this.hooks.trigger(this.hooks.eventTypes.momentum,p,a),p.destination<i?(p.destination=n?Math.max(i-n/4,i-n/p.rate*l):i,p.duration=c):p.destination>o&&(p.destination=n?Math.min(o+n/4,o+n/p.rate*l):o,p.duration=c),p.destination=Math.round(p.destination),p},t.prototype.updateDirection=function(){var t=this.currentPos-this.absStartPos;this.setDirection(t)},t.prototype.refresh=function(t){var e=this.options.rect,s=e.size,i=e.position,o="static"===window.getComputedStyle(this.wrapper,null).position,n=N(this.wrapper);this.wrapperSize=this.wrapper["width"===s?"clientWidth":"clientHeight"],this.setContent(t);var r=N(this.content);this.contentSize=r[s],this.relativeOffset=r[i],o&&(this.relativeOffset-=n[i]),this.computeBoundary(),this.setDirection(0)},t.prototype.setContent=function(t){t!==this.content&&(this.content=t,this.resetState())},t.prototype.resetState=function(){this.currentPos=0,this.startPos=0,this.dist=0,this.setDirection(0),this.setMovingDirection(0),this.resetStartPos()},t.prototype.computeBoundary=function(){this.hooks.trigger(this.hooks.eventTypes.beforeComputeBoundary);var t={minScrollPos:0,maxScrollPos:this.wrapperSize-this.contentSize};t.maxScrollPos<0&&(t.maxScrollPos-=this.relativeOffset,0===this.options.specifiedIndexAsContent&&(t.minScrollPos=-this.relativeOffset)),this.hooks.trigger(this.hooks.eventTypes.computeBoundary,t),this.minScrollPos=t.minScrollPos,this.maxScrollPos=t.maxScrollPos,this.hasScroll=this.options.scrollable&&this.maxScrollPos<this.minScrollPos,!this.hasScroll&&this.minScrollPos<this.maxScrollPos&&(this.maxScrollPos=this.minScrollPos,this.contentSize=this.wrapperSize)},t.prototype.updatePosition=function(t){this.currentPos=t},t.prototype.getCurrentPos=function(){return this.currentPos},t.prototype.checkInBoundary=function(){var t=this.adjustPosition(this.currentPos);return{position:t,inBoundary:t===this.getCurrentPos()}},t.prototype.adjustPosition=function(t){return this.hasScroll||this.hooks.trigger(this.hooks.eventTypes.ignoreHasScroll)?t>this.minScrollPos?t=this.minScrollPos:t<this.maxScrollPos&&(t=this.maxScrollPos):t=this.minScrollPos,t},t.prototype.updateStartPos=function(){this.startPos=this.currentPos},t.prototype.updateAbsStartPos=function(){this.absStartPos=this.currentPos},t.prototype.resetStartPos=function(){this.updateStartPos(),this.updateAbsStartPos()},t.prototype.getAbsDist=function(t){return this.dist+=t,Math.abs(this.dist)},t.prototype.destroy=function(){this.hooks.destroy()},t}(),ct=((nt={}).yes=function(t){return!0},nt.no=function(t){return M(t),!1},nt),ut=((rt={}).horizontal=((at={}).yes="horizontal",at.no="vertical",at),rt.vertical=((lt={}).yes="vertical",lt.no="horizontal",lt),rt),pt=function(){function t(t,e,s){this.directionLockThreshold=t,this.freeScroll=e,this.eventPassthrough=s,this.reset()}return t.prototype.reset=function(){this.directionLocked=""},t.prototype.checkMovingDirection=function(t,e,s){return this.computeDirectionLock(t,e),this.handleEventPassthrough(s)},t.prototype.adjustDelta=function(t,e){return"horizontal"===this.directionLocked?e=0:"vertical"===this.directionLocked&&(t=0),{deltaX:t,deltaY:e}},t.prototype.computeDirectionLock=function(t,e){""!==this.directionLocked||this.freeScroll||(t>e+this.directionLockThreshold?this.directionLocked="horizontal":e>=t+this.directionLockThreshold?this.directionLocked="vertical":this.directionLocked="none")},t.prototype.handleEventPassthrough=function(t){var e=ut[this.directionLocked];if(e){if(this.eventPassthrough===e.yes)return ct.yes(t);if(this.eventPassthrough===e.no)return ct.no(t)}return!1},t}(),gt=function(){function t(t,e,s,i,o){this.hooks=new Q(["start","beforeMove","scrollStart","scroll","beforeEnd","end","scrollEnd","contentNotMoved","detectMovingDirection","coordinateTransformation"]),this.scrollBehaviorX=t,this.scrollBehaviorY=e,this.actionsHandler=s,this.animater=i,this.options=o,this.directionLockAction=new pt(o.directionLockThreshold,o.freeScroll,o.eventPassthrough),this.enabled=!0,this.bindActionsHandler()}return t.prototype.bindActionsHandler=function(){var t=this;this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.start,(function(e){return!t.enabled||t.handleStart(e)})),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.move,(function(e){var s=e.deltaX,i=e.deltaY,o=e.e;if(!t.enabled)return!0;var n=function(t,e,s){return 2===s?[e,-t]:3===s?[-t,-e]:4===s?[-e,t]:[t,e]}(s,i,t.options.quadrant),r={deltaX:n[0],deltaY:n[1]};return t.hooks.trigger(t.hooks.eventTypes.coordinateTransformation,r),t.handleMove(r.deltaX,r.deltaY,o)})),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.end,(function(e){return!t.enabled||t.handleEnd(e)})),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.click,(function(e){t.enabled&&!e._constructed&&t.handleClick(e)}))},t.prototype.handleStart=function(t){var e=k();this.fingerMoved=!1,this.contentMoved=!1,this.startTime=e,this.directionLockAction.reset(),this.scrollBehaviorX.start(),this.scrollBehaviorY.start(),this.animater.doStop(),this.scrollBehaviorX.resetStartPos(),this.scrollBehaviorY.resetStartPos(),this.hooks.trigger(this.hooks.eventTypes.start,t)},t.prototype.handleMove=function(t,e,s){if(!this.hooks.trigger(this.hooks.eventTypes.beforeMove,s)){var i=this.scrollBehaviorX.getAbsDist(t),o=this.scrollBehaviorY.getAbsDist(e),n=k();if(this.checkMomentum(i,o,n))return!0;if(this.directionLockAction.checkMovingDirection(i,o,s))return this.actionsHandler.setInitiated(),!0;var r=this.directionLockAction.adjustDelta(t,e),a=this.scrollBehaviorX.getCurrentPos(),l=this.scrollBehaviorX.move(r.deltaX),h=this.scrollBehaviorY.getCurrentPos(),c=this.scrollBehaviorY.move(r.deltaY);if(!this.hooks.trigger(this.hooks.eventTypes.detectMovingDirection)){this.fingerMoved||(this.fingerMoved=!0);var u=l!==a||c!==h;this.contentMoved||u||this.hooks.trigger(this.hooks.eventTypes.contentNotMoved),!this.contentMoved&&u&&(this.contentMoved=!0,this.hooks.trigger(this.hooks.eventTypes.scrollStart)),this.contentMoved&&u&&(this.animater.translate({x:l,y:c}),this.dispatchScroll(n))}}},t.prototype.dispatchScroll=function(t){t-this.startTime>this.options.momentumLimitTime&&(this.startTime=t,this.scrollBehaviorX.updateStartPos(),this.scrollBehaviorY.updateStartPos(),1===this.options.probeType&&this.hooks.trigger(this.hooks.eventTypes.scroll,this.getCurrentPos())),this.options.probeType>1&&this.hooks.trigger(this.hooks.eventTypes.scroll,this.getCurrentPos())},t.prototype.checkMomentum=function(t,e,s){return s-this.endTime>this.options.momentumLimitTime&&e<this.options.momentumLimitDistance&&t<this.options.momentumLimitDistance},t.prototype.handleEnd=function(t){if(!this.hooks.trigger(this.hooks.eventTypes.beforeEnd,t)){var e=this.getCurrentPos();if(this.scrollBehaviorX.updateDirection(),this.scrollBehaviorY.updateDirection(),this.hooks.trigger(this.hooks.eventTypes.end,t,e))return!0;e=this.ensureIntegerPos(e),this.animater.translate(e),this.endTime=k();var s=this.endTime-this.startTime;this.hooks.trigger(this.hooks.eventTypes.scrollEnd,e,s)}},t.prototype.ensureIntegerPos=function(t){this.ensuringInteger=!0;var e=t.x,s=t.y,i=this.scrollBehaviorX,o=i.minScrollPos,n=i.maxScrollPos,r=this.scrollBehaviorY,a=r.minScrollPos,l=r.maxScrollPos;return e=e>0?Math.ceil(e):Math.floor(e),s=s>0?Math.ceil(s):Math.floor(s),{x:e=T(e,n,o),y:s=T(s,l,a)}},t.prototype.handleClick=function(t){j(t.target,this.options.preventDefaultException)||(M(t),t.stopPropagation())},t.prototype.getCurrentPos=function(){return{x:this.scrollBehaviorX.getCurrentPos(),y:this.scrollBehaviorY.getCurrentPos()}},t.prototype.refresh=function(){this.endTime=0},t.prototype.destroy=function(){this.hooks.destroy()},t}();function dt(t,e,s,i){var o=["momentum","momentumLimitTime","momentumLimitDistance","deceleration","swipeBounceTime","swipeTime","outOfBoundaryDampingFactor","specifiedIndexAsContent"].reduce((function(e,s){return e[s]=t[s],e}),{});return o.scrollable=!!t[e],o.bounces=s,o.rect=i,o}function ft(t,e,s){s.forEach((function(s){var i,o;"string"==typeof s?i=o=s:(i=s.source,o=s.target),t.on(i,(function(){for(var t=[],s=0;s<arguments.length;s++)t[s]=arguments[s];return e.trigger.apply(e,c([o],t))}))}))}var mt=function(){function t(t,e,s){this.wrapper=t,this.content=e,this.resizeTimeout=0,this.hooks=new Q(["beforeStart","beforeMove","beforeScrollStart","scrollStart","scroll","beforeEnd","scrollEnd","resize","touchEnd","end","flick","scrollCancel","momentum","scrollTo","minDistanceScroll","scrollToElement","beforeRefresh"]),this.options=s;var i,o=this.options.bounce,n=o.left,r=o.right,a=o.top,l=o.bottom;this.scrollBehaviorX=new ht(t,e,dt(s,"scrollX",[n,r],{size:"width",position:"left"})),this.scrollBehaviorY=new ht(t,e,dt(s,"scrollY",[a,l],{size:"height",position:"top"})),this.translater=new et(this.content),this.animater=function(t,e,s){var i=s.useTransition,o={};return Object.defineProperty(o,"probeType",{enumerable:!0,configurable:!1,get:function(){return s.probeType}}),i?new it(t,e,o):new ot(t,e,o)}(this.content,this.translater,this.options),this.actionsHandler=new Z(this.options.bindToTarget?this.content:t,(i=this.options,["click","bindToWrapper","disableMouse","disableTouch","preventDefault","stopPropagation","tagException","preventDefaultException","autoEndDistance"].reduce((function(t,e){return t[e]=i[e],t}),{}))),this.actions=new gt(this.scrollBehaviorX,this.scrollBehaviorY,this.actionsHandler,this.animater,this.options);var h=this.resize.bind(this);this.resizeRegister=new J(window,[{name:"orientationchange",handler:h},{name:"resize",handler:h}]),this.registerTransitionEnd(),this.init()}return t.prototype.init=function(){var t=this;this.bindTranslater(),this.bindAnimater(),this.bindActions(),this.hooks.on(this.hooks.eventTypes.scrollEnd,(function(){t.togglePointerEvents(!0)}))},t.prototype.registerTransitionEnd=function(){this.transitionEndRegister=new J(this.content,[{name:Y.transitionEnd,handler:this.transitionEnd.bind(this)}])},t.prototype.bindTranslater=function(){var t=this,e=this.translater.hooks;e.on(e.eventTypes.beforeTranslate,(function(e){t.options.translateZ&&e.push(t.options.translateZ)})),e.on(e.eventTypes.translate,(function(e){var s=t.getCurrentPos();t.updatePositions(e),!0!==t.actions.ensuringInteger?e.x===s.x&&e.y===s.y||t.togglePointerEvents(!1):t.actions.ensuringInteger=!1}))},t.prototype.bindAnimater=function(){var t=this;this.animater.hooks.on(this.animater.hooks.eventTypes.end,(function(e){t.resetPosition(t.options.bounceTime)||(t.animater.setPending(!1),t.hooks.trigger(t.hooks.eventTypes.scrollEnd,e))})),ft(this.animater.hooks,this.hooks,[{source:this.animater.hooks.eventTypes.move,target:this.hooks.eventTypes.scroll},{source:this.animater.hooks.eventTypes.forceStop,target:this.hooks.eventTypes.scrollEnd}])},t.prototype.bindActions=function(){var t=this,e=this.actions;ft(e.hooks,this.hooks,[{source:e.hooks.eventTypes.start,target:this.hooks.eventTypes.beforeStart},{source:e.hooks.eventTypes.start,target:this.hooks.eventTypes.beforeScrollStart},{source:e.hooks.eventTypes.beforeMove,target:this.hooks.eventTypes.beforeMove},{source:e.hooks.eventTypes.scrollStart,target:this.hooks.eventTypes.scrollStart},{source:e.hooks.eventTypes.scroll,target:this.hooks.eventTypes.scroll},{source:e.hooks.eventTypes.beforeEnd,target:this.hooks.eventTypes.beforeEnd}]),e.hooks.on(e.hooks.eventTypes.end,(function(s,i){return t.hooks.trigger(t.hooks.eventTypes.touchEnd,i),!!t.hooks.trigger(t.hooks.eventTypes.end,i)||(!(e.fingerMoved||(t.hooks.trigger(t.hooks.eventTypes.scrollCancel),!t.checkClick(s)))||(t.resetPosition(t.options.bounceTime,U.bounce)?(t.animater.setForceStopped(!1),!0):void 0))})),e.hooks.on(e.hooks.eventTypes.scrollEnd,(function(s,i){var o=Math.abs(s.x-t.scrollBehaviorX.startPos),n=Math.abs(s.y-t.scrollBehaviorY.startPos);if(t.checkFlick(i,o,n))return t.animater.setForceStopped(!1),void t.hooks.trigger(t.hooks.eventTypes.flick);t.momentum(s,i)?t.animater.setForceStopped(!1):(e.contentMoved&&t.hooks.trigger(t.hooks.eventTypes.scrollEnd,s),t.animater.forceStopped&&t.animater.setForceStopped(!1))}))},t.prototype.checkFlick=function(t,e,s){if(this.hooks.events.flick.length>1&&t<this.options.flickLimitTime&&e<this.options.flickLimitDistance&&s<this.options.flickLimitDistance&&(s>1||e>1))return!0},t.prototype.momentum=function(t,e){var s={time:0,easing:U.swiper,newX:t.x,newY:t.y},i=this.scrollBehaviorX.end(e),o=this.scrollBehaviorY.end(e);if(s.newX=w(i.destination)?s.newX:i.destination,s.newY=w(o.destination)?s.newY:o.destination,s.time=Math.max(i.duration,o.duration),this.hooks.trigger(this.hooks.eventTypes.momentum,s,this),s.newX!==t.x||s.newY!==t.y)return(s.newX>this.scrollBehaviorX.minScrollPos||s.newX<this.scrollBehaviorX.maxScrollPos||s.newY>this.scrollBehaviorY.minScrollPos||s.newY<this.scrollBehaviorY.maxScrollPos)&&(s.easing=U.swipeBounce),this.scrollTo(s.newX,s.newY,s.time,s.easing),!0},t.prototype.checkClick=function(t){var e=this.animater.forceStopped;if(this.hooks.trigger(this.hooks.eventTypes.checkClick))return this.animater.setForceStopped(!1),!0;if(!e){var s=this.options.dblclick,i=!1;if(s&&this.lastClickTime){var o=s.delay,n=void 0===o?300:o;k()-this.lastClickTime<n&&(i=!0,function(t){F(t,"dblclick")}(t))}return this.options.tap&&function(t,e){var s=document.createEvent("Event");s.initEvent(e,!0,!0),s.pageX=t.pageX,s.pageY=t.pageY,t.target.dispatchEvent(s)}(t,this.options.tap),this.options.click&&!j(t.target,this.options.preventDefaultException)&&F(t),this.lastClickTime=i?null:k(),!0}return!1},t.prototype.resize=function(){var t=this;this.actions.enabled&&(m&&(this.wrapper.scrollTop=0),clearTimeout(this.resizeTimeout),this.resizeTimeout=window.setTimeout((function(){t.hooks.trigger(t.hooks.eventTypes.resize)}),this.options.resizePolling))},t.prototype.transitionEnd=function(t){t.target===this.content&&this.animater.pending&&(this.animater.transitionTime(),this.resetPosition(this.options.bounceTime,U.bounce)||(this.animater.setPending(!1),3!==this.options.probeType&&this.hooks.trigger(this.hooks.eventTypes.scrollEnd,this.getCurrentPos())))},t.prototype.togglePointerEvents=function(t){void 0===t&&(t=!0);for(var e=this.content.children.length?this.content.children:[this.content],s=t?"auto":"none",i=0;i<e.length;i++){var o=e[i];o.isBScrollContainer||(o.style.pointerEvents=s)}},t.prototype.refresh=function(t){var e=this.setContent(t);this.hooks.trigger(this.hooks.eventTypes.beforeRefresh),this.scrollBehaviorX.refresh(t),this.scrollBehaviorY.refresh(t),e&&(this.translater.setContent(t),this.animater.setContent(t),this.transitionEndRegister.destroy(),this.registerTransitionEnd(),this.options.bindToTarget&&this.actionsHandler.setContent(t)),this.actions.refresh(),this.wrapperOffset=A(this.wrapper)},t.prototype.setContent=function(t){var e=t!==this.content;return e&&(this.content=t),e},t.prototype.scrollBy=function(t,e,s,i){void 0===s&&(s=0);var o=this.getCurrentPos(),n=o.x,r=o.y;i=i||U.bounce,t+=n,e+=r,this.scrollTo(t,e,s,i)},t.prototype.scrollTo=function(t,e,s,i,o){void 0===s&&(s=0),void 0===i&&(i=U.bounce),void 0===o&&(o={start:{},end:{}});var n=this.options.useTransition?i.style:i.fn,r=this.getCurrentPos(),a=h({x:r.x,y:r.y},o.start),l=h({x:t,y:e},o.end);if(this.hooks.trigger(this.hooks.eventTypes.scrollTo,l),!function(t,e){for(var s=0,i=Object.keys(t);s<i.length;s++){var o=i[s];if(t[o]!==e[o])return!1}return!0}(a,l)){var c=Math.abs(l.x-a.x),u=Math.abs(l.y-a.y);c<1&&u<1&&(s=0,this.hooks.trigger(this.hooks.eventTypes.minDistanceScroll)),this.animater.move(a,l,s,n)}},t.prototype.scrollToElement=function(t,e,s,i,o){var n=S(t),r=A(n),a=function(t,e,s){return"number"==typeof t?t:t?Math.round(e/2-s/2):0};s=a(s,n.offsetWidth,this.wrapper.offsetWidth),i=a(i,n.offsetHeight,this.wrapper.offsetHeight);var l=function(t,e,s,i){return t-=e,t=i.adjustPosition(t-s)};r.left=l(r.left,this.wrapperOffset.left,s,this.scrollBehaviorX),r.top=l(r.top,this.wrapperOffset.top,i,this.scrollBehaviorY),this.hooks.trigger(this.hooks.eventTypes.scrollToElement,n,r)||this.scrollTo(r.left,r.top,e,o)},t.prototype.resetPosition=function(t,e){void 0===t&&(t=0),void 0===e&&(e=U.bounce);var s=this.scrollBehaviorX.checkInBoundary(),i=s.position,o=s.inBoundary,n=this.scrollBehaviorY.checkInBoundary(),r=n.position,a=n.inBoundary;return(!o||!a)&&(v&&this.reflow(),this.scrollTo(i,r,t,e),!0)},t.prototype.reflow=function(){this._reflow=this.content.offsetHeight},t.prototype.updatePositions=function(t){this.scrollBehaviorX.updatePosition(t.x),this.scrollBehaviorY.updatePosition(t.y)},t.prototype.getCurrentPos=function(){return this.actions.getCurrentPos()},t.prototype.enable=function(){this.actions.enabled=!0},t.prototype.disable=function(){W(this.animater.timer),this.actions.enabled=!1},t.prototype.destroy=function(){var t=this;["resizeRegister","transitionEndRegister","actionsHandler","actions","hooks","animater","translater","scrollBehaviorX","scrollBehaviorY"].forEach((function(e){return t[e].destroy()}))},t}(),vt=function(t){function e(e,s){var i=t.call(this,["refresh","contentChanged","enable","disable","beforeScrollStart","scrollStart","scroll","scrollEnd","scrollCancel","touchEnd","flick","destroy"])||this,o=S(e);return o?(i.plugins={},i.options=(new V).merge(s).process(),i.setContent(o).valid?(i.hooks=new Q(["refresh","enable","disable","destroy","beforeInitialScrollTo","contentChanged"]),i.init(o),i):i):(p("Can not resolve the wrapper DOM."),i)}return l(e,t),e.use=function(t){var s=t.pluginName;return e.plugins.some((function(e){return t===e.ctor}))?e:w(s)?(p("Plugin Class must specify plugin's name in static property by 'pluginName' field."),e):(e.pluginsMap[s]=!0,e.plugins.push({name:s,applyOrder:t.applyOrder,ctor:t}),e)},e.prototype.setContent=function(t){var e=!1,s=!0,i=t.children[this.options.specifiedIndexAsContent];return i?(e=this.content!==i)&&(this.content=i):(p("The wrapper need at least one child element to be content element to scroll."),s=!1),{valid:s,contentChanged:e}},e.prototype.init=function(t){var e=this;this.wrapper=t,t.isBScrollContainer=!0,this.scroller=new mt(t,this.content,this.options),this.scroller.hooks.on(this.scroller.hooks.eventTypes.resize,(function(){e.refresh()})),this.eventBubbling(),this.handleAutoBlur(),this.enable(),this.proxy(u),this.applyPlugins(),this.refreshWithoutReset(this.content);var s=this.options,i={x:s.startX,y:s.startY};this.hooks.trigger(this.hooks.eventTypes.beforeInitialScrollTo,i)||this.scroller.scrollTo(i.x,i.y)},e.prototype.applyPlugins=function(){var t=this,s=this.options;e.plugins.sort((function(t,e){var s,i=((s={}).pre=-1,s.post=1,s);return(t.applyOrder?i[t.applyOrder]:0)-(e.applyOrder?i[e.applyOrder]:0)})).forEach((function(e){var i=e.ctor;s[e.name]&&"function"==typeof i&&(t.plugins[e.name]=new i(t))}))},e.prototype.handleAutoBlur=function(){this.options.autoBlur&&this.on(this.eventTypes.beforeScrollStart,(function(){var t=document.activeElement;!t||"INPUT"!==t.tagName&&"TEXTAREA"!==t.tagName||t.blur()}))},e.prototype.eventBubbling=function(){ft(this.scroller.hooks,this,[this.eventTypes.beforeScrollStart,this.eventTypes.scrollStart,this.eventTypes.scroll,this.eventTypes.scrollEnd,this.eventTypes.scrollCancel,this.eventTypes.touchEnd,this.eventTypes.flick])},e.prototype.refreshWithoutReset=function(t){this.scroller.refresh(t),this.hooks.trigger(this.hooks.eventTypes.refresh,t),this.trigger(this.eventTypes.refresh,t)},e.prototype.proxy=function(t){var e=this;t.forEach((function(t){var s=t.key,i=t.sourceKey;!function(t,e,s){K.get=function(){return function(t,e){for(var s=e.split("."),i=0;i<s.length-1;i++)if("object"!=typeof(t=t[s[i]])||!t)return;var o=s.pop();return"function"==typeof t[o]?function(){return t[o].apply(t,arguments)}:t[o]}(this,e)},K.set=function(t){!function(t,e,s){for(var i,o=e.split("."),n=0;n<o.length-1;n++)t[i=o[n]]||(t[i]={}),t=t[i];t[o.pop()]=s}(this,e,t)},Object.defineProperty(t,s,K)}(e,i,s)}))},e.prototype.refresh=function(){var t=this.setContent(this.wrapper),e=t.contentChanged;if(t.valid){var s=this.content;this.refreshWithoutReset(s),e&&(this.hooks.trigger(this.hooks.eventTypes.contentChanged,s),this.trigger(this.eventTypes.contentChanged,s)),this.scroller.resetPosition()}},e.prototype.enable=function(){this.scroller.enable(),this.hooks.trigger(this.hooks.eventTypes.enable),this.trigger(this.eventTypes.enable)},e.prototype.disable=function(){this.scroller.disable(),this.hooks.trigger(this.hooks.eventTypes.disable),this.trigger(this.eventTypes.disable)},e.prototype.destroy=function(){this.hooks.trigger(this.hooks.eventTypes.destroy),this.trigger(this.eventTypes.destroy),this.scroller.destroy()},e.prototype.eventRegister=function(t){this.registerType(t)},e.plugins=[],e.pluginsMap={},e}(Q);function yt(t,e){return new vt(t,e)}yt.use=vt.use,yt.plugins=vt.plugins,yt.pluginsMap=vt.pluginsMap;var bt=yt;
/*!
 * better-scroll / slide
 * (c) 2016-2023 ustbhuangyi
 * Released under the MIT License.
 */function kt(t){console.error("[BScroll warn]: "+t)}var Pt="undefined"!=typeof window,wt=Pt&&navigator.userAgent.toLowerCase();wt&&/wechatdevtools/.test(wt),wt&&wt.indexOf("android"),function(){if("string"==typeof wt){var t=/os (\d\d?_\d(_\d)?)/.exec(wt);if(!t)return!1;var e=t[1].split("_").map((function(t){return parseInt(t,10)}));return!!(13===e[0]&&e[1]>=4)}}();if(Pt){try{var Tt={};Object.defineProperty(Tt,"passive",{get:function(){!0}}),window.addEventListener("test-passive",(function(){}),Tt)}catch(t){}}var Ct=function(t,e){for(var s in e)t[s]=e[s];return t};function _t(t,e,s){return t<e?e:t>s?s:t}var xt=Pt&&document.createElement("div").style,St=function(){if(!Pt)return!1;for(var t=0,e=[{key:"standard",value:"transform"},{key:"webkit",value:"webkitTransform"},{key:"Moz",value:"MozTransform"},{key:"O",value:"OTransform"},{key:"ms",value:"msTransform"}];t<e.length;t++){var s=e[t];if(void 0!==xt[s.value])return s.key}return!1}();function Lt(t){return!1===St?t:"standard"===St?"transitionEnd"===t?"transitionend":t:St+t.charAt(0).toUpperCase()+t.substr(1)}St&&"standard"!==St&&St.toLowerCase();Lt("transform"),Lt("transition");Pt&&Lt("perspective"),Lt("transitionTimingFunction"),Lt("transitionDuration"),Lt("transitionDelay"),Lt("transformOrigin"),Lt("transitionEnd"),Lt("transitionProperty");var Bt={swipe:{style:"cubic-bezier(0.23, 1, 0.32, 1)",fn:function(t){return 1+--t*t*t*t*t}},swipeBounce:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(t){return t*(2-t)}},bounce:{style:"cubic-bezier(0.165, 0.84, 0.44, 1)",fn:function(t){return 1- --t*t*t*t}}},Mt={pageX:0,pageY:0,x:0,y:0},At={x:0,y:0,width:0,height:0,cx:0,cy:0},Ot=function(){function t(t){this.scroll=t,this.init()}return t.prototype.init=function(){var t=this.scroll.scroller,e=t.scrollBehaviorX,s=t.scrollBehaviorY;this.wrapperWidth=e.wrapperSize,this.wrapperHeight=s.wrapperSize,this.scrollerHeight=s.contentSize,this.scrollerWidth=e.contentSize,this.pages=this.buildPagesMatrix(this.wrapperWidth,this.wrapperHeight),this.pageLengthOfX=this.pages?this.pages.length:0,this.pageLengthOfY=this.pages&&this.pages[0]?this.pages[0].length:0},t.prototype.getPageStats=function(t,e){return this.pages[t]&&this.pages[t][e]?this.pages[t][e]:At},t.prototype.getNearestPageIndex=function(t,e){for(var s=0,i=0,o=this.pages.length;s<o-1&&!(t>=this.pages[s][0].cx);s++);for(o=this.pages[s]?this.pages[s].length:0;i<o-1&&!(e>=this.pages[0][i].cy);i++);return{pageX:s,pageY:i}},t.prototype.buildPagesMatrix=function(t,e){var s,i,o,n,r=[],a=0,l=0,h=this.scroll.scroller.scrollBehaviorX.maxScrollPos,c=this.scroll.scroller.scrollBehaviorY.maxScrollPos;for(i=Math.round(t/2),o=Math.round(e/2);a>-this.scrollerWidth;){for(r[l]=[],n=0,s=0;s>-this.scrollerHeight;)r[l][n]={x:Math.max(a,h),y:Math.max(s,c),width:t,height:e,cx:a-i,cy:s-o},s-=e,n++;a-=t,l++}return r},t}(),Et=function(){function t(t,e){this.scroll=t,this.slideOptions=e,this.slideX=!1,this.slideY=!1,this.currentPage=Ct({},Mt)}return t.prototype.refresh=function(){this.pagesMatrix=new Ot(this.scroll),this.checkSlideLoop(),this.currentPage=this.getAdjustedCurrentPage()},t.prototype.getAdjustedCurrentPage=function(){var t=this.currentPage,e=t.pageX,s=t.pageY;e=Math.min(e,this.pagesMatrix.pageLengthOfX-1),s=Math.min(s,this.pagesMatrix.pageLengthOfY-1),this.loopX&&(e=Math.min(e,this.pagesMatrix.pageLengthOfX-2)),this.loopY&&(s=Math.min(s,this.pagesMatrix.pageLengthOfY-2));var i=this.pagesMatrix.getPageStats(e,s);return{pageX:e,pageY:s,x:i.x,y:i.y}},t.prototype.setCurrentPage=function(t){this.currentPage=t},t.prototype.getInternalPage=function(t,e){t>=this.pagesMatrix.pageLengthOfX?t=this.pagesMatrix.pageLengthOfX-1:t<0&&(t=0),e>=this.pagesMatrix.pageLengthOfY?e=this.pagesMatrix.pageLengthOfY-1:e<0&&(e=0);var s=this.pagesMatrix.getPageStats(t,e);return{pageX:t,pageY:e,x:s.x,y:s.y}},t.prototype.getInitialPage=function(t,e){void 0===t&&(t=!1),void 0===e&&(e=!1);var s=this.slideOptions,i=s.startPageXIndex,o=s.startPageYIndex,n=this.loopX?1:0,r=this.loopY?1:0,a=t?n:this.currentPage.pageX,l=t?r:this.currentPage.pageY;e?(a=this.loopX?i+1:i,l=this.loopY?o+1:o):(a=t?n:this.currentPage.pageX,l=t?r:this.currentPage.pageY);var h=this.pagesMatrix.getPageStats(a,l);return{pageX:a,pageY:l,x:h.x,y:h.y}},t.prototype.getExposedPage=function(t){var e=Ct({},t);return this.loopX&&(e.pageX=this.fixedPage(e.pageX,this.pagesMatrix.pageLengthOfX-2)),this.loopY&&(e.pageY=this.fixedPage(e.pageY,this.pagesMatrix.pageLengthOfY-2)),e},t.prototype.getExposedPageByPageIndex=function(t,e){var s={pageX:t,pageY:e};this.loopX&&(s.pageX=t+1),this.loopY&&(s.pageY=e+1);var i=this.pagesMatrix.getPageStats(s.pageX,s.pageY);return{x:i.x,y:i.y,pageX:t,pageY:e}},t.prototype.getWillChangedPage=function(t){return t=Ct({},t),this.loopX&&(t.pageX=this.fixedPage(t.pageX,this.pagesMatrix.pageLengthOfX-2),t.x=this.pagesMatrix.getPageStats(t.pageX+1,0).x),this.loopY&&(t.pageY=this.fixedPage(t.pageY,this.pagesMatrix.pageLengthOfY-2),t.y=this.pagesMatrix.getPageStats(0,t.pageY+1).y),t},t.prototype.fixedPage=function(t,e){for(var s=[],i=0;i<e;i++)s.push(i);return s.unshift(e-1),s.push(0),s[t]},t.prototype.getPageStats=function(){return this.pagesMatrix.getPageStats(this.currentPage.pageX,this.currentPage.pageY)},t.prototype.getValidPageIndex=function(t,e){var s=this.pagesMatrix.pageLengthOfX-1,i=this.pagesMatrix.pageLengthOfY-1,o=0,n=0;return this.loopX&&(t+=1,o+=1,s-=1),this.loopY&&(e+=1,n+=1,i-=1),{pageX:t=_t(t,o,s),pageY:e=_t(e,n,i)}},t.prototype.nextPageIndex=function(){return this.getPageIndexByDirection("positive")},t.prototype.prevPageIndex=function(){return this.getPageIndexByDirection("negative")},t.prototype.getNearestPage=function(t,e){var s=this.pagesMatrix.getNearestPageIndex(t,e),i=s.pageX,o=s.pageY;return{x:this.pagesMatrix.getPageStats(i,0).x,y:this.pagesMatrix.getPageStats(0,o).y,pageX:i,pageY:o}},t.prototype.getPageByDirection=function(t,e,s){var i=t.pageX,o=t.pageY;return i===this.currentPage.pageX&&(i=_t(i+e,0,this.pagesMatrix.pageLengthOfX-1)),o===this.currentPage.pageY&&(o=_t(o+s,0,this.pagesMatrix.pageLengthOfY-1)),{x:this.pagesMatrix.getPageStats(i,0).x,y:this.pagesMatrix.getPageStats(0,o).y,pageX:i,pageY:o}},t.prototype.resetLoopPage=function(){if(this.loopX){if(0===this.currentPage.pageX)return{pageX:this.pagesMatrix.pageLengthOfX-2,pageY:this.currentPage.pageY};if(this.currentPage.pageX===this.pagesMatrix.pageLengthOfX-1)return{pageX:1,pageY:this.currentPage.pageY}}if(this.loopY){if(0===this.currentPage.pageY)return{pageX:this.currentPage.pageX,pageY:this.pagesMatrix.pageLengthOfY-2};if(this.currentPage.pageY===this.pagesMatrix.pageLengthOfY-1)return{pageX:this.currentPage.pageX,pageY:1}}},t.prototype.getPageIndexByDirection=function(t){var e=this.currentPage.pageX,s=this.currentPage.pageY;return this.slideX&&(e="negative"===t?e-1:e+1),this.slideY&&(s="negative"===t?s-1:s+1),{pageX:e,pageY:s}},t.prototype.checkSlideLoop=function(){this.wannaLoop=this.slideOptions.loop,this.pagesMatrix.pageLengthOfX>1?this.slideX=!0:this.slideX=!1,this.pagesMatrix.pages[0]&&this.pagesMatrix.pageLengthOfY>1?this.slideY=!0:this.slideY=!1,this.loopX=this.wannaLoop&&this.slideX,this.loopY=this.wannaLoop&&this.slideY,this.slideX&&this.slideY&&kt("slide does not support two direction at the same time.")},t}(),It=[{key:"next",name:"next"},{key:"prev",name:"prev"},{key:"goToPage",name:"goToPage"},{key:"getCurrentPage",name:"getCurrentPage"},{key:"startPlay",name:"startPlay"},{key:"pausePlay",name:"pausePlay"}].map((function(t){return{key:t.key,sourceKey:"plugins.slide."+t.name}})),Dt=function(){function t(t){this.scroll=t,this.cachedClonedPageDOM=[],this.resetLooping=!1,this.autoplayTimer=0,this.satisfyInitialization()&&this.init()}return t.prototype.satisfyInitialization=function(){return!(this.scroll.scroller.content.children.length<=0)||(kt("slide need at least one slide page to be initialised.please check your DOM layout."),!1)},t.prototype.init=function(){this.willChangeToPage=Ct({},Mt),this.handleBScroll(),this.handleOptions(),this.handleHooks(),this.createPages()},t.prototype.createPages=function(){this.pages=new Et(this.scroll,this.options)},t.prototype.handleBScroll=function(){this.scroll.registerType(["slideWillChange","slidePageChanged"]),this.scroll.proxy(It)},t.prototype.handleOptions=function(){var t=!0===this.scroll.options.slide?{}:this.scroll.options.slide,e={loop:!0,threshold:.1,speed:400,easing:Bt.bounce,listenFlick:!0,autoplay:!0,interval:3e3,startPageXIndex:0,startPageYIndex:0};this.options=Ct(e,t)},t.prototype.handleLoop=function(t){var e=this.options.loop,s=this.scroll.scroller.content,i=s.children.length;e&&(s!==t?(this.resetLoopChangedStatus(),this.removeClonedSlidePage(t),i>1&&this.cloneFirstAndLastSlidePage(s)):3===i&&this.initialised?(this.removeClonedSlidePage(s),this.moreToOnePageInLoop=!0,this.oneToMorePagesInLoop=!1):i>1?(this.initialised&&0===this.cachedClonedPageDOM.length?(this.oneToMorePagesInLoop=!0,this.moreToOnePageInLoop=!1):(this.removeClonedSlidePage(s),this.resetLoopChangedStatus()),this.cloneFirstAndLastSlidePage(s)):this.resetLoopChangedStatus())},t.prototype.resetLoopChangedStatus=function(){this.moreToOnePageInLoop=!1,this.oneToMorePagesInLoop=!1},t.prototype.handleHooks=function(){var t=this,e=this.scroll.hooks,s=this.scroll.scroller.hooks,i=this.options.listenFlick;this.prevContent=this.scroll.scroller.content,this.hooksFn=[],this.registerHooks(this.scroll,this.scroll.eventTypes.beforeScrollStart,this.pausePlay),this.registerHooks(this.scroll,this.scroll.eventTypes.scrollEnd,this.modifyCurrentPage),this.registerHooks(this.scroll,this.scroll.eventTypes.scrollEnd,this.startPlay),this.scroll.eventTypes.mousewheelMove&&(this.registerHooks(this.scroll,this.scroll.eventTypes.mousewheelMove,(function(){return!0})),this.registerHooks(this.scroll,this.scroll.eventTypes.mousewheelEnd,(function(e){1!==e.directionX&&1!==e.directionY||t.next(),-1!==e.directionX&&-1!==e.directionY||t.prev()}))),this.registerHooks(e,e.eventTypes.refresh,this.refreshHandler),this.registerHooks(e,e.eventTypes.destroy,this.destroy),this.registerHooks(s,s.eventTypes.beforeRefresh,(function(){t.handleLoop(t.prevContent),t.setSlideInlineStyle()})),this.registerHooks(s,s.eventTypes.momentum,this.modifyScrollMetaHandler),this.registerHooks(s,s.eventTypes.scroll,this.scrollHandler),this.registerHooks(s,s.eventTypes.checkClick,this.startPlay),i&&this.registerHooks(s,s.eventTypes.flick,this.flickHandler)},t.prototype.startPlay=function(){var t=this,e=this.options,s=e.interval;e.autoplay&&(clearTimeout(this.autoplayTimer),this.autoplayTimer=window.setTimeout((function(){t.next()}),s))},t.prototype.pausePlay=function(){this.options.autoplay&&clearTimeout(this.autoplayTimer)},t.prototype.setSlideInlineStyle=function(){var t=this.scroll.scroller,e=t.content,s=t.wrapper,i=this.scroll.options;[{direction:"scrollX",sizeType:"offsetWidth",styleType:"width"},{direction:"scrollY",sizeType:"offsetHeight",styleType:"height"}].forEach((function(t){var o=t.direction,n=t.sizeType,r=t.styleType;if(i[o]){for(var a=s[n],l=e.children,h=l.length,c=0;c<h;c++){l[c].style[r]=a+"px"}e.style[r]=a*h+"px"}}))},t.prototype.next=function(t,e){var s=this.pages.nextPageIndex(),i=s.pageX,o=s.pageY;this.goTo(i,o,t,e)},t.prototype.prev=function(t,e){var s=this.pages.prevPageIndex(),i=s.pageX,o=s.pageY;this.goTo(i,o,t,e)},t.prototype.goToPage=function(t,e,s,i){var o=this.pages.getValidPageIndex(t,e);this.goTo(o.pageX,o.pageY,s,i)},t.prototype.getCurrentPage=function(){return this.exposedPage||this.pages.getInitialPage(!1,!0)},t.prototype.setCurrentPage=function(t){this.pages.setCurrentPage(t),this.exposedPage=this.pages.getExposedPage(t)},t.prototype.nearestPage=function(t,e){var s=this.scroll.scroller,i=s.scrollBehaviorX,o=s.scrollBehaviorY,n=i.maxScrollPos,r=i.minScrollPos,a=o.maxScrollPos,l=o.minScrollPos;return this.pages.getNearestPage(_t(t,n,r),_t(e,a,l))},t.prototype.satisfyThreshold=function(t,e){var s=this.scroll.scroller,i=s.scrollBehaviorX,o=s.scrollBehaviorY,n=!0;return Math.abs(t-i.absStartPos)<=this.thresholdX&&Math.abs(e-o.absStartPos)<=this.thresholdY&&(n=!1),n},t.prototype.refreshHandler=function(t){var e=this;if(this.satisfyInitialization()){this.pages.refresh(),this.computeThreshold();var s=this.contentChanged=this.prevContent!==t;s&&(this.prevContent=t);var i=this.pages.getInitialPage(this.oneToMorePagesInLoop||this.moreToOnePageInLoop,s||!this.initialised);this.initialised?this.goTo(i.pageX,i.pageY,0):this.registerHooks(this.scroll.hooks,this.scroll.hooks.eventTypes.beforeInitialScrollTo,(function(t){e.initialised=!0,t.x=i.x,t.y=i.y})),this.startPlay()}},t.prototype.computeThreshold=function(){var t=this.options.threshold;if(t%1==0)this.thresholdX=t,this.thresholdY=t;else{var e=this.pages.getPageStats(),s=e.width,i=e.height;this.thresholdX=Math.round(s*t),this.thresholdY=Math.round(i*t)}},t.prototype.cloneFirstAndLastSlidePage=function(t){var e,s,i,o=t.children,n=o[o.length-1].cloneNode(!0),r=o[0].cloneNode(!0);e=n,(i=(s=t).firstChild)?function(t,e){e.parentNode.insertBefore(t,e)}(e,i):s.appendChild(e),t.appendChild(r),this.cachedClonedPageDOM=[n,r]},t.prototype.removeClonedSlidePage=function(t){(t&&t.children||[]).length&&this.cachedClonedPageDOM.forEach((function(e){!function(t,e){t.removeChild(e)}(t,e)})),this.cachedClonedPageDOM=[]},t.prototype.modifyCurrentPage=function(t){var e=this.getCurrentPage(),s=e.pageX,i=e.pageY,o=this.nearestPage(t.x,t.y);if(this.setCurrentPage(o),this.contentChanged)return this.contentChanged=!1,!0;var n=this.getCurrentPage(),r=n.pageX,a=n.pageY;if(this.pageWillChangeTo(o),this.oneToMorePagesInLoop)return this.oneToMorePagesInLoop=!1,!0;if(this.moreToOnePageInLoop&&0===s&&0===i)return this.moreToOnePageInLoop=!1,!0;if(s!==r||i!==a){var l=this.pages.getExposedPageByPageIndex(r,a);this.scroll.trigger(this.scroll.eventTypes.slidePageChanged,l)}if(!this.resetLooping){var h=this.pages.resetLoopPage();return h?(this.resetLooping=!0,this.goTo(h.pageX,h.pageY,0),!0):void 0}this.resetLooping=!1},t.prototype.goTo=function(t,e,s,i){var o=this.pages.getInternalPage(t,e),n=i||this.options.easing||Bt.bounce,r=o.x,a=o.y,l=r-this.scroll.scroller.scrollBehaviorX.currentPos,h=a-this.scroll.scroller.scrollBehaviorY.currentPos;l||h?(s=void 0===s?this.getEaseTime(l,h):s,this.scroll.scroller.scrollTo(r,a,s,n)):this.scroll.scroller.togglePointerEvents(!0)},t.prototype.flickHandler=function(){var t=this.scroll.scroller,e=t.scrollBehaviorX,s=t.scrollBehaviorY,i=e.currentPos,o=e.startPos,n=e.direction,r=s.currentPos,a=s.startPos,l=s.direction,h=this.pages.currentPage,c=h.pageX,u=h.pageY,p=this.getEaseTime(i-o,r-a);this.goTo(c+n,u+l,p)},t.prototype.getEaseTime=function(t,e){return this.options.speed||Math.max(Math.max(Math.min(Math.abs(t),1e3),Math.min(Math.abs(e),1e3)),300)},t.prototype.modifyScrollMetaHandler=function(t){var e=this.scroll.scroller,s=e.scrollBehaviorX,i=e.scrollBehaviorY,o=e.animater,n=t.newX,r=t.newY,a=this.satisfyThreshold(n,r)||o.forceStopped?this.pages.getPageByDirection(this.nearestPage(n,r),s.direction,i.direction):this.pages.currentPage;t.time=this.getEaseTime(t.newX-a.x,t.newY-a.y),t.newX=a.x,t.newY=a.y,t.easing=this.options.easing||Bt.bounce},t.prototype.scrollHandler=function(t){var e=t.x,s=t.y;if(this.satisfyThreshold(e,s)){var i=this.nearestPage(e,s);this.pageWillChangeTo(i)}},t.prototype.pageWillChangeTo=function(t){var e,s,i=this.pages.getWillChangedPage(t);e=this.willChangeToPage,s=i,(e.pageX!==s.pageX||e.pageY!==s.pageY)&&(this.willChangeToPage=i,this.scroll.trigger(this.scroll.eventTypes.slideWillChange,this.willChangeToPage))},t.prototype.registerHooks=function(t,e,s){t.on(e,s,this),this.hooksFn.push([t,e,s])},t.prototype.destroy=function(){var t=this.scroll.scroller.content,e=this.options,s=e.loop,i=e.autoplay;s&&this.removeClonedSlidePage(t),i&&clearTimeout(this.autoplayTimer),this.hooksFn.forEach((function(t){var e=t[0],s=t[1],i=t[2];e.eventTypes[s]&&e.off(s,i)})),this.hooksFn.length=0},t.pluginName="slide",t}(),$t=(s(415),Object(n.a)({},(function(){var t=this._self._c;return t("div",{staticClass:"main-wrapper"},[t("div",{staticClass:"main-left"},[this._t("mainLeft")],2),this._v(" "),t("div",{staticClass:"main-right"},[this._t("mainRight")],2)])}),[],!1,null,null,null).exports),Yt=(s(27),{props:{category:{type:String,default:""},tag:{type:String,default:""},currentPage:{type:Number,default:1},perPage:{type:Number,default:10}},data:()=>({sortPosts:[],postListOffsetTop:0}),created(){this.setPosts()},mounted(){},watch:{currentPage(){this.$route.query.p!=this.currentPage&&this.$router.push({query:{...this.$route.query,p:this.currentPage}}),this.setPosts()},category(){this.setPosts()},tag(){this.setPosts()}},methods:{setPosts(){const t=this.currentPage,e=this.perPage;let s=[];s=this.category?this.$groupPosts.categories[this.category]:this.tag?this.$groupPosts.tags[this.tag]:this.$sortPosts,this.sortPosts=s.slice((t-1)*e,t*e)}}}),Xt=(s(416),Object(n.a)(Yt,(function(){var t=this,e=t._self._c;return e("div",{ref:"postList",staticClass:"post-list"},[e("transition-group",{attrs:{tag:"div",name:"post"}},t._l(t.sortPosts,(function(s){return e("div",{key:s.key,staticClass:"post card-box",class:s.frontmatter.sticky&&"iconfont icon-zhiding"},[e("div",{staticClass:"title-wrapper"},[e("h2",[e("router-link",{attrs:{to:s.path}},[t._v("\n            "+t._s(s.title)+"\n            "),s.frontmatter.titleTag?e("span",{staticClass:"title-tag"},[t._v(t._s(s.frontmatter.titleTag))]):t._e()])],1),t._v(" "),e("div",{staticClass:"article-info"},[s.author&&s.author.href?e("a",{staticClass:"iconfont icon-touxiang",attrs:{title:"作者",target:"_blank",href:s.author.href}},[t._v(t._s(s.author.name?s.author.name:s.author))]):s.author?e("span",{staticClass:"iconfont icon-touxiang",attrs:{title:"作者"}},[t._v(t._s(s.author.name?s.author.name:s.author))]):t._e(),t._v(" "),s.frontmatter.date?e("span",{staticClass:"iconfont icon-riqi",attrs:{title:"创建时间"}},[t._v(t._s(s.frontmatter.date.split(" ")[0]))]):t._e(),t._v(" "),!1!==t.$themeConfig.category&&s.frontmatter.categories?e("span",{staticClass:"iconfont icon-wenjian",attrs:{title:"分类"}},t._l(s.frontmatter.categories,(function(s,i){return e("router-link",{key:i,attrs:{to:"/categories/?category="+encodeURIComponent(s)}},[t._v(t._s(s))])})),1):t._e(),t._v(" "),!1!==t.$themeConfig.tag&&s.frontmatter.tags&&s.frontmatter.tags[0]?e("span",{staticClass:"iconfont icon-biaoqian tags",attrs:{title:"标签"}},t._l(s.frontmatter.tags,(function(s,i){return e("router-link",{key:i,attrs:{to:"/tags/?tag="+encodeURIComponent(s)}},[t._v(t._s(s))])})),1):t._e()])]),t._v(" "),s.excerpt?e("div",{staticClass:"excerpt-wrapper"},[e("div",{staticClass:"excerpt",domProps:{innerHTML:t._s(s.excerpt)}}),t._v(" "),e("router-link",{staticClass:"readmore iconfont icon-jiantou-you",attrs:{to:s.path}},[t._v("阅读全文")])],1):t._e()])})),0)],1)}),[],!1,null,null,null).exports),Nt={name:"UpdateArticle",props:{length:{type:[String,Number],default:3},moreArticle:String},data:()=>({posts:[],currentPath:""}),created(){this.posts=this.$site.pages,this.currentPath=this.$page.path},computed:{topPublishPosts(){return this.$sortPostsByDate?this.$sortPostsByDate.filter(t=>{const{path:e}=t;return e!==this.currentPath}).slice(0,this.length):[]},isShowArticle(){const{frontmatter:t}=this.$page;return!(!1!==t.article)}},methods:{getNum:t=>t<9?"0"+(t+1):t+1,getDate:t=>t.frontmatter.date?t.frontmatter.date.split(" ")[0].slice(5,10):""},watch:{$route(){this.currentPath=this.$page.path}}},jt=(s(417),Object(n.a)(Nt,(function(){var t=this,e=t._self._c;return e("div",{class:["article-list",{"no-article-list":t.isShowArticle}]},[e("div",{staticClass:"article-title"},[e("router-link",{staticClass:"iconfont icon-bi",attrs:{to:t.moreArticle||"/archives/"}},[t._v("最近更新")])],1),t._v(" "),e("div",{staticClass:"article-wrapper"},[t._l(t.topPublishPosts,(function(s,i){return e("dl",{key:i},[e("dd",[t._v(t._s(t.getNum(i)))]),t._v(" "),e("dt",[e("router-link",{attrs:{to:s.path}},[e("div",[t._v("\n            "+t._s(s.title)+"\n            "),s.frontmatter.titleTag?e("span",{staticClass:"title-tag"},[t._v("\n              "+t._s(s.frontmatter.titleTag)+"\n            ")]):t._e()])]),t._v(" "),e("span",{staticClass:"date"},[t._v(t._s(t.getDate(s)))])],1)])})),t._v(" "),e("dl",[e("dd"),t._v(" "),e("dt",[e("router-link",{staticClass:"more",attrs:{to:t.moreArticle||"/archives/"}},[t._v("更多文章>")])],1)])],2)])}),[],!1,null,null,null).exports),Ht={props:{total:{type:Number,default:10},perPage:{type:Number,default:10},currentPage:{type:Number,default:1}},computed:{pages(){return Math.ceil(this.total/this.perPage)}},methods:{threeNum(){let t=3;const e=this.currentPage,s=this.pages;return t=e<3?3:e>s-3?s-2:e,t},goPrex(){let t=this.currentPage;t>1&&this.handleEmit(--t)},goNext(){let t=this.currentPage;t<this.pages&&this.handleEmit(++t)},goIndex(t){t!==this.currentPage&&this.handleEmit(t)},handleEmit(t){this.$emit("getCurrentPage",t)}}},Ft=(s(418),Object(n.a)(Ht,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"pagination"},[e("span",{staticClass:"card-box prev iconfont icon-jiantou-zuo",class:{disabled:1===t.currentPage},on:{click:function(e){return t.goPrex()}}},[e("p",[t._v("上一页")])]),t._v(" "),t.pages<=5?e("div",{staticClass:"pagination-list"},t._l(t.pages,(function(s){return e("span",{key:s,staticClass:"card-box",class:{active:t.currentPage===s},on:{click:function(e){return t.goIndex(s)}}},[t._v(t._s(s))])})),0):e("div",{staticClass:"pagination-list"},[e("span",{staticClass:"card-box",class:{active:1===t.currentPage},on:{click:function(e){return t.goIndex(1)}}},[t._v("1")]),t._v(" "),e("span",{directives:[{name:"show",rawName:"v-show",value:t.currentPage>3,expression:"currentPage > 3"}],staticClass:"ellipsis ell-two",attrs:{title:"上两页"},on:{click:function(e){return t.goIndex(t.currentPage-2)}}}),t._v(" "),e("span",{directives:[{name:"show",rawName:"v-show",value:t.currentPage<=3,expression:"currentPage <= 3"}],staticClass:"card-box",class:{active:2===t.currentPage},on:{click:function(e){return t.goIndex(2)}}},[t._v("2")]),t._v(" "),e("span",{staticClass:"card-box",class:{active:t.currentPage>=3&&t.currentPage<=t.pages-2},on:{click:function(e){t.goIndex(t.threeNum())}}},[t._v(t._s(t.threeNum()))]),t._v(" "),e("span",{directives:[{name:"show",rawName:"v-show",value:t.currentPage<t.pages-2,expression:"currentPage < pages - 2"}],staticClass:"ellipsis ell-four",attrs:{title:"下两页"},on:{click:function(e){return t.goIndex(t.currentPage+2)}}}),t._v(" "),e("span",{directives:[{name:"show",rawName:"v-show",value:t.currentPage>=t.pages-2,expression:"currentPage >= pages - 2"}],staticClass:"card-box",class:{active:t.currentPage===t.pages-1},on:{click:function(e){return t.goIndex(t.pages-1)}}},[t._v(t._s(t.pages-1))]),t._v(" "),e("span",{staticClass:"card-box",class:{active:t.currentPage===t.pages},on:{click:function(e){return t.goIndex(t.pages)}}},[t._v(t._s(t.pages))])]),t._v(" "),e("span",{staticClass:"card-box next iconfont icon-jiantou-you",class:{disabled:t.currentPage===t.pages},on:{click:function(e){return t.goNext()}}},[e("p",[t._v("下一页")])])])}),[],!1,null,null,null).exports),Ut={computed:{blogger(){return this.$themeConfig.blogger},social(){return this.$themeConfig.social}}},zt=(s(419),Object(n.a)(Ut,(function(){var t=this,e=t._self._c;return e("aside",{staticClass:"blogger-wrapper card-box"},[e("div",{staticClass:"avatar"},[e("img",{attrs:{src:t.blogger.avatar,alt:"头像",title:"我好看吗"}})]),t._v(" "),t.social&&t.social.icons&&t.social.icons.length?e("div",{staticClass:"icons"},t._l(t.social.icons,(function(s,i){return e("a",{key:i,class:["iconfont",s.iconClass],style:{width:100/t.social.icons.length+"%"},attrs:{href:s.link,title:s.title,target:"_blank"}})})),0):t._e(),t._v(" "),e("div",{staticClass:"blogger"},[e("span",{staticClass:"name"},[t._v(t._s(t.blogger.name))]),t._v(" "),e("span",{staticClass:"slogan"},[t._v(t._s(t.blogger.slogan))])])])}),[],!1,null,null,null).exports),Rt={props:{category:{type:String,default:""},categoriesData:{type:Array,default:[]},length:{type:[String,Number],default:"all"}},computed:{categories(){return"all"===this.length?this.categoriesData:this.categoriesData.slice(0,this.length)}}},qt=(s(420),Object(n.a)(Rt,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"categories-wrapper card-box"},[e("router-link",{staticClass:"title iconfont icon-wenjianjia",attrs:{to:"/categories/",title:"全部分类"}},[t._v(t._s("all"===t.length?"全部分类":"文章分类"))]),t._v(" "),e("div",{staticClass:"categories"},[t._l(t.categories,(function(s,i){return e("router-link",{key:i,class:{active:s.key===t.category},attrs:{to:"/categories/?category="+encodeURIComponent(s.key)}},[t._v("\n      "+t._s(s.key)+"\n      "),e("span",[t._v(t._s(s.length))])])})),t._v(" "),"all"!==t.length&&t.length<t.categoriesData.length?e("router-link",{staticClass:"more",attrs:{to:"/categories/"}},[t._v("更多 ...")]):t._e()],2)],1)}),[],!1,null,null,null).exports),Wt={props:{tag:{type:String,default:""},tagsData:{type:Array,default:[]},length:{type:[String,Number],default:"all"}},data:()=>({tagBgColor:["#11a8cd","#F8B26A","#67CC86","#E15B64","#F47E60","#849B87"],tagStyleList:[]}),created(){for(let t=0,e=this.tags.length;t<e;t++)this.tagStyleList.push(this.getTagStyle())},computed:{tags(){return"all"===this.length?this.tagsData:this.tagsData.slice(0,this.length)}},methods:{getTagStyle(){const t=this.tagBgColor,e=t[Math.floor(Math.random()*t.length)];return`background: ${e};--randomColor:${e};`}}},Gt=(s(421),Object(n.a)(Wt,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"tags-wrapper card-box"},[e("router-link",{staticClass:"title iconfont icon-biaoqian1",attrs:{to:"/tags/",title:"全部标签"}},[t._v(t._s("all"===t.length?"全部标签":"热门标签"))]),t._v(" "),e("div",{staticClass:"tags"},[t._l(t.tags,(function(s,i){return[e("router-link",{key:i,class:{active:s.key===t.tag},style:t.tagStyleList[i],attrs:{to:"/tags/?tag="+encodeURIComponent(s.key)}},[t._v(t._s(s.key))]),t._v(" "),e("span",{key:i+t.tags.length})]})),t._v(" "),"all"!==t.length&&t.tagsData.length>t.length?e("router-link",{attrs:{to:"/tags/"}},[t._v("更多...")]):t._e()],2)],1)}),[],!1,null,null,null).exports);bt.use(Dt);var Kt={data:()=>({isMQMobile:!1,slide:null,currentPageIndex:0,playTimer:0,mark:0,total:0,perPage:10,currentPage:1}),computed:{homeData(){return{...this.$page.frontmatter}},hasFeatures(){return!(!this.homeData.features||!this.homeData.features.length)},homeSidebarB(){const{htmlModules:t}=this.$themeConfig;return t?t.homeSidebarB:""},showBanner(){return!(this.$route.query.p&&1!=this.$route.query.p&&(!this.homeData.postList||"detailed"===this.homeData.postList))},bannerBgStyle(){let t=this.homeData.bannerBg;return t&&"auto"!==t?"none"===t?this.$themeConfig.bodyBgImg?"":"background: var(--mainBg);color: var(--textColor)":t.indexOf("background:")>-1?t:t.indexOf(".")>-1?`background: url(${this.$withBase(t)}) center center / cover no-repeat`:void 0:this.$themeConfig.bodyBgImg?"":"background: rgb(40,40,45) url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAABOSURBVFhH7c6xCQAgDAVRR9A6E4hLu4uLiWJ7tSnuQcIvr2TRYsw3/zOGGEOMIcYQY4gxxBhiDDGGGEOMIcYQY4gxxBhiDLkx52W4Gn1tuslCtHJvL54AAAAASUVORK5CYII=)"},actionLink(){return{link:this.homeData.actionLink,text:this.homeData.actionText}}},components:{NavLink:r,MainLayout:$t,PostList:Xt,UpdateArticle:jt,BloggerBar:zt,CategoriesBar:qt,TagsBar:Gt,Pagination:Ft},created(){this.total=this.$sortPosts.length},beforeMount(){this.isMQMobile=window.innerWidth<720},mounted(){this.$route.query.p&&(this.currentPage=Number(this.$route.query.p)),!this.hasFeatures||!this.isMQMobile||this.$route.query.p&&1!=this.$route.query.p||this.init(),this.hasFeatures&&window.addEventListener("resize",()=>{this.isMQMobile=window.innerWidth<720,!this.isMQMobile||this.slide||this.mark||(this.mark++,setTimeout(()=>{this.init()},60))})},beforeDestroy(){clearTimeout(this.playTimer),this.slide&&this.slide.destroy()},watch:{"$route.query.p"(){this.$route.query.p?this.currentPage=Number(this.$route.query.p):this.currentPage=1,this.hasFeatures&&1===this.currentPage&&this.isMQMobile&&setTimeout(()=>{this.slide&&this.slide.destroy(),this.init()},0)}},methods:{init(){clearTimeout(this.playTimer),this.slide=new bt(this.$refs.slide,{scrollX:!0,scrollY:!1,slide:{loop:!0,threshold:100},useTransition:!0,momentum:!1,bounce:!1,stopPropagation:!1,probeType:2,preventDefault:!1}),this.slide.on("beforeScrollStart",()=>{clearTimeout(this.playTimer)}),this.slide.on("scrollEnd",()=>{this.autoGoNext()}),this.slide.on("slideWillChange",t=>{this.currentPageIndex=t.pageX}),this.autoGoNext()},autoGoNext(){clearTimeout(this.playTimer),this.playTimer=setTimeout(()=>{this.slide.next()},4e3)},handlePagination(t){this.currentPage=t},getScrollTop:()=>window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop}},Qt=(s(422),Object(n.a)(Kt,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"home-wrapper"},[e("div",{staticClass:"banner",class:{"hide-banner":!t.showBanner},style:t.bannerBgStyle},[e("div",{staticClass:"banner-conent",style:!t.homeData.features&&!t.homeData.heroImage&&"padding-top: 7rem"},[e("header",{staticClass:"hero"},[t.homeData.heroImage?e("img",{attrs:{src:t.$withBase(t.homeData.heroImage),alt:t.homeData.heroAlt}}):t._e(),t._v(" "),t.homeData.heroText?e("h1",{attrs:{id:"main-title"}},[t._v("\n          "+t._s(t.homeData.heroText)+"\n        ")]):t._e(),t._v(" "),t.homeData.tagline?e("p",{staticClass:"description"},[t._v("\n          "+t._s(t.homeData.tagline)+"\n        ")]):t._e(),t._v(" "),t.homeData.actionText&&t.homeData.actionLink?e("p",{staticClass:"action"},[e("NavLink",{staticClass:"action-button",attrs:{item:t.actionLink}})],1):t._e()]),t._v(" "),t.hasFeatures&&!t.isMQMobile?e("div",{staticClass:"features"},t._l(t.homeData.features,(function(s,i){return e("div",{key:i,staticClass:"feature"},[s.link?e("router-link",{attrs:{to:s.link}},[s.imgUrl?e("img",{staticClass:"feature-img",attrs:{src:t.$withBase(s.imgUrl),alt:s.title}}):t._e(),t._v(" "),e("h2",[t._v(t._s(s.title))]),t._v(" "),e("p",[t._v(t._s(s.details))])]):e("a",{attrs:{href:"javascript:;"}},[s.imgUrl?e("img",{staticClass:"feature-img",attrs:{src:t.$withBase(s.imgUrl),alt:s.title}}):t._e(),t._v(" "),e("h2",[t._v(t._s(s.title))]),t._v(" "),e("p",[t._v(t._s(s.details))])])],1)})),0):t._e()]),t._v(" "),t.hasFeatures?e("div",{directives:[{name:"show",rawName:"v-show",value:t.isMQMobile,expression:"isMQMobile"}],staticClass:"slide-banner"},[e("div",{staticClass:"banner-wrapper"},[e("div",{ref:"slide",staticClass:"slide-banner-scroll"},[e("div",{staticClass:"slide-banner-wrapper"},t._l(t.homeData.features,(function(s,i){return e("div",{key:i,staticClass:"slide-item"},[s.link?e("router-link",{attrs:{to:s.link}},[s.imgUrl?e("img",{staticClass:"feature-img",attrs:{src:t.$withBase(s.imgUrl),alt:s.title}}):t._e(),t._v(" "),e("h2",[t._v(t._s(s.title))]),t._v(" "),e("p",[t._v(t._s(s.details))])]):e("a",{attrs:{href:"javascript:;"}},[s.imgUrl?e("img",{staticClass:"feature-img",attrs:{src:t.$withBase(s.imgUrl),alt:s.title}}):t._e(),t._v(" "),e("h2",[t._v(t._s(s.title))]),t._v(" "),e("p",[t._v(t._s(s.details))])])],1)})),0)]),t._v(" "),e("div",{staticClass:"docs-wrapper"},t._l(t.homeData.features.length,(function(s,i){return e("span",{key:i,staticClass:"doc",class:{active:t.currentPageIndex===i}})})),0)])]):t._e()]),t._v(" "),e("MainLayout",{scopedSlots:t._u([{key:"mainLeft",fn:function(){return["simple"===t.homeData.postList?e("UpdateArticle",{staticClass:"card-box",attrs:{length:t.homeData.simplePostListLength||10,moreArticle:t.$themeConfig.updateBar&&t.$themeConfig.updateBar.moreArticle}}):t.homeData.postList&&"detailed"!==t.homeData.postList?t._e():[e("PostList",{attrs:{currentPage:t.currentPage,perPage:t.perPage}}),t._v(" "),e("Pagination",{directives:[{name:"show",rawName:"v-show",value:Math.ceil(t.total/t.perPage)>1,expression:"Math.ceil(total / perPage) > 1"}],attrs:{total:t.total,perPage:t.perPage,currentPage:t.currentPage},on:{getCurrentPage:t.handlePagination}})],t._v(" "),e("Content",{staticClass:"theme-vdoing-content custom card-box"})]},proxy:!0},t.homeData.hideRightBar?null:{key:"mainRight",fn:function(){return[t.$themeConfig.blogger?e("BloggerBar"):t._e(),t._v(" "),!1!==t.$themeConfig.category&&t.$categoriesAndTags.categories.length?e("CategoriesBar",{attrs:{categoriesData:t.$categoriesAndTags.categories,length:10}}):t._e(),t._v(" "),!1!==t.$themeConfig.tag&&t.$categoriesAndTags.tags.length?e("TagsBar",{attrs:{tagsData:t.$categoriesAndTags.tags,length:30}}):t._e(),t._v(" "),t.homeSidebarB?e("div",{staticClass:"custom-html-box card-box",domProps:{innerHTML:t._s(t.homeSidebarB)}}):t._e()]},proxy:!0}],null,!0)})],1)}),[],!1,null,"7d2bb426",null).exports),Jt=(s(303),s(304),s(305),s(306),s(423)),Vt=s.n(Jt),Zt=s(291),te=s.n(Zt);let ee=null,se=null,ie=null,oe=null;const ne=/[\u3131-\u314e|\u314f-\u3163|\uac00-\ud7a3]|[\u4E00-\u9FCC\u3400-\u4DB5\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\ud840-\ud868][\udc00-\udfff]|\ud869[\udc00-\uded6\udf00-\udfff]|[\ud86a-\ud86c][\udc00-\udfff]|\ud86d[\udc00-\udf34\udf40-\udfff]|\ud86e[\udc00-\udc1d]|[\u3041-\u3096]|[\u30A1-\u30FA]/giu;var re={buildIndex(t,e){const s=t.filter(t=>!t.frontmatter||!1!==t.frontmatter.search),i={encode:e.encode||"simple",tokenize:e.tokenize||"forward",split:e.split||/\W+/,async:!0,doc:{id:"key",field:["title","headersStr","content"]}};ee=new Vt.a(i),ee.add(s);const o=s.filter(t=>t.charsets.cyrillic),n=s.filter(t=>t.charsets.cjk);o.length&&(se=new Vt.a({...i,encode:"icase",split:/\s+/,tokenize:"forward"}),se.add(o)),n.length&&(ie=new Vt.a({...i,encode:!1,tokenize:function(t){const e=[];let s=null;do{s=ne.exec(t),s&&e.push(s[0])}while(s);return e}}),ie.add(n)),oe=te.a.keyBy(s,"path")},async match(t,e,s=7){const i=[{field:"title",query:t,limit:s,boost:10},{field:"headersStr",query:t,limit:s,boost:7},{field:"content",query:t,limit:s}],o=await ee.search(i),n=se?await se.search(i):[],r=ie?await ie.search(i):[],a=te.a.uniqBy([...o,...n,...r],"path").map(s=>({...s,parentPageTitle:ae(s),...le(s,ge(t),e)})),l=te.a.groupBy(a,"parentPageTitle");return te.a.values(l).map(t=>t.map((t,e)=>0===e?t:{...t,parentPageTitle:null})).flat()},normalizeString:ge};function ae(t){const e=t.path.split("/");let s="/";e[1]&&(s=`/${e[1]}/`);return(oe[s]||t).title}function le(t,e,s){const i=function(t,e,s){const i=s.map(e=>ce(t,e)||ue(t,e)).filter(t=>t);if(0===i.length)return null;if(i.every(t=>null!=t.headerIndex))return ce(t,e)||i[0];return ue(t,e)||i.find(t=>null==t.headerIndex)}(t,e.toLowerCase(),s);if(!i)return{...he(t),slug:"",contentStr:null};if(null!=i.headerIndex)return{...he(t,i.headerIndex,i),slug:"#"+t.headers[i.headerIndex].slug,contentStr:null};let o=te.a.findLastIndex(t.headers||[],t=>null!=t.charIndex&&t.charIndex<i.charIndex);return-1===o&&(o=null),{...he(t,o),slug:null==o?"":"#"+t.headers[o].slug,...pe(t,i)}}function he(t,e,s){if(null==e)return{headingStr:t.title};const i=[];for(;null!=e;){const s=t.headers[e];i.unshift(s),-1===(e=te.a.findLastIndex(t.headers,t=>t.level===s.level-1,e-1))&&(e=null)}const o=i.map(t=>t.title).join(" > "),n=i.slice(0,-1),r=te.a.sum(n.map(t=>(t.title||"").length))+3*n.length;return{headingStr:o,headingHighlight:s&&null!=s.headerIndex&&[s.charIndex+r,s.termLength]}}function ce(t,e){if(!t.headers)return null;for(let s=0;s<t.headers.length;s++){const i=t.headers[s].normalizedTitle.indexOf(e);if(-1!==i)return{headerIndex:s,charIndex:i,termLength:e.length}}return null}function ue(t,e){if(!t.normalizedContent)return null;const s=t.normalizedContent.indexOf(e);return-1===s?null:{headerIndex:null,charIndex:s,termLength:e.length}}function pe(t,e){const{charIndex:s,termLength:i}=e;let o=t.content.lastIndexOf("\n",s),n=t.content.indexOf("\n",s);-1===o&&(o=0),-1===n&&(n=t.content.length);const r=t.content.slice(o,n),a=s-o,l=[a,i];if(120>=r.length)return{contentStr:r,contentHighlight:l};const h=te.a.round((120-i)/2),c=Math.max(a-h,0),u=Math.min(c+120,r.length);let p=r.slice(c,u);return l[0]=l[0]-c,c>0&&(p="..."+p,l[0]=l[0]+3),u<r.length&&(p+="..."),{contentStr:p,contentHighlight:l}}function ge(t){return t?t.trim().toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,""):t}var de={};function fe(t,e){if(!t)return{};if(!e)return{prefix:t};const[s,i]=e,o=s+i;return{prefix:t.slice(0,s),highlightedContent:t.slice(s,o),suffix:t.slice(o)}}var me={name:"SearchBox",data:()=>({query:"",focused:!1,focusIndex:0,placeholder:void 0,suggestions:null}),computed:{queryTerms(){if(!this.query)return[];return re.normalizeString(this.query).split(/[^\p{L}\p{N}_]+/iu).filter(t=>t)},showSuggestions(){return this.focused&&this.suggestions&&this.suggestions.length},alignRight(){return(this.$site.themeConfig.nav||[]).length+(this.$site.repo?1:0)<=2}},watch:{query(){this.getSuggestions()}},mounted(){re.buildIndex(this.$site.pages,{}||{}),this.placeholder=this.$site.themeConfig.searchPlaceholder||"",document.addEventListener("keydown",this.onHotkey);const t=this.urlParams();if(t){const e=t.get("query");e&&(this.query=decodeURI(e),this.focused=!0)}},beforeDestroy(){document.removeEventListener("keydown",this.onHotkey)},methods:{async getSuggestions(){if(!this.query||!this.queryTerms.length)return void(this.suggestions=[]);let t=await re.match(this.query,this.queryTerms,this.$site.themeConfig.searchMaxSuggestions||5);de.processSuggestions&&(t=await de.processSuggestions(t,this.query,this.queryTerms)),this.suggestions=t.map(t=>({...t,headingDisplay:fe(t.headingStr,t.headingHighlight),contentDisplay:fe(t.contentStr,t.contentHighlight)}))},getPageLocalePath(t){for(const e in this.$site.locales||{})if("/"!==e&&0===t.path.indexOf(e))return e;return"/"},isSearchable(t){let e=null;return null===e||(e=Array.isArray(e)?e:new Array(e),e.filter(e=>t.path.match(e)).length>0)},onHotkey(t){t.srcElement===document.body&&["s","/"].includes(t.key)&&(this.$refs.input.focus(),t.preventDefault())},onUp(){this.showSuggestions&&(this.focusIndex>0?this.focusIndex--:this.focusIndex=this.suggestions.length-1)},onDown(){this.showSuggestions&&(this.focusIndex<this.suggestions.length-1?this.focusIndex++:this.focusIndex=0)},go(t){if(this.showSuggestions){if(de.onGoToSuggestion){if(!0===de.onGoToSuggestion(t,this.suggestions[t],this.query,this.queryTerms))return}if(this.suggestions[t].external)window.open(this.suggestions[t].path+this.suggestions[t].slug,"_blank");else{this.$router.push(this.suggestions[t].path+this.suggestions[t].slug),this.query="",this.focusIndex=0,this.focused=!1;const e=this.urlParams();if(e){e.delete("query");const t=e.toString(),s=window.location.pathname+(t?"?"+t:"");history.pushState(null,"",s)}}}},focus(t){this.focusIndex=t},unfocus(){this.focusIndex=-1},urlParams:()=>window.location.search?new URLSearchParams(window.location.search):null}},ve=(s(424),Object(n.a)(me,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"search-box"},[e("input",{ref:"input",class:{focused:t.focused},attrs:{"aria-label":"Search",placeholder:t.placeholder,autocomplete:"off",spellcheck:"false"},domProps:{value:t.query},on:{input:function(e){t.query=e.target.value},focus:function(e){t.focused=!0},blur:function(e){t.focused=!1},keyup:[function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.go(t.focusIndex)},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"up",38,e.key,["Up","ArrowUp"])?null:t.onUp.apply(null,arguments)},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"down",40,e.key,["Down","ArrowDown"])?null:t.onDown.apply(null,arguments)}]}}),t._v(" "),t.showSuggestions?e("ul",{staticClass:"suggestions",class:{"align-right":t.alignRight},on:{mouseleave:t.unfocus}},t._l(t.suggestions,(function(s,i){return e("li",{key:i,staticClass:"suggestion",class:{focused:i===t.focusIndex},on:{mousedown:function(e){return t.go(i)},mouseenter:function(e){return t.focus(i)}}},[e("a",{attrs:{href:s.path+s.slug},on:{click:function(t){t.preventDefault()}}},[s.parentPageTitle?e("div",{staticClass:"parent-page-title",domProps:{innerHTML:t._s(s.parentPageTitle)}}):t._e(),t._v(" "),e("div",{staticClass:"suggestion-row"},[e("div",{staticClass:"page-title"},[t._v(t._s(s.title||s.path))]),t._v(" "),e("div",{staticClass:"suggestion-content"},[s.headingStr?e("div",{staticClass:"header"},[t._v("\n              "+t._s(s.headingDisplay.prefix)),e("span",{staticClass:"highlight"},[t._v(t._s(s.headingDisplay.highlightedContent))]),t._v(t._s(s.headingDisplay.suffix)+"\n            ")]):t._e(),t._v(" "),s.contentStr?e("div",[t._v("\n              "+t._s(s.contentDisplay.prefix)),e("span",{staticClass:"highlight"},[t._v(t._s(s.contentDisplay.highlightedContent))]),t._v(t._s(s.contentDisplay.suffix)+"\n            ")]):t._e()])])])])})),0):t._e()])}),[],!1,null,null,null).exports),ye=(s(425),Object(n.a)({},(function(){var t=this,e=t._self._c;return e("div",{staticClass:"sidebar-button",attrs:{title:"目录"},on:{click:function(e){return t.$emit("toggle-sidebar")}}},[e("svg",{staticClass:"icon",attrs:{xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",role:"img",viewBox:"0 0 448 512"}},[e("path",{attrs:{fill:"currentColor",d:"M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"}})])])}),[],!1,null,null,null).exports),be=s(413),ke=s(128),Pe=s.n(ke),we={components:{NavLink:r,DropdownTransition:be.a},data:()=>({open:!1,isMQMobile:!1}),props:{item:{required:!0}},computed:{dropdownAriaLabel(){return this.item.ariaLabel||this.item.text}},beforeMount(){this.isMQMobile=window.innerWidth<720,window.addEventListener("resize",()=>{this.isMQMobile=window.innerWidth<720})},methods:{toggle(){this.isMQMobile&&(this.open=!this.open)},isLastItemOfArray:(t,e)=>Pe()(e)===t},watch:{$route(){this.open=!1}}},Te=(s(427),{components:{NavLink:r,DropdownLink:Object(n.a)(we,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"dropdown-wrapper",class:{open:t.open}},[e("button",{staticClass:"dropdown-title",attrs:{type:"button","aria-label":t.dropdownAriaLabel},on:{click:t.toggle}},[t.item.link?e("router-link",{staticClass:"link-title",attrs:{to:t.item.link}},[t._v(t._s(t.item.text))]):t._e(),t._v(" "),e("span",{directives:[{name:"show",rawName:"v-show",value:!t.item.link,expression:"!item.link"}],staticClass:"title"},[t._v(t._s(t.item.text))]),t._v(" "),e("span",{staticClass:"arrow",class:t.open?"down":"right"})],1),t._v(" "),e("DropdownTransition",[e("ul",{directives:[{name:"show",rawName:"v-show",value:t.open,expression:"open"}],staticClass:"nav-dropdown"},t._l(t.item.items,(function(s,i){return e("li",{key:s.link||i,staticClass:"dropdown-item"},["links"===s.type?e("h4",[t._v(t._s(s.text))]):t._e(),t._v(" "),"links"===s.type?e("ul",{staticClass:"dropdown-subitem-wrapper"},t._l(s.items,(function(i){return e("li",{key:i.link,staticClass:"dropdown-subitem"},[e("NavLink",{attrs:{item:i},on:{focusout:function(e){t.isLastItemOfArray(i,s.items)&&t.isLastItemOfArray(s,t.item.items)&&t.toggle()}}})],1)})),0):e("NavLink",{attrs:{item:s},on:{focusout:function(e){t.isLastItemOfArray(s,t.item.items)&&t.toggle()}}})],1)})),0)])],1)}),[],!1,null,null,null).exports},computed:{userNav(){return this.$themeLocaleConfig.nav||this.$site.themeConfig.nav||[]},nav(){const{locales:t}=this.$site;if(t&&Object.keys(t).length>1){const e=this.$page.path,s=this.$router.options.routes,i=this.$site.themeConfig.locales||{},o={text:this.$themeLocaleConfig.selectText||"Languages",ariaLabel:this.$themeLocaleConfig.ariaLabel||"Select language",items:Object.keys(t).map(o=>{const n=t[o],r=i[o]&&i[o].label||n.lang;let a;return n.lang===this.$lang?a=e:(a=e.replace(this.$localeConfig.path,o),s.some(t=>t.path===a)||(a=o)),{text:r,link:a}})};return[...this.userNav,o]}return this.userNav},userLinks(){return(this.nav||[]).map(t=>Object.assign(Object(i.k)(t),{items:(t.items||[]).map(i.k)}))},repoLink(){const{repo:t}=this.$site.themeConfig;return t?/^https?:/.test(t)?t:"https://github.com/"+t:null},repoLabel(){if(!this.repoLink)return;if(this.$site.themeConfig.repoLabel)return this.$site.themeConfig.repoLabel;const t=this.repoLink.match(/^https?:\/\/[^/]+/)[0],e=["GitHub","GitLab","Bitbucket"];for(let s=0;s<e.length;s++){const i=e[s];if(new RegExp(i,"i").test(t))return i}return"Source"}}}),Ce=(s(428),Object(n.a)(Te,(function(){var t=this,e=t._self._c;return t.userLinks.length||t.repoLink?e("nav",{staticClass:"nav-links"},[t._l(t.userLinks,(function(t){return e("div",{key:t.link,staticClass:"nav-item"},["links"===t.type?e("DropdownLink",{attrs:{item:t}}):e("NavLink",{attrs:{item:t}})],1)})),t._v(" "),t.repoLink?e("a",{staticClass:"repo-link",attrs:{href:t.repoLink,target:"_blank",rel:"noopener noreferrer"}},[t._v("\n    "+t._s(t.repoLabel)+"\n    "),e("OutboundLink")],1):t._e()],2):t._e()}),[],!1,null,null,null).exports);function _e(t,e){return t.ownerDocument.defaultView.getComputedStyle(t,null)[e]}var xe={components:{SidebarButton:ye,NavLinks:Ce,SearchBox:ve,AlgoliaSearchBox:{}},data:()=>({linksWrapMaxWidth:null}),mounted(){const t=parseInt(_e(this.$el,"paddingLeft"))+parseInt(_e(this.$el,"paddingRight")),e=()=>{document.documentElement.clientWidth<719?this.linksWrapMaxWidth=null:this.linksWrapMaxWidth=this.$el.offsetWidth-t-(this.$refs.siteName&&this.$refs.siteName.offsetWidth||0)};e(),window.addEventListener("resize",e,!1)},computed:{algolia(){return this.$themeLocaleConfig.algolia||this.$site.themeConfig.algolia||{}},isAlgoliaSearch(){return this.algolia&&this.algolia.apiKey&&this.algolia.indexName}}},Se=(s(429),Object(n.a)(xe,(function(){var t=this,e=t._self._c;return e("header",{staticClass:"navbar blur"},[e("SidebarButton",{on:{"toggle-sidebar":function(e){return t.$emit("toggle-sidebar")}}}),t._v(" "),e("router-link",{staticClass:"home-link",attrs:{to:t.$localePath}},[t.$site.themeConfig.logo?e("img",{staticClass:"logo",attrs:{src:t.$withBase(t.$site.themeConfig.logo),alt:t.$siteTitle}}):t._e(),t._v(" "),t.$siteTitle?e("span",{ref:"siteName",staticClass:"site-name",class:{"can-hide":t.$site.themeConfig.logo}},[t._v(t._s(t.$siteTitle))]):t._e()]),t._v(" "),e("div",{staticClass:"links",style:t.linksWrapMaxWidth?{"max-width":t.linksWrapMaxWidth+"px"}:{}},[t.isAlgoliaSearch?e("AlgoliaSearchBox",{attrs:{options:t.algolia}}):!1!==t.$site.themeConfig.search&&!1!==t.$page.frontmatter.search?e("SearchBox"):t._e(),t._v(" "),e("NavLinks",{staticClass:"can-hide"})],1)],1)}),[],!1,null,null,null).exports),Le=s(349),Be=s.n(Le),Me={name:"PageEdit",computed:{tags(){return this.$frontmatter.tags},lastUpdated(){return this.$page.lastUpdated},lastUpdatedText(){return"string"==typeof this.$themeLocaleConfig.lastUpdated?this.$themeLocaleConfig.lastUpdated:"string"==typeof this.$site.themeConfig.lastUpdated?this.$site.themeConfig.lastUpdated:"Last Updated"},editLink(){const t=Be()(this.$page.frontmatter.editLink)?this.$site.themeConfig.editLinks:this.$page.frontmatter.editLink,{repo:e,docsDir:s="",docsBranch:i="master",docsRepo:o=e}=this.$site.themeConfig;return t&&o&&this.$page.relativePath?this.createEditLink(e,o,s,i,this.$page.relativePath):null},editLinkText(){return this.$themeLocaleConfig.editLinkText||this.$site.themeConfig.editLinkText||"Edit this page"}},methods:{createEditLink(t,e,s,o,n){if(/bitbucket.org/.test(e)){return e.replace(i.b,"")+"/src"+`/${o}/`+(s?s.replace(i.b,"")+"/":"")+n+`?mode=edit&spa=0&at=${o}&fileviewer=file-view-default`}if(/gitlab.com/.test(e)){return e.replace(i.b,"")+"/-/edit"+`/${o}/`+(s?s.replace(i.b,"")+"/":"")+n}const r=/gitee.com/;if(r.test(e)){return e.replace(r,"gitee.com/-/ide/project")+"/edit"+`/${o}/-/`+(s?s.replace(i.b,"")+"/":"")+n}return(i.j.test(e)?e:"https://github.com/"+e).replace(i.b,"")+"/edit"+`/${o}/`+(s?s.replace(i.b,"")+"/":"")+n}}},Ae=(s(430),Object(n.a)(Me,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"page-edit"},[t.editLink?e("div",{staticClass:"edit-link"},[e("a",{attrs:{href:t.editLink,target:"_blank",rel:"noopener noreferrer"}},[t._v(t._s(t.editLinkText))]),t._v(" "),e("OutboundLink")],1):t._e(),t._v(" "),!1!==t.$themeConfig.tag&&t.tags&&t.tags[0]?e("div",{staticClass:"tags"},t._l(t.tags,(function(s,i){return e("router-link",{key:i,attrs:{to:"/tags/?tag="+encodeURIComponent(s),title:"标签"}},[t._v("#"+t._s(s))])})),1):t._e(),t._v(" "),t.lastUpdated?e("div",{staticClass:"last-updated"},[e("span",{staticClass:"prefix"},[t._v(t._s(t.lastUpdatedText)+":")]),t._v(" "),e("span",{staticClass:"time"},[t._v(t._s(t.lastUpdated))])]):t._e()])}),[],!1,null,null,null).exports),Oe=s(431),Ee=s.n(Oe),Ie={name:"PageNav",props:["sidebarItems"],computed:{prev(){return $e(De.PREV,this)},next(){return $e(De.NEXT,this)}},methods:{showTooltip(t){const e=document.body.clientWidth,s=t.clientX,i=t.target.querySelector(".tooltip");if(!i)return;const o=i.style;s<e/2?(o.right=null,o.left=s+10+"px"):(o.left=null,o.right=e-s+10+"px"),o.top=t.clientY+10+"px"}}};const De={NEXT:{resolveLink:function(t,e){return Ye(t,e,1)},getThemeLinkConfig:({nextLinks:t})=>t,getPageLinkConfig:({frontmatter:t})=>t.next},PREV:{resolveLink:function(t,e){return Ye(t,e,-1)},getThemeLinkConfig:({prevLinks:t})=>t,getPageLinkConfig:({frontmatter:t})=>t.prev}};function $e(t,{$themeConfig:e,$page:s,$route:o,$site:n,sidebarItems:r}){const{resolveLink:a,getThemeLinkConfig:l,getPageLinkConfig:h}=t,c=l(e),u=h(s),p=Be()(u)?c:u;return!1===p?void 0:Ee()(p)?Object(i.l)(n.pages,p,o.path):a(s,r)}function Ye(t,e,s){const i=[];!function t(e,s){for(let i=0,o=e.length;i<o;i++)"group"===e[i].type?t(e[i].children||[],s):s.push(e[i])}(e,i);for(let e=0;e<i.length;e++){const o=i[e];if("page"===o.type&&o.path===decodeURIComponent(t.path))return i[e+s]}}var Xe=Ie,Ne=(s(432),Object(n.a)(Xe,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"page-nav-wapper"},[!1!==t.$themeConfig.pageButton&&(t.prev||t.next)?e("div",{staticClass:"page-nav-centre-wrap"},[t.prev?e("router-link",{staticClass:"page-nav-centre page-nav-centre-prev",attrs:{to:t.prev.path},nativeOn:{mouseenter:function(e){return t.showTooltip(e)},mousemove:function(e){return t.showTooltip(e)}}},[e("div",{staticClass:"tooltip"},[t._v(t._s(t.prev.title||t.prev.path))])]):t._e(),t._v(" "),t.next?e("router-link",{staticClass:"page-nav-centre page-nav-centre-next",attrs:{to:t.next.path},nativeOn:{mouseenter:function(e){return t.showTooltip(e)},mousemove:function(e){return t.showTooltip(e)}}},[e("div",{staticClass:"tooltip"},[t._v(t._s(t.next.title||t.next.path))])]):t._e()],1):t._e(),t._v(" "),t.prev||t.next?e("div",{staticClass:"page-nav"},[e("p",{staticClass:"inner"},[t.prev?e("span",{staticClass:"prev"},[t._v("\n        ←\n        "),t.prev?e("router-link",{staticClass:"prev",attrs:{to:t.prev.path}},[t._v(t._s(t.prev.title||t.prev.path))]):t._e()],1):t._e(),t._v(" "),t.next?e("span",{staticClass:"next"},[t.next?e("router-link",{attrs:{to:t.next.path}},[t._v(t._s(t.next.title||t.next.path))]):t._e(),t._v("→\n      ")],1):t._e()])]):t._e()])}),[],!1,null,null,null).exports),je={data:()=>({date:"",classify1:"",classifyList:[],cataloguePermalink:"",author:null,categories:[]}),created(){this.getPageInfo()},watch:{"$route.path"(){this.classifyList=[],this.getPageInfo()}},methods:{getPageInfo(){const t=this.$page,{relativePath:e}=t,{sidebar:s}=this.$themeConfig,i=e.split("/");i.forEach((t,e)=>{const s=t.split(".");if(e!==i.length-1)if(1===s)this.classifyList.push(s[0]);else{const e=t.indexOf(".");this.classifyList.push(t.substring(e+1)||"")}}),this.classify1=this.classifyList[0];const o=s&&s.catalogue?s.catalogue[this.classify1]:"",n=this.$frontmatter.author||this.$themeConfig.author;let r=(t.frontmatter.date||"").split(" ")[0];const{categories:a}=this.$frontmatter;this.date=r,this.cataloguePermalink=o,this.author=n,this.categories=a},getLink(t){const{cataloguePermalink:e}=this;return t===e?e:`${e}${"/"===e.charAt(e.length-1)?"":"/"}#${t}`}}},He=(s(433),Object(n.a)(je,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"articleInfo-wrap"},[e("div",{staticClass:"articleInfo"},[t.classify1&&"_posts"!==t.classify1?e("ul",{staticClass:"breadcrumbs"},[e("li",[e("router-link",{staticClass:"iconfont icon-home",attrs:{to:"/",title:"首页"}})],1),t._v(" "),t._l(t.classifyList,(function(s){return e("li",{key:s},[t.cataloguePermalink?e("router-link",{attrs:{to:t.getLink(s)}},[t._v(t._s(s))]):!1!==t.$themeConfig.category?e("router-link",{attrs:{to:"/categories/?category="+encodeURIComponent(s),title:"分类"}},[t._v(t._s(s))]):e("span",[t._v(t._s(s))])],1)}))],2):t._e(),t._v(" "),e("div",{staticClass:"info"},[t.author?e("div",{staticClass:"author iconfont icon-touxiang",attrs:{title:"作者"}},[t.author.href||t.author.link&&"string"==typeof t.author.link?e("a",{staticClass:"beLink",attrs:{href:t.author.href||t.author.link,target:"_blank",title:"作者"}},[t._v(t._s(t.author.name))]):e("a",{attrs:{href:"javascript:;"}},[t._v(t._s(t.author.name||t.author))])]):t._e(),t._v(" "),t.date?e("div",{staticClass:"date iconfont icon-riqi",attrs:{title:"创建时间"}},[e("a",{attrs:{href:"javascript:;"}},[t._v(t._s(t.date))])]):t._e(),t._v(" "),!1===t.$themeConfig.category||t.classify1&&"_posts"!==t.classify1||!t.categories?t._e():e("div",{staticClass:"date iconfont icon-wenjian",attrs:{title:"分类"}},t._l(t.categories,(function(s,i){return e("router-link",{key:i,attrs:{to:"/categories/?category="+encodeURIComponent(s)}},[t._v(t._s(s+" "))])})),1)])])])}),[],!1,null,"06225672",null).exports),Fe={data:()=>({pageData:null,isStructuring:!0,appointDir:{}}),created(){this.getPageData();const t=this.$themeConfig.sidebar;t&&"auto"!==t||(this.isStructuring=!1,console.error("目录页数据依赖于结构化的侧边栏数据，请在主题设置中将侧边栏字段设置为'structuring'，否则无法获取目录数据。"))},methods:{getPageData(){const t=this.$frontmatter.pageComponent;t&&t.data?this.pageData={...t.data,title:this.$frontmatter.title}:console.error("请在front matter中设置pageComponent和pageComponent.data数据")},getCatalogueList(){const{sidebar:t}=this.$site.themeConfig,{data:e}=this.$frontmatter.pageComponent;let s=(e.path||e.key).split("/"),i=t[`/${s[0]}/`];return s.length>1&&(s.shift(),i=this.appointDirDeal(0,s,i)),i||console.error("未获取到目录数据，请查看front matter中设置的path是否正确。"),i},type:t=>Object.prototype.toString.call(t).match(/\[object (.*?)\]/)[1].toLowerCase(),appointDirDeal(t,e,s){let i=e[t];void 0!==i&&-1!==i.indexOf(".")&&(i=i.substring(i.indexOf(".")+1));for(let o=0;o<s.length;o++)s[o].title===i&&(this.appointDir=s[o],t<e.length-1&&this.appointDirDeal(t+1,e,s[o].children));return this.appointDir.children}},watch:{"$route.path"(){this.getPageData()}}},Ue=(s(434),Object(n.a)(Fe,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"theme-vdoing-content"},[e("div",{staticClass:"column-wrapper"},[t.pageData.imgUrl?e("img",{attrs:{src:t.$withBase(t.pageData.imgUrl)}}):t._e(),t._v(" "),e("dl",{staticClass:"column-info"},[e("dt",{staticClass:"title"},[t._v(t._s(t.pageData.title))]),t._v(" "),e("dd",{staticClass:"description",domProps:{innerHTML:t._s(t.pageData.description)}})])]),t._v(" "),t.isStructuring?e("div",{staticClass:"catalogue-wrapper"},[e("div",{staticClass:"catalogue-title"},[t._v("目录")]),t._v(" "),e("div",{staticClass:"catalogue-content"},[t._l(t.getCatalogueList(),(function(s,i){return["array"===t.type(s)?e("dl",{key:i,staticClass:"inline"},[e("dt",[e("router-link",{attrs:{to:s[2]}},[t._v(t._s(`${i+1}. ${s[1]}`)+"\n              "),s[3]?e("span",{staticClass:"title-tag"},[t._v("\n                "+t._s(s[3])+"\n              ")]):t._e()])],1)]):"object"===t.type(s)?e("dl",{key:i},[e("dt",{attrs:{id:t.anchorText=s.title}},[e("a",{staticClass:"header-anchor",attrs:{href:"#"+t.anchorText}},[t._v("#")]),t._v("\n            "+t._s(`${i+1}. ${s.title}`)+"\n          ")]),t._v(" "),e("dd",[t._l(s.children,(function(s,o){return["array"===t.type(s)?[e("router-link",{key:o,attrs:{to:s[2]}},[t._v(t._s(`${i+1}-${o+1}. ${s[1]}`)+"\n                  "),s[3]?e("span",{staticClass:"title-tag"},[t._v("\n                    "+t._s(s[3])+"\n                  ")]):t._e()])]:"object"===t.type(s)?e("div",{key:o,staticClass:"sub-cat-wrap"},[e("div",{staticClass:"sub-title",attrs:{id:t.anchorText=s.title}},[e("a",{staticClass:"header-anchor",attrs:{href:"#"+t.anchorText}},[t._v("#")]),t._v("\n                  "+t._s(`${i+1}-${o+1}. ${s.title}`)+"\n                ")]),t._v(" "),t._l(s.children,(function(s,n){return e("router-link",{key:`${i+1}-${o+1}-${n+1}`,attrs:{to:s[2]}},[t._v("\n                  "+t._s(`${i+1}-${o+1}-${n+1}. ${s[1]}`)+"\n                  "),s[3]?e("span",{staticClass:"title-tag"},[t._v("\n                    "+t._s(s[3])+"\n                  ")]):t._e()])}))],2):t._e()]}))],2)]):t._e()]}))],2)]):t._e()])}),[],!1,null,"2cf874fa",null).exports),ze={data:()=>({headers:[],hashText:""}),mounted(){this.getHeadersData(),this.getHashText()},watch:{$route(){this.headers=this.$page.headers,this.getHashText()}},methods:{getHeadersData(){this.headers=this.$page.headers},getHashText(){this.hashText=decodeURIComponent(window.location.hash.slice(1))}}},Re=(s(435),{data:()=>({badges:["data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAABGpJREFUSA3tVVtoXFUU3fvOI53UlmCaKIFmwEhsE7QK0ipFEdHEKpXaZGrp15SINsXUWvBDpBgQRKi0+KKoFeJHfZA+ED9KKoIU2gYD9UejTW4rVIzm0VSTziPzuNu1z507dibTTjL4U/DAzLn3nL3X2o91ziX6f9wMFdh6Jvbm9nNSV0msViVO6tN1Rm7NMu2OpeJ9lWBUTDxrJbYTS0hInuwciu9eLHlFxCLCZEk3MegsJmZ5K/JD6t7FkFdEvGUo1g7qJoG3MHImqRIn8/nzY1K9UPKKiJmtnUqHVE3Gbuay6vJE/N2FEmuxFjW2nUuE0yQXRRxLiTUAzs36zhZvOXJPdX850EVnnLZkB8prodQoM5JGj7Xk2mvC7JB8tG04Ef5PiXtG0UtxupRQSfTnBoCy554x18yJHI6I+G5Eru4LHmPJZEQsrvPUbMiA8G/WgMK7w7I+ez7++o2ANfbrjvaOl1tFMs+htG3IrZH9/hDX1Pr8Tc0UvH8tcX29KzAgIGcEkINyW5BF9x891hw6VYqgJHEk0huccS7vh3C6gTiODL+26huuBtbct8eZnqLML8PkxGYpuPZBqtqwkSjgc4mB5gbgig5i+y0UDK35LMxXisn9xQtK+nd26gTIHsHe/oblK/b29fUmN/8Y+9jAQrnBp56m1LcDlDp9irKTExSKduXJVWSqdBMA08pEJnEIOB3FPPMybu/oeV8zFeYN3xx576Q6RH+VmplE4ncQV5v+5rzSoyOU7PuEAg8g803PwBJ0CExno/jcMbN8tONYeOmHiuUNryvm3fRUy4tMPVLdAGkUhNWuggGrJcXPv+ouCjz0MKUHz1J2/E8IC9nqTabcxgaBYM0hPhD5Y65FsbxRQKxCQrDjDctW7PUM3HuZunFyifSAqEfuzCp48Il24luWUWZoyJCaPR82jE0+kFA643wRFVni4RYSq3ohJO2pZ7B5dO4xkDWbEpossJPLSrPjYID8rS2UHTlvyNxqIGsg674XJJ7vnh5L7PNwC4hh2sjCI96mzszOTpxLF0T7l88Yz7lAuK6OnL8gXLOnTvpzSb22YG8W7us3jSebFHeeqnXRG1vt+MoUM84LQIBmMsCTAcOauTh0T0l0neQK7m2bLMt2mGxU3HYssS0J2cdv5wljlPsrIuZLAG/2DOZIXgCYT8uMGZN+e2kSirfxZOPCsC0f24nTZzspnVn9VePS1Z5vubmAGGXG8ZFno9Hel0yfA5ZPhF7Dh972BQJ2qCpgH67lmWtBYbvk6sz02wjky2vXyz0XErP/kFB619js1BtwfOV4OPRqOQBjy3Qbk18vigUPPSD5ceHnwck7W9bhAqZdd7SuG7w4/P2F/GaJh8c7e9qgow+Q7cGBo+98WsLkuktFqiZabtXuQTu/Y5ETbR0v7tNSFnvrmu6pjdoan2KjMu8q/Hmj1EfCO2ZGfEIbIXKUlw8qaX9/b2oeSJmFksSeT/Fn0V3nSypChh4Gjh74ybO9aeZ/AN2dwciu2/MhAAAAAElFTkSuQmCC","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAABH1JREFUSA3tVl1oHFUUPmdmd2ltklqbpJDiNnXFmgbFktho7YMPNiJSSZM0+CAYSkUELVhM6YuwIPpgoOKDqOBDC0XE2CQoNtQXBUFTTcCi+Wlh1V2TQExsUzcltd3M9Tt3ZjZzZ2fT+OJTL8yeM+eee757fmeJbq//KQL8X3DUSFOcfr7cRsRtxNQMWueeVzOkaITIGqQHNg5y8+jNW9ldM7A6nTpAjuolUikAwq7CE3WcM2RRDz+XGVgN3FptU/aUSlvq9Pa3iZ1+sgAqJyyAFqkipd9dqiwHF3P65YycLWc/6sqGrvoEoIp6DOFaX5h6+dnfjkWprwqsPk0dUGq5vySwDImC10KxFHgGL1SWoc92O3eVht09qdXNH11I2SsTsJYqMWzihqGMi+A+Garf3BAuuLI5oGlULyNfyB/HYNujwktOfRrMr5t77NmevqaUopx0grnKAyvVpmwUDB4x6FPXuGvYLTDwWsejwgtgkYKPqRJg8SV6xaiZ3ZTppGneS4yfH5/66fZSDHv+QZci/+h5c5UHtpy67JUqGppM0sh0Nc1dW6/N1W5Yoqat8/TU/VnadmdeW2PLLSyh0cvxBs3KbqTmwYPpxN4do/mzE8nEpvX/UMu2Wbp74zUAK5q6WkHns7V0eWkdPbPzd3rxkTGybadYySumVzhcaJFbs5UrEkQ/+CK8gF5dnh/6ciIZ73gwQ927L1IitoxKLXYP3SjYdOrHHfTZhRRlFyrorafPk20B3HPD1y2G3qKZME5Jcf3t/HUC13/8tSd++vqFveMUTwAUxSUFI1QekR1+bIze3D9MF2aq6cPvG72CgnldWCFqyRw3lwH8ZMerjTD9ElRO7Gv44wNpC90aASqGfVlz/Rx17srQ57/UU26hkhQqUB7dBR71WmzQhHUnblGmVOEw0jhbV1n9OlXUDCIRGaNV5Jp43N516fN7JmnTHdfp7Hgy0luO4aMhtkLL8Bi3bUWYvzh5Mn1dTxrL6QmGuRhGL/TiTTxRoEdTszSaq9GR0NGA3KdkOz3hqSV3MIDhQ5IVX/Ivx3umBti2es2h4eZby7x8br1rkf7Mo90AqC8aQ3sJeNzqFRu+vSANAQe3PL7l0HGOAdwDCeZYvNKeoZp1Qfs6Aipndh86HmFRi0LAnEO47wsqM6cdfjh3jBPUzhZy7nvlUfFsamED1VQt6aISHVymXZ/B2aCtIG8AI8xfobj2d3en1wWVhOeHELKmLQ1s211s88comkv4UCwWyF787mJdYXtNfhKAXVqnKTq8QZvGAGGOfaTo5pGZ/PwbUCr5+DPr/1J92JNHr9aOl/F3iI5+O1nfybsGxoimvZ3ViWSluDITw3P37mypheDIPY0tw7+O/5ApbkYw+zpfaUVu32Pi98+defdUhEpZkRFq0aqyNh9FuL9hpYbEm6iwi0z2REd09ZmyENEbuhjDWzKvZXTqKYaBIr3tt5kuPtQBZFvEUwHt60vfCNu41XsksH9Ij1BMMz1Y0OOunHNShFIP5868g5zeXmuLwL9T4b6Q2+KejgAAAABJRU5ErkJggg==","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAABKFJREFUSA3tVl1oFVcQnrMbrak3QUgkya1akpJYcrUtIqW1JvFBE9LiQ5v6JmJpolbMg32rVrhgoYK0QiMY6i9Y6EMaW5D+xFJaTYItIuK2Kr3+BJNwkxBj05sQY3b3nM6cs2dv9t7NT/vQJw/sndk5M/PNzJkzewGerP+pAmy+ON8lLzUJgA8ZYxYIYZmGYRnctDaWvJJAmTtfP1pvXsBCCPP8QFcCaRkZYACgDZFO4stNIcBCajEOlmmC9XpJ9bAGCaPaPmzPl32dvLSVu3BWCTQs0XQQ6g0DYgwLIoAZbBCdW/i+781o1VVlm/410mw4h06Y7bIPHNyWDyL4FHkX03Q8SrzNhZTZriieckWt7cL6MM85YcLpsi/7O9/iXFT6MswI0DmmpkSaJ0qLxFIm3+i1THHB3zmBH3PYx9CcykcLOeQVVa7QtdxTgQgEleX2AjHYfwA+2ddV77ruGoJUbhGDI09YSNXyMpUt5ylOzxgbUmtOp7NmbNt8v3arjTBfYELmLUV+M+nSawNNAUqpT3ClJWg5I3BLT+cGW/DXNGCa6tx1aakCGEigArTn4TDIPdrXXYKCZNrHLMCOEPvHBlLQ99s9eHB7EB6NTki73CVPQ2F5MSx/uRQixfmq7rK0wYD8w8E905bnPDfwoWs/rfv93NWN/ZfvwsLIU7A09gxECyISeGJkHAau98L97tuw7NXnoPyNF8FcYGLGKsOs0mN3OEyec9esGW/ZEl945dTP34wlR2FZVQWU1q0Cw8Tr7p+hgLLNL0FPxx/Q35mA8aEUrH6nCgwEl0tn7wUiZYJnNRh6DK4UH/k0lfyrsBKdPVv/AriGIQcEDQZ65LBAGe2Rzui9Ybjz7XUppz1/uKBbyVPGkN3ZAeC6hr0x7Nr38N5+EqkoOm17xpoqR9ohQF55ERSvr4Dkr3chNfC3DMzGJlNBElW8w9nsGQvhNGIzDkXzCg8cLK951xHsFBlTJspJNi3ZFIMF2AeDV3q8DNOB+YHi6QTrChDIWDBRi5U5f+ZMfJLu3ccrqxtdxk4SKH336LFxSmkqefwU5T8fhdSdQf9IVKD6aNiwI/hnmcAZ91isYMJIaCUCx9W098+LgruikeTqzqqxKPUwqJyCPJiyemVVZBOijDGjD38Os0jOiSPL1z3SPjXNANbiNPXAdzTfukjjuknNBbyz3nwgTd3AVFqUJ5hpHlq9MveLnWwttUfoygBmvVjuikxND3znrhsELnZk7k+OjIGxeNEkomyLVta0xxn+HZhjBc4YZ/AFjHjz9u3xRZl2BN4aq9nFwWh16IrQ1aHHEd3j1+4/dB9OtH4e29A2H1DyHQRmOSfQZ1Fy7MHBTGB6J/Djq6p3OxyO2cB+4Car7v/o3GXgfAkj23+x9ID1Teoamo/SXcbvSf2PX7Vc8DdCmE1vN9di+32P9/5YR3vLnhCVGUWBjEkr3yh4H8v9CzmsbdhzOKzsJKM90iFdaTMjRPhGVsakRvOaRidljo6H6G7j+ctrJpsP+4COhDIl0La2+FS4+5mlocBaXY5QnGZysIBYoeSsl5qQzrSj/cgNrfuEzlWBfwA+EjrZyWUvpAAAAABJRU5ErkJggg=="],currentBadge:""}),created(){this.$themeConfig.titleBadgeIcons&&(this.badges=this.$themeConfig.titleBadgeIcons),this.currentBadge=this.getBadge()},watch:{"$route.path"(){this.currentBadge=this.getBadge()}},methods:{getBadge(){return this.badges[Math.floor(Math.random()*this.badges.length)]}}}),qe={mixins:[Re],data:()=>({updateBarConfig:null}),props:["sidebarItems"],components:{PageEdit:Ae,PageNav:Ne,ArticleInfo:He,Catalogue:Ue,UpdateArticle:jt,RightMenu:Object(n.a)(ze,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"right-menu-wrapper"},[e("div",{staticClass:"right-menu-margin"},[e("div",{staticClass:"right-menu-title"},[t._v("目录")]),t._v(" "),e("div",{staticClass:"right-menu-content"},t._l(t.headers,(function(s,i){return e("div",{key:i,class:["right-menu-item","level"+s.level,{active:s.slug===t.hashText}]},[e("a",{attrs:{href:"#"+s.slug}},[t._v(t._s(s.title))])])})),0)])])}),[],!1,null,null,null).exports},created(){this.updateBarConfig=this.$themeConfig.updateBar},computed:{bgStyle(){const{contentBgStyle:t}=this.$themeConfig;return t?"bg-style-"+t:""},isShowUpdateBar(){return!this.updateBarConfig||!1!==this.updateBarConfig.showToArticle},showTitle(){return!this.$frontmatter.pageComponent},showRightMenu(){const{$frontmatter:t,$themeConfig:e,$page:s}=this,{sidebar:i}=t;return!1!==e.rightMenuBar&&s.headers&&!1!==(t&&i&&!1!==i)},pageComponent(){return!!this.$frontmatter.pageComponent&&this.$frontmatter.pageComponent.name},isShowSlotT(){return this.getShowStatus("pageTshowMode")},isShowSlotB(){return this.getShowStatus("pageBshowMode")}},methods:{getShowStatus(t){const{htmlModules:e}=this.$themeConfig;return!!e&&("article"===e[t]?this.isArticle():"custom"!==e[t]||!this.isArticle())},isArticle(){return!1!==this.$frontmatter.article}}},We=(s(436),Object(n.a)(qe,(function(){var t=this,e=t._self._c;return e("div",[e("main",{staticClass:"page"},[e("div",{class:"theme-vdoing-wrapper "+t.bgStyle},[t.isArticle()?e("ArticleInfo"):e("div",{staticClass:"placeholder"}),t._v(" "),t.pageComponent?e(t.pageComponent,{tag:"component",staticClass:"theme-vdoing-content"}):t._e(),t._v(" "),e("div",{staticClass:"content-wrapper"},[t.showRightMenu?e("RightMenu"):t._e(),t._v(" "),t.showTitle?e("h1",[!1!==t.$themeConfig.titleBadge?e("img",{attrs:{src:t.currentBadge}}):t._e(),t._v(t._s(this.$page.title)),t.$frontmatter.titleTag?e("span",{staticClass:"title-tag"},[t._v(t._s(t.$frontmatter.titleTag))]):t._e()]):t._e(),t._v(" "),t.isShowSlotT?t._t("top"):t._e(),t._v(" "),e("Content",{staticClass:"theme-vdoing-content"})],2),t._v(" "),t.isShowSlotB?t._t("bottom"):t._e(),t._v(" "),e("PageEdit"),t._v(" "),e("PageNav",t._b({},"PageNav",{sidebarItems:t.sidebarItems},!1))],2),t._v(" "),t.isShowUpdateBar?e("UpdateArticle",{attrs:{length:3,moreArticle:t.updateBarConfig&&t.updateBarConfig.moreArticle}}):t._e()],1)])}),[],!1,null,null,null).exports),Ge={data:()=>({category:"",total:0,perPage:10,currentPage:1}),components:{MainLayout:$t,PostList:Xt,Pagination:Ft,CategoriesBar:qt},mounted(){const t=this.$route.query.category;t?(this.category=t,this.total=this.$groupPosts.categories[t].length):this.total=this.$sortPosts.length,this.$route.query.p&&(this.currentPage=Number(this.$route.query.p));const e=document.querySelector(".categories");e&&setTimeout(()=>{const t=e.querySelector(".active"),s=t?t.offsetTop:0;e.scrollTo({top:s,behavior:"smooth"})},300)},methods:{handlePagination(t){this.currentPage=t}},watch:{"$route.query.category"(t){this.category=t?decodeURIComponent(t):"",this.category?this.total=this.$groupPosts.categories[this.category].length:this.total=this.$sortPosts.length,this.currentPage=1}}},Ke=(s(437),Object(n.a)(Ge,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"custom-page categories-page"},[e("MainLayout",{scopedSlots:t._u([{key:"mainLeft",fn:function(){return[t.$categoriesAndTags.categories.length?e("CategoriesBar",{attrs:{categoriesData:t.$categoriesAndTags.categories,category:t.category}}):t._e(),t._v(" "),e("PostList",{attrs:{currentPage:t.currentPage,perPage:t.perPage,category:t.category}}),t._v(" "),e("Pagination",{directives:[{name:"show",rawName:"v-show",value:Math.ceil(t.total/t.perPage)>1,expression:"Math.ceil(total / perPage) > 1"}],attrs:{total:t.total,perPage:t.perPage,currentPage:t.currentPage},on:{getCurrentPage:t.handlePagination}})]},proxy:!0},{key:"mainRight",fn:function(){return[t.$categoriesAndTags.categories.length?e("CategoriesBar",{attrs:{categoriesData:t.$categoriesAndTags.categories,category:t.category}}):t._e()]},proxy:!0}])})],1)}),[],!1,null,null,null).exports),Qe={data:()=>({tag:"",total:0,perPage:10,currentPage:1}),components:{MainLayout:$t,PostList:Xt,Pagination:Ft,TagsBar:Gt},mounted(){const t=this.$route.query.tag;t?(this.tag=t,this.total=this.$groupPosts.tags[t].length):this.total=this.$sortPosts.length,this.$route.query.p&&(this.currentPage=Number(this.$route.query.p))},methods:{handlePagination(t){this.currentPage=t}},watch:{"$route.query.tag"(t){this.tag=t?decodeURIComponent(t):"",this.tag?this.total=this.$groupPosts.tags[this.tag].length:this.total=this.$sortPosts.length,this.currentPage=1}}},Je=(s(438),Object(n.a)(Qe,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"custom-page tags-page"},[e("MainLayout",{scopedSlots:t._u([{key:"mainLeft",fn:function(){return[t.$categoriesAndTags.tags.length?e("TagsBar",{attrs:{tagsData:t.$categoriesAndTags.tags,tag:t.tag}}):t._e(),t._v(" "),e("PostList",{attrs:{currentPage:t.currentPage,perPage:t.perPage,tag:t.tag}}),t._v(" "),e("Pagination",{directives:[{name:"show",rawName:"v-show",value:Math.ceil(t.total/t.perPage)>1,expression:"Math.ceil(total / perPage) > 1"}],attrs:{total:t.total,perPage:t.perPage,currentPage:t.currentPage},on:{getCurrentPage:t.handlePagination}})]},proxy:!0},{key:"mainRight",fn:function(){return[t.$categoriesAndTags.tags.length?e("TagsBar",{attrs:{tagsData:t.$categoriesAndTags.tags,tag:t.tag}}):t._e()]},proxy:!0}])})],1)}),[],!1,null,null,null).exports),Ve=s(66),Ze=s.n(Ve),ts={mixins:[Re],data:()=>({postsList:[],countByYear:{},perPage:80,currentPage:1}),created(){this.getPageData();const{$sortPostsByDate:t,countByYear:e}=this;for(let s=0;s<t.length;s++){const{frontmatter:{date:o}}=t[s];if(o&&"string"===Object(i.n)(o)){const t=o.slice(0,4);e[t]||(e[t]=0),e[t]=e[t]+1}}this.countByYear=e},mounted(){window.addEventListener("scroll",Ze()(()=>{if(this.postsList.length<this.$sortPostsByDate.length){const t=document.documentElement,e=document.body,s=t.scrollTop||e.scrollTop,i=t.clientHeight||e.clientHeight,o=t.scrollHeight||e.scrollHeight;o>i&&s+i>=o-250&&this.loadmore()}},200))},methods:{getPageData(){const t=this.currentPage,e=this.perPage;this.postsList=this.postsList.concat(this.$sortPostsByDate.slice((t-1)*e,t*e))},loadmore(){this.currentPage=this.currentPage+1,this.getPageData()},getYear(t){const e=this.postsList[t];if(!e)return;const{frontmatter:{date:s}}=e;return s&&"string"===Object(i.n)(s)?s.slice(0,4):void 0},getDate(t){const{frontmatter:{date:e}}=t;if(e&&"string"===Object(i.n)(e))return e.slice(5,10)}}},es=(s(439),Object(n.a)(ts,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"custom-page archives-page"},[e("div",{staticClass:"theme-vdoing-wrapper"},[e("h1",[!1!==t.$themeConfig.titleBadge?e("img",{attrs:{src:t.currentBadge}}):t._e(),t._v("\n      "+t._s(t.$page.title)+"\n    ")]),t._v(" "),e("div",{staticClass:"count"},[t._v("\n      总共 "),e("i",[t._v(t._s(t.$sortPostsByDate.length))]),t._v(" 篇文章\n    ")]),t._v(" "),e("ul",[t._l(t.postsList,(function(s,i){return[(t.year=t.getYear(i))!==t.getYear(i-1)?e("li",{key:i+t.$sortPostsByDate.length,staticClass:"year"},[e("h2",[t._v("\n            "+t._s(t.year)+"\n            "),e("span",[e("i",[t._v(t._s(t.countByYear[t.year]))]),t._v(" 篇\n            ")])])]):t._e(),t._v(" "),e("li",{key:i},[e("router-link",{attrs:{to:s.path}},[e("span",{staticClass:"date"},[t._v(t._s(t.getDate(s)))]),t._v("\n            "+t._s(s.title)+"\n            "),s.frontmatter.titleTag?e("span",{staticClass:"title-tag"},[t._v("\n              "+t._s(s.frontmatter.titleTag)+"\n            ")]):t._e()])],1)]}))],2)])])}),[],!1,null,null,null).exports),ss={name:"Sidebar",components:{SidebarLinks:s(412).default,NavLinks:Ce},props:["items"],computed:{blogger(){return this.$themeConfig.blogger}}},is=(s(442),Object(n.a)(ss,(function(){var t=this,e=t._self._c;return e("aside",{staticClass:"sidebar"},[t.blogger?e("div",{staticClass:"blogger"},[e("img",{attrs:{src:t.blogger.avatar}}),t._v(" "),e("div",{staticClass:"blogger-info"},[e("h3",[t._v(t._s(t.blogger.name))]),t._v(" "),t.blogger.social?e("div",{staticClass:"icons"},t._l(t.blogger.social.icons,(function(t,s){return e("a",{key:s,class:["iconfont",t.iconClass],attrs:{href:t.link,title:t.title,target:"_blank"}})})),0):e("span",[t._v(t._s(t.blogger.slogan))])])]):t._e(),t._v(" "),e("NavLinks"),t._v(" "),t._t("top"),t._v(" "),e("SidebarLinks",{attrs:{depth:0,items:t.items}}),t._v(" "),t._t("bottom")],2)}),[],!1,null,null,null).exports),os=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var s=arguments[e];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(t[i]=s[i])}return t},ns="undefined"==typeof window,rs={version:"1.1.1",storage:ns?null:window.localStorage,session:{storage:ns?null:window.sessionStorage}},as={set:function(t,e){if(!this.disabled)return void 0===e?this.remove(t):(this.storage.setItem(t,function(t){return JSON.stringify(t)}(e)),e)},get:function(t,e){if(this.disabled)return e;var s=function(t){if("string"!=typeof t)return;try{return JSON.parse(t)}catch(e){return t||void 0}}(this.storage.getItem(t));return void 0===s?e:s},has:function(t){return void 0!==this.get(t)},remove:function(t){this.disabled||this.storage.removeItem(t)},clear:function(){this.disabled||this.storage.clear()},getAll:function(){if(this.disabled)return null;var t={};return this.forEach((function(e,s){t[e]=s})),t},forEach:function(t){if(!this.disabled)for(var e=0;e<this.storage.length;e++){var s=this.storage.key(e);t(s,this.get(s))}}};os(rs,as),os(rs.session,as);try{var ls="__storejs__";rs.set(ls,ls),rs.get(ls)!==ls&&(rs.disabled=!0),rs.remove(ls)}catch(t){rs.disabled=!0}var hs=rs;var cs={data:()=>({threshold:100,scrollTop:null,showCommentBut:!1,commentTop:null,currentMode:"",showModeBox:!1,modeList:[{name:"跟随系统",icon:"icon-zidong",KEY:"auto"},{name:"浅色模式",icon:"icon-rijianmoshi",KEY:"light"},{name:"深色模式",icon:"icon-yejianmoshi",KEY:"dark"},{name:"阅读模式",icon:"icon-yuedu",KEY:"read"}],_scrollTimer:null,_textareaEl:null,_recordScrollTop:null,COMMENT_SELECTOR_1:"#vuepress-plugin-comment",COMMENT_SELECTOR_2:"#valine-vuepress-comment",COMMENT_SELECTOR_3:".vssue"}),mounted(){if(this.currentMode=hs.get("mode")||this.$themeConfig.defaultMode||"auto",this.scrollTop=this.getScrollTop(),window.addEventListener("scroll",Ze()(()=>{this.scrollTop=this.getScrollTop()},100)),window.addEventListener("load",()=>{this.getCommentTop()}),document.documentElement.clientWidth<719){this.$refs.modeBox.onclick=()=>{this.showModeBox=!1},window.addEventListener("scroll",Ze()(()=>{this.showModeBox&&(this.showModeBox=!1)},100))}const t=document.querySelectorAll(".buttons .button");for(let e=0;e<t.length;e++){const s=t[e];s.addEventListener("touchstart",(function(){s.classList.add("hover")})),s.addEventListener("touchend",(function(){setTimeout(()=>{s.classList.remove("hover")},150)}))}},computed:{showToTop(){return this.scrollTop>this.threshold}},methods:{toggleMode(t){this.currentMode=t,this.$emit("toggle-theme-mode",t)},getScrollTop:()=>window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,scrollToTop(){window.scrollTo({top:0,behavior:"smooth"}),this.scrollTop=0},getCommentTop(){setTimeout(()=>{let t=document.querySelector(this.COMMENT_SELECTOR_1)||document.querySelector(this.COMMENT_SELECTOR_2)||document.querySelector(this.COMMENT_SELECTOR_3);t&&(this.showCommentBut=!1!==this.$frontmatter.comment&&!0!==this.$frontmatter.home,this.commentTop=t.offsetTop-58)},500)},scrollToComment(){window.scrollTo({top:this.commentTop,behavior:"smooth"}),this._textareaEl=document.querySelector(this.COMMENT_SELECTOR_1+" textarea")||document.querySelector(this.COMMENT_SELECTOR_2+" input")||document.querySelector(this.COMMENT_SELECTOR_3+" textarea"),this._textareaEl&&this.getScrollTop()!==this._recordScrollTop?document.addEventListener("scroll",this._handleListener):this._textareaEl&&this.getScrollTop()===this._recordScrollTop&&this._handleFocus()},_handleListener(){clearTimeout(this._scrollTimer),this._scrollTimer=setTimeout(()=>{document.removeEventListener("scroll",this._handleListener),this._recordScrollTop=this.getScrollTop(),this._handleFocus()},30)},_handleFocus(){this._textareaEl.focus(),this._textareaEl.classList.add("yellowBorder"),setTimeout(()=>{this._textareaEl.classList.remove("yellowBorder")},500)}},watch:{"$route.path"(){this.showCommentBut=!1,this.getCommentTop()}}},us=(s(443),Object(n.a)(cs,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"buttons"},[e("transition",{attrs:{name:"fade"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.showToTop,expression:"showToTop"}],staticClass:"button blur go-to-top iconfont icon-fanhuidingbu",attrs:{title:"返回顶部"},on:{click:t.scrollToTop}})]),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showCommentBut,expression:"showCommentBut"}],staticClass:"button blur go-to-comment iconfont icon-pinglun",attrs:{title:"去评论"},on:{click:t.scrollToComment}}),t._v(" "),e("div",{staticClass:"button blur theme-mode-but iconfont icon-zhuti",attrs:{title:"主题模式"},on:{mouseenter:function(e){t.showModeBox=!0},mouseleave:function(e){t.showModeBox=!1},click:function(e){t.showModeBox=!0}}},[e("transition",{attrs:{name:"mode"}},[e("ul",{directives:[{name:"show",rawName:"v-show",value:t.showModeBox,expression:"showModeBox"}],ref:"modeBox",staticClass:"select-box",on:{click:function(t){t.stopPropagation()},touchstart:function(t){t.stopPropagation()}}},t._l(t.modeList,(function(s){return e("li",{key:s.KEY,staticClass:"iconfont",class:[s.icon,{active:s.KEY===t.currentMode}],on:{click:function(e){return t.toggleMode(s.KEY)}}},[t._v("\n          "+t._s(s.name)+"\n        ")])})),0)])],1)],1)}),[],!1,null,null,null).exports),ps={computed:{social(){return this.$themeConfig.social},footer(){return this.$themeConfig.footer}}},gs=(s(444),Object(n.a)(ps,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"footer"},[t.social&&t.social.icons?e("div",{staticClass:"icons"},t._l(t.social.icons,(function(t,s){return e("a",{key:s,class:["iconfont",t.iconClass],attrs:{href:t.link,title:t.title,target:"_blank"}})})),0):t._e(),t._v(" "),t._v("\n  Theme by\n  "),e("a",{attrs:{href:"https://github.com/xugaoyi/vuepress-theme-vdoing",target:"_blank",title:"本站主题"}},[t._v("Vdoing")]),t._v(" "),t.footer?[t._v("\n    | Copyright © "+t._s(t.footer.createYear)+"-"+t._s((new Date).getFullYear())+"\n    "),e("span",{domProps:{innerHTML:t._s(t.footer.copyrightInfo)}})]:t._e()],2)}),[],!1,null,null,null).exports),ds={data:()=>({bgImg:"",opacity:.5}),mounted(){let{bodyBgImg:t,bodyBgImgOpacity:e,bodyBgImgInterval:s=15}=this.$themeConfig;if("string"===Object(i.n)(t))this.bgImg=t;else if("array"===Object(i.n)(t)){let e=0,i=null;this.bgImg=t[e],clearInterval(i),i=setInterval(()=>{if(++e>=t.length&&(e=0),this.bgImg=t[e],t[e+1]){(new Image).src=t[e+1]}},1e3*s)}void 0!==e&&(this.opacity=e)}},fs=(s(445),Object(n.a)(ds,(function(){return(0,this._self._c)("div",{staticClass:"body-bg",style:`background: url(${this.bgImg}) center center / cover no-repeat;opacity:${this.opacity}`})}),[],!1,null,null,null).exports);var ms={components:{Home:Qt,Navbar:Se,Page:We,CategoriesPage:Ke,TagsPage:Je,ArchivesPage:es,Sidebar:is,Footer:gs,Buttons:us,BodyBgImg:fs},data:()=>({hideNavbar:!1,isSidebarOpen:!0,showSidebar:!1,themeMode:"auto",showWindowLB:!0,showWindowRB:!0}),computed:{sidebarSlotTop(){return this.getHtmlStr("sidebarT")},sidebarSlotBottom(){return this.getHtmlStr("sidebarB")},pageSlotTop(){return this.getHtmlStr("pageT")},pageSlotBottom(){return this.getHtmlStr("pageB")},windowLB(){return this.getHtmlStr("windowLB")},windowRB(){return this.getHtmlStr("windowRB")},showRightMenu(){const{headers:t}=this.$page;return!this.$frontmatter.home&&!1!==this.$themeConfig.rightMenuBar&&t&&t.length&&!1!==this.$frontmatter.sidebar},shouldShowNavbar(){const{themeConfig:t}=this.$site,{frontmatter:e}=this.$page;return!1!==e.navbar&&!1!==t.navbar&&(this.$title||t.logo||t.repo||t.nav||this.$themeLocaleConfig.nav)},shouldShowSidebar(){const{frontmatter:t}=this.$page;return!t.home&&!1!==t.sidebar&&this.sidebarItems.length&&!1!==t.showSidebar},sidebarItems(){return Object(i.m)(this.$page,this.$page.regularPath,this.$site,this.$localePath)},pageClasses(){const t=this.$page.frontmatter.pageClass;return[{"no-navbar":!this.shouldShowNavbar,"hide-navbar":this.hideNavbar,"sidebar-open":this.isSidebarOpen,"no-sidebar":!this.shouldShowSidebar,"have-rightmenu":this.showRightMenu,"have-body-img":this.$themeConfig.bodyBgImg,"only-sidebarItem":1===this.sidebarItems.length&&"page"===this.sidebarItems[0].type},t]}},created(){const t=this.$themeConfig.sidebarOpen;!1===t&&(this.isSidebarOpen=t)},beforeMount(){this.isSidebarOpenOfclientWidth();const t=hs.get("mode"),{defaultMode:e}=this.$themeConfig;e&&"auto"!==e&&!t?this.themeMode=e:t&&"auto"!==t&&"auto"!==e?this.themeMode=t:this._autoMode(),this.setBodyClass();const s=this.$themeConfig.social;if(s&&s.iconfontCssFile){let t=document.createElement("link");t.setAttribute("rel","stylesheet"),t.setAttribute("type","text/css"),t.setAttribute("href",s.iconfontCssFile),document.head.appendChild(t)}},mounted(){const t=document.location.hash;if(t.length>1){const e=decodeURIComponent(t.substring(1)),s=document.getElementById(e);s&&s.scrollIntoView()}this.showSidebar=!0,this.$router.afterEach(()=>{this.isSidebarOpenOfclientWidth()});let e=0,s=0;window.addEventListener("scroll",te.a.throttle(()=>{this.isSidebarOpen||(e=this.getScrollTop(),this.hideNavbar=s<e&&e>58,setTimeout(()=>{s=e},0))},300))},watch:{isSidebarOpen(){this.isSidebarOpen&&(this.hideNavbar=!1)},themeMode(){this.setBodyClass()}},methods:{getHtmlStr(t){const{htmlModules:e}=this.$themeConfig;return e?e[t]:""},setBodyClass(){let{pageStyle:t="card",bodyBgImg:e}=this.$themeConfig;("card"!==t&&"line"!==t||e)&&(t="card"),document.body.className=`theme-mode-${this.themeMode} theme-style-${t}`},getScrollTop:()=>window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,isSidebarOpenOfclientWidth(){document.documentElement.clientWidth<719&&(this.isSidebarOpen=!1)},toggleSidebar(t){this.isSidebarOpen="boolean"==typeof t?t:!this.isSidebarOpen,this.$emit("toggle-sidebar",this.isSidebarOpen)},_autoMode(){window.matchMedia("(prefers-color-scheme: dark)").matches?this.themeMode="dark":this.themeMode="light"},toggleThemeMode(t){"auto"===t?this._autoMode():this.themeMode=t,hs.set("mode",t)},onTouchStart(t){this.touchStart={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY}},onTouchEnd(t){const e=t.changedTouches[0].clientX-this.touchStart.x,s=t.changedTouches[0].clientY-this.touchStart.y;Math.abs(e)>Math.abs(s)&&Math.abs(e)>40&&(e>0&&this.touchStart.x<=80?this.toggleSidebar(!0):this.toggleSidebar(!1))}}},vs=(s(446),Object(n.a)(ms,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"theme-container",class:t.pageClasses,on:{touchstart:t.onTouchStart,touchend:t.onTouchEnd}},[t.shouldShowNavbar?e("Navbar",{on:{"toggle-sidebar":t.toggleSidebar}}):t._e(),t._v(" "),e("div",{staticClass:"sidebar-mask",on:{click:function(e){return t.toggleSidebar(!1)}}}),t._v(" "),!1!==t.$themeConfig.sidebarHoverTriggerOpen?e("div",{staticClass:"sidebar-hover-trigger"}):t._e(),t._v(" "),e("Sidebar",{directives:[{name:"show",rawName:"v-show",value:t.showSidebar,expression:"showSidebar"}],attrs:{items:t.sidebarItems},on:{"toggle-sidebar":t.toggleSidebar},scopedSlots:t._u([t.sidebarSlotTop?{key:"top",fn:function(){return[e("div",{staticClass:"sidebar-slot sidebar-slot-top",domProps:{innerHTML:t._s(t.sidebarSlotTop)}})]},proxy:!0}:null,t.sidebarSlotBottom?{key:"bottom",fn:function(){return[e("div",{staticClass:"sidebar-slot sidebar-slot-bottom",domProps:{innerHTML:t._s(t.sidebarSlotBottom)}})]},proxy:!0}:null],null,!0)}),t._v(" "),t.$page.frontmatter.home?e("Home"):t.$page.frontmatter.categoriesPage?e("CategoriesPage"):t.$page.frontmatter.tagsPage?e("TagsPage"):t.$page.frontmatter.archivesPage?e("ArchivesPage"):e("Page",{attrs:{"sidebar-items":t.sidebarItems},scopedSlots:t._u([t.pageSlotTop?{key:"top",fn:function(){return[e("div",{staticClass:"page-slot page-slot-top",domProps:{innerHTML:t._s(t.pageSlotTop)}})]},proxy:!0}:null,t.pageSlotBottom?{key:"bottom",fn:function(){return[e("div",{staticClass:"page-slot page-slot-bottom",domProps:{innerHTML:t._s(t.pageSlotBottom)}})]},proxy:!0}:null],null,!0)}),t._v(" "),e("Footer"),t._v(" "),e("Buttons",{ref:"buttons",on:{"toggle-theme-mode":t.toggleThemeMode}}),t._v(" "),t.$themeConfig.bodyBgImg?e("BodyBgImg"):t._e(),t._v(" "),t.windowLB?e("div",{directives:[{name:"show",rawName:"v-show",value:t.showWindowLB,expression:"showWindowLB"}],staticClass:"custom-html-window custom-html-window-lb"},[e("div",{staticClass:"custom-wrapper"},[e("span",{staticClass:"close-but",on:{click:function(e){t.showWindowLB=!1}}},[t._v("×")]),t._v(" "),e("div",{domProps:{innerHTML:t._s(t.windowLB)}})])]):t._e(),t._v(" "),t.windowRB?e("div",{directives:[{name:"show",rawName:"v-show",value:t.showWindowRB,expression:"showWindowRB"}],staticClass:"custom-html-window custom-html-window-rb"},[e("div",{staticClass:"custom-wrapper"},[e("span",{staticClass:"close-but",on:{click:function(e){t.showWindowRB=!1}}},[t._v("×")]),t._v(" "),e("div",{domProps:{innerHTML:t._s(t.windowRB)}})])]):t._e()],1)}),[],!1,null,null,null));e.default=vs.exports}}]);