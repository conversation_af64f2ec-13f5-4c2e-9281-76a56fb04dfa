<template>
  <div class="filter-box">
    <div class="title-box" @click="changeFold">
      <div class="title-icon"></div>
      <div class="title-text">
        <span
          v-if="required"
          style="
            font-weight: bold;
            color: #e33d5c;
            align-self: center;
            margin-right: 3px;
            line-height: 10px;
          "
          >*</span
        >{{ title }}
      </div>
      <slot name="btn"></slot>
      <img class="arrow-icon" :src="isFold ? arrowDowm : arrowUp" />
    </div>
    <slot v-if="!isFold"></slot>
    <div class="filter-list-box" v-show="!isFold">
      <van-search
        v-model="bbRtlCustLicNoKeyWords"
        placeholder="请输入许可证号"
        @input="setInputItem"
        style="width: 100%"
      >
      </van-search>
    </div>
  </div>
</template>

<script>
import arrowDowm from './imgs/arrow-down.png'
import arrowUp from './imgs/arrow-up.png'
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: '',
    },
    folded: {
      type: Boolean,
      default: false,
    },
    required: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    // value() {
    //   this.inputItem = this.value
    //   console.log('11111', this.inputItem)
    // },
    // inputItem() {
    //   console.log('wt2f', this.inputItem)
    // },
  },
  data() {
    return {
      //是否折叠
      isFold: false,
      arrowDowm,
      arrowUp,
      bbRtlCustLicNoKeyWords: '',
      //双向绑定所选项
      inputItem: '',
    }
  },
  created() {
    this.bbRtlCustLicNoKeyWords = this.value ? this.value : ''
    this.isFold = this.folded
  },
  methods: {
    changeFold() {
      this.isFold = !this.isFold
    },
    // onSearch() {
    //   this.$emit('doSearch', this.bbRtlCustLicNoKeyWords)
    // },
    setInputItem() {
      this.$emit('input', this.bbRtlCustLicNoKeyWords)
    },
  },
}
</script>

<style lang="less" scoped>
.filter-box {
  background: #ffffff;
  border-radius: 10px;
  .title-box {
    display: flex;
    padding: 15px 10px;
    align-items: center;
    .title-icon {
      width: 5px;
      height: 15px;
      background: linear-gradient(180deg, #7fc7ff, #2d8cf0);
      border-radius: 2px;
    }
    .title-text {
      font-weight: bold;
      font-size: 15px;
      color: #333333;
      margin-left: 6px;
    }
    .arrow-icon {
      margin-left: auto;
      width: 8px;
      height: 5px;
    }
  }
  .filter-list-box {
    display: flex;
    flex-wrap: wrap;
    .filter-item-box {
      text-align: center;
      background: #f6f6f6;
      border-radius: 6px;
      width: calc((100% - 18px) / 3);
      padding: 10px 0;
      margin: 3px;
      font-weight: 400;
      font-size: 13px;
      color: #333333;
      &.selected {
        background: #e7f4fd;
        width: calc((100% - 24px) / 3);
        border: 1px solid #b2d7fe;
        margin: 2px 3px;
        font-weight: bold;
        color: #2d8cf0;
      }
    }
    .van-search {
      .van-search__action {
        .search {
          font-weight: 400;
          font-size: 13px;
          color: #2d8cf0;
          line-height: 13px;
        }
      }
    }
  }
}
</style>
