<template>
  <div class="language-json">
    <pre class="language-json">
      <code>{{ rlt }}</code>
    </pre>
  </div>
</template>

<script>
import { row2column } from '@indfnd/utils'

const allData = [
  { comId: '11370101', comName: '济南', year: '2022', qty: 110, qtySame: 100, qtyIncPer: 10 },
  { comId: '11370201', comName: '青岛', year: '2022', qty: 60, qtySame: 50, qtyIncPer: 20 },
]

const columnGroups = [
  {
    keyProp: 'year',
    titleProp: 'year',
    titleFormatter: (title) => `${title}年`,
    children: [
      {
        // 多层指标时，列定义的 key 会直接和下层的 key 拼在一起，中间没有分隔符
        // 如 qty + Same = qtySame ，处理数据时会去找 qtySame 对应的值
        // 当然，你也可以在这一层传空字符串，在最后一层传指标的完整属性名
        // 但多种类型的指标都有本期同期增幅就无法复用了哦
        key: 'qty',
        title: '销量',
        children: [
          { key: '', title: '本期', width: 50, align: 'right' },
          { key: 'Same', title: '同期', width: 50, align: 'right' },
          { key: 'IncPer', title: '增幅(%)', width: 50, align: 'right' },
        ],
      },
    ],
  },
]

export default {
  data() {
    return {
      rlt: null,
    }
  },
  created() {
    this.rlt = row2column(allData, columnGroups, 'comId')
  },
}
</script>
