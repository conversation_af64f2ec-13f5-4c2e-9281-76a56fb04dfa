(window.webpackJsonp=window.webpackJsonp||[]).push([[59],{594:function(s,t,a){"use strict";a.r(t);var e=a(15),n=Object(e.a)({},(function(){var s=this,t=s._self._c;return t("ContentSlotsDistributor",{attrs:{"slot-key":s.$parent.slotKey}},[t("h2",{attrs:{id:"样式目录结构"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#样式目录结构"}},[s._v("#")]),s._v(" 样式目录结构")]),s._v(" "),t("div",{staticClass:"language- extra-class"},[t("pre",{pre:!0,attrs:{class:"language-text"}},[t("code",[s._v("styles                       \n├─ components                \n│  └─ ...                    \n├─ third                     \n│  └─ ...                    \n├─ variables                 \n│  ├─ base.less              \n│  ├─ index.less             \n│  ├─ theme1.less            \n│  └─ theme2.less            \n├─ index.less                \n└─ reset.less                \n")])])]),t("ul",[t("li",[s._v("components: 各组件的样式")]),s._v(" "),t("li",[s._v("third: 第三方组件库的覆盖样式，如 view-design 的")]),s._v(" "),t("li",[s._v("variables\n"),t("ul",[t("li",[s._v("base.less: 基础样式变量")]),s._v(" "),t("li",[s._v("theme1.less: theme1样式变量")]),s._v(" "),t("li",[s._v("theme2.less: theme2样式变量")])])]),s._v(" "),t("li",[s._v("reset.less: 覆盖浏览器默认样式，如 "),t("code",[s._v("font-family")]),s._v(" 、滚动条样式")])]),s._v(" "),t("p",[s._v("TODO: 缺少第三方UI库变量的覆盖文件")]),s._v(" "),t("h2",{attrs:{id:"使用方法"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#使用方法"}},[s._v("#")]),s._v(" 使用方法")]),s._v(" "),t("p",[s._v("样式使用 "),t("code",[s._v("CSS变量")]),s._v(" 统一定义，原则上是")]),s._v(" "),t("ul",[t("li",[s._v("base.less 定义基础样式变量，但不直接使用")]),s._v(" "),t("li",[s._v("theme.less 定义各主题实际使用变量，变量命名需统一，业务代码(有需要的话)使用主题变量，便于切换主题")])]),s._v(" "),t("p",[s._v("但目前工期紧张，加上业务写样式情况较少，有很多样式还是写死的，后续若有时间再优化。")]),s._v(" "),t("h2",{attrs:{id:"css变量用法"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#css变量用法"}},[s._v("#")]),s._v(" CSS变量用法")]),s._v(" "),t("p",[s._v("以下只列出常用用法，若想更深入学习，请看 "),t("a",{attrs:{href:"https://developer.mozilla.org/zh-CN/docs/Web/CSS/Using_CSS_custom_properties",target:"_blank",rel:"noopener noreferrer"}},[s._v("MDN-使用 CSS 自定义属性（变量）"),t("OutboundLink")],1)]),s._v(" "),t("h3",{attrs:{id:"定义"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#定义"}},[s._v("#")]),s._v(" 定义")]),s._v(" "),t("ul",[t("li",[s._v("定义变量以 "),t("code",[s._v("--")]),s._v(" 开头，后接变量名，如："),t("code",[s._v("--color: blue;")])]),s._v(" "),t("li",[s._v("在样式文件内，变量需包裹在选择器内，若想在全局生效，需包裹在 根伪类 "),t("code",[s._v(":root")]),s._v(" 内，如："),t("div",{staticClass:"language-less extra-class"},[t("pre",{pre:!0,attrs:{class:"language-less"}},[t("code",[t("span",{pre:!0,attrs:{class:"token selector"}},[s._v(":root")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("{")]),s._v("\n  "),t("span",{pre:!0,attrs:{class:"token property"}},[s._v("--color")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(":")]),s._v(" blue"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(";")]),s._v("\n"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("}")]),s._v("\n"),t("span",{pre:!0,attrs:{class:"token selector"}},[s._v("body")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("{")]),s._v("\n  "),t("span",{pre:!0,attrs:{class:"token property"}},[s._v("--color")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(":")]),s._v(" blue"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(";")]),s._v("\n"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("}")]),s._v("\n")])])])])]),s._v(" "),t("h3",{attrs:{id:"取值"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#取值"}},[s._v("#")]),s._v(" 取值")]),s._v(" "),t("p",[s._v("使用时使用 "),t("code",[s._v("var()")]),s._v(" 取值，支持设置默认值，如：")]),s._v(" "),t("div",{staticClass:"language-less extra-class"},[t("pre",{pre:!0,attrs:{class:"language-less"}},[t("code",[t("span",{pre:!0,attrs:{class:"token selector"}},[s._v(":root")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("{")]),s._v("\n  "),t("span",{pre:!0,attrs:{class:"token property"}},[s._v("--color")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(":")]),s._v(" blue"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(";")]),s._v("\n"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("}")]),s._v("\n"),t("span",{pre:!0,attrs:{class:"token selector"}},[s._v("body")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("{")]),s._v("\n  "),t("span",{pre:!0,attrs:{class:"token property"}},[s._v("color")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(":")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token function"}},[s._v("var")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("(")]),t("span",{pre:!0,attrs:{class:"token operator"}},[s._v("-")]),t("span",{pre:!0,attrs:{class:"token operator"}},[s._v("-")]),s._v("color"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(")")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(";")]),s._v("\n  "),t("span",{pre:!0,attrs:{class:"token property"}},[s._v("font-size")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(":")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token function"}},[s._v("var")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("(")]),t("span",{pre:!0,attrs:{class:"token operator"}},[s._v("-")]),t("span",{pre:!0,attrs:{class:"token operator"}},[s._v("-")]),s._v("font"),t("span",{pre:!0,attrs:{class:"token operator"}},[s._v("-")]),s._v("size"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(",")]),s._v(" 14px"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(")")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(";")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token comment"}},[s._v("// 第二个参数为默认值")]),s._v("\n"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("}")]),s._v("\n")])])]),t("h3",{attrs:{id:"优先级"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#优先级"}},[s._v("#")]),s._v(" 优先级")]),s._v(" "),t("p",[s._v("当全局配置了一个 CSS变量，DOM对应的选择器也配置了同样变量名的变量，以选择器为准，若多个选择器都配置了，则和 css 优先级一样处理")]),s._v(" "),t("div",{staticClass:"language-less extra-class"},[t("pre",{pre:!0,attrs:{class:"language-less"}},[t("code",[t("span",{pre:!0,attrs:{class:"token selector"}},[s._v(":root")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("{")]),s._v("\n  "),t("span",{pre:!0,attrs:{class:"token property"}},[s._v("--color")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(":")]),s._v(" blue"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(";")]),s._v("\n"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("}")]),s._v("\n"),t("span",{pre:!0,attrs:{class:"token selector"}},[s._v("body")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("{")]),s._v("\n  "),t("span",{pre:!0,attrs:{class:"token property"}},[s._v("--color")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(":")]),s._v(" #333"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(";")]),s._v("\n  "),t("span",{pre:!0,attrs:{class:"token property"}},[s._v("color")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(":")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token function"}},[s._v("var")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("(")]),t("span",{pre:!0,attrs:{class:"token operator"}},[s._v("-")]),t("span",{pre:!0,attrs:{class:"token operator"}},[s._v("-")]),s._v("color"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(")")]),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v(";")]),s._v(" "),t("span",{pre:!0,attrs:{class:"token comment"}},[s._v("// 使用的是 #333")]),s._v("\n"),t("span",{pre:!0,attrs:{class:"token punctuation"}},[s._v("}")]),s._v("\n")])])]),t("p",[s._v("由于此特性，切换主题变得十分容易，只需在 "),t("code",[s._v(":root")]),s._v(" 配置基础变量，主题类选择器内配置相应主题变量，在切换主题时更改 body 的样式类即可。这也是原则上禁止业务直接使用基础变量(起码字体大小、颜色、边距别直接使用)的原因，如果将来要支持多套样式，直接使用基础变量或写死就会有一大推坑。")])])}),[],!1,null,null,null);t.default=n.exports}}]);