<template>
  <div>
    <van-uploader
      class="uploader"
      ref="uploader"
      v-model="fileList"
      accept="image/*"
      :capture="capture"
      :before-read="beforeRead"
      :after-read="afterRead"
      @oversize="onOversize"
    >
    </van-uploader>
    <div v-if="showEditorModal" class="modal-overlay">
      <div class="modal-content">
        <img-editor :img-src="imgData" @cancel="showEditorModal = false" @confirm="handleConfirm" />
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import config from '@lambo-design-mobile/shared/config/config'
import imgEditor from '@/components/uploader/src/components/img-editor.vue'
import Compressor from 'compressorjs'

export default {
  name: 'uploader',
  props: {
    maxSize: {
      type: [Number, String, Function], // number | string | (file: File) => boolean
    },
    maxWidth: {
      type: [Number],
    },
    maxHeight: {
      type: [Number],
    },
  },
  components: {
    imgEditor,
  },
  data() {
    return {
      fileList: [],
      capture: undefined,
      imgData: '',
      showEditorModal: false,
    }
  },
  methods: {
    beforeRead(file) {
      if (!['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'].includes(file.type)) {
        Toast('请上传图片格式文件')
        this.reject()
        return false
      }
      return true
    },
    afterRead(file) {
      // 使用 Compressor.js 压缩图片
      let self = this
      console.log('config', config.ImageCompression)
      // 是否压缩
      let enable = !!(
        self.maxWidth ||
        self.maxHeight ||
        config.ImageCompression?.maxWidth ||
        config.ImageCompression?.maxHeight
      )
      console.log('enable', enable)
      if (enable) {
        new Compressor(file.file, {
          quality: 0.8, // 压缩质量（0 ~ 1）该参数只对 jpeg、webp有效
          maxWidth: self.maxWidth || config.ImageCompression?.maxWidth, // 最大宽度
          maxHeight: self.maxHeight || config.ImageCompression?.maxHeight, // 最大高度
          success: (result) => {
            // result 是一个压缩后的 Blob 对象
            const reader = new FileReader()
            reader.onload = (e) => {
              self.imgData = e.target.result.split(',')[1]
              self.showEditorModal = true
            }
            reader.readAsDataURL(result)
          },
          error(err) {
            Toast('图片压缩失败')
            console.error(err)
          },
        })
      } else {
        const reader = new FileReader()
        reader.onload = (e) => {
          // 去掉 data:image/xxx;base64, 前缀，只保留 base64 字符串
          this.imgData = e.target.result.split(',')[1]
          this.showEditorModal = true
        }
        reader.readAsDataURL(file.file)
      }
      // this.resolve(file.file)
    },
    handleConfirm(rotatedBase64) {
      this.showEditorModal = false

      // 将 base64 转为 Blob 并生成 File 对象
      const byteString = atob(rotatedBase64)
      const ab = new ArrayBuffer(byteString.length)
      const ia = new Uint8Array(ab)

      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i)
      }

      const blob = new Blob([ab], { type: 'image/jpeg' })
      const fileName = 'rotated-image.jpg'
      const file = new File([blob], fileName, { type: 'image/jpeg', lastModified: Date.now() })

      this.resolve(file)
    },
    onOversize() {
      let size
      let sizeStr
      if (isNaN(this.maxSize)) {
        if (typeof this.maxSize === 'string') {
          sizeStr = this.maxSize
        } else {
          sizeStr = '限制大小'
        }
      } else if (this.maxSize % 1024 === 0) {
        size = this.maxSize / 1024
        if (size % 1024 === 0) {
          size = size / 1024
          sizeStr = size + 'MB'
        } else {
          sizeStr = size + 'KB'
        }
      } else {
        sizeStr = size + 'B'
      }
      Toast('图片大小不能超过' + sizeStr)
    },

    //exposed api
    chooseFile(sourceType) {
      return new Promise((resolve, reject) => {
        this.resolve = resolve
        this.reject = reject
        if (sourceType === 'camera') {
          this.capture = 'camera'
        } else {
          this.capture = undefined
        }
        this.$refs.uploader.chooseFile()
      })
    },
  },
}
</script>

<style scoped lang="less">
.uploader {
  display: none;
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: auto;
}
</style>
