<template>
  <div class="img-editor">
    <img ref="dist" class="im-img-editor-img" :src="'data:image/gif;base64,' + imgData" />
    <img ref="src" class="im-img-editor-src-img" :src="'data:image/gif;base64,' + imgData" />
    <canvas v-if="refresh" ref="imgEditorCanvas" class="im-img-editor-canvas"></canvas>
    <!--    <div class="im-help">请通过旋转，确保图片中烟包顶部朝上</div>-->
    <div class="im-row">
      <div class="im-button im-left" @click="cancel">取消</div>
      <div class="im-button im-left" @click="rotate(-1)">向左旋转</div>
      <div class="im-button im-right" @click="rotate(1)">向右旋转</div>
      <div class="im-button im-left" @click="confirm">确认</div>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
export default {
  data() {
    return {
      imgData: '',
      refresh: true,
      rotaing: false, // 不能连续旋转，要等第一次有结果
    }
  },
  props: {
    imgSrc: String,
  },
  methods: {
    cancel() {
      this.$emit('cancel')
    },
    confirm() {
      this.$emit('confirm', this.imgData)
    },
    // 图片旋转
    async rotate(direction) {
      if (this.rotaing) {
        return false
      }
      this.rotaing = true
      // const toast = Toast.loading({
      //   duration: 0, // 持续展示 toast
      //   forbidClick: true,
      //   message: '处理中',
      //   getContainer: 'body',
      // })
      console.log('do rotate, direction is ', direction)
      setTimeout(async () => {
        // canvas重新渲染
        this.refresh = false
        await this.$nextTick()
        this.refresh = true
        await this.$nextTick()

        this.source = this.$refs.src
        this.canvas = this.$refs.imgEditorCanvas
        this.context = this.canvas.getContext('2d')

        const height = this.source.clientHeight
        const width = this.source.clientWidth
        this.canvas.height = width
        this.canvas.width = height
        this.context.clearRect(0, 0, width, height)

        if (direction == 1) {
          this.context.translate(height, 0)
        } else if (direction == -1) {
          this.context.translate(0, width)
        }
        this.context.rotate((direction * 90 * Math.PI) / 180)
        this.context.drawImage(this.source, 0, 0, width, height)
        this.imgData = this.canvas
          .toDataURL('image/jpeg', 1)
          .replace(/data:image\/(.*);base64,(\s)?/g, '')
        // console.log(this.imgData)
        await this.$nextTick()
        Toast.clear()
        this.rotaing = false
      }, 100)
    },
    drawCanvas() {
      this.source = this.$refs.src
      this.canvas = this.$refs.imgEditorCanvas
      this.context = this.canvas.getContext('2d')

      const height = this.source.clientHeight
      const width = this.source.clientWidth
      this.canvas.height = height
      this.canvas.width = width
      this.context.drawImage(this.source, 0, 0, width, height)
    },
  },
  created() {
    this.imgData = this.imgSrc
  },
  mounted() {
    this.drawCanvas()
  },
  watch: {
    imgSrc() {
      this.imgData = this.imgSrc
      this.drawCanvas()
    },
  },
}
</script>

<style lang="less">
.img-editor {
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 99;
  background: #000;
  overflow: hidden;
  .im-img-editor-img {
    z-index: 99;
    width: 100%;
    height: 100vh;
    object-fit: contain;
  }
  .im-row {
    width: 100%;
    position: fixed;
    z-index: 99;
    bottom: 0;
    left: 0;
    height: 50px;
    display: flex;
    justify-content: space-between;
    .im-button {
      margin-top: 0;
      width: 100%;
      height: 100%;
      background-color: #1989fa;
      color: #fff;
      padding: 0;
      line-height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .im-img-editor-canvas,
  .im-img-editor-src-img {
    top: 0;
    left: 0;
    visibility: hidden;
    z-index: -1;
    position: fixed;
  }
  .im-help {
    width: 100vw;
    line-height: 50px;
    position: fixed;
    bottom: 50px;
    left: 0;
    color: #f2f2f2;
    text-align: center;
  }
}
</style>
