export declare enum ModuleNames {
    CommunityCoreModule = "../main",
    InfiniteRowModelModule = "@ag-grid-community/infinite-row-model",
    ClientSideRowModelModule = "@ag-grid-community/client-side-row-model",
    CsvExportModule = "@ag-grid-community/csv-export",
    EnterpriseCoreModule = "@ag-grid-enterprise/core",
    RowGroupingModule = "@ag-grid-enterprise/row-grouping",
    ColumnsToolPanelModule = "@ag-grid-enterprise/column-tool-panel",
    FiltersToolPanelModule = "@ag-grid-enterprise/filter-tool-panel",
    MenuModule = "@ag-grid-enterprise/menu",
    SetFilterModule = "@ag-grid-enterprise/set-filter",
    MultiFilterModule = "@ag-grid-enterprise/multi-filter",
    StatusBarModule = "@ag-grid-enterprise/status-bar",
    SideBarModule = "@ag-grid-enterprise/side-bar",
    RangeSelectionModule = "@ag-grid-enterprise/range-selection",
    MasterDetailModule = "@ag-grid-enterprise/master-detail",
    RichSelectModule = "@ag-grid-enterprise/rich-select",
    GridChartsModule = "@ag-grid-enterprise/charts",
    ViewportRowModelModule = "@ag-grid-enterprise/viewport-row-model",
    ServerSideRowModelModule = "@ag-grid-enterprise/server-side-row-model",
    ExcelExportModule = "@ag-grid-enterprise/excel-export",
    ClipboardModule = "@ag-grid-enterprise/clipboard",
    SparklinesModule = "@ag-grid-enterprise/sparklines",
    AdvancedFilterModule = "@ag-grid-enterprise/advanced-filter",
    AngularModule = "@ag-grid-community/angular",
    ReactModule = "@ag-grid-community/react",
    VueModule = "@ag-grid-community/vue"
}
