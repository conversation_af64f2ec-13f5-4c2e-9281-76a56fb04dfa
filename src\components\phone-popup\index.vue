<template>
  <div class="popup-box">
    <van-popup round v-model="showPopup" position="bottom" @close="closePopup">
      <div class="item">
        <a style="color: black" href="#" @click="openContacts">{{ getPhone }}</a>
      </div>
    </van-popup>
  </div>
</template>
<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    phoneList: {
      type: Array,
    },
  },
  watch: {
    show: {
      deep: true,
      handler() {
        if (this.show) {
          this.showPopup = true
        }
      },
    },
  },
  data() {
    return {
      showPopup: false,
      list: [],
    }
  },
  computed: {
    getPhone() {
      console.log('this.phonelist----------------------', this.phoneList)
      if (this.phoneList && this.phoneList.length > 0) {
        return this.phoneList[0]
      } else {
        return ''
      }
    },
  },
  methods: {
    closePopup() {
      this.$emit('close', this.showPopup)
    },
    openContacts: function () {
      event.preventDefault() // 阻止默认行为

      // 获取设备类型
      const userAgent = navigator.userAgent
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
        // 移动设备
        if (/iPhone|iPad|iPod/i.test(userAgent)) {
          // iOS
          window.location.href = 'tel://' + this.getPhone
        } else if (/Android/i.test(userAgent)) {
          // Android
          window.location.href = 'tel:' + this.getPhone
        } else if (/Windows Phone/i.test(userAgent)) {
          // Windows Phone
          window.location.href = 'tel:' + this.getPhone
        }
      } else {
        console.error('此功能仅在移动设备上可用。')
      }
    },
  },
  mounted() {
    console.log(this.$props.phoneList)
  },
}
</script>
<style lang="less" scoped>
.item {
  text-align: center;
  padding: 25px 0;
  font-size: 16px;
  border-bottom: 1px solid #f3f3f3;
}
</style>
