<template>
  <div class="search-bar" :style="`background: ${bgColor}`">
    <van-search
      class="search"
      v-model="searchContent"
      :show-action="true"
      :placeholder="placeholder"
      :readonly="readonly"
      @search="onSearch"
      @focus="onFocus"
      shape="round"
      maxlength="20"
    >
      <template #action>
        <div class="btn" @click="onSearch">搜索</div>
      </template>
    </van-search>
  </div>
</template>

<script>
export default {
  name: 'SearchBar',
  props: {
    initialValue: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '请输入搜索关键词',
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    bgColor: {
      type: String,
      default: '#FFFFFF',
    },
  },
  data() {
    return {
      searchContent: '',
    }
  },
  watch: {
    initialValue: {
      immediate: true,
      handler(v) {
        if (v) {
          this.searchContent = v
        }
      },
    },
  },
  methods: {
    onSearch() {
      this.$emit('search', this.searchContent)
    },
    onFocus() {
      this.$emit('focus')
    },
  },
}
</script>

<style lang="less" scoped>
.search-bar {
  position: sticky;
  display: flex;
  align-items: center;
  height: 40px;
  box-sizing: border-box;
  z-index: 99;
  overflow: visible;
  /deep/.search {
    height: 30px;
    width: 100vw;
    padding: 0 10px;
    display: flex;
    align-items: center;
    border-radius: 30px;
    background: #f6f6f6;
    .van-icon {
      font-size: 14px;
    }
    .van-search__content {
      padding: 0;
      font-size: 12px;
      background-color: transparent;
    }
    .van-search__action {
      position: relative;
      padding: 0 0 0 12px;
      font-size: 12px;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 13px;
        background: #2d8df0;
        opacity: 0.4;
      }
    }
    .btn {
      color: #2d8df0;
    }
  }
}
</style>
