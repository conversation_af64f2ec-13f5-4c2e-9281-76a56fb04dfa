.ag-icon {
  font-family: var(--ag-icon-font-family);
  font-size: var(--ag-icon-size);
  line-height: var(--ag-icon-size);
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ag-icon-aggregation::before {
  content: var(--ag-icon-font-code-aggregation, "\f101");
}

.ag-icon-arrows::before {
  content: var(--ag-icon-font-code-arrows, "\f102");
}

.ag-icon-asc::before {
  content: var(--ag-icon-font-code-asc, "\f103");
}

.ag-icon-cancel::before {
  content: var(--ag-icon-font-code-cancel, "\f104");
}

.ag-icon-chart::before {
  content: var(--ag-icon-font-code-chart, "\f105");
}

.ag-icon-checkbox-checked::before {
  content: var(--ag-icon-font-code-checkbox-checked, "\f106");
}

.ag-icon-checkbox-indeterminate::before {
  content: var(--ag-icon-font-code-checkbox-indeterminate, "\f107");
}

.ag-icon-checkbox-unchecked::before {
  content: var(--ag-icon-font-code-checkbox-unchecked, "\f108");
}

.ag-icon-color-picker::before {
  content: var(--ag-icon-font-code-color-picker, "\f109");
}

.ag-icon-columns::before {
  content: var(--ag-icon-font-code-columns, "\f10a");
}

.ag-icon-contracted::before {
  content: var(--ag-icon-font-code-contracted, "\f10b");
}

.ag-icon-copy::before {
  content: var(--ag-icon-font-code-copy, "\f10c");
}

.ag-icon-cross::before {
  content: var(--ag-icon-font-code-cross, "\f10d");
}

.ag-icon-csv::before {
  content: var(--ag-icon-font-code-csv, "\f10e");
}

.ag-icon-cut::before {
  content: var(--ag-icon-font-code-cut, "\f10f");
}

.ag-icon-desc::before {
  content: var(--ag-icon-font-code-desc, "\f110");
}

.ag-icon-excel::before {
  content: var(--ag-icon-font-code-excel, "\f111");
}

.ag-icon-expanded::before {
  content: var(--ag-icon-font-code-expanded, "\f112");
}

.ag-icon-eye-slash::before {
  content: var(--ag-icon-font-code-eye-slash, "\f113");
}

.ag-icon-eye::before {
  content: var(--ag-icon-font-code-eye, "\f114");
}

.ag-icon-filter::before {
  content: var(--ag-icon-font-code-filter, "\f115");
}

.ag-icon-first::before {
  content: var(--ag-icon-font-code-first, "\f116");
}

.ag-icon-grip::before {
  content: var(--ag-icon-font-code-grip, "\f117");
}

.ag-icon-group::before {
  content: var(--ag-icon-font-code-group, "\f118");
}

.ag-icon-last::before {
  content: var(--ag-icon-font-code-last, "\f119");
}

.ag-icon-left::before {
  content: var(--ag-icon-font-code-left, "\f11a");
}

.ag-icon-linked::before {
  content: var(--ag-icon-font-code-linked, "\f11b");
}

.ag-icon-loading::before {
  content: var(--ag-icon-font-code-loading, "\f11c");
}

.ag-icon-maximize::before {
  content: var(--ag-icon-font-code-maximize, "\f11d");
}

.ag-icon-menu::before {
  content: var(--ag-icon-font-code-menu, "\f11e");
}

.ag-icon-minimize::before {
  content: var(--ag-icon-font-code-minimize, "\f11f");
}

.ag-icon-next::before {
  content: var(--ag-icon-font-code-next, "\f120");
}

.ag-icon-none::before {
  content: var(--ag-icon-font-code-none, "\f121");
}

.ag-icon-not-allowed::before {
  content: var(--ag-icon-font-code-not-allowed, "\f122");
}

.ag-icon-paste::before {
  content: var(--ag-icon-font-code-paste, "\f123");
}

.ag-icon-pin::before {
  content: var(--ag-icon-font-code-pin, "\f124");
}

.ag-icon-pivot::before {
  content: var(--ag-icon-font-code-pivot, "\f125");
}

.ag-icon-previous::before {
  content: var(--ag-icon-font-code-previous, "\f126");
}

.ag-icon-radio-button-off::before {
  content: var(--ag-icon-font-code-radio-button-off, "\f127");
}

.ag-icon-radio-button-on::before {
  content: var(--ag-icon-font-code-radio-button-on, "\f128");
}

.ag-icon-right::before {
  content: var(--ag-icon-font-code-right, "\f129");
}

.ag-icon-save::before {
  content: var(--ag-icon-font-code-save, "\f12a");
}

.ag-icon-small-down::before {
  content: var(--ag-icon-font-code-small-down, "\f12b");
}

.ag-icon-small-left::before {
  content: var(--ag-icon-font-code-small-left, "\f12c");
}

.ag-icon-small-right::before {
  content: var(--ag-icon-font-code-small-right, "\f12d");
}

.ag-icon-small-up::before {
  content: var(--ag-icon-font-code-small-up, "\f12e");
}

.ag-icon-tick::before {
  content: var(--ag-icon-font-code-tick, "\f12f");
}

.ag-icon-tree-closed::before {
  content: var(--ag-icon-font-code-tree-closed, "\f130");
}

.ag-icon-tree-indeterminate::before {
  content: var(--ag-icon-font-code-tree-indeterminate, "\f131");
}

.ag-icon-tree-open::before {
  content: var(--ag-icon-font-code-tree-open, "\f132");
}

.ag-icon-unlinked::before {
  content: var(--ag-icon-font-code-unlinked, "\f133");
}

.ag-icon-up::before {
  content: var(--ag-icon-font-code-up, "\f134");
}

.ag-icon-down::before {
  content: var(--ag-icon-font-code-down, "\f135");
}

.ag-icon-plus::before {
  content: var(--ag-icon-font-code-plus, "\f136");
}

.ag-icon-minus::before {
  content: var(--ag-icon-font-code-minus, "\f137");
}

.ag-icon-row-drag::before {
  content: var(--ag-icon-font-code-grip);
}

.ag-left-arrow::before {
  content: var(--ag-icon-font-code-left);
}

.ag-right-arrow::before {
  content: var(--ag-icon-font-code-right);
}

[class*=ag-theme-] {
  --ag-foreground-color: #000;
  --ag-data-color: var(--ag-foreground-color);
  --ag-secondary-foreground-color: var(--ag-foreground-color);
  --ag-header-foreground-color: var(--ag-secondary-foreground-color);
  --ag-disabled-foreground-color: rgba(0, 0, 0, 0.5);
  --ag-background-color: #fff;
  --ag-header-background-color: transparent;
  --ag-tooltip-background-color: transparent;
  --ag-subheader-background-color: transparent;
  --ag-subheader-toolbar-background-color: transparent;
  --ag-control-panel-background-color: transparent;
  --ag-side-button-selected-background-color: var(--ag-control-panel-background-color);
  --ag-selected-row-background-color: #BBB;
  --ag-odd-row-background-color: var(--ag-background-color);
  --ag-modal-overlay-background-color: rgba(255, 255, 255, 0.66);
  --ag-row-hover-color: transparent;
  --ag-column-hover-color: transparent;
  --ag-range-selection-border-color: var(--ag-foreground-color);
  --ag-range-selection-border-style: solid;
  --ag-range-selection-background-color: rgba(0, 0, 0, 0.2);
  --ag-range-selection-background-color-2: var(--ag-range-selection-background-color);
  --ag-range-selection-background-color-3: var(--ag-range-selection-background-color);
  --ag-range-selection-background-color-4: var(--ag-range-selection-background-color);
  --ag-range-selection-highlight-color: var(--ag-range-selection-border-color);
  --ag-selected-tab-underline-color: var(--ag-range-selection-border-color);
  --ag-selected-tab-underline-width: 0;
  --ag-selected-tab-underline-transition-speed: 0s;
  --ag-range-selection-chart-category-background-color: rgba(0, 255, 132, 0.1);
  --ag-range-selection-chart-background-color: rgba(0, 88, 255, 0.1);
  --ag-header-cell-hover-background-color: transparent;
  --ag-header-cell-moving-background-color: var(--ag-background-color);
  --ag-value-change-value-highlight-background-color: rgba(22, 160, 133, 0.5);
  --ag-value-change-delta-up-color: #43a047;
  --ag-value-change-delta-down-color: #e53935;
  --ag-chip-background-color: transparent;
  --ag-borders: solid 1px;
  --ag-border-color: rgba(0, 0, 0, 0.25);
  --ag-borders-critical: var(--ag-borders);
  --ag-borders-secondary: var(--ag-borders);
  --ag-secondary-border-color: var(--ag-border-color);
  --ag-row-border-style: solid;
  --ag-row-border-color: var(--ag-secondary-border-color);
  --ag-row-border-width: 1px;
  --ag-cell-horizontal-border: solid transparent;
  --ag-borders-input: var(--ag-borders-secondary);
  --ag-input-border-color: var(--ag-secondary-border-color);
  --ag-borders-input-invalid: solid 2px;
  --ag-input-border-color-invalid: var(--ag-invalid-color);
  --ag-borders-side-button: var(--ag-borders);
  --ag-border-radius: 0px;
  --ag-row-border-color: var(--ag-secondary-border-color);
  --ag-header-column-separator-display: none;
  --ag-header-column-separator-height: 100%;
  --ag-header-column-separator-width: 1px;
  --ag-header-column-separator-color: var(--ag-secondary-border-color);
  --ag-header-column-resize-handle-display: none;
  --ag-header-column-resize-handle-height: 50%;
  --ag-header-column-resize-handle-width: 1px;
  --ag-header-column-resize-handle-color: var(--ag-secondary-border-color);
  --ag-invalid-color: red;
  --ag-input-disabled-border-color: var(--ag-input-border-color);
  --ag-input-disabled-background-color: transparent;
  --ag-checkbox-background-color: transparent;
  --ag-checkbox-border-radius: var(--ag-border-radius);
  --ag-checkbox-checked-color: var(--ag-foreground-color);
  --ag-checkbox-unchecked-color: var(--ag-foreground-color);
  --ag-checkbox-indeterminate-color: var(--ag-checkbox-unchecked-color);
  --ag-toggle-button-off-border-color: var(--ag-checkbox-unchecked-color);
  --ag-toggle-button-off-background-color: var(--ag-checkbox-unchecked-color);
  --ag-toggle-button-on-border-color: var(--ag-checkbox-checked-color);
  --ag-toggle-button-on-background-color: var(--ag-checkbox-checked-color);
  --ag-toggle-button-switch-background-color: var(--ag-background-color);
  --ag-toggle-button-switch-border-color: var(--ag-toggle-button-off-border-color);
  --ag-toggle-button-border-width: 1px;
  --ag-toggle-button-height: var(--ag-icon-size);
  --ag-toggle-button-width: calc(var(--ag-toggle-button-height) * 2);
  --ag-input-focus-box-shadow: none;
  --ag-input-focus-border-color: none;
  --ag-minichart-selected-chart-color: var(--ag-checkbox-checked-color);
  --ag-minichart-selected-page-color: var(--ag-checkbox-checked-color);
  --ag-grid-size: 4px;
  --ag-icon-size: 12px;
  --ag-widget-container-horizontal-padding: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-container-vertical-padding: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-horizontal-spacing: calc(var(--ag-grid-size) * 2);
  --ag-widget-vertical-spacing: var(--ag-grid-size);
  --ag-cell-horizontal-padding: calc(var(--ag-grid-size) * 3);
  --ag-cell-widget-spacing: var(--ag-cell-horizontal-padding);
  --ag-row-height: calc(var(--ag-grid-size) * 6 + 1px);
  --ag-header-height: var(--ag-row-height);
  --ag-list-item-height: calc(var(--ag-grid-size) * 5);
  --ag-column-select-indent-size: calc(var(--ag-grid-size) + var(--ag-icon-size));
  --ag-set-filter-indent-size: calc(var(--ag-grid-size) + var(--ag-icon-size));
  --ag-advanced-filter-builder-indent-size: calc(var(--ag-grid-size) * 2 + var(--ag-icon-size));
  --ag-row-group-indent-size: calc(var(--ag-cell-widget-spacing) + var(--ag-icon-size));
  --ag-filter-tool-panel-group-indent: 16px;
  --ag-tab-min-width: 220px;
  --ag-menu-min-width: 181px;
  --ag-side-bar-panel-width: 200px;
  --ag-font-family: "Helvetica Neue", sans-serif;
  --ag-font-size: 14px;
  --ag-card-radius: var(--ag-border-radius);
  --ag-card-shadow: none;
  --ag-popup-shadow: 5px 5px 10px rgba(0, 0, 0, 0.3);
  --ag-advanced-filter-join-pill-color: #f08e8d;
  --ag-advanced-filter-column-pill-color: #a6e194;
  --ag-advanced-filter-option-pill-color: #f3c08b;
  --ag-advanced-filter-value-pill-color: #85c0e4;
}

.ag-root-wrapper, .ag-sticky-top, .ag-dnd-ghost {
  background-color: var(--ag-background-color);
}

[class*=ag-theme-] {
  -webkit-font-smoothing: antialiased;
  font-family: var(--ag-font-family);
  font-size: var(--ag-font-size);
  line-height: normal;
  color: var(--ag-foreground-color);
}

ag-grid, ag-grid-angular, ag-grid-ng2, ag-grid-polymer, ag-grid-aurelia {
  display: block;
}

.ag-hidden {
  display: none !important;
}

.ag-invisible {
  visibility: hidden !important;
}

.ag-no-transition {
  transition: none !important;
}

.ag-drag-handle {
  cursor: grab;
}

.ag-column-drop-wrapper {
  display: flex;
}

.ag-column-drop-horizontal-half-width {
  display: inline-block;
  width: 50% !important;
}

.ag-unselectable {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-selectable {
  -moz-user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.ag-tab {
  position: relative;
}

.ag-tab-guard {
  position: absolute;
  width: 0;
  height: 0;
  display: block;
}

.ag-select-agg-func-popup {
  position: absolute;
}

.ag-input-wrapper, .ag-picker-field-wrapper {
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  line-height: normal;
  position: relative;
}

.ag-shake-left-to-right {
  animation-direction: alternate;
  animation-duration: 0.2s;
  animation-iteration-count: infinite;
  animation-name: ag-shake-left-to-right;
}

@keyframes ag-shake-left-to-right {
  from {
    padding-left: 6px;
    padding-right: 2px;
  }
  to {
    padding-left: 2px;
    padding-right: 6px;
  }
}
.ag-root-wrapper {
  cursor: default;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.ag-root-wrapper.ag-layout-normal {
  height: 100%;
}

.ag-watermark {
  position: absolute;
  bottom: 20px;
  right: 25px;
  opacity: 0.5;
  transition: opacity 1s ease-out 3s;
}
.ag-watermark::before {
  content: "";
  background-image: url(data:image/svg+xml;base64,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);
  background-repeat: no-repeat;
  background-size: 170px 40px;
  display: block;
  height: 40px;
  width: 170px;
  opacity: 0.5;
}

.ag-watermark-text {
  opacity: 0.5;
  font-weight: bold;
  font-family: Impact, sans-serif;
  font-size: 19px;
  padding-left: 0.7rem;
}

.ag-root-wrapper-body {
  display: flex;
  flex-direction: row;
}
.ag-root-wrapper-body.ag-layout-normal {
  flex: 1 1 auto;
  height: 0;
  min-height: 0;
}

.ag-root {
  position: relative;
  display: flex;
  flex-direction: column;
}
.ag-root.ag-layout-normal, .ag-root.ag-layout-auto-height {
  overflow: hidden;
  flex: 1 1 auto;
  width: 0;
}
.ag-root.ag-layout-normal {
  height: 100%;
}

.ag-header-viewport,
.ag-floating-top-viewport,
.ag-body-viewport,
.ag-center-cols-viewport,
.ag-floating-bottom-viewport,
.ag-body-horizontal-scroll-viewport,
.ag-body-vertical-scroll-viewport,
.ag-virtual-list-viewport,
.ag-sticky-top-viewport {
  position: relative;
  height: 100%;
  min-width: 0px;
  overflow: hidden;
  flex: 1 1 auto;
}

.ag-body-viewport, .ag-center-cols-viewport {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.ag-body-viewport::-webkit-scrollbar, .ag-center-cols-viewport::-webkit-scrollbar {
  display: none;
}

.ag-body-viewport {
  display: flex;
}
.ag-body-viewport.ag-layout-normal {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.ag-center-cols-viewport {
  min-height: 100%;
  width: 100%;
  overflow-x: auto;
}

.ag-body-horizontal-scroll-viewport {
  overflow-x: scroll;
}

.ag-body-vertical-scroll-viewport {
  overflow-y: scroll;
}

.ag-virtual-list-viewport {
  overflow: auto;
  width: 100%;
}

.ag-header-container,
.ag-floating-top-container,
.ag-body-container,
.ag-pinned-right-cols-container,
.ag-center-cols-container,
.ag-pinned-left-cols-container,
.ag-floating-bottom-container,
.ag-body-horizontal-scroll-container,
.ag-body-vertical-scroll-container,
.ag-full-width-container,
.ag-floating-bottom-full-width-container,
.ag-virtual-list-container,
.ag-sticky-top-container {
  position: relative;
}

.ag-header-container,
.ag-floating-top-container,
.ag-floating-bottom-container,
.ag-sticky-top-container {
  height: 100%;
  white-space: nowrap;
}

.ag-center-cols-container {
  display: block;
}

.ag-pinned-right-cols-container {
  display: block;
}

.ag-body-horizontal-scroll-container {
  height: 100%;
}

.ag-body-vertical-scroll-container {
  width: 100%;
}

.ag-full-width-container,
.ag-floating-top-full-width-container,
.ag-floating-bottom-full-width-container,
.ag-sticky-top-full-width-container {
  position: absolute;
  top: 0px;
  pointer-events: none;
}
.ag-ltr .ag-full-width-container,
.ag-ltr .ag-floating-top-full-width-container,
.ag-ltr .ag-floating-bottom-full-width-container,
.ag-ltr .ag-sticky-top-full-width-container {
  left: 0;
}
.ag-rtl .ag-full-width-container,
.ag-rtl .ag-floating-top-full-width-container,
.ag-rtl .ag-floating-bottom-full-width-container,
.ag-rtl .ag-sticky-top-full-width-container {
  right: 0;
}

.ag-full-width-container {
  width: 100%;
}

.ag-floating-bottom-full-width-container, .ag-floating-top-full-width-container {
  display: inline-block;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.ag-virtual-list-container {
  overflow: hidden;
}

.ag-body {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  flex-direction: row !important;
  min-height: 0;
}

.ag-body-horizontal-scroll,
.ag-body-vertical-scroll {
  min-height: 0;
  min-width: 0;
  display: flex;
  position: relative;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible,
.ag-body-vertical-scroll.ag-scrollbar-invisible {
  position: absolute;
  bottom: 0;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible.ag-apple-scrollbar,
.ag-body-vertical-scroll.ag-scrollbar-invisible.ag-apple-scrollbar {
  opacity: 0;
  transition: opacity 400ms;
  visibility: hidden;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-scrolling, .ag-body-horizontal-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-active,
.ag-body-vertical-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-scrolling,
.ag-body-vertical-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-active {
  visibility: visible;
  opacity: 1;
}

.ag-body-horizontal-scroll {
  width: 100%;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible {
  left: 0;
  right: 0;
}

.ag-body-vertical-scroll {
  height: 100%;
}
.ag-body-vertical-scroll.ag-scrollbar-invisible {
  top: 0;
  z-index: 10;
}
.ag-ltr .ag-body-vertical-scroll.ag-scrollbar-invisible {
  right: 0;
}
.ag-rtl .ag-body-vertical-scroll.ag-scrollbar-invisible {
  left: 0;
}

.ag-force-vertical-scroll {
  overflow-y: scroll !important;
}

.ag-horizontal-left-spacer, .ag-horizontal-right-spacer {
  height: 100%;
  min-width: 0;
  overflow-x: scroll;
}
.ag-horizontal-left-spacer.ag-scroller-corner, .ag-horizontal-right-spacer.ag-scroller-corner {
  overflow-x: hidden;
}

.ag-header, .ag-pinned-left-header, .ag-pinned-right-header {
  display: inline-block;
  overflow: hidden;
  position: relative;
}

.ag-header-cell-sortable .ag-header-cell-label {
  cursor: pointer;
}

.ag-header {
  display: flex;
  width: 100%;
  white-space: nowrap;
}

.ag-pinned-left-header {
  height: 100%;
}

.ag-pinned-right-header {
  height: 100%;
}

.ag-header-row {
  position: absolute;
}

.ag-header-row:not(.ag-header-row-column-group) {
  overflow: hidden;
}

.ag-header.ag-header-allow-overflow .ag-header-row {
  overflow: visible;
}

.ag-header-cell {
  display: inline-flex;
  align-items: center;
  position: absolute;
  height: 100%;
  overflow: hidden;
}

.ag-header-cell.ag-header-active .ag-header-cell-menu-button {
  opacity: 1;
}

.ag-header-cell-menu-button:not(.ag-header-menu-always-show) {
  transition: opacity 0.2s;
  opacity: 0;
}

.ag-header-group-cell-label, .ag-header-cell-label {
  display: flex;
  flex: 1 1 auto;
  overflow: hidden;
  align-items: center;
  text-overflow: ellipsis;
  align-self: stretch;
}

.ag-header-group-cell-label.ag-sticky-label {
  position: sticky;
  flex: none;
  max-width: 100%;
}

.ag-header-cell-text {
  overflow: hidden;
  text-overflow: ellipsis;
}

.ag-header-cell:not(.ag-header-cell-auto-height) .ag-header-cell-comp-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
}

.ag-header-cell-comp-wrapper {
  width: 100%;
  overflow: hidden;
}

.ag-header-cell-wrap-text .ag-header-cell-comp-wrapper {
  white-space: normal;
}

.ag-right-aligned-header .ag-header-cell-label {
  flex-direction: row-reverse;
}

.ag-header-group-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ag-header-cell-resize {
  position: absolute;
  z-index: 2;
  height: 100%;
  width: 8px;
  top: 0;
  cursor: ew-resize;
}
.ag-ltr .ag-header-cell-resize {
  right: -4px;
}
.ag-rtl .ag-header-cell-resize {
  left: -4px;
}

.ag-pinned-left-header .ag-header-cell-resize {
  right: -4px;
}

.ag-pinned-right-header .ag-header-cell-resize {
  left: -4px;
}

.ag-header-select-all {
  display: flex;
}

.ag-column-moving .ag-cell {
  transition: left 0.2s;
}
.ag-column-moving .ag-header-cell {
  transition: left 0.2s;
}
.ag-column-moving .ag-header-group-cell {
  transition: left 0.2s, width 0.2s;
}

.ag-column-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1 1 auto;
}

.ag-column-select {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 3 1 0px;
}

.ag-column-select-header {
  position: relative;
  display: flex;
  flex: none;
}

.ag-column-select-header-icon {
  position: relative;
}

.ag-column-select-header-filter-wrapper {
  flex: 1 1 auto;
}

.ag-column-select-header-filter {
  width: 100%;
}

.ag-column-select-list {
  flex: 1 1 0px;
  overflow: hidden;
}

.ag-column-drop {
  position: relative;
  display: inline-flex;
  align-items: center;
  overflow: auto;
  width: 100%;
}

.ag-column-drop-list {
  display: flex;
  align-items: center;
}

.ag-column-drop-cell {
  position: relative;
  display: flex;
  align-items: center;
}

.ag-column-drop-cell-text {
  overflow: hidden;
  flex: 1 1 auto;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ag-column-drop-vertical {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  align-items: stretch;
  flex: 1 1 0px;
}

.ag-column-drop-vertical-title-bar {
  display: flex;
  align-items: center;
  flex: none;
}

.ag-column-drop-vertical-list {
  position: relative;
  align-items: stretch;
  flex-grow: 1;
  flex-direction: column;
  overflow-x: auto;
}
.ag-column-drop-vertical-list > * {
  flex: none;
}

.ag-column-drop-empty .ag-column-drop-vertical-list {
  overflow: hidden;
}

.ag-column-drop-vertical-empty-message {
  display: block;
}

.ag-column-drop.ag-column-drop-horizontal {
  white-space: nowrap;
  overflow: hidden;
}

.ag-column-drop-cell-button {
  cursor: pointer;
}

.ag-filter-toolpanel {
  flex: 1 1 0px;
  min-width: 0;
}

.ag-filter-toolpanel-header {
  position: relative;
}

.ag-filter-toolpanel-header, .ag-filter-toolpanel-search {
  display: flex;
  align-items: center;
}
.ag-filter-toolpanel-header > *, .ag-filter-toolpanel-search > * {
  display: flex;
  align-items: center;
}

.ag-filter-apply-panel {
  display: flex;
  justify-content: flex-end;
  overflow: hidden;
}

.ag-row-animation .ag-row {
  transition: transform 0.4s, top 0.4s, background-color 0.1s, opacity 0.2s;
}

.ag-row-animation .ag-row.ag-after-created {
  transition: transform 0.4s, top 0.4s, height 0.4s, background-color 0.1s, opacity 0.2s;
}

.ag-row-no-animation .ag-row {
  transition: background-color 0.1s;
}

.ag-row {
  white-space: nowrap;
  width: 100%;
}

.ag-row-loading {
  display: flex;
  align-items: center;
}

.ag-row-position-absolute {
  position: absolute;
}

.ag-row-position-relative {
  position: relative;
}

.ag-full-width-row {
  overflow: hidden;
  pointer-events: all;
}

.ag-row-inline-editing {
  z-index: 1;
}

.ag-row-dragging {
  z-index: 2;
}

.ag-stub-cell {
  display: flex;
  align-items: center;
}

.ag-cell {
  display: inline-block;
  position: absolute;
  white-space: nowrap;
  height: 100%;
}

.ag-cell-value {
  flex: 1 1 auto;
}

.ag-cell-value, .ag-group-value {
  overflow: hidden;
  text-overflow: ellipsis;
}

.ag-cell-wrap-text {
  white-space: normal;
  word-break: break-all;
}

.ag-cell-wrapper {
  display: flex;
  align-items: center;
}
.ag-cell-wrapper.ag-row-group {
  align-items: flex-start;
}

.ag-sparkline-wrapper {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}

.ag-full-width-row .ag-cell-wrapper.ag-row-group {
  height: 100%;
  align-items: center;
}

.ag-cell-inline-editing {
  z-index: 1;
}
.ag-cell-inline-editing .ag-cell-wrapper,
.ag-cell-inline-editing .ag-cell-edit-wrapper,
.ag-cell-inline-editing .ag-cell-editor,
.ag-cell-inline-editing .ag-cell-editor .ag-wrapper,
.ag-cell-inline-editing .ag-cell-editor input {
  height: 100%;
  width: 100%;
  line-height: normal;
}

.ag-cell .ag-icon {
  display: inline-block;
  vertical-align: middle;
}

.ag-set-filter-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.ag-set-filter-item-checkbox {
  display: flex;
  overflow: hidden;
}

.ag-set-filter-group-icons {
  display: block;
}
.ag-set-filter-group-icons > * {
  cursor: pointer;
}

.ag-filter-body-wrapper {
  display: flex;
  flex-direction: column;
}

.ag-filter-filter {
  flex: 1 1 0px;
}

.ag-filter-condition {
  display: flex;
  justify-content: center;
}

.ag-floating-filter-body {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  height: 100%;
}

.ag-floating-filter-full-body {
  display: flex;
  flex: 1 1 auto;
  height: 100%;
  width: 100%;
  align-items: center;
  overflow: hidden;
}

.ag-floating-filter-full-body > div {
  flex: 1 1 auto;
}

.ag-floating-filter-input {
  align-items: center;
  display: flex;
  width: 100%;
}
.ag-floating-filter-input > * {
  flex: 1 1 auto;
}

.ag-floating-filter-button {
  display: flex;
  flex: none;
}

.ag-set-floating-filter-input input[disabled] {
  pointer-events: none;
}

.ag-dnd-ghost {
  position: absolute;
  display: inline-flex;
  align-items: center;
  cursor: move;
  white-space: nowrap;
  z-index: 9999;
}

.ag-overlay {
  height: 100%;
  left: 0;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 2;
}

.ag-overlay-panel {
  display: flex;
  height: 100%;
  width: 100%;
}

.ag-overlay-wrapper {
  display: flex;
  flex: none;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.ag-overlay-loading-wrapper {
  pointer-events: all;
}

.ag-popup-child {
  z-index: 5;
  top: 0;
}

.ag-popup-editor {
  position: absolute;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-large-text-input {
  display: block;
}

.ag-virtual-list-item {
  position: absolute;
  width: 100%;
}

.ag-floating-top {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  position: relative;
  display: flex;
}

.ag-pinned-left-floating-top {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-pinned-right-floating-top {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-floating-bottom {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  position: relative;
  display: flex;
}

.ag-pinned-left-floating-bottom {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-pinned-right-floating-bottom {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-sticky-top {
  position: absolute;
  display: flex;
  width: 100%;
}

.ag-pinned-left-sticky-top,
.ag-pinned-right-sticky-top {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.ag-sticky-top-full-width-container {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.ag-dialog, .ag-panel {
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.ag-panel-title-bar {
  display: flex;
  flex: none;
  align-items: center;
  cursor: default;
}

.ag-panel-title-bar-title {
  flex: 1 1 auto;
}

.ag-panel-title-bar-buttons {
  display: flex;
}

.ag-panel-title-bar-button {
  cursor: pointer;
}

.ag-panel-content-wrapper {
  display: flex;
  flex: 1 1 auto;
  position: relative;
  overflow: hidden;
}

.ag-dialog {
  position: absolute;
}

.ag-resizer {
  position: absolute;
  pointer-events: none;
  z-index: 1;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.ag-resizer.ag-resizer-topLeft {
  top: 0;
  left: 0;
  height: 5px;
  width: 5px;
  cursor: nwse-resize;
}
.ag-resizer.ag-resizer-top {
  top: 0;
  left: 5px;
  right: 5px;
  height: 5px;
  cursor: ns-resize;
}
.ag-resizer.ag-resizer-topRight {
  top: 0;
  right: 0;
  height: 5px;
  width: 5px;
  cursor: nesw-resize;
}
.ag-resizer.ag-resizer-right {
  top: 5px;
  right: 0;
  bottom: 5px;
  width: 5px;
  cursor: ew-resize;
}
.ag-resizer.ag-resizer-bottomRight {
  bottom: 0;
  right: 0;
  height: 5px;
  width: 5px;
  cursor: nwse-resize;
}
.ag-resizer.ag-resizer-bottom {
  bottom: 0;
  left: 5px;
  right: 5px;
  height: 5px;
  cursor: ns-resize;
}
.ag-resizer.ag-resizer-bottomLeft {
  bottom: 0;
  left: 0;
  height: 5px;
  width: 5px;
  cursor: nesw-resize;
}
.ag-resizer.ag-resizer-left {
  left: 0;
  top: 5px;
  bottom: 5px;
  width: 5px;
  cursor: ew-resize;
}

.ag-tooltip {
  position: absolute;
  z-index: 99999;
}

.ag-tooltip-custom {
  position: absolute;
  z-index: 99999;
}

.ag-tooltip:not(.ag-tooltip-interactive),
.ag-tooltip-custom:not(.ag-tooltip-interactive) {
  pointer-events: none;
}

.ag-value-slide-out {
  margin-right: 5px;
  opacity: 1;
  transition: opacity 3s, margin-right 3s;
  transition-timing-function: linear;
}

.ag-value-slide-out-end {
  margin-right: 10px;
  opacity: 0;
}

.ag-opacity-zero {
  opacity: 0 !important;
}

.ag-menu {
  max-height: 100%;
  overflow-y: auto;
  position: absolute;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-menu-column-select-wrapper {
  height: 265px;
  overflow: auto;
}
.ag-menu-column-select-wrapper .ag-column-select {
  height: 100%;
}

.ag-menu-list {
  display: table;
  width: 100%;
}

.ag-menu-option, .ag-menu-separator {
  display: table-row;
}

.ag-menu-option-part, .ag-menu-separator-part {
  display: table-cell;
  vertical-align: middle;
}

.ag-menu-option-text {
  white-space: nowrap;
}

.ag-compact-menu-option {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
}

.ag-compact-menu-option-text {
  white-space: nowrap;
  flex: 1 1 auto;
}

.ag-rich-select {
  cursor: default;
  outline: none;
  height: 100%;
}

.ag-rich-select-value {
  display: flex;
  align-items: center;
  height: 100%;
}
.ag-rich-select-value .ag-picker-field-display {
  overflow: hidden;
  text-overflow: ellipsis;
}
.ag-rich-select-value .ag-picker-field-display.ag-display-as-placeholder {
  opacity: 0.5;
}

.ag-rich-select-list {
  position: relative;
}
.ag-rich-select-list .ag-loading-text {
  min-height: 2rem;
}

.ag-rich-select-row {
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  height: 100%;
}

.ag-rich-select-field-input {
  flex: 1 1 auto;
}
.ag-rich-select-field-input .ag-input-field-input {
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  text-overflow: ellipsis;
}
.ag-rich-select-field-input .ag-input-field-input::placeholder {
  opacity: 0.8;
}

.ag-autocomplete {
  align-items: center;
  display: flex;
}
.ag-autocomplete > * {
  flex: 1 1 auto;
}

.ag-autocomplete-list-popup {
  position: absolute;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-autocomplete-list {
  position: relative;
}

.ag-autocomplete-virtual-list-item {
  display: flex;
}

.ag-autocomplete-row {
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  overflow: hidden;
}

.ag-autocomplete-row-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ag-paging-panel {
  align-items: center;
  display: flex;
  justify-content: flex-end;
}

.ag-paging-page-summary-panel {
  display: flex;
  align-items: center;
}

.ag-paging-button {
  position: relative;
}

.ag-disabled .ag-paging-page-summary-panel {
  pointer-events: none;
}

.ag-tool-panel-wrapper {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  cursor: default;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-column-select-column,
.ag-column-select-column-group,
.ag-select-agg-func-item {
  position: relative;
  align-items: center;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  height: 100%;
}
.ag-column-select-column > *,
.ag-column-select-column-group > *,
.ag-select-agg-func-item > * {
  flex: none;
}

.ag-select-agg-func-item,
.ag-column-select-column-label {
  flex: 1 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ag-column-select-checkbox {
  display: flex;
}

.ag-tool-panel-horizontal-resize {
  cursor: ew-resize;
  height: 100%;
  position: absolute;
  top: 0;
  width: 5px;
  z-index: 1;
}

.ag-ltr .ag-side-bar-left .ag-tool-panel-horizontal-resize {
  right: -3px;
}
.ag-rtl .ag-side-bar-left .ag-tool-panel-horizontal-resize {
  left: -3px;
}

.ag-ltr .ag-side-bar-right .ag-tool-panel-horizontal-resize {
  left: -3px;
}
.ag-rtl .ag-side-bar-right .ag-tool-panel-horizontal-resize {
  right: -3px;
}

.ag-details-row {
  width: 100%;
}

.ag-details-row-fixed-height {
  height: 100%;
}

.ag-details-grid {
  width: 100%;
}

.ag-details-grid-fixed-height {
  height: 100%;
}

.ag-header-group-cell {
  display: flex;
  align-items: center;
  height: 100%;
  position: absolute;
}

.ag-header-group-cell-no-group.ag-header-span-height .ag-header-cell-resize {
  display: none;
}

.ag-cell-label-container {
  display: flex;
  justify-content: space-between;
  flex-direction: row-reverse;
  align-items: center;
  height: 100%;
  width: 100%;
  overflow: hidden;
  padding: 5px 0px;
}

.ag-right-aligned-header .ag-cell-label-container {
  flex-direction: row;
}
.ag-right-aligned-header .ag-header-cell-text {
  text-align: end;
}

.ag-side-bar {
  display: flex;
  flex-direction: row-reverse;
}

.ag-side-bar-left {
  order: -1;
  flex-direction: row;
}

.ag-side-button-button {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  white-space: nowrap;
  outline: none;
  cursor: pointer;
}

.ag-side-button-label {
  writing-mode: vertical-lr;
}

.ag-status-bar {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
}

.ag-status-panel {
  display: inline-flex;
}

.ag-status-name-value {
  white-space: nowrap;
}

.ag-status-bar-left {
  display: inline-flex;
}

.ag-status-bar-center {
  display: inline-flex;
}

.ag-status-bar-right {
  display: inline-flex;
}

.ag-icon {
  display: block;
  speak: none;
}

.ag-group {
  position: relative;
  width: 100%;
}

.ag-group-title-bar {
  display: flex;
  align-items: center;
}

.ag-group-title {
  display: block;
  flex: 1 1 auto;
  min-width: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ag-group-title-bar .ag-group-title {
  cursor: default;
}

.ag-group-toolbar {
  display: flex;
  align-items: center;
}

.ag-group-container {
  display: flex;
}

.ag-disabled .ag-group-container {
  pointer-events: none;
}

.ag-group-container-horizontal {
  flex-direction: row;
  flex-wrap: wrap;
}

.ag-group-container-vertical {
  flex-direction: column;
}

.ag-column-group-icons {
  display: block;
}
.ag-column-group-icons > * {
  cursor: pointer;
}

.ag-group-item-alignment-stretch .ag-group-item {
  align-items: stretch;
}

.ag-group-item-alignment-start .ag-group-item {
  align-items: flex-start;
}

.ag-group-item-alignment-end .ag-group-item {
  align-items: flex-end;
}

.ag-toggle-button-icon {
  transition: right 0.3s;
  position: absolute;
  top: -1px;
}

.ag-input-field, .ag-select {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.ag-input-field-input {
  flex: 1 1 auto;
}

.ag-floating-filter-input .ag-input-field-input[type=date] {
  width: 1px;
}

.ag-range-field {
  display: flex;
  align-items: center;
}

.ag-angle-select {
  display: flex;
  align-items: center;
}

.ag-angle-select-wrapper {
  display: flex;
}

.ag-angle-select-parent-circle {
  display: block;
  position: relative;
}

.ag-angle-select-child-circle {
  position: absolute;
}

.ag-slider-wrapper {
  display: flex;
}
.ag-slider-wrapper .ag-input-field {
  flex: 1 1 auto;
}

.ag-picker-field-display {
  flex: 1 1 auto;
}

.ag-picker-field {
  display: flex;
  align-items: center;
}

.ag-picker-field-icon {
  display: flex;
  border: 0;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.ag-picker-field-wrapper {
  overflow: hidden;
}

.ag-label-align-right .ag-label {
  order: 1;
}
.ag-label-align-right > * {
  flex: none;
}

.ag-label-align-top {
  flex-direction: column;
  align-items: flex-start;
}
.ag-label-align-top > * {
  align-self: stretch;
}

.ag-label-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.ag-color-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
  text-align: center;
}

.ag-spectrum-color {
  flex: 1 1 auto;
  position: relative;
  overflow: hidden;
  cursor: default;
}

.ag-spectrum-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.ag-spectrum-val {
  cursor: pointer;
}

.ag-spectrum-dragger {
  position: absolute;
  pointer-events: none;
  cursor: pointer;
}

.ag-spectrum-hue {
  cursor: default;
  background: linear-gradient(to left, #ff0000 3%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
}

.ag-spectrum-alpha {
  cursor: default;
}

.ag-spectrum-hue-background {
  width: 100%;
  height: 100%;
}

.ag-spectrum-alpha-background {
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgb(0, 0, 0));
  width: 100%;
  height: 100%;
}

.ag-spectrum-tool {
  cursor: pointer;
}

.ag-spectrum-slider {
  position: absolute;
  pointer-events: none;
}

.ag-recent-colors {
  display: flex;
}

.ag-recent-color {
  cursor: pointer;
}

.ag-ltr .ag-column-select-indent-1 {
  padding-left: 20px;
}
.ag-rtl .ag-column-select-indent-1 {
  padding-right: 20px;
}

.ag-ltr .ag-set-filter-indent-1 {
  padding-left: 20px;
}
.ag-rtl .ag-set-filter-indent-1 {
  padding-right: 20px;
}

.ag-ltr .ag-row-group-indent-1 {
  padding-left: 20px;
}
.ag-rtl .ag-row-group-indent-1 {
  padding-right: 20px;
}

.ag-ltr .ag-column-select-indent-2 {
  padding-left: 40px;
}
.ag-rtl .ag-column-select-indent-2 {
  padding-right: 40px;
}

.ag-ltr .ag-set-filter-indent-2 {
  padding-left: 40px;
}
.ag-rtl .ag-set-filter-indent-2 {
  padding-right: 40px;
}

.ag-ltr .ag-row-group-indent-2 {
  padding-left: 40px;
}
.ag-rtl .ag-row-group-indent-2 {
  padding-right: 40px;
}

.ag-ltr .ag-column-select-indent-3 {
  padding-left: 60px;
}
.ag-rtl .ag-column-select-indent-3 {
  padding-right: 60px;
}

.ag-ltr .ag-set-filter-indent-3 {
  padding-left: 60px;
}
.ag-rtl .ag-set-filter-indent-3 {
  padding-right: 60px;
}

.ag-ltr .ag-row-group-indent-3 {
  padding-left: 60px;
}
.ag-rtl .ag-row-group-indent-3 {
  padding-right: 60px;
}

.ag-ltr .ag-column-select-indent-4 {
  padding-left: 80px;
}
.ag-rtl .ag-column-select-indent-4 {
  padding-right: 80px;
}

.ag-ltr .ag-set-filter-indent-4 {
  padding-left: 80px;
}
.ag-rtl .ag-set-filter-indent-4 {
  padding-right: 80px;
}

.ag-ltr .ag-row-group-indent-4 {
  padding-left: 80px;
}
.ag-rtl .ag-row-group-indent-4 {
  padding-right: 80px;
}

.ag-ltr .ag-column-select-indent-5 {
  padding-left: 100px;
}
.ag-rtl .ag-column-select-indent-5 {
  padding-right: 100px;
}

.ag-ltr .ag-set-filter-indent-5 {
  padding-left: 100px;
}
.ag-rtl .ag-set-filter-indent-5 {
  padding-right: 100px;
}

.ag-ltr .ag-row-group-indent-5 {
  padding-left: 100px;
}
.ag-rtl .ag-row-group-indent-5 {
  padding-right: 100px;
}

.ag-ltr .ag-column-select-indent-6 {
  padding-left: 120px;
}
.ag-rtl .ag-column-select-indent-6 {
  padding-right: 120px;
}

.ag-ltr .ag-set-filter-indent-6 {
  padding-left: 120px;
}
.ag-rtl .ag-set-filter-indent-6 {
  padding-right: 120px;
}

.ag-ltr .ag-row-group-indent-6 {
  padding-left: 120px;
}
.ag-rtl .ag-row-group-indent-6 {
  padding-right: 120px;
}

.ag-ltr .ag-column-select-indent-7 {
  padding-left: 140px;
}
.ag-rtl .ag-column-select-indent-7 {
  padding-right: 140px;
}

.ag-ltr .ag-set-filter-indent-7 {
  padding-left: 140px;
}
.ag-rtl .ag-set-filter-indent-7 {
  padding-right: 140px;
}

.ag-ltr .ag-row-group-indent-7 {
  padding-left: 140px;
}
.ag-rtl .ag-row-group-indent-7 {
  padding-right: 140px;
}

.ag-ltr .ag-column-select-indent-8 {
  padding-left: 160px;
}
.ag-rtl .ag-column-select-indent-8 {
  padding-right: 160px;
}

.ag-ltr .ag-set-filter-indent-8 {
  padding-left: 160px;
}
.ag-rtl .ag-set-filter-indent-8 {
  padding-right: 160px;
}

.ag-ltr .ag-row-group-indent-8 {
  padding-left: 160px;
}
.ag-rtl .ag-row-group-indent-8 {
  padding-right: 160px;
}

.ag-ltr .ag-column-select-indent-9 {
  padding-left: 180px;
}
.ag-rtl .ag-column-select-indent-9 {
  padding-right: 180px;
}

.ag-ltr .ag-set-filter-indent-9 {
  padding-left: 180px;
}
.ag-rtl .ag-set-filter-indent-9 {
  padding-right: 180px;
}

.ag-ltr .ag-row-group-indent-9 {
  padding-left: 180px;
}
.ag-rtl .ag-row-group-indent-9 {
  padding-right: 180px;
}

.ag-ltr {
  direction: ltr;
}
.ag-ltr .ag-body, .ag-ltr .ag-floating-top, .ag-ltr .ag-floating-bottom, .ag-ltr .ag-header, .ag-ltr .ag-sticky-top, .ag-ltr .ag-body-viewport, .ag-ltr .ag-body-horizontal-scroll {
  flex-direction: row;
}

.ag-rtl {
  direction: rtl;
}
.ag-rtl .ag-body, .ag-rtl .ag-floating-top, .ag-rtl .ag-floating-bottom, .ag-rtl .ag-header, .ag-rtl .ag-sticky-top, .ag-rtl .ag-body-viewport, .ag-rtl .ag-body-horizontal-scroll {
  flex-direction: row-reverse;
}
.ag-rtl .ag-icon-contracted,
.ag-rtl .ag-icon-expanded,
.ag-rtl .ag-icon-tree-closed {
  display: block;
  transform: rotate(180deg);
}

.ag-body .ag-body-viewport {
  -webkit-overflow-scrolling: touch;
}

.ag-layout-print.ag-body {
  display: block;
  height: unset;
}
.ag-layout-print.ag-root-wrapper {
  display: inline-block;
}
.ag-layout-print .ag-body-vertical-scroll {
  display: none;
}
.ag-layout-print .ag-body-horizontal-scroll {
  display: none;
}
.ag-layout-print.ag-force-vertical-scroll {
  overflow-y: visible !important;
}

@media print {
  .ag-root-wrapper.ag-layout-print {
    display: table;
  }
  .ag-root-wrapper.ag-layout-print .ag-root-wrapper-body,
  .ag-root-wrapper.ag-layout-print .ag-root,
  .ag-root-wrapper.ag-layout-print .ag-body-viewport,
  .ag-root-wrapper.ag-layout-print .ag-center-cols-container,
  .ag-root-wrapper.ag-layout-print .ag-center-cols-viewport,
  .ag-root-wrapper.ag-layout-print .ag-body-horizontal-scroll-viewport,
  .ag-root-wrapper.ag-layout-print .ag-virtual-list-viewport {
    height: auto !important;
    overflow: hidden !important;
    display: block !important;
  }
  .ag-root-wrapper.ag-layout-print .ag-row, .ag-root-wrapper.ag-layout-print .ag-cell {
    break-inside: avoid;
  }
}
[class^=ag-], [class^=ag-]:focus, [class^=ag-]:after, [class^=ag-]:before {
  box-sizing: border-box;
  outline: none;
}

[class^=ag-]::-ms-clear {
  display: none;
}

.ag-checkbox .ag-input-wrapper,
.ag-radio-button .ag-input-wrapper {
  overflow: visible;
}

.ag-range-field .ag-input-wrapper {
  height: 100%;
}

.ag-toggle-button {
  flex: none;
  width: unset;
  min-width: unset;
}

.ag-button {
  border-radius: 0px;
  color: var(--ag-foreground-color);
}

.ag-button:hover {
  background-color: transparent;
}

.ag-ltr .ag-label-align-right .ag-label {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-label-align-right .ag-label {
  margin-right: var(--ag-grid-size);
}

input[class^=ag-] {
  margin: 0;
  background-color: var(--ag-background-color);
}

textarea[class^=ag-],
select[class^=ag-] {
  background-color: var(--ag-background-color);
}

input[class^=ag-]:not([type]),
input[class^=ag-][type=text],
input[class^=ag-][type=number],
input[class^=ag-][type=tel],
input[class^=ag-][type=date],
input[class^=ag-][type=datetime-local],
textarea[class^=ag-] {
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  border: var(--ag-borders-input) var(--ag-input-border-color);
}
input[class^=ag-]:not([type]):disabled,
input[class^=ag-][type=text]:disabled,
input[class^=ag-][type=number]:disabled,
input[class^=ag-][type=tel]:disabled,
input[class^=ag-][type=date]:disabled,
input[class^=ag-][type=datetime-local]:disabled,
textarea[class^=ag-]:disabled {
  color: var(--ag-disabled-foreground-color);
  background-color: var(--ag-input-disabled-background-color);
  border-color: var(--ag-input-disabled-border-color);
}
input[class^=ag-]:not([type]):focus,
input[class^=ag-][type=text]:focus,
input[class^=ag-][type=number]:focus,
input[class^=ag-][type=tel]:focus,
input[class^=ag-][type=date]:focus,
input[class^=ag-][type=datetime-local]:focus,
textarea[class^=ag-]:focus {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-input-focus-border-color);
}
input[class^=ag-]:not([type]):invalid,
input[class^=ag-][type=text]:invalid,
input[class^=ag-][type=number]:invalid,
input[class^=ag-][type=tel]:invalid,
input[class^=ag-][type=date]:invalid,
input[class^=ag-][type=datetime-local]:invalid,
textarea[class^=ag-]:invalid {
  border: var(--ag-borders-input-invalid) var(--ag-input-border-color-invalid);
}

input[class^=ag-][type=number]:not(.ag-number-field-input-stepper) {
  -moz-appearance: textfield;
}
input[class^=ag-][type=number]:not(.ag-number-field-input-stepper)::-webkit-outer-spin-button, input[class^=ag-][type=number]:not(.ag-number-field-input-stepper)::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[class^=ag-][type=range] {
  padding: 0;
}

input[class^=ag-][type=button]:focus, button[class^=ag-]:focus {
  box-shadow: var(--ag-input-focus-box-shadow);
}

.ag-drag-handle {
  color: var(--ag-secondary-foreground-color);
}

.ag-list-item, .ag-virtual-list-item {
  height: var(--ag-list-item-height);
}

.ag-keyboard-focus .ag-virtual-list-item:focus {
  outline: none;
}
.ag-keyboard-focus .ag-virtual-list-item:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-select-list {
  background-color: var(--ag-background-color);
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: var(--ag-border-radius);
  border: var(--ag-borders) var(--ag-border-color);
}

.ag-list-item {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ag-list-item.ag-active-item {
  background-color: var(--ag-row-hover-color);
}

.ag-select-list-item {
  padding-left: 4px;
  padding-right: 4px;
  cursor: default;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.ag-select-list-item span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.ag-row-drag,
.ag-selection-checkbox,
.ag-group-expanded,
.ag-group-contracted {
  color: var(--ag-secondary-foreground-color);
}
.ag-ltr .ag-row-drag,
.ag-ltr .ag-selection-checkbox,
.ag-ltr .ag-group-expanded,
.ag-ltr .ag-group-contracted {
  margin-right: var(--ag-cell-widget-spacing);
}
.ag-rtl .ag-row-drag,
.ag-rtl .ag-selection-checkbox,
.ag-rtl .ag-group-expanded,
.ag-rtl .ag-group-contracted {
  margin-left: var(--ag-cell-widget-spacing);
}

.ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value) {
  --ag-internal-calculated-line-height: var(--ag-line-height, calc(var(--ag-row-height) - var(--ag-row-border-width)));
  --ag-internal-padded-row-height: calc(var(--ag-row-height) - var(--ag-row-border-width));
  height: min(var(--ag-internal-calculated-line-height), var(--ag-internal-padded-row-height));
  display: flex;
  align-items: center;
  flex: none;
}

.ag-group-expanded,
.ag-group-contracted {
  cursor: pointer;
}

.ag-group-title-bar-icon {
  cursor: pointer;
  flex: none;
  color: var(--ag-secondary-foreground-color);
}

.ag-ltr .ag-group-child-count {
  margin-left: 2px;
}
.ag-rtl .ag-group-child-count {
  margin-right: 2px;
}

.ag-group-title-bar {
  background-color: var(--ag-subheader-background-color);
  padding: var(--ag-grid-size);
}

.ag-group-toolbar {
  padding: var(--ag-grid-size);
  background-color: var(--ag-subheader-toolbar-background-color);
}

.ag-disabled-group-title-bar, .ag-disabled-group-container {
  opacity: 0.5;
}

.group-item {
  margin: calc(var(--ag-grid-size) * 0.5) 0;
}

.ag-label {
  white-space: nowrap;
}
.ag-ltr .ag-label {
  margin-right: var(--ag-grid-size);
}
.ag-rtl .ag-label {
  margin-left: var(--ag-grid-size);
}

.ag-label-align-top .ag-label {
  margin-bottom: calc(var(--ag-grid-size) * 0.5);
}

.ag-angle-select[disabled] {
  color: var(--ag-disabled-foreground-color);
  pointer-events: none;
}
.ag-angle-select[disabled] .ag-angle-select-field {
  opacity: 0.4;
}

.ag-ltr .ag-slider-field,
.ag-ltr .ag-angle-select-field {
  margin-right: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-slider-field,
.ag-rtl .ag-angle-select-field {
  margin-left: calc(var(--ag-grid-size) * 2);
}

.ag-angle-select-parent-circle {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border: solid 1px;
  border-color: var(--ag-border-color);
  background-color: var(--ag-background-color);
}

.ag-angle-select-child-circle {
  top: 4px;
  left: 12px;
  width: 6px;
  height: 6px;
  margin-left: -3px;
  margin-top: -4px;
  border-radius: 3px;
  background-color: var(--ag-secondary-foreground-color);
}

.ag-picker-field-wrapper {
  border: var(--ag-borders);
  border-color: var(--ag-border-color);
  border-radius: 5px;
}
.ag-picker-field-wrapper:disabled {
  color: var(--ag-disabled-foreground-color);
  background-color: var(--ag-input-disabled-background-color);
  border-color: var(--ag-input-disabled-border-color);
}
.ag-picker-field-wrapper.ag-picker-has-focus, .ag-picker-field-wrapper:focus-within {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-input-focus-border-color);
}

.ag-picker-field-button {
  background-color: var(--ag-background-color);
  color: var(--ag-secondary-foreground-color);
}

.ag-dialog.ag-color-dialog {
  border-radius: 5px;
}

.ag-color-picker .ag-picker-field-display {
  height: var(--ag-icon-size);
}

.ag-color-panel {
  padding: var(--ag-grid-size);
}

.ag-spectrum-color {
  background-color: rgb(255, 0, 0);
  border-radius: 2px;
}

.ag-spectrum-tools {
  padding: 10px;
}

.ag-spectrum-sat {
  background-image: linear-gradient(to right, white, rgba(204, 154, 129, 0));
}

.ag-spectrum-val {
  background-image: linear-gradient(to top, black, rgba(204, 154, 129, 0));
}

.ag-spectrum-dragger {
  border-radius: 12px;
  height: 12px;
  width: 12px;
  border: 1px solid white;
  background: black;
  box-shadow: 0 0 2px 0px rgba(0, 0, 0, 0.24);
}

.ag-spectrum-hue-background {
  border-radius: 2px;
}

.ag-spectrum-alpha-background {
  border-radius: 2px;
}

.ag-spectrum-tool {
  margin-bottom: 10px;
  height: 11px;
  border-radius: 2px;
}

.ag-spectrum-slider {
  margin-top: -12px;
  width: 13px;
  height: 13px;
  border-radius: 13px;
  background-color: rgb(248, 248, 248);
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.37);
}

.ag-recent-color {
  margin: 0 3px;
}
.ag-recent-color:first-child {
  margin-left: 0;
}
.ag-recent-color:last-child {
  margin-right: 0;
}

.ag-spectrum-color:focus-visible:not(:disabled):not([readonly]),
.ag-spectrum-slider:focus-visible:not(:disabled):not([readonly]),
.ag-recent-color:focus-visible:not(:disabled):not([readonly]) {
  box-shadow: var(--ag-input-focus-box-shadow);
}

.ag-dnd-ghost {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
  overflow: hidden;
  text-overflow: ellipsis;
  border: var(--ag-borders-secondary) var(--ag-secondary-border-color);
  color: var(--ag-secondary-foreground-color);
  height: var(--ag-header-height) !important;
  line-height: var(--ag-header-height);
  margin: 0;
  padding: 0 calc(var(--ag-grid-size) * 2);
  transform: translateY(calc(var(--ag-grid-size) * 2));
}

.ag-dnd-ghost-icon {
  margin-right: var(--ag-grid-size);
  color: var(--ag-foreground-color);
}

.ag-popup-child:not(.ag-tooltip-custom) {
  box-shadow: var(--ag-popup-shadow);
}

.ag-select .ag-picker-field-wrapper {
  background-color: var(--ag-background-color);
  min-height: var(--ag-list-item-height);
  cursor: default;
}
.ag-select.ag-disabled .ag-picker-field-wrapper:focus {
  box-shadow: none;
}
.ag-select:not(.ag-cell-editor, .ag-label-align-top) {
  height: var(--ag-list-item-height);
}
.ag-select .ag-picker-field-display {
  margin: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ag-select .ag-picker-field-icon {
  display: flex;
  align-items: center;
}
.ag-select.ag-disabled {
  opacity: 0.5;
}

.ag-rich-select-value,
.ag-rich-select-list {
  background-color: var(--ag-background-color);
}

.ag-rich-select-list {
  width: 100%;
  height: auto;
  border-radius: var(--ag-border-radius);
  border: var(--ag-borders) var(--ag-border-color);
}
.ag-rich-select-list .ag-loading-text {
  padding: var(--ag-widget-vertical-spacing) var(--ag-widget-horizontal-spacing);
}

.ag-rich-select-value {
  border-bottom: var(--ag-borders-secondary) var(--ag-secondary-border-color);
  padding-top: 0;
  padding-bottom: 0;
}
.ag-ltr .ag-rich-select-value {
  padding-left: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-rich-select-value {
  padding-right: var(--ag-cell-horizontal-padding);
}
.ag-ltr .ag-rich-select-value {
  padding-right: var(--ag-grid-size);
}
.ag-rtl .ag-rich-select-value {
  padding-left: var(--ag-grid-size);
}

.ag-ltr .ag-rich-select-field-input {
  left: calc(var(--ag-cell-horizontal-padding));
}
.ag-rtl .ag-rich-select-field-input {
  right: calc(var(--ag-cell-horizontal-padding));
}

.ag-popup-editor .ag-rich-select-value {
  height: var(--ag-row-height);
  min-width: 200px;
}

.ag-rich-select-virtual-list-item {
  cursor: default;
  height: var(--ag-list-item-height);
}
.ag-keyboard-focus .ag-rich-select-virtual-list-item:focus::after {
  content: none;
}
.ag-rich-select-virtual-list-item:hover {
  background-color: var(--ag-row-hover-color);
}

.ag-ltr .ag-rich-select-row {
  padding-left: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-rich-select-row {
  padding-right: var(--ag-cell-horizontal-padding);
}

.ag-rich-select-row-selected {
  background-color: var(--ag-selected-row-background-color);
}

.ag-rich-select-row-text-highlight {
  font-weight: bold;
}

.ag-autocomplete {
  width: 100%;
}

.ag-autocomplete-list {
  width: 100%;
  min-width: 200px;
  height: calc(var(--ag-row-height) * 6.5);
}

.ag-autocomplete-virtual-list-item {
  cursor: default;
  height: var(--ag-list-item-height);
}
.ag-keyboard-focus .ag-autocomplete-virtual-list-item:focus::after {
  content: none;
}
.ag-autocomplete-virtual-list-item:hover {
  background-color: var(--ag-row-hover-color);
}

.ag-autocomplete-row-label {
  margin: 0px var(--ag-widget-container-horizontal-padding);
}

.ag-autocomplete-row-selected {
  background-color: var(--ag-selected-row-background-color);
}

.ag-dragging-range-handle .ag-dialog,
.ag-dragging-fill-handle .ag-dialog {
  opacity: 0.7;
  pointer-events: none;
}

.ag-dialog {
  border-radius: var(--ag-border-radius);
  border: var(--ag-borders) var(--ag-border-color);
  box-shadow: var(--ag-popup-shadow);
}

.ag-panel {
  background-color: var(--ag-background-color);
}

.ag-panel-title-bar {
  background-color: var(--ag-header-background-color);
  color: var(--ag-header-foreground-color);
  height: var(--ag-header-height);
  padding: var(--ag-grid-size) var(--ag-cell-horizontal-padding);
  border-bottom: var(--ag-borders) var(--ag-border-color);
}

.ag-ltr .ag-panel-title-bar-button {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-panel-title-bar-button {
  margin-right: var(--ag-grid-size);
}

.ag-tooltip {
  background-color: var(--ag-tooltip-background-color);
  color: var(--ag-foreground-color);
  padding: var(--ag-grid-size);
  border: var(--ag-borders) var(--ag-border-color);
  border-radius: var(--ag-card-radius);
  white-space: normal;
}

.ag-tooltip.ag-tooltip-animate,
.ag-tooltip-custom.ag-tooltip-animate {
  transition: opacity 1s;
}
.ag-tooltip.ag-tooltip-animate.ag-tooltip-hiding,
.ag-tooltip-custom.ag-tooltip-animate.ag-tooltip-hiding {
  opacity: 0;
}

.ag-ltr .ag-column-select-indent-1 {
  padding-left: calc(1 * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-indent-1 {
  padding-right: calc(1 * var(--ag-column-select-indent-size));
}

.ag-ltr .ag-column-select-indent-2 {
  padding-left: calc(2 * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-indent-2 {
  padding-right: calc(2 * var(--ag-column-select-indent-size));
}

.ag-ltr .ag-column-select-indent-3 {
  padding-left: calc(3 * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-indent-3 {
  padding-right: calc(3 * var(--ag-column-select-indent-size));
}

.ag-ltr .ag-column-select-indent-4 {
  padding-left: calc(4 * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-indent-4 {
  padding-right: calc(4 * var(--ag-column-select-indent-size));
}

.ag-ltr .ag-column-select-indent-5 {
  padding-left: calc(5 * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-indent-5 {
  padding-right: calc(5 * var(--ag-column-select-indent-size));
}

.ag-ltr .ag-column-select-indent-6 {
  padding-left: calc(6 * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-indent-6 {
  padding-right: calc(6 * var(--ag-column-select-indent-size));
}

.ag-ltr .ag-column-select-indent-7 {
  padding-left: calc(7 * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-indent-7 {
  padding-right: calc(7 * var(--ag-column-select-indent-size));
}

.ag-ltr .ag-column-select-indent-8 {
  padding-left: calc(8 * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-indent-8 {
  padding-right: calc(8 * var(--ag-column-select-indent-size));
}

.ag-ltr .ag-column-select-indent-9 {
  padding-left: calc(9 * var(--ag-column-select-indent-size));
}
.ag-rtl .ag-column-select-indent-9 {
  padding-right: calc(9 * var(--ag-column-select-indent-size));
}

.ag-column-select-header-icon {
  cursor: pointer;
}

.ag-keyboard-focus .ag-column-select-header-icon:focus {
  outline: none;
}
.ag-keyboard-focus .ag-column-select-header-icon:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 0px;
  left: 0px;
  display: block;
  width: calc(100% - 0px);
  height: calc(100% - 0px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-ltr .ag-column-group-icons:not(:last-child),
.ag-ltr .ag-column-select-header-icon:not(:last-child),
.ag-ltr .ag-column-select-header-checkbox:not(:last-child),
.ag-ltr .ag-column-select-header-filter-wrapper:not(:last-child),
.ag-ltr .ag-column-select-checkbox:not(:last-child),
.ag-ltr .ag-column-select-column-drag-handle:not(:last-child),
.ag-ltr .ag-column-select-column-group-drag-handle:not(:last-child),
.ag-ltr .ag-column-select-column-label:not(:last-child) {
  margin-right: var(--ag-widget-horizontal-spacing);
}
.ag-rtl .ag-column-group-icons:not(:last-child),
.ag-rtl .ag-column-select-header-icon:not(:last-child),
.ag-rtl .ag-column-select-header-checkbox:not(:last-child),
.ag-rtl .ag-column-select-header-filter-wrapper:not(:last-child),
.ag-rtl .ag-column-select-checkbox:not(:last-child),
.ag-rtl .ag-column-select-column-drag-handle:not(:last-child),
.ag-rtl .ag-column-select-column-group-drag-handle:not(:last-child),
.ag-rtl .ag-column-select-column-label:not(:last-child) {
  margin-left: var(--ag-widget-horizontal-spacing);
}

.ag-keyboard-focus .ag-column-select-virtual-list-item:focus {
  outline: none;
}
.ag-keyboard-focus .ag-column-select-virtual-list-item:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 1px;
  left: 1px;
  display: block;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-column-select-column-group:not(:last-child),
.ag-column-select-column:not(:last-child) {
  margin-bottom: var(--ag-widget-vertical-spacing);
}

.ag-column-select-column-readonly,
.ag-column-select-column-group-readonly {
  color: var(--ag-disabled-foreground-color);
  pointer-events: none;
}

.ag-ltr .ag-column-select-add-group-indent {
  margin-left: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
}
.ag-rtl .ag-column-select-add-group-indent {
  margin-right: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
}

.ag-column-select-virtual-list-viewport {
  padding: calc(var(--ag-widget-container-vertical-padding) * 0.5) 0px;
}

.ag-column-select-virtual-list-item {
  padding: 0 var(--ag-widget-container-horizontal-padding);
}

.ag-checkbox-edit {
  padding-left: var(--ag-cell-horizontal-padding);
  padding-right: var(--ag-cell-horizontal-padding);
}

.ag-rtl {
  text-align: right;
}

.ag-root-wrapper {
  border: var(--ag-borders) var(--ag-border-color);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-1 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 1);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-1 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 1);
}

.ag-ltr .ag-row-group-indent-1 {
  padding-left: calc(1 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-1 {
  padding-right: calc(1 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-1 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-1 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-2 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 2);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-2 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 2);
}

.ag-ltr .ag-row-group-indent-2 {
  padding-left: calc(2 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-2 {
  padding-right: calc(2 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-2 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-2 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-3 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 3);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-3 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 3);
}

.ag-ltr .ag-row-group-indent-3 {
  padding-left: calc(3 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-3 {
  padding-right: calc(3 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-3 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-3 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-4 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 4);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-4 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 4);
}

.ag-ltr .ag-row-group-indent-4 {
  padding-left: calc(4 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-4 {
  padding-right: calc(4 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-4 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-4 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-5 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 5);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-5 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 5);
}

.ag-ltr .ag-row-group-indent-5 {
  padding-left: calc(5 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-5 {
  padding-right: calc(5 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-5 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-5 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-6 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 6);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-6 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 6);
}

.ag-ltr .ag-row-group-indent-6 {
  padding-left: calc(6 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-6 {
  padding-right: calc(6 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-6 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-6 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-7 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 7);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-7 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 7);
}

.ag-ltr .ag-row-group-indent-7 {
  padding-left: calc(7 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-7 {
  padding-right: calc(7 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-7 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-7 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-8 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 8);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-8 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 8);
}

.ag-ltr .ag-row-group-indent-8 {
  padding-left: calc(8 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-8 {
  padding-right: calc(8 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-8 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-8 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-9 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 9);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-9 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 9);
}

.ag-ltr .ag-row-group-indent-9 {
  padding-left: calc(9 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-9 {
  padding-right: calc(9 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-9 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-9 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-10 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 10);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-10 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 10);
}

.ag-ltr .ag-row-group-indent-10 {
  padding-left: calc(10 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-10 {
  padding-right: calc(10 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-10 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-10 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-11 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 11);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-11 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 11);
}

.ag-ltr .ag-row-group-indent-11 {
  padding-left: calc(11 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-11 {
  padding-right: calc(11 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-11 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-11 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-12 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 12);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-12 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 12);
}

.ag-ltr .ag-row-group-indent-12 {
  padding-left: calc(12 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-12 {
  padding-right: calc(12 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-12 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-12 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-13 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 13);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-13 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 13);
}

.ag-ltr .ag-row-group-indent-13 {
  padding-left: calc(13 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-13 {
  padding-right: calc(13 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-13 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-13 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-14 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 14);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-14 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 14);
}

.ag-ltr .ag-row-group-indent-14 {
  padding-left: calc(14 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-14 {
  padding-right: calc(14 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-14 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-14 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-15 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 15);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-15 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 15);
}

.ag-ltr .ag-row-group-indent-15 {
  padding-left: calc(15 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-15 {
  padding-right: calc(15 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-15 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-15 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-16 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 16);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-16 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 16);
}

.ag-ltr .ag-row-group-indent-16 {
  padding-left: calc(16 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-16 {
  padding-right: calc(16 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-16 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-16 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-17 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 17);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-17 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 17);
}

.ag-ltr .ag-row-group-indent-17 {
  padding-left: calc(17 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-17 {
  padding-right: calc(17 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-17 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-17 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-18 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 18);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-18 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 18);
}

.ag-ltr .ag-row-group-indent-18 {
  padding-left: calc(18 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-18 {
  padding-right: calc(18 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-18 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-18 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-19 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 19);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-19 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 19);
}

.ag-ltr .ag-row-group-indent-19 {
  padding-left: calc(19 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-19 {
  padding-right: calc(19 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-19 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-19 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-20 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 20);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-20 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 20);
}

.ag-ltr .ag-row-group-indent-20 {
  padding-left: calc(20 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-20 {
  padding-right: calc(20 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-20 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-20 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-21 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 21);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-21 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 21);
}

.ag-ltr .ag-row-group-indent-21 {
  padding-left: calc(21 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-21 {
  padding-right: calc(21 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-21 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-21 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-22 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 22);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-22 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 22);
}

.ag-ltr .ag-row-group-indent-22 {
  padding-left: calc(22 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-22 {
  padding-right: calc(22 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-22 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-22 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-23 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 23);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-23 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 23);
}

.ag-ltr .ag-row-group-indent-23 {
  padding-left: calc(23 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-23 {
  padding-right: calc(23 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-23 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-23 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-24 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 24);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-24 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 24);
}

.ag-ltr .ag-row-group-indent-24 {
  padding-left: calc(24 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-24 {
  padding-right: calc(24 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-24 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-24 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-25 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 25);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-25 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 25);
}

.ag-ltr .ag-row-group-indent-25 {
  padding-left: calc(25 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-25 {
  padding-right: calc(25 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-25 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-25 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-26 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 26);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-26 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 26);
}

.ag-ltr .ag-row-group-indent-26 {
  padding-left: calc(26 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-26 {
  padding-right: calc(26 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-26 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-26 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-27 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 27);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-27 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 27);
}

.ag-ltr .ag-row-group-indent-27 {
  padding-left: calc(27 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-27 {
  padding-right: calc(27 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-27 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-27 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-28 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 28);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-28 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 28);
}

.ag-ltr .ag-row-group-indent-28 {
  padding-left: calc(28 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-28 {
  padding-right: calc(28 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-28 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-28 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-29 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 29);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-29 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 29);
}

.ag-ltr .ag-row-group-indent-29 {
  padding-left: calc(29 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-29 {
  padding-right: calc(29 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-29 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-29 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-30 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 30);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-30 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 30);
}

.ag-ltr .ag-row-group-indent-30 {
  padding-left: calc(30 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-30 {
  padding-right: calc(30 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-30 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-30 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-31 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 31);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-31 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 31);
}

.ag-ltr .ag-row-group-indent-31 {
  padding-left: calc(31 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-31 {
  padding-right: calc(31 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-31 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-31 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-32 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 32);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-32 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 32);
}

.ag-ltr .ag-row-group-indent-32 {
  padding-left: calc(32 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-32 {
  padding-right: calc(32 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-32 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-32 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-33 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 33);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-33 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 33);
}

.ag-ltr .ag-row-group-indent-33 {
  padding-left: calc(33 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-33 {
  padding-right: calc(33 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-33 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-33 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-34 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 34);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-34 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 34);
}

.ag-ltr .ag-row-group-indent-34 {
  padding-left: calc(34 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-34 {
  padding-right: calc(34 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-34 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-34 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-35 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 35);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-35 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 35);
}

.ag-ltr .ag-row-group-indent-35 {
  padding-left: calc(35 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-35 {
  padding-right: calc(35 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-35 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-35 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-36 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 36);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-36 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 36);
}

.ag-ltr .ag-row-group-indent-36 {
  padding-left: calc(36 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-36 {
  padding-right: calc(36 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-36 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-36 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-37 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 37);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-37 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 37);
}

.ag-ltr .ag-row-group-indent-37 {
  padding-left: calc(37 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-37 {
  padding-right: calc(37 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-37 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-37 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-38 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 38);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-38 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 38);
}

.ag-ltr .ag-row-group-indent-38 {
  padding-left: calc(38 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-38 {
  padding-right: calc(38 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-38 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-38 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-39 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 39);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-39 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 39);
}

.ag-ltr .ag-row-group-indent-39 {
  padding-left: calc(39 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-39 {
  padding-right: calc(39 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-39 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-39 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-40 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 40);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-40 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 40);
}

.ag-ltr .ag-row-group-indent-40 {
  padding-left: calc(40 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-40 {
  padding-right: calc(40 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-40 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-40 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-41 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 41);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-41 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 41);
}

.ag-ltr .ag-row-group-indent-41 {
  padding-left: calc(41 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-41 {
  padding-right: calc(41 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-41 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-41 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-42 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 42);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-42 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 42);
}

.ag-ltr .ag-row-group-indent-42 {
  padding-left: calc(42 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-42 {
  padding-right: calc(42 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-42 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-42 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-43 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 43);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-43 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 43);
}

.ag-ltr .ag-row-group-indent-43 {
  padding-left: calc(43 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-43 {
  padding-right: calc(43 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-43 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-43 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-44 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 44);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-44 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 44);
}

.ag-ltr .ag-row-group-indent-44 {
  padding-left: calc(44 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-44 {
  padding-right: calc(44 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-44 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-44 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-45 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 45);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-45 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 45);
}

.ag-ltr .ag-row-group-indent-45 {
  padding-left: calc(45 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-45 {
  padding-right: calc(45 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-45 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-45 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-46 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 46);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-46 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 46);
}

.ag-ltr .ag-row-group-indent-46 {
  padding-left: calc(46 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-46 {
  padding-right: calc(46 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-46 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-46 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-47 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 47);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-47 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 47);
}

.ag-ltr .ag-row-group-indent-47 {
  padding-left: calc(47 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-47 {
  padding-right: calc(47 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-47 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-47 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-48 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 48);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-48 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 48);
}

.ag-ltr .ag-row-group-indent-48 {
  padding-left: calc(48 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-48 {
  padding-right: calc(48 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-48 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-48 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-49 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 49);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-49 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 49);
}

.ag-ltr .ag-row-group-indent-49 {
  padding-left: calc(49 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-49 {
  padding-right: calc(49 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-49 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-49 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-50 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 50);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-50 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 50);
}

.ag-ltr .ag-row-group-indent-50 {
  padding-left: calc(50 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-50 {
  padding-right: calc(50 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-50 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-50 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-51 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 51);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-51 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 51);
}

.ag-ltr .ag-row-group-indent-51 {
  padding-left: calc(51 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-51 {
  padding-right: calc(51 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-51 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-51 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-52 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 52);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-52 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 52);
}

.ag-ltr .ag-row-group-indent-52 {
  padding-left: calc(52 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-52 {
  padding-right: calc(52 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-52 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-52 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-53 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 53);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-53 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 53);
}

.ag-ltr .ag-row-group-indent-53 {
  padding-left: calc(53 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-53 {
  padding-right: calc(53 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-53 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-53 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-54 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 54);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-54 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 54);
}

.ag-ltr .ag-row-group-indent-54 {
  padding-left: calc(54 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-54 {
  padding-right: calc(54 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-54 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-54 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-55 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 55);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-55 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 55);
}

.ag-ltr .ag-row-group-indent-55 {
  padding-left: calc(55 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-55 {
  padding-right: calc(55 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-55 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-55 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-56 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 56);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-56 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 56);
}

.ag-ltr .ag-row-group-indent-56 {
  padding-left: calc(56 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-56 {
  padding-right: calc(56 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-56 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-56 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-57 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 57);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-57 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 57);
}

.ag-ltr .ag-row-group-indent-57 {
  padding-left: calc(57 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-57 {
  padding-right: calc(57 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-57 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-57 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-58 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 58);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-58 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 58);
}

.ag-ltr .ag-row-group-indent-58 {
  padding-left: calc(58 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-58 {
  padding-right: calc(58 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-58 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-58 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-59 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 59);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-59 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 59);
}

.ag-ltr .ag-row-group-indent-59 {
  padding-left: calc(59 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-59 {
  padding-right: calc(59 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-59 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-59 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-60 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 60);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-60 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 60);
}

.ag-ltr .ag-row-group-indent-60 {
  padding-left: calc(60 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-60 {
  padding-right: calc(60 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-60 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-60 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-61 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 61);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-61 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 61);
}

.ag-ltr .ag-row-group-indent-61 {
  padding-left: calc(61 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-61 {
  padding-right: calc(61 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-61 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-61 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-62 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 62);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-62 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 62);
}

.ag-ltr .ag-row-group-indent-62 {
  padding-left: calc(62 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-62 {
  padding-right: calc(62 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-62 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-62 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-63 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 63);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-63 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 63);
}

.ag-ltr .ag-row-group-indent-63 {
  padding-left: calc(63 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-63 {
  padding-right: calc(63 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-63 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-63 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-64 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 64);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-64 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 64);
}

.ag-ltr .ag-row-group-indent-64 {
  padding-left: calc(64 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-64 {
  padding-right: calc(64 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-64 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-64 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-65 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 65);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-65 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 65);
}

.ag-ltr .ag-row-group-indent-65 {
  padding-left: calc(65 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-65 {
  padding-right: calc(65 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-65 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-65 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-66 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 66);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-66 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 66);
}

.ag-ltr .ag-row-group-indent-66 {
  padding-left: calc(66 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-66 {
  padding-right: calc(66 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-66 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-66 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-67 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 67);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-67 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 67);
}

.ag-ltr .ag-row-group-indent-67 {
  padding-left: calc(67 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-67 {
  padding-right: calc(67 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-67 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-67 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-68 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 68);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-68 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 68);
}

.ag-ltr .ag-row-group-indent-68 {
  padding-left: calc(68 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-68 {
  padding-right: calc(68 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-68 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-68 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-69 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 69);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-69 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 69);
}

.ag-ltr .ag-row-group-indent-69 {
  padding-left: calc(69 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-69 {
  padding-right: calc(69 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-69 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-69 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-70 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 70);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-70 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 70);
}

.ag-ltr .ag-row-group-indent-70 {
  padding-left: calc(70 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-70 {
  padding-right: calc(70 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-70 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-70 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-71 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 71);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-71 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 71);
}

.ag-ltr .ag-row-group-indent-71 {
  padding-left: calc(71 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-71 {
  padding-right: calc(71 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-71 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-71 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-72 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 72);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-72 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 72);
}

.ag-ltr .ag-row-group-indent-72 {
  padding-left: calc(72 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-72 {
  padding-right: calc(72 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-72 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-72 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-73 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 73);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-73 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 73);
}

.ag-ltr .ag-row-group-indent-73 {
  padding-left: calc(73 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-73 {
  padding-right: calc(73 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-73 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-73 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-74 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 74);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-74 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 74);
}

.ag-ltr .ag-row-group-indent-74 {
  padding-left: calc(74 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-74 {
  padding-right: calc(74 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-74 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-74 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-75 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 75);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-75 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 75);
}

.ag-ltr .ag-row-group-indent-75 {
  padding-left: calc(75 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-75 {
  padding-right: calc(75 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-75 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-75 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-76 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 76);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-76 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 76);
}

.ag-ltr .ag-row-group-indent-76 {
  padding-left: calc(76 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-76 {
  padding-right: calc(76 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-76 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-76 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-77 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 77);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-77 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 77);
}

.ag-ltr .ag-row-group-indent-77 {
  padding-left: calc(77 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-77 {
  padding-right: calc(77 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-77 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-77 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-78 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 78);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-78 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 78);
}

.ag-ltr .ag-row-group-indent-78 {
  padding-left: calc(78 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-78 {
  padding-right: calc(78 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-78 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-78 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-79 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 79);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-79 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 79);
}

.ag-ltr .ag-row-group-indent-79 {
  padding-left: calc(79 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-79 {
  padding-right: calc(79 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-79 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-79 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-80 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 80);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-80 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 80);
}

.ag-ltr .ag-row-group-indent-80 {
  padding-left: calc(80 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-80 {
  padding-right: calc(80 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-80 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-80 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-81 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 81);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-81 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 81);
}

.ag-ltr .ag-row-group-indent-81 {
  padding-left: calc(81 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-81 {
  padding-right: calc(81 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-81 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-81 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-82 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 82);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-82 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 82);
}

.ag-ltr .ag-row-group-indent-82 {
  padding-left: calc(82 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-82 {
  padding-right: calc(82 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-82 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-82 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-83 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 83);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-83 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 83);
}

.ag-ltr .ag-row-group-indent-83 {
  padding-left: calc(83 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-83 {
  padding-right: calc(83 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-83 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-83 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-84 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 84);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-84 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 84);
}

.ag-ltr .ag-row-group-indent-84 {
  padding-left: calc(84 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-84 {
  padding-right: calc(84 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-84 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-84 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-85 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 85);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-85 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 85);
}

.ag-ltr .ag-row-group-indent-85 {
  padding-left: calc(85 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-85 {
  padding-right: calc(85 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-85 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-85 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-86 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 86);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-86 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 86);
}

.ag-ltr .ag-row-group-indent-86 {
  padding-left: calc(86 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-86 {
  padding-right: calc(86 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-86 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-86 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-87 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 87);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-87 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 87);
}

.ag-ltr .ag-row-group-indent-87 {
  padding-left: calc(87 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-87 {
  padding-right: calc(87 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-87 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-87 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-88 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 88);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-88 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 88);
}

.ag-ltr .ag-row-group-indent-88 {
  padding-left: calc(88 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-88 {
  padding-right: calc(88 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-88 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-88 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-89 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 89);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-89 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 89);
}

.ag-ltr .ag-row-group-indent-89 {
  padding-left: calc(89 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-89 {
  padding-right: calc(89 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-89 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-89 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-90 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 90);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-90 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 90);
}

.ag-ltr .ag-row-group-indent-90 {
  padding-left: calc(90 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-90 {
  padding-right: calc(90 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-90 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-90 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-91 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 91);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-91 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 91);
}

.ag-ltr .ag-row-group-indent-91 {
  padding-left: calc(91 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-91 {
  padding-right: calc(91 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-91 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-91 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-92 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 92);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-92 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 92);
}

.ag-ltr .ag-row-group-indent-92 {
  padding-left: calc(92 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-92 {
  padding-right: calc(92 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-92 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-92 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-93 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 93);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-93 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 93);
}

.ag-ltr .ag-row-group-indent-93 {
  padding-left: calc(93 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-93 {
  padding-right: calc(93 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-93 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-93 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-94 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 94);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-94 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 94);
}

.ag-ltr .ag-row-group-indent-94 {
  padding-left: calc(94 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-94 {
  padding-right: calc(94 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-94 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-94 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-95 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 95);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-95 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 95);
}

.ag-ltr .ag-row-group-indent-95 {
  padding-left: calc(95 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-95 {
  padding-right: calc(95 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-95 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-95 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-96 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 96);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-96 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 96);
}

.ag-ltr .ag-row-group-indent-96 {
  padding-left: calc(96 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-96 {
  padding-right: calc(96 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-96 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-96 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-97 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 97);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-97 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 97);
}

.ag-ltr .ag-row-group-indent-97 {
  padding-left: calc(97 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-97 {
  padding-right: calc(97 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-97 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-97 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-98 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 98);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-98 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 98);
}

.ag-ltr .ag-row-group-indent-98 {
  padding-left: calc(98 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-98 {
  padding-right: calc(98 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-98 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-98 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-99 {
  padding-left: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 99);
}
.ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-99 {
  padding-right: calc(var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * 99);
}

.ag-ltr .ag-row-group-indent-99 {
  padding-left: calc(99 * var(--ag-row-group-indent-size));
}
.ag-rtl .ag-row-group-indent-99 {
  padding-right: calc(99 * var(--ag-row-group-indent-size));
}

.ag-ltr .ag-row-level-99 .ag-pivot-leaf-group {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-level-99 .ag-pivot-leaf-group {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-ltr .ag-row-group-leaf-indent {
  margin-left: var(--ag-row-group-indent-size);
}
.ag-rtl .ag-row-group-leaf-indent {
  margin-right: var(--ag-row-group-indent-size);
}

.ag-value-change-delta {
  padding-right: 2px;
}

.ag-value-change-delta-up {
  color: var(--ag-value-change-delta-up-color);
}

.ag-value-change-delta-down {
  color: var(--ag-value-change-delta-down-color);
}

.ag-value-change-value {
  background-color: transparent;
  border-radius: 1px;
  padding-left: 1px;
  padding-right: 1px;
  transition: background-color 1s;
}

.ag-value-change-value-highlight {
  background-color: var(--ag-value-change-value-highlight-background-color);
  transition: background-color 0.1s;
}

.ag-cell-data-changed {
  background-color: var(--ag-value-change-value-highlight-background-color) !important;
}

.ag-cell-data-changed-animation {
  background-color: transparent;
}

.ag-cell-highlight {
  background-color: var(--ag-range-selection-highlight-color) !important;
}

.ag-row {
  height: var(--ag-row-height);
  background-color: var(--ag-background-color);
  color: var(--ag-data-color);
  border-bottom: var(--ag-row-border-style) var(--ag-row-border-color) var(--ag-row-border-width);
}

.ag-row-highlight-above::after, .ag-row-highlight-below::after {
  content: "";
  position: absolute;
  width: calc(100% - 1px);
  height: 1px;
  background-color: var(--ag-range-selection-border-color);
  left: 1px;
}

.ag-row-highlight-above::after {
  top: -1px;
}

.ag-row-highlight-above.ag-row-first::after {
  top: 0;
}

.ag-row-highlight-below::after {
  bottom: 0px;
}

.ag-row-odd {
  background-color: var(--ag-odd-row-background-color);
}

.ag-body-horizontal-scroll:not(.ag-scrollbar-invisible) .ag-horizontal-left-spacer:not(.ag-scroller-corner) {
  border-right: var(--ag-borders-critical) var(--ag-border-color);
}
.ag-body-horizontal-scroll:not(.ag-scrollbar-invisible) .ag-horizontal-right-spacer:not(.ag-scroller-corner) {
  border-left: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-row-selected::before {
  content: "";
  background-color: var(--ag-selected-row-background-color);
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.ag-row-hover:not(.ag-full-width-row)::before,
.ag-row-hover.ag-full-width-row.ag-row-group::before {
  content: "";
  background-color: var(--ag-row-hover-color);
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.ag-row-hover.ag-full-width-row.ag-row-group > * {
  position: relative;
}

.ag-row-hover.ag-row-selected::before {
  background-color: var(--ag-row-hover-color);
  background-image: linear-gradient(var(--ag-selected-row-background-color), var(--ag-selected-row-background-color));
}

.ag-column-hover {
  background-color: var(--ag-column-hover-color);
}

.ag-ltr .ag-right-aligned-cell {
  text-align: right;
}
.ag-rtl .ag-right-aligned-cell {
  text-align: left;
}

.ag-ltr .ag-right-aligned-cell .ag-cell-value,
.ag-ltr .ag-right-aligned-cell .ag-group-value {
  margin-left: auto;
}
.ag-rtl .ag-right-aligned-cell .ag-cell-value,
.ag-rtl .ag-right-aligned-cell .ag-group-value {
  margin-right: auto;
}

.ag-cell, .ag-full-width-row .ag-cell-wrapper.ag-row-group {
  --ag-internal-calculated-line-height: var(--ag-line-height, calc(var(--ag-row-height) - var(--ag-row-border-width)));
  --ag-internal-padded-row-height: calc(var(--ag-row-height) - var(--ag-row-border-width));
  border: 1px solid transparent;
  line-height: min(var(--ag-internal-calculated-line-height), var(--ag-internal-padded-row-height));
  padding-left: calc(var(--ag-cell-horizontal-padding) - 1px);
  padding-right: calc(var(--ag-cell-horizontal-padding) - 1px);
  -webkit-font-smoothing: subpixel-antialiased;
}

.ag-row > .ag-cell-wrapper {
  padding-left: calc(var(--ag-cell-horizontal-padding) - 1px);
  padding-right: calc(var(--ag-cell-horizontal-padding) - 1px);
}

.ag-row-dragging {
  cursor: move;
  opacity: 0.5;
}

.ag-cell-inline-editing {
  border: 1px solid var(--ag-border-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: 0;
  background-color: var(--ag-control-panel-background-color);
}

.ag-popup-editor .ag-large-text,
.ag-autocomplete-list-popup {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
  background-color: var(--ag-control-panel-background-color);
  padding: 0;
}

.ag-large-text-input {
  height: auto;
  padding: var(--ag-cell-horizontal-padding);
}

.ag-rtl .ag-large-text-input textarea {
  resize: none;
}

.ag-details-row {
  padding: calc(var(--ag-grid-size) * 5);
  background-color: var(--ag-background-color);
}

.ag-layout-auto-height .ag-center-cols-viewport, .ag-layout-auto-height .ag-center-cols-container, .ag-layout-print .ag-center-cols-viewport, .ag-layout-print .ag-center-cols-container {
  min-height: 50px;
}

.ag-overlay-loading-wrapper {
  background-color: var(--ag-modal-overlay-background-color);
}

.ag-overlay-loading-center {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
}

.ag-overlay-no-rows-wrapper.ag-layout-auto-height {
  padding-top: 30px;
}

.ag-loading {
  display: flex;
  height: 100%;
  align-items: center;
}
.ag-ltr .ag-loading {
  padding-left: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-loading {
  padding-right: var(--ag-cell-horizontal-padding);
}

.ag-ltr .ag-loading-icon {
  padding-right: var(--ag-cell-widget-spacing);
}
.ag-rtl .ag-loading-icon {
  padding-left: var(--ag-cell-widget-spacing);
}

.ag-icon-loading {
  animation-name: spin;
  animation-duration: 1000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.ag-floating-top {
  border-bottom: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-floating-bottom {
  border-top: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-ltr .ag-cell {
  border-right: var(--ag-cell-horizontal-border);
}
.ag-rtl .ag-cell {
  border-left: var(--ag-cell-horizontal-border);
}
.ag-ltr .ag-cell {
  border-right-width: 1px;
}
.ag-rtl .ag-cell {
  border-left-width: 1px;
}

.ag-cell.ag-cell-first-right-pinned:not(.ag-cell-range-left):not(.ag-cell-range-single-cell) {
  border-left: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(.ag-cell-range-single-cell) {
  border-right: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-cell-range-selected:not(.ag-cell-focus),
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-single-cell:not(.ag-cell-inline-editing) {
  background-color: var(--ag-range-selection-background-color);
}
.ag-cell-range-selected:not(.ag-cell-focus).ag-cell-range-chart,
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-single-cell:not(.ag-cell-inline-editing).ag-cell-range-chart {
  background-color: var(--ag-range-selection-chart-background-color) !important;
}
.ag-cell-range-selected:not(.ag-cell-focus).ag-cell-range-chart.ag-cell-range-chart-category,
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-single-cell:not(.ag-cell-inline-editing).ag-cell-range-chart.ag-cell-range-chart-category {
  background-color: var(--ag-range-selection-chart-category-background-color) !important;
}

.ag-cell-range-selected-1:not(.ag-cell-focus),
.ag-root:not(.ag-context-menu-open) .ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-1:not(.ag-cell-inline-editing) {
  background-color: var(--ag-range-selection-background-color);
}

.ag-cell-range-selected-2:not(.ag-cell-focus),
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-2 {
  background-color: var(--ag-range-selection-background-color-2);
}

.ag-cell-range-selected-3:not(.ag-cell-focus),
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-3 {
  background-color: var(--ag-range-selection-background-color-3);
}

.ag-cell-range-selected-4:not(.ag-cell-focus),
.ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-4 {
  background-color: var(--ag-range-selection-background-color-4);
}

.ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-top {
  border-top-color: var(--ag-range-selection-border-color);
  border-top-style: var(--ag-range-selection-border-style);
}
.ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-right {
  border-right-color: var(--ag-range-selection-border-color);
  border-right-style: var(--ag-range-selection-border-style);
}
.ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-bottom {
  border-bottom-color: var(--ag-range-selection-border-color);
  border-bottom-style: var(--ag-range-selection-border-style);
}
.ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-left {
  border-left-color: var(--ag-range-selection-border-color);
  border-left-style: var(--ag-range-selection-border-style);
}

.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
.ag-ltr .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
.ag-ltr .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
.ag-ltr .ag-cell-range-single-cell,
.ag-ltr .ag-cell-range-single-cell.ag-cell-range-handle, .ag-rtl .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
.ag-rtl .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
.ag-rtl .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
.ag-rtl .ag-cell-range-single-cell,
.ag-rtl .ag-cell-range-single-cell.ag-cell-range-handle {
  border: 1px solid;
  border-color: var(--ag-range-selection-border-color);
  border-style: var(--ag-range-selection-border-style);
  outline: initial;
}

.ag-cell.ag-selection-fill-top,
.ag-cell.ag-selection-fill-top.ag-cell-range-selected {
  border-top: 1px dashed;
  border-top-color: var(--ag-range-selection-border-color);
}

.ag-ltr .ag-cell.ag-selection-fill-right,
.ag-ltr .ag-cell.ag-selection-fill-right.ag-cell-range-selected {
  border-right: 1px dashed var(--ag-range-selection-border-color) !important;
}
.ag-rtl .ag-cell.ag-selection-fill-right,
.ag-rtl .ag-cell.ag-selection-fill-right.ag-cell-range-selected {
  border-left: 1px dashed var(--ag-range-selection-border-color) !important;
}

.ag-cell.ag-selection-fill-bottom,
.ag-cell.ag-selection-fill-bottom.ag-cell-range-selected {
  border-bottom: 1px dashed;
  border-bottom-color: var(--ag-range-selection-border-color);
}

.ag-ltr .ag-cell.ag-selection-fill-left,
.ag-ltr .ag-cell.ag-selection-fill-left.ag-cell-range-selected {
  border-left: 1px dashed var(--ag-range-selection-border-color) !important;
}
.ag-rtl .ag-cell.ag-selection-fill-left,
.ag-rtl .ag-cell.ag-selection-fill-left.ag-cell-range-selected {
  border-right: 1px dashed var(--ag-range-selection-border-color) !important;
}

.ag-fill-handle, .ag-range-handle {
  position: absolute;
  width: 6px;
  height: 6px;
  bottom: -1px;
  background-color: var(--ag-range-selection-border-color);
}
.ag-ltr .ag-fill-handle, .ag-ltr .ag-range-handle {
  right: -1px;
}
.ag-rtl .ag-fill-handle, .ag-rtl .ag-range-handle {
  left: -1px;
}

.ag-fill-handle {
  cursor: cell;
}

.ag-range-handle {
  cursor: nwse-resize;
}

.ag-cell-inline-editing {
  border-color: var(--ag-input-focus-border-color) !important;
}

.ag-menu {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
  padding: 0;
}

.ag-menu-list {
  cursor: default;
  padding: var(--ag-grid-size) 0;
}

.ag-menu-separator {
  height: calc(var(--ag-grid-size) * 2 + 1px);
}

.ag-menu-separator-part::after {
  content: "";
  display: block;
  border-top: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-menu-option-active, .ag-compact-menu-option-active {
  background-color: var(--ag-row-hover-color);
}

.ag-menu-option-part, .ag-compact-menu-option-part {
  line-height: var(--ag-icon-size);
  padding: calc(var(--ag-grid-size) + 2px) 0;
}

.ag-menu-option-disabled, .ag-compact-menu-option-disabled {
  opacity: 0.5;
}

.ag-menu-option-icon, .ag-compact-menu-option-icon {
  width: var(--ag-icon-size);
}
.ag-ltr .ag-menu-option-icon, .ag-ltr .ag-compact-menu-option-icon {
  padding-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-menu-option-icon, .ag-rtl .ag-compact-menu-option-icon {
  padding-right: calc(var(--ag-grid-size) * 2);
}

.ag-menu-option-text, .ag-compact-menu-option-text {
  padding-left: calc(var(--ag-grid-size) * 2);
  padding-right: calc(var(--ag-grid-size) * 2);
}

.ag-ltr .ag-menu-option-shortcut, .ag-ltr .ag-compact-menu-option-shortcut {
  padding-right: var(--ag-grid-size);
}
.ag-rtl .ag-menu-option-shortcut, .ag-rtl .ag-compact-menu-option-shortcut {
  padding-left: var(--ag-grid-size);
}

.ag-menu-option-popup-pointer, .ag-compact-menu-option-popup-pointer {
  padding-right: var(--ag-grid-size);
}

.ag-tabs {
  min-width: var(--ag-tab-min-width);
}

.ag-tabs-header {
  width: 100%;
  display: flex;
}

.ag-tab {
  border-bottom: var(--ag-selected-tab-underline-width) solid transparent;
  transition: border-bottom var(--ag-selected-tab-underline-transition-speed);
  display: flex;
  flex: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.ag-keyboard-focus .ag-tab:focus {
  outline: none;
}
.ag-keyboard-focus .ag-tab:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-tab-selected {
  border-bottom-color: var(--ag-selected-tab-underline-color);
}

.ag-menu-header {
  color: var(--ag-secondary-foreground-color);
}

.ag-filter-separator {
  border-top: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-filter-select .ag-picker-field-wrapper {
  width: 0;
}

.ag-filter-condition-operator {
  height: 17px;
}

.ag-ltr .ag-filter-condition-operator-or {
  margin-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-filter-condition-operator-or {
  margin-right: calc(var(--ag-grid-size) * 2);
}

.ag-set-filter-select-all {
  padding-top: var(--ag-widget-container-vertical-padding);
}

.ag-set-filter-list, .ag-filter-no-matches {
  height: calc(var(--ag-list-item-height) * 6);
}

.ag-set-filter-tree-list {
  height: calc(var(--ag-list-item-height) * 10);
}

.ag-set-filter-filter {
  margin-top: var(--ag-widget-container-vertical-padding);
  margin-left: var(--ag-widget-container-horizontal-padding);
  margin-right: var(--ag-widget-container-horizontal-padding);
}

.ag-filter-to {
  margin-top: var(--ag-widget-vertical-spacing);
}

.ag-mini-filter {
  margin: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
}

.ag-set-filter-item {
  margin: 0px var(--ag-widget-container-horizontal-padding);
}

.ag-ltr .ag-set-filter-add-group-indent {
  margin-left: calc(var(--ag-widget-container-horizontal-padding) + var(--ag-icon-size) + var(--ag-grid-size) * 2);
}
.ag-rtl .ag-set-filter-add-group-indent {
  margin-right: calc(var(--ag-widget-container-horizontal-padding) + var(--ag-icon-size) + var(--ag-grid-size) * 2);
}

.ag-ltr .ag-set-filter-indent-1 {
  padding-left: calc(1 * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-indent-1 {
  padding-right: calc(1 * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-indent-2 {
  padding-left: calc(2 * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-indent-2 {
  padding-right: calc(2 * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-indent-3 {
  padding-left: calc(3 * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-indent-3 {
  padding-right: calc(3 * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-indent-4 {
  padding-left: calc(4 * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-indent-4 {
  padding-right: calc(4 * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-indent-5 {
  padding-left: calc(5 * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-indent-5 {
  padding-right: calc(5 * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-indent-6 {
  padding-left: calc(6 * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-indent-6 {
  padding-right: calc(6 * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-indent-7 {
  padding-left: calc(7 * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-indent-7 {
  padding-right: calc(7 * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-indent-8 {
  padding-left: calc(8 * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-indent-8 {
  padding-right: calc(8 * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-indent-9 {
  padding-left: calc(9 * var(--ag-set-filter-indent-size));
}
.ag-rtl .ag-set-filter-indent-9 {
  padding-right: calc(9 * var(--ag-set-filter-indent-size));
}

.ag-ltr .ag-set-filter-group-icons {
  margin-right: var(--ag-widget-container-horizontal-padding);
}
.ag-rtl .ag-set-filter-group-icons {
  margin-left: var(--ag-widget-container-horizontal-padding);
}

.ag-keyboard-focus .ag-filter-virtual-list-item:focus {
  outline: none;
}
.ag-keyboard-focus .ag-filter-virtual-list-item:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 1px;
  left: 1px;
  display: block;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-filter-apply-panel {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-filter-apply-panel-button {
  line-height: 1.5;
}
.ag-ltr .ag-filter-apply-panel-button {
  margin-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-filter-apply-panel-button {
  margin-right: calc(var(--ag-grid-size) * 2);
}

.ag-simple-filter-body-wrapper {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  padding-bottom: calc(var(--ag-widget-container-vertical-padding) - var(--ag-widget-vertical-spacing));
  overflow-y: auto;
  min-height: calc(var(--ag-list-item-height) + var(--ag-widget-container-vertical-padding) + var(--ag-widget-vertical-spacing));
}
.ag-simple-filter-body-wrapper > * {
  margin-bottom: var(--ag-widget-vertical-spacing);
}
.ag-simple-filter-body-wrapper .ag-resizer-wrapper {
  margin: 0;
}

.ag-menu:not(.ag-tabs) .ag-filter .ag-simple-filter-body-wrapper,
.ag-menu:not(.ag-tabs) .ag-filter > *:not(.ag-filter-wrapper) {
  min-width: calc(var(--ag-menu-min-width) - 2px);
}

.ag-filter-no-matches {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
}

.ag-multi-filter-menu-item {
  margin: var(--ag-grid-size) 0;
}

.ag-multi-filter-group-title-bar {
  padding: calc(var(--ag-grid-size) * 2) var(--ag-grid-size);
  background-color: transparent;
}

.ag-group-filter-field-select-wrapper {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  padding-bottom: calc(var(--ag-widget-container-vertical-padding) - var(--ag-widget-vertical-spacing));
}
.ag-group-filter-field-select-wrapper > * {
  margin-bottom: var(--ag-widget-vertical-spacing);
}

.ag-keyboard-focus .ag-multi-filter-group-title-bar:focus {
  outline: none;
}
.ag-keyboard-focus .ag-multi-filter-group-title-bar:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-side-bar {
  position: relative;
}

.ag-tool-panel-wrapper {
  width: var(--ag-side-bar-panel-width);
  background-color: var(--ag-control-panel-background-color);
}

.ag-side-buttons {
  padding-top: calc(var(--ag-grid-size) * 4);
  width: calc(var(--ag-icon-size) + 4px);
  position: relative;
  overflow: hidden;
}

button.ag-side-button-button {
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  background: transparent;
  padding: calc(var(--ag-grid-size) * 2) 0 calc(var(--ag-grid-size) * 2) 0;
  width: 100%;
  margin: 0;
  min-height: calc(var(--ag-grid-size) * 18);
  background-position-y: center;
  background-position-x: center;
  background-repeat: no-repeat;
  border: none;
  border-top: var(--ag-borders-side-button) var(--ag-border-color);
  border-bottom: var(--ag-borders-side-button) var(--ag-border-color);
}
button.ag-side-button-button:focus {
  box-shadow: none;
}

.ag-keyboard-focus .ag-side-button-button:focus {
  outline: none;
}
.ag-keyboard-focus .ag-side-button-button:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-selected button.ag-side-button-button {
  background-color: var(--ag-side-button-selected-background-color);
}

.ag-side-button-icon-wrapper {
  margin-bottom: 3px;
}

.ag-ltr .ag-side-bar-left,
.ag-rtl .ag-side-bar-right {
  border-right: var(--ag-borders) var(--ag-border-color);
}
.ag-ltr .ag-side-bar-left .ag-tool-panel-wrapper,
.ag-rtl .ag-side-bar-right .ag-tool-panel-wrapper {
  border-left: var(--ag-borders) var(--ag-border-color);
}
.ag-ltr .ag-side-bar-left .ag-side-button-button,
.ag-rtl .ag-side-bar-right .ag-side-button-button {
  border-right: var(--ag-selected-tab-underline-width) solid transparent;
  transition: border-right var(--ag-selected-tab-underline-transition-speed);
}
.ag-ltr .ag-side-bar-left .ag-selected .ag-side-button-button,
.ag-rtl .ag-side-bar-right .ag-selected .ag-side-button-button {
  border-right-color: var(--ag-selected-tab-underline-color);
}

.ag-rtl .ag-side-bar-left,
.ag-ltr .ag-side-bar-right {
  border-left: var(--ag-borders) var(--ag-border-color);
}
.ag-rtl .ag-side-bar-left .ag-tool-panel-wrapper,
.ag-ltr .ag-side-bar-right .ag-tool-panel-wrapper {
  border-right: var(--ag-borders) var(--ag-border-color);
}
.ag-rtl .ag-side-bar-left .ag-side-button-button,
.ag-ltr .ag-side-bar-right .ag-side-button-button {
  border-left: var(--ag-selected-tab-underline-width) solid transparent;
  transition: border-left var(--ag-selected-tab-underline-transition-speed);
}
.ag-rtl .ag-side-bar-left .ag-selected .ag-side-button-button,
.ag-ltr .ag-side-bar-right .ag-selected .ag-side-button-button {
  border-left-color: var(--ag-selected-tab-underline-color);
}

.ag-filter-toolpanel-header {
  height: calc(var(--ag-grid-size) * 6);
}

.ag-ltr .ag-filter-toolpanel-header,
.ag-ltr .ag-filter-toolpanel-search {
  padding-left: var(--ag-grid-size);
}
.ag-rtl .ag-filter-toolpanel-header,
.ag-rtl .ag-filter-toolpanel-search {
  padding-right: var(--ag-grid-size);
}

.ag-keyboard-focus .ag-filter-toolpanel-header:focus {
  outline: none;
}
.ag-keyboard-focus .ag-filter-toolpanel-header:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-filter-toolpanel-group.ag-has-filter > .ag-group-title-bar .ag-group-title::after {
  font-family: var(--ag-icon-font-family);
  font-size: var(--ag-icon-size);
  line-height: var(--ag-icon-size);
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: var(--ag-icon-font-code-filter, "\f115");
  position: absolute;
}
.ag-ltr .ag-filter-toolpanel-group.ag-has-filter > .ag-group-title-bar .ag-group-title::after {
  padding-left: var(--ag-grid-size);
}
.ag-rtl .ag-filter-toolpanel-group.ag-has-filter > .ag-group-title-bar .ag-group-title::after {
  padding-right: var(--ag-grid-size);
}

.ag-filter-toolpanel-group-level-0-header {
  height: calc(var(--ag-grid-size) * 8);
}

.ag-filter-toolpanel-group-item {
  margin-top: calc(var(--ag-grid-size) * 0.5);
  margin-bottom: calc(var(--ag-grid-size) * 0.5);
}

.ag-filter-toolpanel-search {
  height: var(--ag-header-height);
}

.ag-filter-toolpanel-search-input {
  flex-grow: 1;
  height: calc(var(--ag-grid-size) * 4);
}
.ag-ltr .ag-filter-toolpanel-search-input {
  margin-right: var(--ag-grid-size);
}
.ag-rtl .ag-filter-toolpanel-search-input {
  margin-left: var(--ag-grid-size);
}

.ag-filter-toolpanel-group-level-0 {
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-ltr .ag-filter-toolpanel-expand,
.ag-ltr .ag-filter-toolpanel-group-title-bar-icon {
  margin-right: var(--ag-grid-size);
}
.ag-rtl .ag-filter-toolpanel-expand,
.ag-rtl .ag-filter-toolpanel-group-title-bar-icon {
  margin-left: var(--ag-grid-size);
}

.ag-filter-toolpanel-group-level-1 .ag-filter-toolpanel-group-level-1-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-1 .ag-filter-toolpanel-group-level-2-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 1 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-1 .ag-filter-toolpanel-group-level-2-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 1 + var(--ag-grid-size));
}

.ag-filter-toolpanel-group-level-2 .ag-filter-toolpanel-group-level-2-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-2 .ag-filter-toolpanel-group-level-3-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 2 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-2 .ag-filter-toolpanel-group-level-3-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 2 + var(--ag-grid-size));
}

.ag-filter-toolpanel-group-level-3 .ag-filter-toolpanel-group-level-3-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-3 .ag-filter-toolpanel-group-level-4-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 3 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-3 .ag-filter-toolpanel-group-level-4-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 3 + var(--ag-grid-size));
}

.ag-filter-toolpanel-group-level-4 .ag-filter-toolpanel-group-level-4-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-4 .ag-filter-toolpanel-group-level-5-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 4 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-4 .ag-filter-toolpanel-group-level-5-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 4 + var(--ag-grid-size));
}

.ag-filter-toolpanel-group-level-5 .ag-filter-toolpanel-group-level-5-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-5 .ag-filter-toolpanel-group-level-6-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 5 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-5 .ag-filter-toolpanel-group-level-6-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 5 + var(--ag-grid-size));
}

.ag-filter-toolpanel-group-level-6 .ag-filter-toolpanel-group-level-6-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-6 .ag-filter-toolpanel-group-level-7-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 6 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-6 .ag-filter-toolpanel-group-level-7-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 6 + var(--ag-grid-size));
}

.ag-filter-toolpanel-group-level-7 .ag-filter-toolpanel-group-level-7-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-7 .ag-filter-toolpanel-group-level-8-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 7 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-7 .ag-filter-toolpanel-group-level-8-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 7 + var(--ag-grid-size));
}

.ag-filter-toolpanel-group-level-8 .ag-filter-toolpanel-group-level-8-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-8 .ag-filter-toolpanel-group-level-9-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 8 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-8 .ag-filter-toolpanel-group-level-9-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 8 + var(--ag-grid-size));
}

.ag-filter-toolpanel-group-level-9 .ag-filter-toolpanel-group-level-9-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-9 .ag-filter-toolpanel-group-level-10-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 9 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-9 .ag-filter-toolpanel-group-level-10-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 9 + var(--ag-grid-size));
}

.ag-filter-toolpanel-group-level-10 .ag-filter-toolpanel-group-level-10-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-ltr .ag-filter-toolpanel-group-level-10 .ag-filter-toolpanel-group-level-11-header {
  padding-left: calc(var(--ag-filter-tool-panel-group-indent) * 10 + var(--ag-grid-size));
}
.ag-rtl .ag-filter-toolpanel-group-level-10 .ag-filter-toolpanel-group-level-11-header {
  padding-right: calc(var(--ag-filter-tool-panel-group-indent) * 10 + var(--ag-grid-size));
}

.ag-filter-toolpanel-instance-header.ag-filter-toolpanel-group-level-1-header {
  padding-left: var(--ag-grid-size);
}

.ag-filter-toolpanel-instance-filter {
  border-bottom: var(--ag-borders) var(--ag-border-color);
  border-top: var(--ag-borders) var(--ag-border-color);
  margin-top: var(--ag-grid-size);
}

.ag-ltr .ag-filter-toolpanel-instance-header-icon {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-filter-toolpanel-instance-header-icon {
  margin-right: var(--ag-grid-size);
}

.ag-set-filter-group-icons {
  color: var(--ag-secondary-foreground-color);
}

.ag-pivot-mode-panel {
  min-height: var(--ag-header-height);
  height: var(--ag-header-height);
  display: flex;
}

.ag-pivot-mode-select {
  display: flex;
  align-items: center;
}
.ag-ltr .ag-pivot-mode-select {
  margin-left: var(--ag-widget-container-horizontal-padding);
}
.ag-rtl .ag-pivot-mode-select {
  margin-right: var(--ag-widget-container-horizontal-padding);
}

.ag-keyboard-focus .ag-column-select-header:focus {
  outline: none;
}
.ag-keyboard-focus .ag-column-select-header:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-column-select-header {
  height: var(--ag-header-height);
  align-items: center;
  padding: 0 var(--ag-widget-container-horizontal-padding);
  border-bottom: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-column-panel-column-select {
  border-bottom: var(--ag-borders-secondary) var(--ag-secondary-border-color);
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-column-group-icons,
.ag-column-select-header-icon {
  color: var(--ag-secondary-foreground-color);
}

.ag-column-select-list .ag-list-item-hovered::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--ag-range-selection-border-color);
}
.ag-column-select-list .ag-item-highlight-top::after {
  top: 0;
}
.ag-column-select-list .ag-item-highlight-bottom::after {
  bottom: 0;
}

.ag-header, .ag-advanced-filter-header {
  background-color: var(--ag-header-background-color);
  border-bottom: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-header-row {
  color: var(--ag-header-foreground-color);
  height: var(--ag-header-height);
}

.ag-pinned-right-header {
  border-left: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-pinned-left-header {
  border-right: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-ltr .ag-header-cell:not(.ag-right-aligned-header) .ag-header-label-icon {
  margin-left: var(--ag-grid-size);
}
.ag-rtl .ag-header-cell:not(.ag-right-aligned-header) .ag-header-label-icon {
  margin-right: var(--ag-grid-size);
}

.ag-ltr .ag-header-cell.ag-right-aligned-header .ag-header-label-icon {
  margin-right: var(--ag-grid-size);
}
.ag-rtl .ag-header-cell.ag-right-aligned-header .ag-header-label-icon {
  margin-left: var(--ag-grid-size);
}

.ag-header-cell,
.ag-header-group-cell {
  padding-left: var(--ag-cell-horizontal-padding);
  padding-right: var(--ag-cell-horizontal-padding);
}
.ag-header-cell.ag-header-cell-moving,
.ag-header-group-cell.ag-header-cell-moving {
  background-color: var(--ag-header-cell-moving-background-color);
}

.ag-ltr .ag-header-group-cell-label.ag-sticky-label {
  left: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-header-group-cell-label.ag-sticky-label {
  right: var(--ag-cell-horizontal-padding);
}

.ag-header-cell.ag-header-span-height::after,
.ag-header-cell.ag-header-span-height .ag-header-cell-resize::after {
  height: calc(100% - var(--ag-grid-size) * 4);
  top: calc(var(--ag-grid-size) * 2);
}

.ag-keyboard-focus .ag-header-cell:focus {
  outline: none;
}
.ag-keyboard-focus .ag-header-cell:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}
.ag-keyboard-focus .ag-header-group-cell:focus {
  outline: none;
}
.ag-keyboard-focus .ag-header-group-cell:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}
.ag-keyboard-focus .ag-advanced-filter-header-cell:focus {
  outline: none;
}
.ag-keyboard-focus .ag-advanced-filter-header-cell:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-header-icon {
  color: var(--ag-secondary-foreground-color);
}

.ag-header-expand-icon {
  cursor: pointer;
}
.ag-ltr .ag-header-expand-icon {
  padding-left: 4px;
}
.ag-rtl .ag-header-expand-icon {
  padding-right: 4px;
}

.ag-header-row:not(:first-child) .ag-header-cell:not(.ag-header-span-height.ag-header-span-total),
.ag-header-row:not(:first-child) .ag-header-group-cell.ag-header-group-cell-with-group {
  border-top: var(--ag-borders-critical) var(--ag-border-color);
}

.ag-header-group-cell:not(.ag-column-resizing) + .ag-header-group-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover, .ag-header-group-cell:not(.ag-column-resizing) + .ag-header-group-cell:not(.ag-column-hover).ag-column-resizing,
.ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover,
.ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover).ag-column-resizing,
.ag-header-group-cell:first-of-type:not(.ag-header-cell-moving):hover,
.ag-header-group-cell:first-of-type.ag-column-resizing,
.ag-header-cell:not(.ag-column-hover):first-of-type:not(.ag-header-cell-moving):hover,
.ag-header-cell:not(.ag-column-hover):first-of-type.ag-column-resizing {
  background-color: var(--ag-header-cell-hover-background-color);
}

.ag-header-cell::after,
.ag-header-group-cell::after {
  content: "";
  position: absolute;
  z-index: 1;
  display: var(--ag-header-column-separator-display);
  width: var(--ag-header-column-separator-width);
  height: var(--ag-header-column-separator-height);
  top: calc(50% - var(--ag-header-column-separator-height) * 0.5);
  background-color: var(--ag-header-column-separator-color);
}
.ag-ltr .ag-header-cell::after,
.ag-ltr .ag-header-group-cell::after {
  right: 0;
}
.ag-rtl .ag-header-cell::after,
.ag-rtl .ag-header-group-cell::after {
  left: 0;
}

.ag-header-cell-resize {
  display: flex;
  align-items: center;
}

.ag-header-cell-resize::after {
  content: "";
  position: absolute;
  z-index: 1;
  display: var(--ag-header-column-resize-handle-display);
  width: var(--ag-header-column-resize-handle-width);
  height: var(--ag-header-column-resize-handle-height);
  top: calc(50% - var(--ag-header-column-resize-handle-height) * 0.5);
  background-color: var(--ag-header-column-resize-handle-color);
}

.ag-header-cell.ag-header-span-height::after,
.ag-header-cell.ag-header-span-height .ag-header-cell-resize::after {
  height: calc(100% - var(--ag-grid-size) * 4);
  top: calc(var(--ag-grid-size) * 2);
}

.ag-ltr .ag-header-viewport .ag-header-cell-resize::after {
  left: calc(50% - var(--ag-header-column-resize-handle-width));
}
.ag-rtl .ag-header-viewport .ag-header-cell-resize::after {
  right: calc(50% - var(--ag-header-column-resize-handle-width));
}

.ag-pinned-left-header .ag-header-cell-resize::after {
  left: calc(50% - var(--ag-header-column-resize-handle-width));
}

.ag-pinned-right-header .ag-header-cell-resize::after {
  left: 50%;
}

.ag-ltr .ag-header-select-all {
  margin-right: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-header-select-all {
  margin-left: var(--ag-cell-horizontal-padding);
}

.ag-ltr .ag-floating-filter-button {
  margin-left: calc(var(--ag-grid-size) * 3);
}
.ag-rtl .ag-floating-filter-button {
  margin-right: calc(var(--ag-grid-size) * 3);
}

.ag-floating-filter-button-button {
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  appearance: none;
  background: transparent;
  border: none;
  height: var(--ag-icon-size);
  padding: 0;
  width: var(--ag-icon-size);
}

.ag-filter-loading {
  background-color: var(--ag-control-panel-background-color);
  height: 100%;
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  position: absolute;
  width: 100%;
  z-index: 1;
}

.ag-paging-panel {
  border-top: 1px solid;
  border-top-color: var(--ag-border-color);
  color: var(--ag-secondary-foreground-color);
  height: var(--ag-header-height);
}
.ag-paging-panel > * {
  margin: 0 var(--ag-cell-horizontal-padding);
}

.ag-paging-button {
  cursor: pointer;
}

.ag-paging-button.ag-disabled {
  cursor: default;
  color: var(--ag-disabled-foreground-color);
}

.ag-keyboard-focus .ag-paging-button:focus {
  outline: none;
}
.ag-keyboard-focus .ag-paging-button:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 0px;
  left: 0px;
  display: block;
  width: calc(100% - 0px);
  height: calc(100% - 0px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-paging-button, .ag-paging-description {
  margin: 0 var(--ag-grid-size);
}

.ag-status-bar {
  border-top: var(--ag-borders) var(--ag-border-color);
  color: var(--ag-disabled-foreground-color);
  padding-right: calc(var(--ag-grid-size) * 4);
  padding-left: calc(var(--ag-grid-size) * 4);
  line-height: 1.5;
}

.ag-status-name-value-value {
  color: var(--ag-foreground-color);
}

.ag-status-bar-center {
  text-align: center;
}

.ag-status-name-value {
  margin-left: var(--ag-grid-size);
  margin-right: var(--ag-grid-size);
  padding-top: calc(var(--ag-grid-size) * 2);
  padding-bottom: calc(var(--ag-grid-size) * 2);
}

.ag-column-drop-cell {
  background: var(--ag-chip-background-color);
  border-radius: calc(var(--ag-grid-size) * 4);
  height: calc(var(--ag-grid-size) * 4);
  padding: 0 calc(var(--ag-grid-size) * 0.5);
  border: 1px solid transparent;
}

.ag-keyboard-focus .ag-column-drop-cell:focus {
  outline: none;
}
.ag-keyboard-focus .ag-column-drop-cell:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 2px;
  left: 2px;
  display: block;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-column-drop-cell-text {
  margin: 0 var(--ag-grid-size);
}

.ag-column-drop-cell-button {
  min-width: calc(var(--ag-grid-size) * 4);
  margin: 0 calc(var(--ag-grid-size) * 0.5);
  color: var(--ag-secondary-foreground-color);
}

.ag-column-drop-cell-drag-handle {
  margin-left: calc(var(--ag-grid-size) * 2);
}

.ag-column-drop-cell-ghost {
  opacity: 0.5;
}

.ag-column-drop-horizontal {
  background-color: var(--ag-control-panel-background-color);
  color: var(--ag-secondary-foreground-color);
  height: var(--ag-row-height);
  border-bottom: var(--ag-borders) var(--ag-border-color);
}
.ag-ltr .ag-column-drop-horizontal {
  padding-left: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-column-drop-horizontal {
  padding-right: var(--ag-cell-horizontal-padding);
}

.ag-ltr .ag-column-drop-horizontal-half-width:not(:last-child) {
  border-right: var(--ag-borders) var(--ag-border-color);
}
.ag-rtl .ag-column-drop-horizontal-half-width:not(:last-child) {
  border-left: var(--ag-borders) var(--ag-border-color);
}

.ag-column-drop-horizontal-cell-separator {
  margin: 0 var(--ag-grid-size);
  color: var(--ag-secondary-foreground-color);
}

.ag-column-drop-horizontal-empty-message {
  color: var(--ag-disabled-foreground-color);
}

.ag-ltr .ag-column-drop-horizontal-icon {
  margin-right: var(--ag-cell-horizontal-padding);
}
.ag-rtl .ag-column-drop-horizontal-icon {
  margin-left: var(--ag-cell-horizontal-padding);
}

.ag-column-drop-vertical-list {
  padding-bottom: var(--ag-grid-size);
  padding-right: var(--ag-grid-size);
  padding-left: var(--ag-grid-size);
}

.ag-column-drop-vertical-cell {
  margin-top: var(--ag-grid-size);
}

.ag-column-drop-vertical {
  min-height: 50px;
  border-bottom: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}
.ag-column-drop-vertical.ag-last-column-drop {
  border-bottom: none;
}

.ag-column-drop-vertical-icon {
  margin-left: var(--ag-grid-size);
  margin-right: var(--ag-grid-size);
}

.ag-column-drop-vertical-empty-message {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
  color: var(--ag-disabled-foreground-color);
  margin-top: var(--ag-grid-size);
}

.ag-select-agg-func-popup {
  border: var(--ag-borders) var(--ag-border-color);
  background: var(--ag-background-color);
  border-radius: var(--ag-card-radius);
  box-shadow: var(--ag-card-shadow);
  padding: var(--ag-grid-size);
  background: var(--ag-background-color);
  height: calc(var(--ag-grid-size) * 5 * 3.5);
  padding: 0;
}

.ag-select-agg-func-virtual-list-item {
  cursor: default;
}
.ag-ltr .ag-select-agg-func-virtual-list-item {
  padding-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-select-agg-func-virtual-list-item {
  padding-right: calc(var(--ag-grid-size) * 2);
}
.ag-select-agg-func-virtual-list-item:hover {
  background-color: var(--ag-selected-row-background-color);
}

.ag-keyboard-focus .ag-select-agg-func-virtual-list-item:focus {
  outline: none;
}
.ag-keyboard-focus .ag-select-agg-func-virtual-list-item:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 1px;
  left: 1px;
  display: block;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-sort-indicator-container {
  display: flex;
}

.ag-ltr .ag-sort-indicator-icon {
  padding-left: var(--ag-grid-size);
}
.ag-rtl .ag-sort-indicator-icon {
  padding-right: var(--ag-grid-size);
}

.ag-chart {
  position: relative;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.ag-chart-components-wrapper {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  overflow: hidden;
}

.ag-chart-title-edit {
  position: absolute;
  display: none;
  top: 0;
  left: 0;
  text-align: center;
}

.ag-chart-title-edit.currently-editing {
  display: inline-block;
}

.ag-chart-canvas-wrapper {
  position: relative;
  flex: 1 1 auto;
  overflow: hidden;
}

.ag-charts-canvas {
  display: block;
}

.ag-chart-menu {
  position: absolute;
  top: 10px;
  width: 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.ag-ltr .ag-chart-menu {
  right: 20px;
}
.ag-rtl .ag-chart-menu {
  left: 20px;
}

.ag-chart-docked-container {
  position: relative;
  width: 0;
  min-width: 0;
  transition: min-width 0.4s;
}

.ag-chart-menu-hidden ~ .ag-chart-docked-container {
  max-width: 0;
  overflow: hidden;
}

.ag-chart-tabbed-menu {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ag-chart-tabbed-menu-header {
  flex: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}

.ag-chart-tabbed-menu-body {
  display: flex;
  flex: 1 1 auto;
  align-items: stretch;
  overflow: hidden;
}

.ag-chart-tab {
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
}

.ag-chart-settings {
  overflow-x: hidden;
}

.ag-chart-settings-wrapper {
  position: relative;
  flex-direction: column;
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}

.ag-chart-settings-nav-bar {
  display: flex;
  align-items: center;
  width: 100%;
  height: 30px;
  padding: 0 10px;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-chart-settings-card-selector {
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex: 1 1 auto;
  height: 100%;
  padding: 0 10px;
}

.ag-chart-settings-card-item {
  cursor: pointer;
  width: 10px;
  height: 10px;
  background-color: #000;
  position: relative;
}
.ag-chart-settings-card-item.ag-not-selected {
  opacity: 0.2;
}
.ag-chart-settings-card-item::before {
  content: " ";
  display: block;
  position: absolute;
  background-color: transparent;
  left: 50%;
  top: 50%;
  margin-left: -10px;
  margin-top: -10px;
  width: 20px;
  height: 20px;
}

.ag-chart-settings-prev,
.ag-chart-settings-next {
  position: relative;
  flex: none;
}

.ag-chart-settings-prev-button,
.ag-chart-settings-next-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  opacity: 0;
}

.ag-chart-settings-mini-charts-container {
  position: relative;
  flex: 1 1 auto;
  overflow-x: hidden;
  overflow-y: auto;
}

.ag-chart-settings-mini-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  overflow: hidden;
}
.ag-chart-settings-mini-wrapper.ag-animating {
  transition: left 0.3s;
  transition-timing-function: ease-in-out;
}

.ag-chart-mini-thumbnail {
  cursor: pointer;
}

.ag-chart-mini-thumbnail-canvas {
  display: block;
}

.ag-chart-data-wrapper,
.ag-chart-format-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.ag-chart-data-wrapper {
  height: 100%;
  overflow-y: auto;
}

.ag-chart-data-section,
.ag-chart-format-section {
  display: flex;
  margin: 0;
}

.ag-chart-empty-text {
  display: flex;
  top: 0;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.ag-chart .ag-chart-menu {
  display: none;
}

.ag-chart-menu-hidden:hover .ag-chart-menu {
  display: block;
}

.ag-chart .ag-chart-tool-panel-button-enable .ag-chart-menu {
  display: flex;
  flex-direction: row;
  overflow: auto;
  top: 5px;
  gap: calc(var(--ag-grid-size) * 3 - 8px);
  width: auto;
}
.ag-ltr .ag-chart .ag-chart-tool-panel-button-enable .ag-chart-menu {
  right: calc(var(--ag-cell-horizontal-padding) + var(--ag-grid-size) - 4px);
  justify-content: right;
}
.ag-rtl .ag-chart .ag-chart-tool-panel-button-enable .ag-chart-menu {
  left: calc(var(--ag-cell-horizontal-padding) + var(--ag-grid-size) - 4px);
  justify-content: left;
}

.ag-chart-menu-close {
  display: none;
}

.ag-chart-tool-panel-button-enable .ag-chart-menu-close {
  position: absolute;
  top: 50%;
  transition: transform 0.33s ease-in-out;
  padding: 0;
  display: block;
  cursor: pointer;
  border: none;
}
.ag-ltr .ag-chart-tool-panel-button-enable .ag-chart-menu-close {
  right: 0px;
}
.ag-rtl .ag-chart-tool-panel-button-enable .ag-chart-menu-close {
  left: 0px;
}
.ag-chart-tool-panel-button-enable .ag-chart-menu-close .ag-icon {
  padding: 14px 5px 14px 2px;
}
.ag-chart-tool-panel-button-enable .ag-chart-menu-close:before {
  content: "";
  position: absolute;
  top: -40px;
  bottom: -40px;
}
.ag-ltr .ag-chart-tool-panel-button-enable .ag-chart-menu-close:before {
  right: 0px;
}
.ag-rtl .ag-chart-tool-panel-button-enable .ag-chart-menu-close:before {
  left: 0px;
}
.ag-ltr .ag-chart-tool-panel-button-enable .ag-chart-menu-close:before {
  left: -10px;
}
.ag-rtl .ag-chart-tool-panel-button-enable .ag-chart-menu-close:before {
  right: -10px;
}
.ag-chart-tool-panel-button-enable .ag-icon-menu {
  display: none;
}

.ag-ltr .ag-chart-tool-panel-button-enable .ag-chart-menu-close {
  transform: translate(3px, -50%);
}
.ag-ltr .ag-chart-tool-panel-button-enable .ag-chart-menu-close:hover {
  transform: translate(0, -50%);
}
.ag-ltr .ag-chart-menu-visible .ag-chart-tool-panel-button-enable .ag-chart-menu-close:hover {
  transform: translate(5px, -50%);
}

.ag-rtl .ag-chart-tool-panel-button-enable .ag-chart-menu-close {
  transform: translate(-3px, -50%);
}
.ag-rtl .ag-chart-tool-panel-button-enable .ag-chart-menu-close:hover {
  transform: translate(0, -50%);
}
.ag-rtl .ag-chart-menu-visible .ag-chart-tool-panel-button-enable .ag-chart-menu-close:hover {
  transform: translate(-5px, -50%);
}

.ag-charts-font-size-color {
  display: flex;
  align-self: stretch;
  justify-content: space-between;
}

.ag-charts-data-group-item {
  position: relative;
}

.ag-chart-menu {
  border-radius: var(--ag-card-radius);
  background: var(--ag-background-color);
}

.ag-chart-menu-icon {
  opacity: 0.5;
  line-height: 24px;
  font-size: 24px;
  width: 24px;
  height: 24px;
  margin: 2px 0;
  cursor: pointer;
  border-radius: var(--ag-card-radius);
  color: var(--ag-secondary-foreground-color);
}
.ag-chart-menu-icon:hover {
  opacity: 1;
}

.ag-chart-mini-thumbnail {
  border: 1px solid var(--ag-secondary-border-color);
  border-radius: 5px;
  margin: 5px;
}
.ag-chart-mini-thumbnail:nth-last-child(3), .ag-chart-mini-thumbnail:nth-last-child(3) ~ .ag-chart-mini-thumbnail {
  margin-left: auto;
  margin-right: auto;
}
.ag-ltr .ag-chart-mini-thumbnail:first-child {
  margin-left: 0;
}
.ag-rtl .ag-chart-mini-thumbnail:first-child {
  margin-right: 0;
}
.ag-ltr .ag-chart-mini-thumbnail:last-child {
  margin-right: 0;
}
.ag-rtl .ag-chart-mini-thumbnail:last-child {
  margin-left: 0;
}
.ag-chart-mini-thumbnail.ag-selected {
  border-color: var(--ag-minichart-selected-chart-color);
}

.ag-chart-settings-card-item {
  background: var(--ag-foreground-color);
  width: 8px;
  height: 8px;
  border-radius: 4px;
}
.ag-chart-settings-card-item.ag-selected {
  background-color: var(--ag-minichart-selected-page-color);
}

.ag-chart-data-column-drag-handle {
  margin-left: var(--ag-grid-size);
}

.ag-charts-settings-group-title-bar,
.ag-charts-data-group-title-bar,
.ag-charts-format-top-level-group-title-bar {
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-charts-settings-group-container {
  padding: var(--ag-grid-size);
}

.ag-charts-data-group-container {
  padding: calc(var(--ag-widget-container-vertical-padding) * 0.5) var(--ag-widget-container-horizontal-padding);
}
.ag-charts-data-group-container .ag-charts-data-group-item:not(.ag-charts-format-sub-level-group) {
  height: var(--ag-list-item-height);
}
.ag-charts-data-group-container .ag-list-item-hovered::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--ag-range-selection-border-color);
}
.ag-charts-data-group-container .ag-item-highlight-top::after {
  top: 0;
}
.ag-charts-data-group-container .ag-item-highlight-bottom::after {
  bottom: 0;
}

.ag-charts-format-top-level-group-container {
  margin-left: calc(var(--ag-grid-size) * 2);
  padding: var(--ag-grid-size);
}

.ag-charts-format-top-level-group-item {
  margin: var(--ag-grid-size) 0;
}

.ag-charts-format-sub-level-group-container {
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  padding-bottom: calc(var(--ag-widget-container-vertical-padding) - var(--ag-widget-vertical-spacing));
}
.ag-charts-format-sub-level-group-container > * {
  margin-bottom: var(--ag-widget-vertical-spacing);
}

.ag-charts-group-container.ag-group-container-horizontal {
  padding: var(--ag-grid-size);
}

.ag-chart-data-section,
.ag-chart-format-section {
  display: flex;
  margin: 0;
}

.ag-chart-menu-panel {
  background-color: var(--ag-control-panel-background-color);
}
.ag-ltr .ag-chart-menu-panel {
  border-left: solid 1px var(--ag-border-color);
}
.ag-rtl .ag-chart-menu-panel {
  border-right: solid 1px var(--ag-border-color);
}

.ag-date-time-list-page-title-bar {
  display: flex;
}

.ag-date-time-list-page-title {
  flex-grow: 1;
  text-align: center;
}

.ag-date-time-list-page-column-labels-row,
.ag-date-time-list-page-entries-row {
  display: flex;
}

.ag-date-time-list-page-column-label,
.ag-date-time-list-page-entry {
  flex-basis: 0;
  flex-grow: 1;
}

.ag-date-time-list-page-entry {
  cursor: pointer;
  text-align: center;
}

.ag-date-time-list-page-column-label {
  text-align: center;
}

.ag-advanced-filter-header {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: var(--ag-cell-horizontal-padding);
  padding-right: var(--ag-cell-horizontal-padding);
}

.ag-advanced-filter {
  display: flex;
  align-items: center;
  width: 100%;
}

.ag-advanced-filter-apply-button, .ag-advanced-filter-builder-button {
  line-height: normal;
  white-space: nowrap;
}
.ag-ltr .ag-advanced-filter-apply-button, .ag-ltr .ag-advanced-filter-builder-button {
  margin-left: calc(var(--ag-grid-size) * 2);
}
.ag-rtl .ag-advanced-filter-apply-button, .ag-rtl .ag-advanced-filter-builder-button {
  margin-right: calc(var(--ag-grid-size) * 2);
}

.ag-advanced-filter-builder-button {
  display: flex;
  align-items: center;
  border: 0;
  background-color: unset;
}
.ag-advanced-filter-builder-button:hover:not(:disabled) {
  background-color: var(--ag-row-hover-color);
}
.ag-advanced-filter-builder-button:not(:disabled) {
  cursor: pointer;
}

.ag-advanced-filter-builder-button-label {
  margin-left: var(--ag-grid-size);
}

.ag-advanced-filter-builder {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 100%;
  background-color: var(--ag-control-panel-background-color);
  display: flex;
  flex-direction: column;
}

.ag-advanced-filter-builder-list {
  flex: 1;
  overflow: auto;
}
.ag-advanced-filter-builder-list .ag-list-item-hovered::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--ag-range-selection-border-color);
}
.ag-advanced-filter-builder-list .ag-item-highlight-top::after {
  top: 0;
}
.ag-advanced-filter-builder-list .ag-item-highlight-bottom::after {
  bottom: 0;
}

.ag-advanced-filter-builder-button-panel {
  display: flex;
  justify-content: flex-end;
  padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding);
  border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
}

.ag-advanced-filter-builder .ag-advanced-filter-builder-button-panel .ag-advanced-filter-builder-apply-button,
.ag-advanced-filter-builder .ag-advanced-filter-builder-button-panel .ag-advanced-filter-builder-cancel-button {
  margin-left: calc(var(--ag-grid-size) * 2);
}

.ag-advanced-filter-builder-item-wrapper {
  display: flex;
  flex: 1 1 auto;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  padding-left: calc(var(--ag-icon-size) / 2);
  padding-right: var(--ag-icon-size);
}

.ag-advanced-filter-builder-item-tree-lines > * {
  width: var(--ag-advanced-filter-builder-indent-size);
}

.ag-advanced-filter-builder-item-tree-lines .ag-advanced-filter-builder-item-tree-line-root {
  width: var(--ag-icon-size);
}
.ag-advanced-filter-builder-item-tree-lines .ag-advanced-filter-builder-item-tree-line-root::before {
  top: 50%;
  height: 50%;
}

.ag-advanced-filter-builder-item-tree-line-horizontal,
.ag-advanced-filter-builder-item-tree-line-vertical,
.ag-advanced-filter-builder-item-tree-line-vertical-top,
.ag-advanced-filter-builder-item-tree-line-vertical-bottom {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}
.ag-advanced-filter-builder-item-tree-line-horizontal::before, .ag-advanced-filter-builder-item-tree-line-horizontal::after,
.ag-advanced-filter-builder-item-tree-line-vertical::before,
.ag-advanced-filter-builder-item-tree-line-vertical::after,
.ag-advanced-filter-builder-item-tree-line-vertical-top::before,
.ag-advanced-filter-builder-item-tree-line-vertical-top::after,
.ag-advanced-filter-builder-item-tree-line-vertical-bottom::before,
.ag-advanced-filter-builder-item-tree-line-vertical-bottom::after {
  content: "";
  position: absolute;
  height: 100%;
}

.ag-advanced-filter-builder-item-tree-line-horizontal::after {
  height: 50%;
  width: calc(var(--ag-advanced-filter-builder-indent-size) - var(--ag-icon-size));
  top: 0;
  left: calc(var(--ag-icon-size) / 2);
  border-bottom: 1px solid;
  border-color: var(--ag-border-color);
}

.ag-advanced-filter-builder-item-tree-line-vertical::before {
  width: calc(var(--ag-advanced-filter-builder-indent-size) - var(--ag-icon-size) / 2);
  top: 0;
  left: calc(var(--ag-icon-size) / 2);
  border-left: 1px solid;
  border-color: var(--ag-border-color);
}

.ag-advanced-filter-builder-item-tree-line-vertical-top::before {
  height: 50%;
  width: calc(var(--ag-advanced-filter-builder-indent-size) - var(--ag-icon-size) / 2);
  top: 0;
  left: calc(var(--ag-icon-size) / 2);
  border-left: 1px solid;
  border-color: var(--ag-border-color);
}

.ag-advanced-filter-builder-item-tree-line-vertical-bottom::before {
  height: calc((100% - 1.5 * var(--ag-icon-size)) / 2);
  width: calc(var(--ag-icon-size) / 2);
  top: calc((100% + 1.5 * var(--ag-icon-size)) / 2);
  left: calc(var(--ag-icon-size) / 2);
  border-left: 1px solid;
  border-color: var(--ag-border-color);
}

.ag-advanced-filter-builder-item-condition {
  padding-top: var(--ag-grid-size);
  padding-bottom: var(--ag-grid-size);
}

.ag-advanced-filter-builder-item,
.ag-advanced-filter-builder-item-condition,
.ag-advanced-filter-builder-pill-wrapper,
.ag-advanced-filter-builder-pill,
.ag-advanced-filter-builder-item-buttons,
.ag-advanced-filter-builder-item-tree-lines {
  display: flex;
  align-items: center;
  height: 100%;
}

.ag-advanced-filter-builder-pill-wrapper {
  margin: 0px var(--ag-grid-size);
}

.ag-advanced-filter-builder-pill {
  position: relative;
  border-radius: var(--ag-border-radius);
  padding: var(--ag-grid-size) calc(var(--ag-grid-size) * 2);
  min-height: calc(100% - var(--ag-grid-size) * 3);
  min-width: calc(var(--ag-grid-size) * 2);
}
.ag-advanced-filter-builder-pill .ag-picker-field-display {
  margin-right: var(--ag-grid-size);
}
.ag-advanced-filter-builder-pill .ag-advanced-filter-builder-value-number {
  font-family: monospace;
  font-weight: 700;
}
.ag-advanced-filter-builder-pill .ag-advanced-filter-builder-value-empty {
  color: var(--ag-disabled-foreground-color);
}

.ag-keyboard-focus .ag-advanced-filter-builder-pill:focus {
  outline: none;
}
.ag-keyboard-focus .ag-advanced-filter-builder-pill:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: -4px;
  left: -4px;
  display: block;
  width: calc(100% - -8px);
  height: calc(100% - -8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}
.ag-keyboard-focus .ag-advanced-filter-builder-item-button:focus {
  outline: none;
}
.ag-keyboard-focus .ag-advanced-filter-builder-item-button:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: -4px;
  left: -4px;
  display: block;
  width: calc(100% - -8px);
  height: calc(100% - -8px);
  border: 1px solid;
  border-color: var(--ag-input-focus-border-color);
}

.ag-advanced-filter-builder-pill-display {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.ag-advanced-filter-builder-join-pill {
  color: var(--ag-foreground-color);
  background-color: var(--ag-advanced-filter-join-pill-color);
  cursor: pointer;
}

.ag-advanced-filter-builder-column-pill {
  color: var(--ag-foreground-color);
  background-color: var(--ag-advanced-filter-column-pill-color);
  cursor: pointer;
}

.ag-advanced-filter-builder-option-pill {
  color: var(--ag-foreground-color);
  background-color: var(--ag-advanced-filter-option-pill-color);
  cursor: pointer;
}

.ag-advanced-filter-builder-value-pill {
  color: var(--ag-foreground-color);
  background-color: var(--ag-advanced-filter-value-pill-color);
  cursor: text;
  max-width: 140px;
}
.ag-advanced-filter-builder-value-pill .ag-advanced-filter-builder-pill-display {
  display: block;
}

.ag-advanced-filter-builder-item-buttons > * {
  margin: 0 calc(var(--ag-grid-size) * 0.5);
}

.ag-advanced-filter-builder-item-button {
  position: relative;
  cursor: pointer;
  color: var(--ag-secondary-foreground-color);
  opacity: 50%;
}

.ag-advanced-filter-builder-item-button-disabled {
  color: var(--ag-disabled-foreground-color);
  cursor: default;
}

.ag-advanced-filter-builder-virtual-list-container {
  top: var(--ag-grid-size);
}

.ag-advanced-filter-builder-virtual-list-item {
  display: flex;
  cursor: default;
  height: var(--ag-list-item-height);
}
.ag-advanced-filter-builder-virtual-list-item:hover {
  background-color: var(--ag-row-hover-color);
}
.ag-advanced-filter-builder-virtual-list-item:hover .ag-advanced-filter-builder-item-button {
  opacity: 100%;
}

.ag-keyboard-focus .ag-advanced-filter-builder-virtual-list-item-highlight .ag-advanced-filter-builder-item-button,
.ag-advanced-filter-builder-validation .ag-advanced-filter-builder-invalid {
  opacity: 100%;
}

.ag-advanced-filter-builder-invalid {
  margin: 0 var(--ag-grid-size);
  color: var(--ag-invalid-color);
  cursor: default;
}

.ag-input-field-input {
  width: 100%;
  min-width: 0;
}

.ag-checkbox-input-wrapper {
  font-family: var(--ag-icon-font-family);
  font-size: var(--ag-icon-size);
  line-height: var(--ag-icon-size);
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-checkbox-background-color);
  border-radius: var(--ag-checkbox-border-radius);
  display: inline-block;
  vertical-align: middle;
  flex: none;
}
.ag-checkbox-input-wrapper input, .ag-checkbox-input-wrapper input {
  -webkit-appearance: none;
  opacity: 0;
  width: 100%;
  height: 100%;
}
.ag-checkbox-input-wrapper:focus-within, .ag-checkbox-input-wrapper:active {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
}
.ag-checkbox-input-wrapper.ag-disabled {
  opacity: 0.5;
}
.ag-checkbox-input-wrapper::after {
  content: var(--ag-icon-font-code-checkbox-unchecked, "\f108");
  color: var(--ag-checkbox-unchecked-color);
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-checkbox-input-wrapper.ag-checked::after {
  content: var(--ag-icon-font-code-checkbox-checked, "\f106");
  color: var(--ag-checkbox-checked-color);
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-checkbox-input-wrapper.ag-indeterminate::after {
  content: var(--ag-icon-font-code-checkbox-indeterminate, "\f107");
  color: var(--ag-checkbox-indeterminate-color);
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.ag-toggle-button-input-wrapper {
  box-sizing: border-box;
  width: var(--ag-toggle-button-width);
  height: var(--ag-toggle-button-height);
  background-color: var(--ag-toggle-button-off-background-color);
  border-radius: calc(var(--ag-toggle-button-height) * 0.5);
  position: relative;
  flex: none;
  border: var(--ag-toggle-button-border-width) solid;
  border-color: var(--ag-toggle-button-off-border-color);
}
.ag-toggle-button-input-wrapper input {
  opacity: 0;
  height: 100%;
  width: 100%;
}
.ag-toggle-button-input-wrapper:focus-within {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
}
.ag-toggle-button-input-wrapper.ag-disabled {
  opacity: 0.5;
}
.ag-toggle-button-input-wrapper.ag-checked {
  background-color: var(--ag-toggle-button-on-background-color);
  border-color: var(--ag-toggle-button-on-border-color);
}
.ag-toggle-button-input-wrapper::before {
  content: " ";
  position: absolute;
  top: calc(0px - var(--ag-toggle-button-border-width));
  left: calc(0px - var(--ag-toggle-button-border-width));
  display: block;
  box-sizing: border-box;
  height: var(--ag-toggle-button-height);
  width: var(--ag-toggle-button-height);
  background-color: var(--ag-toggle-button-switch-background-color);
  border-radius: 100%;
  transition: left 100ms;
  border: var(--ag-toggle-button-border-width) solid;
  border-color: var(--ag-toggle-button-switch-border-color);
}
.ag-toggle-button-input-wrapper.ag-checked::before {
  left: calc(100% - var(--ag-toggle-button-height));
  border-color: var(--ag-toggle-button-on-border-color);
}

.ag-radio-button-input-wrapper {
  font-family: var(--ag-icon-font-family);
  font-size: var(--ag-icon-size);
  line-height: var(--ag-icon-size);
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-checkbox-background-color);
  border-radius: var(--ag-checkbox-border-radius);
  display: inline-block;
  vertical-align: middle;
  flex: none;
  border-radius: var(--ag-icon-size);
}
.ag-radio-button-input-wrapper input, .ag-radio-button-input-wrapper input {
  -webkit-appearance: none;
  opacity: 0;
  width: 100%;
  height: 100%;
}
.ag-radio-button-input-wrapper:focus-within, .ag-radio-button-input-wrapper:active {
  outline: none;
  box-shadow: var(--ag-input-focus-box-shadow);
}
.ag-radio-button-input-wrapper.ag-disabled {
  opacity: 0.5;
}
.ag-radio-button-input-wrapper::after {
  content: var(--ag-icon-font-code-radio-button-off, "\f127");
  color: var(--ag-checkbox-unchecked-color);
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-radio-button-input-wrapper.ag-checked::after {
  content: var(--ag-icon-font-code-radio-button-on, "\f128");
  color: var(--ag-checkbox-checked-color);
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

input[class^=ag-][type=range] {
  -webkit-appearance: none;
  width: 100%;
  height: 100%;
  background: none;
  overflow: visible;
}
input[class^=ag-][type=range]::-webkit-slider-runnable-track {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 3px;
  background-color: var(--ag-border-color);
  border-radius: var(--ag-border-radius);
  border-radius: var(--ag-checkbox-border-radius);
}
input[class^=ag-][type=range]::-moz-range-track {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 3px;
  background-color: var(--ag-border-color);
  border-radius: var(--ag-border-radius);
  border-radius: var(--ag-checkbox-border-radius);
}
input[class^=ag-][type=range]::-ms-track {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 3px;
  background-color: var(--ag-border-color);
  border-radius: var(--ag-border-radius);
  border-radius: var(--ag-checkbox-border-radius);
  color: transparent;
  width: calc(100% - 2px);
}
input[class^=ag-][type=range]::-webkit-slider-thumb {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-background-color);
  border: 1px solid;
  border-color: var(--ag-checkbox-unchecked-color);
  border-radius: var(--ag-icon-size);
  transform: translateY(calc(var(--ag-icon-size) * -0.5 + 1.5px));
}
input[class^=ag-][type=range]::-ms-thumb {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-background-color);
  border: 1px solid;
  border-color: var(--ag-checkbox-unchecked-color);
  border-radius: var(--ag-icon-size);
}
input[class^=ag-][type=range]::-moz-ag-range-thumb {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: var(--ag-icon-size);
  height: var(--ag-icon-size);
  background-color: var(--ag-background-color);
  border: 1px solid;
  border-color: var(--ag-checkbox-unchecked-color);
  border-radius: var(--ag-icon-size);
}
input[class^=ag-][type=range]:focus {
  outline: none;
}
input[class^=ag-][type=range]:focus::-webkit-slider-thumb {
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-checkbox-checked-color);
}
input[class^=ag-][type=range]:focus::-ms-thumb {
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-checkbox-checked-color);
}
input[class^=ag-][type=range]:focus::-moz-ag-range-thumb {
  box-shadow: var(--ag-input-focus-box-shadow);
  border-color: var(--ag-checkbox-checked-color);
}
input[class^=ag-][type=range]:active::-webkit-slider-runnable-track {
  background-color: var(--ag-input-focus-border-color);
}
input[class^=ag-][type=range]:active::-moz-ag-range-track {
  background-color: var(--ag-input-focus-border-color);
}
input[class^=ag-][type=range]:active::-ms-track {
  background-color: var(--ag-input-focus-border-color);
}
input[class^=ag-][type=range]:disabled {
  opacity: 0.5;
}
