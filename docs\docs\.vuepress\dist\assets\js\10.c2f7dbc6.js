(window.webpackJsonp=window.webpackJsonp||[]).push([[10],{331:function(e,t,a){"use strict";a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return m}));var i=a(282);const l=4,n=[{title:"协议单位",formKey:"coms",type:"treeselect",props:{showCheckbox:!0,dataApi:i.P}},{title:"卷烟",formKey:"items",type:"treeselect",props:{showCheckbox:!0,dataApi:i.R}},{title:"发货点",formKey:"contDelivWhseId",type:"select",enumList:[]}],m=[{title:"",type:"checkbox",width:70,minWidth:70,pinned:"left"},{title:"序号",type:"index",width:70,minWidth:70,pinned:"left"},{title:"协议单位",field:"comSname",width:120,pinned:"left"},{title:"是否含托盘",field:"itemDayPlanTrayType",type:"enum",cellRendererParams:{enumData:i.a},width:100,pinned:"left"},{title:"建议发货日",field:"expecDelivDate",width:120},{title:"建议到货日",field:"expecArrivalDate",width:120},{title:"发货点",field:"contDelivWhseName",width:120},{title:"合计",width:120,align:"center",children:[{title:"计划量",field:"sumQtyPlan",width:100,align:"right",isSum:!0,isSwitchUnit:!0,cellRendererParams:{scale:l}},{title:"剩余量",field:"sumQty",width:100,align:"right",isSum:!0,isSwitchUnit:!0,cellRendererParams:{scale:l}},{title:"剩余盘",field:"sumQtyT",width:100,align:"right",isSum:!0,cellRendererParams:{scale:l}}]}];i.a},332:function(e,t,a){"use strict";function i(e){return Promise.resolve({code:1,message:"success",data:{contractList:[{itemDayPlanCode:"SCWP2023022100002000001751",pcomCode:"11510001",pcomSname:"四川",comCode:"11510101",contDelivWhseId:"203700010001001",comContReachWhseId:"450e235854378ffbbe7a72c3da7cb368",itemDayReqCode:"SCWP2023022100002",comSname:"成都",contDelivWhseName:"青岛卷烟厂",comContReachWhseName:"四川省烟草公司成都市公司物流中心仓库",expecDelivDate:"20230301",createTime:"20240112154051",expecArrivalDate:"20230302",itemDayPlanType:"10",itemDayPlanStatus:"1",isMonopolyPuh:null,isCombine:null,itemDayPlanNote:null,itemDayPlanTrayType:"0",sumQtyPlan:100,sumQty:42,itemList:[{itemDayPlanId:"00bf1d38-b36f-11ed-a363-0242ac",itemCode:"6901028147828",itemName:"泰山(新东方)",itemDayPlanQty:null,qty:20,rateDay:0,preMeasurement:0,distSize:null,marketPrice:null},{itemDayPlanId:"00bf2d9b-b36f-11ed-a363-0242ac",itemCode:"6901028148764",itemName:"泰山(领秀)",itemDayPlanQty:null,qty:22,rateDay:0,preMeasurement:0,distSize:null,marketPrice:null}],num:2,preMeasurement:0},{itemDayPlanCode:"SCWP2023022100002000002091",pcomCode:"11510001",pcomSname:"四川",comCode:"11510101",contDelivWhseId:"203700010002001",comContReachWhseId:"450e235854378ffbbe7a72c3da7cb368",itemDayReqCode:"SCWP2023022100002",comSname:"成都",contDelivWhseName:"滕州卷烟厂",comContReachWhseName:"四川省烟草公司成都市公司物流中心仓库",expecDelivDate:"20230301",createTime:"20240112154051",expecArrivalDate:"20230302",itemDayPlanType:"10",itemDayPlanStatus:"1",isMonopolyPuh:null,isCombine:null,itemDayPlanNote:null,itemDayPlanTrayType:"0",sumQtyPlan:100,sumQty:18,itemList:[{itemDayPlanId:"00bf23f7-b36f-11ed-a363-0242ac",itemCode:"6901028148481",itemName:"泰山(好客)",itemDayPlanQty:null,qty:18,rateDay:0,preMeasurement:0,distSize:null,marketPrice:null}],num:1,preMeasurement:0},{itemDayPlanCode:"SCWP2023022100002000001752",pcomCode:"11510001",pcomSname:"四川",comCode:"11510101",contDelivWhseId:"203700010001001",comContReachWhseId:"450e235854378ffbbe7a72c3da7cb368",itemDayReqCode:"SCWP2023022100002",comSname:"成都",contDelivWhseName:"青岛卷烟厂",comContReachWhseName:"四川省烟草公司成都市公司物流中心仓库",expecDelivDate:"20230301",createTime:"20240112154051",expecArrivalDate:"20230302",itemDayPlanType:"10",itemDayPlanStatus:"1",isMonopolyPuh:null,isCombine:null,itemDayPlanNote:null,itemDayPlanTrayType:"0",sumQtyPlan:100,sumQty:13,itemList:[{itemDayPlanId:"00bf2b55-b36f-11ed-a363-0242ac",itemCode:"6901028148603",itemName:"泰山(拂光细支)",itemDayPlanQty:null,qty:13,rateDay:3,preMeasurement:0,distSize:null,marketPrice:null}],num:1,preMeasurement:0}],itemList:[{itemCode:"6901028147828",itemName:"泰山(新东方)",qtyPlan:0,qty:0,priTax:null,itemStore:null,priNoTax:null,taryCapacity:null},{itemCode:"6901028148764",itemName:"泰山(领秀)",qtyPlan:0,qty:0,priTax:null,itemStore:null,priNoTax:null,taryCapacity:null},{itemCode:"6901028148481",itemName:"泰山(好客)",qtyPlan:0,qty:0,priTax:null,itemStore:null,priNoTax:null,taryCapacity:null},{itemCode:"6901028148603",itemName:"泰山(拂光细支)",qtyPlan:0,qty:0,priTax:null,itemStore:null,priNoTax:null,taryCapacity:null}]}})}function l(e){return Promise.resolve({code:1,message:"success",data:{contractList:[{itemDayPlanCode:"SCWP2023022100002000001751",pcomCode:"11510001",pcomSname:"四川",comCode:"11510101",contDelivWhseId:"203700010001001",comContReachWhseId:"450e235854378ffbbe7a72c3da7cb368",itemDayReqCode:"SCWP2023022100002",comSname:"四川",contDelivWhseName:"青岛卷烟厂",comContReachWhseName:"四川省烟草公司成都市公司物流中心仓库",expecDelivDate:"20230301",createTime:"20240112154051",expecArrivalDate:"20230302",itemDayPlanType:"10",itemDayPlanStatus:"1",isMonopolyPuh:null,isCombine:null,itemDayPlanNote:null,itemDayPlanTrayType:"0",sumQtyPlan:100,sumQty:42,itemList:[{itemDayPlanId:"00bf1d38-b36f-11ed-a363-0242ac",itemCode:"6901028147828",itemName:"泰山(新东方)",itemDayPlanQty:null,qty:20,rateDay:0,preMeasurement:0,distSize:null,marketPrice:null},{itemDayPlanId:"00bf2d9b-b36f-11ed-a363-0242ac",itemCode:"6901028148764",itemName:"泰山(领秀)",itemDayPlanQty:null,qty:22,rateDay:0,preMeasurement:0,distSize:null,marketPrice:null}],num:2,preMeasurement:0,children:[{itemDayPlanCode:"SCWP2023022100002000001751",pcomCode:"11510001",pcomSname:"四川",comCode:"11510101",contDelivWhseId:"203700010001001",comContReachWhseId:"450e235854378ffbbe7a72c3da7cb368",itemDayReqCode:"SCWP2023022100002",comSname:"成都",contDelivWhseName:"青岛卷烟厂",comContReachWhseName:"四川省烟草公司成都市公司物流中心仓库",expecDelivDate:"20230301",createTime:"20240112154051",expecArrivalDate:"20230302",itemDayPlanType:"10",itemDayPlanStatus:"1",isMonopolyPuh:null,isCombine:null,itemDayPlanNote:null,itemDayPlanTrayType:"0",sumQtyPlan:100,sumQty:42,itemList:[{itemDayPlanId:"00bf1d38-b36f-11ed-a363-0242ac",itemCode:"6901028147828",itemName:"泰山(新东方)",itemDayPlanQty:null,qty:20,rateDay:0,preMeasurement:0,distSize:null,marketPrice:null},{itemDayPlanId:"00bf2d9b-b36f-11ed-a363-0242ac",itemCode:"6901028148764",itemName:"泰山(领秀)",itemDayPlanQty:null,qty:22,rateDay:0,preMeasurement:0,distSize:null,marketPrice:null}],num:2,preMeasurement:0},{itemDayPlanCode:"SCWP2023022100002000002091",pcomCode:"11510001",pcomSname:"四川",comCode:"11510101",contDelivWhseId:"203700010002001",comContReachWhseId:"450e235854378ffbbe7a72c3da7cb368",itemDayReqCode:"SCWP2023022100002",comSname:"绵阳",contDelivWhseName:"滕州卷烟厂",comContReachWhseName:"四川省烟草公司成都市公司物流中心仓库",expecDelivDate:"20230301",createTime:"20240112154051",expecArrivalDate:"20230302",itemDayPlanType:"10",itemDayPlanStatus:"1",isMonopolyPuh:null,isCombine:null,itemDayPlanNote:null,itemDayPlanTrayType:"0",sumQtyPlan:100,sumQty:18,itemList:[{itemDayPlanId:"00bf23f7-b36f-11ed-a363-0242ac",itemCode:"6901028148481",itemName:"泰山(好客)",itemDayPlanQty:null,qty:18,rateDay:0,preMeasurement:0,distSize:null,marketPrice:null}],num:1,preMeasurement:0},{itemDayPlanCode:"SCWP2023022100002000001752",pcomCode:"11510001",pcomSname:"四川",comCode:"11510101",contDelivWhseId:"203700010001001",comContReachWhseId:"450e235854378ffbbe7a72c3da7cb368",itemDayReqCode:"SCWP2023022100002",comSname:"雅安",contDelivWhseName:"青岛卷烟厂",comContReachWhseName:"四川省烟草公司成都市公司物流中心仓库",expecDelivDate:"20230301",createTime:"20240112154051",expecArrivalDate:"20230302",itemDayPlanType:"10",itemDayPlanStatus:"1",isMonopolyPuh:null,isCombine:null,itemDayPlanNote:null,itemDayPlanTrayType:"0",sumQtyPlan:100,sumQty:13,itemList:[{itemDayPlanId:"00bf2b55-b36f-11ed-a363-0242ac",itemCode:"6901028148603",itemName:"泰山(拂光细支)",itemDayPlanQty:null,qty:13,rateDay:3,preMeasurement:0,distSize:null,marketPrice:null}],num:1,preMeasurement:0}]}],itemList:[{itemCode:"6901028147828",itemName:"泰山(新东方)",qtyPlan:0,qty:0,priTax:null,itemStore:null,priNoTax:null,taryCapacity:null},{itemCode:"6901028148764",itemName:"泰山(领秀)",qtyPlan:0,qty:0,priTax:null,itemStore:null,priNoTax:null,taryCapacity:null},{itemCode:"6901028148481",itemName:"泰山(好客)",qtyPlan:0,qty:0,priTax:null,itemStore:null,priNoTax:null,taryCapacity:null},{itemCode:"6901028148603",itemName:"泰山(拂光细支)",qtyPlan:0,qty:0,priTax:null,itemStore:null,priNoTax:null,taryCapacity:null}]}})}a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return l}))},550:function(e,t,a){"use strict";a.r(t);a(27);var i=a(291),l=a.n(i),n=a(331),m=a(332),o={data:()=>({searchFieldList:l.a.cloneDeep(n.c),searchForm:{coms:[],items:[],contDelivWhseId:[]},tableLoading:!1,itemList:[],tableData:[],scale:n.b}),computed:{columns:function(){return[...l.a.cloneDeep(n.a),...this.itemList.map(e=>({title:e.itemName,children:[{title:"计划量",field:e.itemCode+"_itemDayPlanQty",width:100,align:"right",isSum:!0,isSwitchUnit:!0,cellRendererParams:{scale:this.scale}},{title:"剩余量",field:e.itemCode+"_sumQty",width:100,align:"right",isSum:!0,isSwitchUnit:!0,cellRendererParams:{scale:this.scale}},{title:"存销比",field:e.itemCode+"_rateDay",width:100,align:"right",isSwitchUnit:!0,cellRendererParams:{scale:2}}]}))]}},mounted(){this.getData()},methods:{async getData(){this.loading=!0;try{const{data:e}=await Object(m.a)(this.searchForm),{contractList:t,itemList:a}=e;t.forEach(e=>{e.itemList.map(t=>{e[t.itemCode+"_itemDayPlanQty"]=t.itemDayPlanQty,e[t.itemCode+"_sumQty"]=t.sumQty,e[t.itemCode+"_rateDay"]=t.rateDay})}),this.tableData=t,this.itemList=a,console.log("this.tableData",this.tableData,"this.itemList",this.itemList)}catch(e){console.log("getPreparationList e",e)}this.loading=!1},doBusi(){let e=this.$refs.table.getSelection();e&&e.length?(console.log("rows is",e),this.$router.push("/ism/am/contract/cont-preparation/preparation")):this.$Message.warning({content:"请至少选择一条合同"})}}},c=a(15),s=Object(c.a)(o,(function(){var e=this,t=e._self._c;return t("IndPageView",{staticStyle:{height:"700px"}},[t("IndTable",{ref:"table",attrs:{showTableOption:!1,disablePage:!0,columns:e.columns,height:500,headerAutoHeight:!1,loading:e.tableLoading},scopedSlots:e._u([{key:"buttons",fn:function(){},proxy:!0}]),model:{value:e.tableData,callback:function(t){e.tableData=t},expression:"tableData"}},[e._v(" "),t("IndFormWrap",{attrs:{slot:"search",formType:"search",fieldList:e.searchFieldList},slot:"search",model:{value:e.searchForm,callback:function(t){e.searchForm=t},expression:"searchForm"}})],1)],1)}),[],!1,null,null,null);t.default=s.exports}}]);