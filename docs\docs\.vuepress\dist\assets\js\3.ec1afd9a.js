(window.webpackJsonp=window.webpackJsonp||[]).push([[3],Array(274).concat([function(e,t,r){
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
var i=r(277),n=i.Buffer;function f(e,t){for(var r in e)t[r]=e[r]}function o(e,t,r){return n(e,t,r)}n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow?e.exports=i:(f(i,t),t.Buffer=o),o.prototype=Object.create(n.prototype),f(n,o),o.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return n(e,t,r)},o.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var i=n(e);return void 0!==t?"string"==typeof r?i.fill(t,r):i.fill(t):i.fill(0),i},o.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i.SlowBuffer(e)}},function(e,t){"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},function(e,t,r){(function(e){!function(e,t){"use strict";function i(e,t){if(!e)throw new Error(t||"Assertion failed")}function n(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}function f(e,t,r){if(f.isBN(e))return e;this.negative=0,this.words=null,this.length=0,this.red=null,null!==e&&("le"!==t&&"be"!==t||(r=t,t=10),this._init(e||0,t||10,r||"be"))}var o;"object"==typeof e?e.exports=f:t.BN=f,f.BN=f,f.wordSize=26;try{o="undefined"!=typeof window&&void 0!==window.Buffer?window.Buffer:r(393).Buffer}catch(e){}function a(e,t){var r=e.charCodeAt(t);return r>=65&&r<=70?r-55:r>=97&&r<=102?r-87:r-48&15}function s(e,t,r){var i=a(e,r);return r-1>=t&&(i|=a(e,r-1)<<4),i}function c(e,t,r,i){for(var n=0,f=Math.min(e.length,r),o=t;o<f;o++){var a=e.charCodeAt(o)-48;n*=i,n+=a>=49?a-49+10:a>=17?a-17+10:a}return n}f.isBN=function(e){return e instanceof f||null!==e&&"object"==typeof e&&e.constructor.wordSize===f.wordSize&&Array.isArray(e.words)},f.max=function(e,t){return e.cmp(t)>0?e:t},f.min=function(e,t){return e.cmp(t)<0?e:t},f.prototype._init=function(e,t,r){if("number"==typeof e)return this._initNumber(e,t,r);if("object"==typeof e)return this._initArray(e,t,r);"hex"===t&&(t=16),i(t===(0|t)&&t>=2&&t<=36);var n=0;"-"===(e=e.toString().replace(/\s+/g,""))[0]&&(n++,this.negative=1),n<e.length&&(16===t?this._parseHex(e,n,r):(this._parseBase(e,t,n),"le"===r&&this._initArray(this.toArray(),t,r)))},f.prototype._initNumber=function(e,t,r){e<0&&(this.negative=1,e=-e),e<67108864?(this.words=[67108863&e],this.length=1):e<4503599627370496?(this.words=[67108863&e,e/67108864&67108863],this.length=2):(i(e<9007199254740992),this.words=[67108863&e,e/67108864&67108863,1],this.length=3),"le"===r&&this._initArray(this.toArray(),t,r)},f.prototype._initArray=function(e,t,r){if(i("number"==typeof e.length),e.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(e.length/3),this.words=new Array(this.length);for(var n=0;n<this.length;n++)this.words[n]=0;var f,o,a=0;if("be"===r)for(n=e.length-1,f=0;n>=0;n-=3)o=e[n]|e[n-1]<<8|e[n-2]<<16,this.words[f]|=o<<a&67108863,this.words[f+1]=o>>>26-a&67108863,(a+=24)>=26&&(a-=26,f++);else if("le"===r)for(n=0,f=0;n<e.length;n+=3)o=e[n]|e[n+1]<<8|e[n+2]<<16,this.words[f]|=o<<a&67108863,this.words[f+1]=o>>>26-a&67108863,(a+=24)>=26&&(a-=26,f++);return this.strip()},f.prototype._parseHex=function(e,t,r){this.length=Math.ceil((e.length-t)/6),this.words=new Array(this.length);for(var i=0;i<this.length;i++)this.words[i]=0;var n,f=0,o=0;if("be"===r)for(i=e.length-1;i>=t;i-=2)n=s(e,t,i)<<f,this.words[o]|=67108863&n,f>=18?(f-=18,o+=1,this.words[o]|=n>>>26):f+=8;else for(i=(e.length-t)%2==0?t+1:t;i<e.length;i+=2)n=s(e,t,i)<<f,this.words[o]|=67108863&n,f>=18?(f-=18,o+=1,this.words[o]|=n>>>26):f+=8;this.strip()},f.prototype._parseBase=function(e,t,r){this.words=[0],this.length=1;for(var i=0,n=1;n<=67108863;n*=t)i++;i--,n=n/t|0;for(var f=e.length-r,o=f%i,a=Math.min(f,f-o)+r,s=0,h=r;h<a;h+=i)s=c(e,h,h+i,t),this.imuln(n),this.words[0]+s<67108864?this.words[0]+=s:this._iaddn(s);if(0!==o){var d=1;for(s=c(e,h,e.length,t),h=0;h<o;h++)d*=t;this.imuln(d),this.words[0]+s<67108864?this.words[0]+=s:this._iaddn(s)}this.strip()},f.prototype.copy=function(e){e.words=new Array(this.length);for(var t=0;t<this.length;t++)e.words[t]=this.words[t];e.length=this.length,e.negative=this.negative,e.red=this.red},f.prototype.clone=function(){var e=new f(null);return this.copy(e),e},f.prototype._expand=function(e){for(;this.length<e;)this.words[this.length++]=0;return this},f.prototype.strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},f.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},f.prototype.inspect=function(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"};var h=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],d=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],u=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function l(e,t,r){r.negative=t.negative^e.negative;var i=e.length+t.length|0;r.length=i,i=i-1|0;var n=0|e.words[0],f=0|t.words[0],o=n*f,a=67108863&o,s=o/67108864|0;r.words[0]=a;for(var c=1;c<i;c++){for(var h=s>>>26,d=67108863&s,u=Math.min(c,t.length-1),l=Math.max(0,c-e.length+1);l<=u;l++){var p=c-l|0;h+=(o=(n=0|e.words[p])*(f=0|t.words[l])+d)/67108864|0,d=67108863&o}r.words[c]=0|d,s=0|h}return 0!==s?r.words[c]=0|s:r.length--,r.strip()}f.prototype.toString=function(e,t){var r;if(t=0|t||1,16===(e=e||10)||"hex"===e){r="";for(var n=0,f=0,o=0;o<this.length;o++){var a=this.words[o],s=(16777215&(a<<n|f)).toString(16);r=0!==(f=a>>>24-n&16777215)||o!==this.length-1?h[6-s.length]+s+r:s+r,(n+=2)>=26&&(n-=26,o--)}for(0!==f&&(r=f.toString(16)+r);r.length%t!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}if(e===(0|e)&&e>=2&&e<=36){var c=d[e],l=u[e];r="";var p=this.clone();for(p.negative=0;!p.isZero();){var b=p.modn(l).toString(e);r=(p=p.idivn(l)).isZero()?b+r:h[c-b.length]+b+r}for(this.isZero()&&(r="0"+r);r.length%t!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}i(!1,"Base should be between 2 and 36")},f.prototype.toNumber=function(){var e=this.words[0];return 2===this.length?e+=67108864*this.words[1]:3===this.length&&1===this.words[2]?e+=4503599627370496+67108864*this.words[1]:this.length>2&&i(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-e:e},f.prototype.toJSON=function(){return this.toString(16)},f.prototype.toBuffer=function(e,t){return i(void 0!==o),this.toArrayLike(o,e,t)},f.prototype.toArray=function(e,t){return this.toArrayLike(Array,e,t)},f.prototype.toArrayLike=function(e,t,r){var n=this.byteLength(),f=r||Math.max(1,n);i(n<=f,"byte array longer than desired length"),i(f>0,"Requested array length <= 0"),this.strip();var o,a,s="le"===t,c=new e(f),h=this.clone();if(s){for(a=0;!h.isZero();a++)o=h.andln(255),h.iushrn(8),c[a]=o;for(;a<f;a++)c[a]=0}else{for(a=0;a<f-n;a++)c[a]=0;for(a=0;!h.isZero();a++)o=h.andln(255),h.iushrn(8),c[f-a-1]=o}return c},Math.clz32?f.prototype._countBits=function(e){return 32-Math.clz32(e)}:f.prototype._countBits=function(e){var t=e,r=0;return t>=4096&&(r+=13,t>>>=13),t>=64&&(r+=7,t>>>=7),t>=8&&(r+=4,t>>>=4),t>=2&&(r+=2,t>>>=2),r+t},f.prototype._zeroBits=function(e){if(0===e)return 26;var t=e,r=0;return 0==(8191&t)&&(r+=13,t>>>=13),0==(127&t)&&(r+=7,t>>>=7),0==(15&t)&&(r+=4,t>>>=4),0==(3&t)&&(r+=2,t>>>=2),0==(1&t)&&r++,r},f.prototype.bitLength=function(){var e=this.words[this.length-1],t=this._countBits(e);return 26*(this.length-1)+t},f.prototype.zeroBits=function(){if(this.isZero())return 0;for(var e=0,t=0;t<this.length;t++){var r=this._zeroBits(this.words[t]);if(e+=r,26!==r)break}return e},f.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},f.prototype.toTwos=function(e){return 0!==this.negative?this.abs().inotn(e).iaddn(1):this.clone()},f.prototype.fromTwos=function(e){return this.testn(e-1)?this.notn(e).iaddn(1).ineg():this.clone()},f.prototype.isNeg=function(){return 0!==this.negative},f.prototype.neg=function(){return this.clone().ineg()},f.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},f.prototype.iuor=function(e){for(;this.length<e.length;)this.words[this.length++]=0;for(var t=0;t<e.length;t++)this.words[t]=this.words[t]|e.words[t];return this.strip()},f.prototype.ior=function(e){return i(0==(this.negative|e.negative)),this.iuor(e)},f.prototype.or=function(e){return this.length>e.length?this.clone().ior(e):e.clone().ior(this)},f.prototype.uor=function(e){return this.length>e.length?this.clone().iuor(e):e.clone().iuor(this)},f.prototype.iuand=function(e){var t;t=this.length>e.length?e:this;for(var r=0;r<t.length;r++)this.words[r]=this.words[r]&e.words[r];return this.length=t.length,this.strip()},f.prototype.iand=function(e){return i(0==(this.negative|e.negative)),this.iuand(e)},f.prototype.and=function(e){return this.length>e.length?this.clone().iand(e):e.clone().iand(this)},f.prototype.uand=function(e){return this.length>e.length?this.clone().iuand(e):e.clone().iuand(this)},f.prototype.iuxor=function(e){var t,r;this.length>e.length?(t=this,r=e):(t=e,r=this);for(var i=0;i<r.length;i++)this.words[i]=t.words[i]^r.words[i];if(this!==t)for(;i<t.length;i++)this.words[i]=t.words[i];return this.length=t.length,this.strip()},f.prototype.ixor=function(e){return i(0==(this.negative|e.negative)),this.iuxor(e)},f.prototype.xor=function(e){return this.length>e.length?this.clone().ixor(e):e.clone().ixor(this)},f.prototype.uxor=function(e){return this.length>e.length?this.clone().iuxor(e):e.clone().iuxor(this)},f.prototype.inotn=function(e){i("number"==typeof e&&e>=0);var t=0|Math.ceil(e/26),r=e%26;this._expand(t),r>0&&t--;for(var n=0;n<t;n++)this.words[n]=67108863&~this.words[n];return r>0&&(this.words[n]=~this.words[n]&67108863>>26-r),this.strip()},f.prototype.notn=function(e){return this.clone().inotn(e)},f.prototype.setn=function(e,t){i("number"==typeof e&&e>=0);var r=e/26|0,n=e%26;return this._expand(r+1),this.words[r]=t?this.words[r]|1<<n:this.words[r]&~(1<<n),this.strip()},f.prototype.iadd=function(e){var t,r,i;if(0!==this.negative&&0===e.negative)return this.negative=0,t=this.isub(e),this.negative^=1,this._normSign();if(0===this.negative&&0!==e.negative)return e.negative=0,t=this.isub(e),e.negative=1,t._normSign();this.length>e.length?(r=this,i=e):(r=e,i=this);for(var n=0,f=0;f<i.length;f++)t=(0|r.words[f])+(0|i.words[f])+n,this.words[f]=67108863&t,n=t>>>26;for(;0!==n&&f<r.length;f++)t=(0|r.words[f])+n,this.words[f]=67108863&t,n=t>>>26;if(this.length=r.length,0!==n)this.words[this.length]=n,this.length++;else if(r!==this)for(;f<r.length;f++)this.words[f]=r.words[f];return this},f.prototype.add=function(e){var t;return 0!==e.negative&&0===this.negative?(e.negative=0,t=this.sub(e),e.negative^=1,t):0===e.negative&&0!==this.negative?(this.negative=0,t=e.sub(this),this.negative=1,t):this.length>e.length?this.clone().iadd(e):e.clone().iadd(this)},f.prototype.isub=function(e){if(0!==e.negative){e.negative=0;var t=this.iadd(e);return e.negative=1,t._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(e),this.negative=1,this._normSign();var r,i,n=this.cmp(e);if(0===n)return this.negative=0,this.length=1,this.words[0]=0,this;n>0?(r=this,i=e):(r=e,i=this);for(var f=0,o=0;o<i.length;o++)f=(t=(0|r.words[o])-(0|i.words[o])+f)>>26,this.words[o]=67108863&t;for(;0!==f&&o<r.length;o++)f=(t=(0|r.words[o])+f)>>26,this.words[o]=67108863&t;if(0===f&&o<r.length&&r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this.length=Math.max(this.length,o),r!==this&&(this.negative=1),this.strip()},f.prototype.sub=function(e){return this.clone().isub(e)};var p=function(e,t,r){var i,n,f,o=e.words,a=t.words,s=r.words,c=0,h=0|o[0],d=8191&h,u=h>>>13,l=0|o[1],p=8191&l,b=l>>>13,y=0|o[2],g=8191&y,m=y>>>13,v=0|o[3],_=8191&v,w=v>>>13,S=0|o[4],M=8191&S,E=S>>>13,k=0|o[5],A=8191&k,x=k>>>13,B=0|o[6],I=8191&B,R=B>>>13,P=0|o[7],C=8191&P,T=P>>>13,j=0|o[8],L=8191&j,O=j>>>13,D=0|o[9],U=8191&D,q=D>>>13,N=0|a[0],z=8191&N,K=N>>>13,F=0|a[1],H=8191&F,Y=F>>>13,W=0|a[2],V=8191&W,X=W>>>13,J=0|a[3],G=8191&J,Z=J>>>13,$=0|a[4],Q=8191&$,ee=$>>>13,te=0|a[5],re=8191&te,ie=te>>>13,ne=0|a[6],fe=8191&ne,oe=ne>>>13,ae=0|a[7],se=8191&ae,ce=ae>>>13,he=0|a[8],de=8191&he,ue=he>>>13,le=0|a[9],pe=8191&le,be=le>>>13;r.negative=e.negative^t.negative,r.length=19;var ye=(c+(i=Math.imul(d,z))|0)+((8191&(n=(n=Math.imul(d,K))+Math.imul(u,z)|0))<<13)|0;c=((f=Math.imul(u,K))+(n>>>13)|0)+(ye>>>26)|0,ye&=67108863,i=Math.imul(p,z),n=(n=Math.imul(p,K))+Math.imul(b,z)|0,f=Math.imul(b,K);var ge=(c+(i=i+Math.imul(d,H)|0)|0)+((8191&(n=(n=n+Math.imul(d,Y)|0)+Math.imul(u,H)|0))<<13)|0;c=((f=f+Math.imul(u,Y)|0)+(n>>>13)|0)+(ge>>>26)|0,ge&=67108863,i=Math.imul(g,z),n=(n=Math.imul(g,K))+Math.imul(m,z)|0,f=Math.imul(m,K),i=i+Math.imul(p,H)|0,n=(n=n+Math.imul(p,Y)|0)+Math.imul(b,H)|0,f=f+Math.imul(b,Y)|0;var me=(c+(i=i+Math.imul(d,V)|0)|0)+((8191&(n=(n=n+Math.imul(d,X)|0)+Math.imul(u,V)|0))<<13)|0;c=((f=f+Math.imul(u,X)|0)+(n>>>13)|0)+(me>>>26)|0,me&=67108863,i=Math.imul(_,z),n=(n=Math.imul(_,K))+Math.imul(w,z)|0,f=Math.imul(w,K),i=i+Math.imul(g,H)|0,n=(n=n+Math.imul(g,Y)|0)+Math.imul(m,H)|0,f=f+Math.imul(m,Y)|0,i=i+Math.imul(p,V)|0,n=(n=n+Math.imul(p,X)|0)+Math.imul(b,V)|0,f=f+Math.imul(b,X)|0;var ve=(c+(i=i+Math.imul(d,G)|0)|0)+((8191&(n=(n=n+Math.imul(d,Z)|0)+Math.imul(u,G)|0))<<13)|0;c=((f=f+Math.imul(u,Z)|0)+(n>>>13)|0)+(ve>>>26)|0,ve&=67108863,i=Math.imul(M,z),n=(n=Math.imul(M,K))+Math.imul(E,z)|0,f=Math.imul(E,K),i=i+Math.imul(_,H)|0,n=(n=n+Math.imul(_,Y)|0)+Math.imul(w,H)|0,f=f+Math.imul(w,Y)|0,i=i+Math.imul(g,V)|0,n=(n=n+Math.imul(g,X)|0)+Math.imul(m,V)|0,f=f+Math.imul(m,X)|0,i=i+Math.imul(p,G)|0,n=(n=n+Math.imul(p,Z)|0)+Math.imul(b,G)|0,f=f+Math.imul(b,Z)|0;var _e=(c+(i=i+Math.imul(d,Q)|0)|0)+((8191&(n=(n=n+Math.imul(d,ee)|0)+Math.imul(u,Q)|0))<<13)|0;c=((f=f+Math.imul(u,ee)|0)+(n>>>13)|0)+(_e>>>26)|0,_e&=67108863,i=Math.imul(A,z),n=(n=Math.imul(A,K))+Math.imul(x,z)|0,f=Math.imul(x,K),i=i+Math.imul(M,H)|0,n=(n=n+Math.imul(M,Y)|0)+Math.imul(E,H)|0,f=f+Math.imul(E,Y)|0,i=i+Math.imul(_,V)|0,n=(n=n+Math.imul(_,X)|0)+Math.imul(w,V)|0,f=f+Math.imul(w,X)|0,i=i+Math.imul(g,G)|0,n=(n=n+Math.imul(g,Z)|0)+Math.imul(m,G)|0,f=f+Math.imul(m,Z)|0,i=i+Math.imul(p,Q)|0,n=(n=n+Math.imul(p,ee)|0)+Math.imul(b,Q)|0,f=f+Math.imul(b,ee)|0;var we=(c+(i=i+Math.imul(d,re)|0)|0)+((8191&(n=(n=n+Math.imul(d,ie)|0)+Math.imul(u,re)|0))<<13)|0;c=((f=f+Math.imul(u,ie)|0)+(n>>>13)|0)+(we>>>26)|0,we&=67108863,i=Math.imul(I,z),n=(n=Math.imul(I,K))+Math.imul(R,z)|0,f=Math.imul(R,K),i=i+Math.imul(A,H)|0,n=(n=n+Math.imul(A,Y)|0)+Math.imul(x,H)|0,f=f+Math.imul(x,Y)|0,i=i+Math.imul(M,V)|0,n=(n=n+Math.imul(M,X)|0)+Math.imul(E,V)|0,f=f+Math.imul(E,X)|0,i=i+Math.imul(_,G)|0,n=(n=n+Math.imul(_,Z)|0)+Math.imul(w,G)|0,f=f+Math.imul(w,Z)|0,i=i+Math.imul(g,Q)|0,n=(n=n+Math.imul(g,ee)|0)+Math.imul(m,Q)|0,f=f+Math.imul(m,ee)|0,i=i+Math.imul(p,re)|0,n=(n=n+Math.imul(p,ie)|0)+Math.imul(b,re)|0,f=f+Math.imul(b,ie)|0;var Se=(c+(i=i+Math.imul(d,fe)|0)|0)+((8191&(n=(n=n+Math.imul(d,oe)|0)+Math.imul(u,fe)|0))<<13)|0;c=((f=f+Math.imul(u,oe)|0)+(n>>>13)|0)+(Se>>>26)|0,Se&=67108863,i=Math.imul(C,z),n=(n=Math.imul(C,K))+Math.imul(T,z)|0,f=Math.imul(T,K),i=i+Math.imul(I,H)|0,n=(n=n+Math.imul(I,Y)|0)+Math.imul(R,H)|0,f=f+Math.imul(R,Y)|0,i=i+Math.imul(A,V)|0,n=(n=n+Math.imul(A,X)|0)+Math.imul(x,V)|0,f=f+Math.imul(x,X)|0,i=i+Math.imul(M,G)|0,n=(n=n+Math.imul(M,Z)|0)+Math.imul(E,G)|0,f=f+Math.imul(E,Z)|0,i=i+Math.imul(_,Q)|0,n=(n=n+Math.imul(_,ee)|0)+Math.imul(w,Q)|0,f=f+Math.imul(w,ee)|0,i=i+Math.imul(g,re)|0,n=(n=n+Math.imul(g,ie)|0)+Math.imul(m,re)|0,f=f+Math.imul(m,ie)|0,i=i+Math.imul(p,fe)|0,n=(n=n+Math.imul(p,oe)|0)+Math.imul(b,fe)|0,f=f+Math.imul(b,oe)|0;var Me=(c+(i=i+Math.imul(d,se)|0)|0)+((8191&(n=(n=n+Math.imul(d,ce)|0)+Math.imul(u,se)|0))<<13)|0;c=((f=f+Math.imul(u,ce)|0)+(n>>>13)|0)+(Me>>>26)|0,Me&=67108863,i=Math.imul(L,z),n=(n=Math.imul(L,K))+Math.imul(O,z)|0,f=Math.imul(O,K),i=i+Math.imul(C,H)|0,n=(n=n+Math.imul(C,Y)|0)+Math.imul(T,H)|0,f=f+Math.imul(T,Y)|0,i=i+Math.imul(I,V)|0,n=(n=n+Math.imul(I,X)|0)+Math.imul(R,V)|0,f=f+Math.imul(R,X)|0,i=i+Math.imul(A,G)|0,n=(n=n+Math.imul(A,Z)|0)+Math.imul(x,G)|0,f=f+Math.imul(x,Z)|0,i=i+Math.imul(M,Q)|0,n=(n=n+Math.imul(M,ee)|0)+Math.imul(E,Q)|0,f=f+Math.imul(E,ee)|0,i=i+Math.imul(_,re)|0,n=(n=n+Math.imul(_,ie)|0)+Math.imul(w,re)|0,f=f+Math.imul(w,ie)|0,i=i+Math.imul(g,fe)|0,n=(n=n+Math.imul(g,oe)|0)+Math.imul(m,fe)|0,f=f+Math.imul(m,oe)|0,i=i+Math.imul(p,se)|0,n=(n=n+Math.imul(p,ce)|0)+Math.imul(b,se)|0,f=f+Math.imul(b,ce)|0;var Ee=(c+(i=i+Math.imul(d,de)|0)|0)+((8191&(n=(n=n+Math.imul(d,ue)|0)+Math.imul(u,de)|0))<<13)|0;c=((f=f+Math.imul(u,ue)|0)+(n>>>13)|0)+(Ee>>>26)|0,Ee&=67108863,i=Math.imul(U,z),n=(n=Math.imul(U,K))+Math.imul(q,z)|0,f=Math.imul(q,K),i=i+Math.imul(L,H)|0,n=(n=n+Math.imul(L,Y)|0)+Math.imul(O,H)|0,f=f+Math.imul(O,Y)|0,i=i+Math.imul(C,V)|0,n=(n=n+Math.imul(C,X)|0)+Math.imul(T,V)|0,f=f+Math.imul(T,X)|0,i=i+Math.imul(I,G)|0,n=(n=n+Math.imul(I,Z)|0)+Math.imul(R,G)|0,f=f+Math.imul(R,Z)|0,i=i+Math.imul(A,Q)|0,n=(n=n+Math.imul(A,ee)|0)+Math.imul(x,Q)|0,f=f+Math.imul(x,ee)|0,i=i+Math.imul(M,re)|0,n=(n=n+Math.imul(M,ie)|0)+Math.imul(E,re)|0,f=f+Math.imul(E,ie)|0,i=i+Math.imul(_,fe)|0,n=(n=n+Math.imul(_,oe)|0)+Math.imul(w,fe)|0,f=f+Math.imul(w,oe)|0,i=i+Math.imul(g,se)|0,n=(n=n+Math.imul(g,ce)|0)+Math.imul(m,se)|0,f=f+Math.imul(m,ce)|0,i=i+Math.imul(p,de)|0,n=(n=n+Math.imul(p,ue)|0)+Math.imul(b,de)|0,f=f+Math.imul(b,ue)|0;var ke=(c+(i=i+Math.imul(d,pe)|0)|0)+((8191&(n=(n=n+Math.imul(d,be)|0)+Math.imul(u,pe)|0))<<13)|0;c=((f=f+Math.imul(u,be)|0)+(n>>>13)|0)+(ke>>>26)|0,ke&=67108863,i=Math.imul(U,H),n=(n=Math.imul(U,Y))+Math.imul(q,H)|0,f=Math.imul(q,Y),i=i+Math.imul(L,V)|0,n=(n=n+Math.imul(L,X)|0)+Math.imul(O,V)|0,f=f+Math.imul(O,X)|0,i=i+Math.imul(C,G)|0,n=(n=n+Math.imul(C,Z)|0)+Math.imul(T,G)|0,f=f+Math.imul(T,Z)|0,i=i+Math.imul(I,Q)|0,n=(n=n+Math.imul(I,ee)|0)+Math.imul(R,Q)|0,f=f+Math.imul(R,ee)|0,i=i+Math.imul(A,re)|0,n=(n=n+Math.imul(A,ie)|0)+Math.imul(x,re)|0,f=f+Math.imul(x,ie)|0,i=i+Math.imul(M,fe)|0,n=(n=n+Math.imul(M,oe)|0)+Math.imul(E,fe)|0,f=f+Math.imul(E,oe)|0,i=i+Math.imul(_,se)|0,n=(n=n+Math.imul(_,ce)|0)+Math.imul(w,se)|0,f=f+Math.imul(w,ce)|0,i=i+Math.imul(g,de)|0,n=(n=n+Math.imul(g,ue)|0)+Math.imul(m,de)|0,f=f+Math.imul(m,ue)|0;var Ae=(c+(i=i+Math.imul(p,pe)|0)|0)+((8191&(n=(n=n+Math.imul(p,be)|0)+Math.imul(b,pe)|0))<<13)|0;c=((f=f+Math.imul(b,be)|0)+(n>>>13)|0)+(Ae>>>26)|0,Ae&=67108863,i=Math.imul(U,V),n=(n=Math.imul(U,X))+Math.imul(q,V)|0,f=Math.imul(q,X),i=i+Math.imul(L,G)|0,n=(n=n+Math.imul(L,Z)|0)+Math.imul(O,G)|0,f=f+Math.imul(O,Z)|0,i=i+Math.imul(C,Q)|0,n=(n=n+Math.imul(C,ee)|0)+Math.imul(T,Q)|0,f=f+Math.imul(T,ee)|0,i=i+Math.imul(I,re)|0,n=(n=n+Math.imul(I,ie)|0)+Math.imul(R,re)|0,f=f+Math.imul(R,ie)|0,i=i+Math.imul(A,fe)|0,n=(n=n+Math.imul(A,oe)|0)+Math.imul(x,fe)|0,f=f+Math.imul(x,oe)|0,i=i+Math.imul(M,se)|0,n=(n=n+Math.imul(M,ce)|0)+Math.imul(E,se)|0,f=f+Math.imul(E,ce)|0,i=i+Math.imul(_,de)|0,n=(n=n+Math.imul(_,ue)|0)+Math.imul(w,de)|0,f=f+Math.imul(w,ue)|0;var xe=(c+(i=i+Math.imul(g,pe)|0)|0)+((8191&(n=(n=n+Math.imul(g,be)|0)+Math.imul(m,pe)|0))<<13)|0;c=((f=f+Math.imul(m,be)|0)+(n>>>13)|0)+(xe>>>26)|0,xe&=67108863,i=Math.imul(U,G),n=(n=Math.imul(U,Z))+Math.imul(q,G)|0,f=Math.imul(q,Z),i=i+Math.imul(L,Q)|0,n=(n=n+Math.imul(L,ee)|0)+Math.imul(O,Q)|0,f=f+Math.imul(O,ee)|0,i=i+Math.imul(C,re)|0,n=(n=n+Math.imul(C,ie)|0)+Math.imul(T,re)|0,f=f+Math.imul(T,ie)|0,i=i+Math.imul(I,fe)|0,n=(n=n+Math.imul(I,oe)|0)+Math.imul(R,fe)|0,f=f+Math.imul(R,oe)|0,i=i+Math.imul(A,se)|0,n=(n=n+Math.imul(A,ce)|0)+Math.imul(x,se)|0,f=f+Math.imul(x,ce)|0,i=i+Math.imul(M,de)|0,n=(n=n+Math.imul(M,ue)|0)+Math.imul(E,de)|0,f=f+Math.imul(E,ue)|0;var Be=(c+(i=i+Math.imul(_,pe)|0)|0)+((8191&(n=(n=n+Math.imul(_,be)|0)+Math.imul(w,pe)|0))<<13)|0;c=((f=f+Math.imul(w,be)|0)+(n>>>13)|0)+(Be>>>26)|0,Be&=67108863,i=Math.imul(U,Q),n=(n=Math.imul(U,ee))+Math.imul(q,Q)|0,f=Math.imul(q,ee),i=i+Math.imul(L,re)|0,n=(n=n+Math.imul(L,ie)|0)+Math.imul(O,re)|0,f=f+Math.imul(O,ie)|0,i=i+Math.imul(C,fe)|0,n=(n=n+Math.imul(C,oe)|0)+Math.imul(T,fe)|0,f=f+Math.imul(T,oe)|0,i=i+Math.imul(I,se)|0,n=(n=n+Math.imul(I,ce)|0)+Math.imul(R,se)|0,f=f+Math.imul(R,ce)|0,i=i+Math.imul(A,de)|0,n=(n=n+Math.imul(A,ue)|0)+Math.imul(x,de)|0,f=f+Math.imul(x,ue)|0;var Ie=(c+(i=i+Math.imul(M,pe)|0)|0)+((8191&(n=(n=n+Math.imul(M,be)|0)+Math.imul(E,pe)|0))<<13)|0;c=((f=f+Math.imul(E,be)|0)+(n>>>13)|0)+(Ie>>>26)|0,Ie&=67108863,i=Math.imul(U,re),n=(n=Math.imul(U,ie))+Math.imul(q,re)|0,f=Math.imul(q,ie),i=i+Math.imul(L,fe)|0,n=(n=n+Math.imul(L,oe)|0)+Math.imul(O,fe)|0,f=f+Math.imul(O,oe)|0,i=i+Math.imul(C,se)|0,n=(n=n+Math.imul(C,ce)|0)+Math.imul(T,se)|0,f=f+Math.imul(T,ce)|0,i=i+Math.imul(I,de)|0,n=(n=n+Math.imul(I,ue)|0)+Math.imul(R,de)|0,f=f+Math.imul(R,ue)|0;var Re=(c+(i=i+Math.imul(A,pe)|0)|0)+((8191&(n=(n=n+Math.imul(A,be)|0)+Math.imul(x,pe)|0))<<13)|0;c=((f=f+Math.imul(x,be)|0)+(n>>>13)|0)+(Re>>>26)|0,Re&=67108863,i=Math.imul(U,fe),n=(n=Math.imul(U,oe))+Math.imul(q,fe)|0,f=Math.imul(q,oe),i=i+Math.imul(L,se)|0,n=(n=n+Math.imul(L,ce)|0)+Math.imul(O,se)|0,f=f+Math.imul(O,ce)|0,i=i+Math.imul(C,de)|0,n=(n=n+Math.imul(C,ue)|0)+Math.imul(T,de)|0,f=f+Math.imul(T,ue)|0;var Pe=(c+(i=i+Math.imul(I,pe)|0)|0)+((8191&(n=(n=n+Math.imul(I,be)|0)+Math.imul(R,pe)|0))<<13)|0;c=((f=f+Math.imul(R,be)|0)+(n>>>13)|0)+(Pe>>>26)|0,Pe&=67108863,i=Math.imul(U,se),n=(n=Math.imul(U,ce))+Math.imul(q,se)|0,f=Math.imul(q,ce),i=i+Math.imul(L,de)|0,n=(n=n+Math.imul(L,ue)|0)+Math.imul(O,de)|0,f=f+Math.imul(O,ue)|0;var Ce=(c+(i=i+Math.imul(C,pe)|0)|0)+((8191&(n=(n=n+Math.imul(C,be)|0)+Math.imul(T,pe)|0))<<13)|0;c=((f=f+Math.imul(T,be)|0)+(n>>>13)|0)+(Ce>>>26)|0,Ce&=67108863,i=Math.imul(U,de),n=(n=Math.imul(U,ue))+Math.imul(q,de)|0,f=Math.imul(q,ue);var Te=(c+(i=i+Math.imul(L,pe)|0)|0)+((8191&(n=(n=n+Math.imul(L,be)|0)+Math.imul(O,pe)|0))<<13)|0;c=((f=f+Math.imul(O,be)|0)+(n>>>13)|0)+(Te>>>26)|0,Te&=67108863;var je=(c+(i=Math.imul(U,pe))|0)+((8191&(n=(n=Math.imul(U,be))+Math.imul(q,pe)|0))<<13)|0;return c=((f=Math.imul(q,be))+(n>>>13)|0)+(je>>>26)|0,je&=67108863,s[0]=ye,s[1]=ge,s[2]=me,s[3]=ve,s[4]=_e,s[5]=we,s[6]=Se,s[7]=Me,s[8]=Ee,s[9]=ke,s[10]=Ae,s[11]=xe,s[12]=Be,s[13]=Ie,s[14]=Re,s[15]=Pe,s[16]=Ce,s[17]=Te,s[18]=je,0!==c&&(s[19]=c,r.length++),r};function b(e,t,r){return(new y).mulp(e,t,r)}function y(e,t){this.x=e,this.y=t}Math.imul||(p=l),f.prototype.mulTo=function(e,t){var r=this.length+e.length;return 10===this.length&&10===e.length?p(this,e,t):r<63?l(this,e,t):r<1024?function(e,t,r){r.negative=t.negative^e.negative,r.length=e.length+t.length;for(var i=0,n=0,f=0;f<r.length-1;f++){var o=n;n=0;for(var a=67108863&i,s=Math.min(f,t.length-1),c=Math.max(0,f-e.length+1);c<=s;c++){var h=f-c,d=(0|e.words[h])*(0|t.words[c]),u=67108863&d;a=67108863&(u=u+a|0),n+=(o=(o=o+(d/67108864|0)|0)+(u>>>26)|0)>>>26,o&=67108863}r.words[f]=a,i=o,o=n}return 0!==i?r.words[f]=i:r.length--,r.strip()}(this,e,t):b(this,e,t)},y.prototype.makeRBT=function(e){for(var t=new Array(e),r=f.prototype._countBits(e)-1,i=0;i<e;i++)t[i]=this.revBin(i,r,e);return t},y.prototype.revBin=function(e,t,r){if(0===e||e===r-1)return e;for(var i=0,n=0;n<t;n++)i|=(1&e)<<t-n-1,e>>=1;return i},y.prototype.permute=function(e,t,r,i,n,f){for(var o=0;o<f;o++)i[o]=t[e[o]],n[o]=r[e[o]]},y.prototype.transform=function(e,t,r,i,n,f){this.permute(f,e,t,r,i,n);for(var o=1;o<n;o<<=1)for(var a=o<<1,s=Math.cos(2*Math.PI/a),c=Math.sin(2*Math.PI/a),h=0;h<n;h+=a)for(var d=s,u=c,l=0;l<o;l++){var p=r[h+l],b=i[h+l],y=r[h+l+o],g=i[h+l+o],m=d*y-u*g;g=d*g+u*y,y=m,r[h+l]=p+y,i[h+l]=b+g,r[h+l+o]=p-y,i[h+l+o]=b-g,l!==a&&(m=s*d-c*u,u=s*u+c*d,d=m)}},y.prototype.guessLen13b=function(e,t){var r=1|Math.max(t,e),i=1&r,n=0;for(r=r/2|0;r;r>>>=1)n++;return 1<<n+1+i},y.prototype.conjugate=function(e,t,r){if(!(r<=1))for(var i=0;i<r/2;i++){var n=e[i];e[i]=e[r-i-1],e[r-i-1]=n,n=t[i],t[i]=-t[r-i-1],t[r-i-1]=-n}},y.prototype.normalize13b=function(e,t){for(var r=0,i=0;i<t/2;i++){var n=8192*Math.round(e[2*i+1]/t)+Math.round(e[2*i]/t)+r;e[i]=67108863&n,r=n<67108864?0:n/67108864|0}return e},y.prototype.convert13b=function(e,t,r,n){for(var f=0,o=0;o<t;o++)f+=0|e[o],r[2*o]=8191&f,f>>>=13,r[2*o+1]=8191&f,f>>>=13;for(o=2*t;o<n;++o)r[o]=0;i(0===f),i(0==(-8192&f))},y.prototype.stub=function(e){for(var t=new Array(e),r=0;r<e;r++)t[r]=0;return t},y.prototype.mulp=function(e,t,r){var i=2*this.guessLen13b(e.length,t.length),n=this.makeRBT(i),f=this.stub(i),o=new Array(i),a=new Array(i),s=new Array(i),c=new Array(i),h=new Array(i),d=new Array(i),u=r.words;u.length=i,this.convert13b(e.words,e.length,o,i),this.convert13b(t.words,t.length,c,i),this.transform(o,f,a,s,i,n),this.transform(c,f,h,d,i,n);for(var l=0;l<i;l++){var p=a[l]*h[l]-s[l]*d[l];s[l]=a[l]*d[l]+s[l]*h[l],a[l]=p}return this.conjugate(a,s,i),this.transform(a,s,u,f,i,n),this.conjugate(u,f,i),this.normalize13b(u,i),r.negative=e.negative^t.negative,r.length=e.length+t.length,r.strip()},f.prototype.mul=function(e){var t=new f(null);return t.words=new Array(this.length+e.length),this.mulTo(e,t)},f.prototype.mulf=function(e){var t=new f(null);return t.words=new Array(this.length+e.length),b(this,e,t)},f.prototype.imul=function(e){return this.clone().mulTo(e,this)},f.prototype.imuln=function(e){i("number"==typeof e),i(e<67108864);for(var t=0,r=0;r<this.length;r++){var n=(0|this.words[r])*e,f=(67108863&n)+(67108863&t);t>>=26,t+=n/67108864|0,t+=f>>>26,this.words[r]=67108863&f}return 0!==t&&(this.words[r]=t,this.length++),this},f.prototype.muln=function(e){return this.clone().imuln(e)},f.prototype.sqr=function(){return this.mul(this)},f.prototype.isqr=function(){return this.imul(this.clone())},f.prototype.pow=function(e){var t=function(e){for(var t=new Array(e.bitLength()),r=0;r<t.length;r++){var i=r/26|0,n=r%26;t[r]=(e.words[i]&1<<n)>>>n}return t}(e);if(0===t.length)return new f(1);for(var r=this,i=0;i<t.length&&0===t[i];i++,r=r.sqr());if(++i<t.length)for(var n=r.sqr();i<t.length;i++,n=n.sqr())0!==t[i]&&(r=r.mul(n));return r},f.prototype.iushln=function(e){i("number"==typeof e&&e>=0);var t,r=e%26,n=(e-r)/26,f=67108863>>>26-r<<26-r;if(0!==r){var o=0;for(t=0;t<this.length;t++){var a=this.words[t]&f,s=(0|this.words[t])-a<<r;this.words[t]=s|o,o=a>>>26-r}o&&(this.words[t]=o,this.length++)}if(0!==n){for(t=this.length-1;t>=0;t--)this.words[t+n]=this.words[t];for(t=0;t<n;t++)this.words[t]=0;this.length+=n}return this.strip()},f.prototype.ishln=function(e){return i(0===this.negative),this.iushln(e)},f.prototype.iushrn=function(e,t,r){var n;i("number"==typeof e&&e>=0),n=t?(t-t%26)/26:0;var f=e%26,o=Math.min((e-f)/26,this.length),a=67108863^67108863>>>f<<f,s=r;if(n-=o,n=Math.max(0,n),s){for(var c=0;c<o;c++)s.words[c]=this.words[c];s.length=o}if(0===o);else if(this.length>o)for(this.length-=o,c=0;c<this.length;c++)this.words[c]=this.words[c+o];else this.words[0]=0,this.length=1;var h=0;for(c=this.length-1;c>=0&&(0!==h||c>=n);c--){var d=0|this.words[c];this.words[c]=h<<26-f|d>>>f,h=d&a}return s&&0!==h&&(s.words[s.length++]=h),0===this.length&&(this.words[0]=0,this.length=1),this.strip()},f.prototype.ishrn=function(e,t,r){return i(0===this.negative),this.iushrn(e,t,r)},f.prototype.shln=function(e){return this.clone().ishln(e)},f.prototype.ushln=function(e){return this.clone().iushln(e)},f.prototype.shrn=function(e){return this.clone().ishrn(e)},f.prototype.ushrn=function(e){return this.clone().iushrn(e)},f.prototype.testn=function(e){i("number"==typeof e&&e>=0);var t=e%26,r=(e-t)/26,n=1<<t;return!(this.length<=r)&&!!(this.words[r]&n)},f.prototype.imaskn=function(e){i("number"==typeof e&&e>=0);var t=e%26,r=(e-t)/26;if(i(0===this.negative,"imaskn works only with positive numbers"),this.length<=r)return this;if(0!==t&&r++,this.length=Math.min(r,this.length),0!==t){var n=67108863^67108863>>>t<<t;this.words[this.length-1]&=n}return this.strip()},f.prototype.maskn=function(e){return this.clone().imaskn(e)},f.prototype.iaddn=function(e){return i("number"==typeof e),i(e<67108864),e<0?this.isubn(-e):0!==this.negative?1===this.length&&(0|this.words[0])<e?(this.words[0]=e-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(e),this.negative=1,this):this._iaddn(e)},f.prototype._iaddn=function(e){this.words[0]+=e;for(var t=0;t<this.length&&this.words[t]>=67108864;t++)this.words[t]-=67108864,t===this.length-1?this.words[t+1]=1:this.words[t+1]++;return this.length=Math.max(this.length,t+1),this},f.prototype.isubn=function(e){if(i("number"==typeof e),i(e<67108864),e<0)return this.iaddn(-e);if(0!==this.negative)return this.negative=0,this.iaddn(e),this.negative=1,this;if(this.words[0]-=e,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var t=0;t<this.length&&this.words[t]<0;t++)this.words[t]+=67108864,this.words[t+1]-=1;return this.strip()},f.prototype.addn=function(e){return this.clone().iaddn(e)},f.prototype.subn=function(e){return this.clone().isubn(e)},f.prototype.iabs=function(){return this.negative=0,this},f.prototype.abs=function(){return this.clone().iabs()},f.prototype._ishlnsubmul=function(e,t,r){var n,f,o=e.length+r;this._expand(o);var a=0;for(n=0;n<e.length;n++){f=(0|this.words[n+r])+a;var s=(0|e.words[n])*t;a=((f-=67108863&s)>>26)-(s/67108864|0),this.words[n+r]=67108863&f}for(;n<this.length-r;n++)a=(f=(0|this.words[n+r])+a)>>26,this.words[n+r]=67108863&f;if(0===a)return this.strip();for(i(-1===a),a=0,n=0;n<this.length;n++)a=(f=-(0|this.words[n])+a)>>26,this.words[n]=67108863&f;return this.negative=1,this.strip()},f.prototype._wordDiv=function(e,t){var r=(this.length,e.length),i=this.clone(),n=e,o=0|n.words[n.length-1];0!==(r=26-this._countBits(o))&&(n=n.ushln(r),i.iushln(r),o=0|n.words[n.length-1]);var a,s=i.length-n.length;if("mod"!==t){(a=new f(null)).length=s+1,a.words=new Array(a.length);for(var c=0;c<a.length;c++)a.words[c]=0}var h=i.clone()._ishlnsubmul(n,1,s);0===h.negative&&(i=h,a&&(a.words[s]=1));for(var d=s-1;d>=0;d--){var u=67108864*(0|i.words[n.length+d])+(0|i.words[n.length+d-1]);for(u=Math.min(u/o|0,67108863),i._ishlnsubmul(n,u,d);0!==i.negative;)u--,i.negative=0,i._ishlnsubmul(n,1,d),i.isZero()||(i.negative^=1);a&&(a.words[d]=u)}return a&&a.strip(),i.strip(),"div"!==t&&0!==r&&i.iushrn(r),{div:a||null,mod:i}},f.prototype.divmod=function(e,t,r){return i(!e.isZero()),this.isZero()?{div:new f(0),mod:new f(0)}:0!==this.negative&&0===e.negative?(a=this.neg().divmod(e,t),"mod"!==t&&(n=a.div.neg()),"div"!==t&&(o=a.mod.neg(),r&&0!==o.negative&&o.iadd(e)),{div:n,mod:o}):0===this.negative&&0!==e.negative?(a=this.divmod(e.neg(),t),"mod"!==t&&(n=a.div.neg()),{div:n,mod:a.mod}):0!=(this.negative&e.negative)?(a=this.neg().divmod(e.neg(),t),"div"!==t&&(o=a.mod.neg(),r&&0!==o.negative&&o.isub(e)),{div:a.div,mod:o}):e.length>this.length||this.cmp(e)<0?{div:new f(0),mod:this}:1===e.length?"div"===t?{div:this.divn(e.words[0]),mod:null}:"mod"===t?{div:null,mod:new f(this.modn(e.words[0]))}:{div:this.divn(e.words[0]),mod:new f(this.modn(e.words[0]))}:this._wordDiv(e,t);var n,o,a},f.prototype.div=function(e){return this.divmod(e,"div",!1).div},f.prototype.mod=function(e){return this.divmod(e,"mod",!1).mod},f.prototype.umod=function(e){return this.divmod(e,"mod",!0).mod},f.prototype.divRound=function(e){var t=this.divmod(e);if(t.mod.isZero())return t.div;var r=0!==t.div.negative?t.mod.isub(e):t.mod,i=e.ushrn(1),n=e.andln(1),f=r.cmp(i);return f<0||1===n&&0===f?t.div:0!==t.div.negative?t.div.isubn(1):t.div.iaddn(1)},f.prototype.modn=function(e){i(e<=67108863);for(var t=(1<<26)%e,r=0,n=this.length-1;n>=0;n--)r=(t*r+(0|this.words[n]))%e;return r},f.prototype.idivn=function(e){i(e<=67108863);for(var t=0,r=this.length-1;r>=0;r--){var n=(0|this.words[r])+67108864*t;this.words[r]=n/e|0,t=n%e}return this.strip()},f.prototype.divn=function(e){return this.clone().idivn(e)},f.prototype.egcd=function(e){i(0===e.negative),i(!e.isZero());var t=this,r=e.clone();t=0!==t.negative?t.umod(e):t.clone();for(var n=new f(1),o=new f(0),a=new f(0),s=new f(1),c=0;t.isEven()&&r.isEven();)t.iushrn(1),r.iushrn(1),++c;for(var h=r.clone(),d=t.clone();!t.isZero();){for(var u=0,l=1;0==(t.words[0]&l)&&u<26;++u,l<<=1);if(u>0)for(t.iushrn(u);u-- >0;)(n.isOdd()||o.isOdd())&&(n.iadd(h),o.isub(d)),n.iushrn(1),o.iushrn(1);for(var p=0,b=1;0==(r.words[0]&b)&&p<26;++p,b<<=1);if(p>0)for(r.iushrn(p);p-- >0;)(a.isOdd()||s.isOdd())&&(a.iadd(h),s.isub(d)),a.iushrn(1),s.iushrn(1);t.cmp(r)>=0?(t.isub(r),n.isub(a),o.isub(s)):(r.isub(t),a.isub(n),s.isub(o))}return{a:a,b:s,gcd:r.iushln(c)}},f.prototype._invmp=function(e){i(0===e.negative),i(!e.isZero());var t=this,r=e.clone();t=0!==t.negative?t.umod(e):t.clone();for(var n,o=new f(1),a=new f(0),s=r.clone();t.cmpn(1)>0&&r.cmpn(1)>0;){for(var c=0,h=1;0==(t.words[0]&h)&&c<26;++c,h<<=1);if(c>0)for(t.iushrn(c);c-- >0;)o.isOdd()&&o.iadd(s),o.iushrn(1);for(var d=0,u=1;0==(r.words[0]&u)&&d<26;++d,u<<=1);if(d>0)for(r.iushrn(d);d-- >0;)a.isOdd()&&a.iadd(s),a.iushrn(1);t.cmp(r)>=0?(t.isub(r),o.isub(a)):(r.isub(t),a.isub(o))}return(n=0===t.cmpn(1)?o:a).cmpn(0)<0&&n.iadd(e),n},f.prototype.gcd=function(e){if(this.isZero())return e.abs();if(e.isZero())return this.abs();var t=this.clone(),r=e.clone();t.negative=0,r.negative=0;for(var i=0;t.isEven()&&r.isEven();i++)t.iushrn(1),r.iushrn(1);for(;;){for(;t.isEven();)t.iushrn(1);for(;r.isEven();)r.iushrn(1);var n=t.cmp(r);if(n<0){var f=t;t=r,r=f}else if(0===n||0===r.cmpn(1))break;t.isub(r)}return r.iushln(i)},f.prototype.invm=function(e){return this.egcd(e).a.umod(e)},f.prototype.isEven=function(){return 0==(1&this.words[0])},f.prototype.isOdd=function(){return 1==(1&this.words[0])},f.prototype.andln=function(e){return this.words[0]&e},f.prototype.bincn=function(e){i("number"==typeof e);var t=e%26,r=(e-t)/26,n=1<<t;if(this.length<=r)return this._expand(r+1),this.words[r]|=n,this;for(var f=n,o=r;0!==f&&o<this.length;o++){var a=0|this.words[o];f=(a+=f)>>>26,a&=67108863,this.words[o]=a}return 0!==f&&(this.words[o]=f,this.length++),this},f.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},f.prototype.cmpn=function(e){var t,r=e<0;if(0!==this.negative&&!r)return-1;if(0===this.negative&&r)return 1;if(this.strip(),this.length>1)t=1;else{r&&(e=-e),i(e<=67108863,"Number is too big");var n=0|this.words[0];t=n===e?0:n<e?-1:1}return 0!==this.negative?0|-t:t},f.prototype.cmp=function(e){if(0!==this.negative&&0===e.negative)return-1;if(0===this.negative&&0!==e.negative)return 1;var t=this.ucmp(e);return 0!==this.negative?0|-t:t},f.prototype.ucmp=function(e){if(this.length>e.length)return 1;if(this.length<e.length)return-1;for(var t=0,r=this.length-1;r>=0;r--){var i=0|this.words[r],n=0|e.words[r];if(i!==n){i<n?t=-1:i>n&&(t=1);break}}return t},f.prototype.gtn=function(e){return 1===this.cmpn(e)},f.prototype.gt=function(e){return 1===this.cmp(e)},f.prototype.gten=function(e){return this.cmpn(e)>=0},f.prototype.gte=function(e){return this.cmp(e)>=0},f.prototype.ltn=function(e){return-1===this.cmpn(e)},f.prototype.lt=function(e){return-1===this.cmp(e)},f.prototype.lten=function(e){return this.cmpn(e)<=0},f.prototype.lte=function(e){return this.cmp(e)<=0},f.prototype.eqn=function(e){return 0===this.cmpn(e)},f.prototype.eq=function(e){return 0===this.cmp(e)},f.red=function(e){return new M(e)},f.prototype.toRed=function(e){return i(!this.red,"Already a number in reduction context"),i(0===this.negative,"red works only with positives"),e.convertTo(this)._forceRed(e)},f.prototype.fromRed=function(){return i(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},f.prototype._forceRed=function(e){return this.red=e,this},f.prototype.forceRed=function(e){return i(!this.red,"Already a number in reduction context"),this._forceRed(e)},f.prototype.redAdd=function(e){return i(this.red,"redAdd works only with red numbers"),this.red.add(this,e)},f.prototype.redIAdd=function(e){return i(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,e)},f.prototype.redSub=function(e){return i(this.red,"redSub works only with red numbers"),this.red.sub(this,e)},f.prototype.redISub=function(e){return i(this.red,"redISub works only with red numbers"),this.red.isub(this,e)},f.prototype.redShl=function(e){return i(this.red,"redShl works only with red numbers"),this.red.shl(this,e)},f.prototype.redMul=function(e){return i(this.red,"redMul works only with red numbers"),this.red._verify2(this,e),this.red.mul(this,e)},f.prototype.redIMul=function(e){return i(this.red,"redMul works only with red numbers"),this.red._verify2(this,e),this.red.imul(this,e)},f.prototype.redSqr=function(){return i(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},f.prototype.redISqr=function(){return i(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},f.prototype.redSqrt=function(){return i(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},f.prototype.redInvm=function(){return i(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},f.prototype.redNeg=function(){return i(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},f.prototype.redPow=function(e){return i(this.red&&!e.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,e)};var g={k256:null,p224:null,p192:null,p25519:null};function m(e,t){this.name=e,this.p=new f(t,16),this.n=this.p.bitLength(),this.k=new f(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function v(){m.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function _(){m.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function w(){m.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function S(){m.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function M(e){if("string"==typeof e){var t=f._prime(e);this.m=t.p,this.prime=t}else i(e.gtn(1),"modulus must be greater than 1"),this.m=e,this.prime=null}function E(e){M.call(this,e),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new f(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}m.prototype._tmp=function(){var e=new f(null);return e.words=new Array(Math.ceil(this.n/13)),e},m.prototype.ireduce=function(e){var t,r=e;do{this.split(r,this.tmp),t=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength()}while(t>this.n);var i=t<this.n?-1:r.ucmp(this.p);return 0===i?(r.words[0]=0,r.length=1):i>0?r.isub(this.p):void 0!==r.strip?r.strip():r._strip(),r},m.prototype.split=function(e,t){e.iushrn(this.n,0,t)},m.prototype.imulK=function(e){return e.imul(this.k)},n(v,m),v.prototype.split=function(e,t){for(var r=Math.min(e.length,9),i=0;i<r;i++)t.words[i]=e.words[i];if(t.length=r,e.length<=9)return e.words[0]=0,void(e.length=1);var n=e.words[9];for(t.words[t.length++]=4194303&n,i=10;i<e.length;i++){var f=0|e.words[i];e.words[i-10]=(4194303&f)<<4|n>>>22,n=f}n>>>=22,e.words[i-10]=n,0===n&&e.length>10?e.length-=10:e.length-=9},v.prototype.imulK=function(e){e.words[e.length]=0,e.words[e.length+1]=0,e.length+=2;for(var t=0,r=0;r<e.length;r++){var i=0|e.words[r];t+=977*i,e.words[r]=67108863&t,t=64*i+(t/67108864|0)}return 0===e.words[e.length-1]&&(e.length--,0===e.words[e.length-1]&&e.length--),e},n(_,m),n(w,m),n(S,m),S.prototype.imulK=function(e){for(var t=0,r=0;r<e.length;r++){var i=19*(0|e.words[r])+t,n=67108863&i;i>>>=26,e.words[r]=n,t=i}return 0!==t&&(e.words[e.length++]=t),e},f._prime=function(e){if(g[e])return g[e];var t;if("k256"===e)t=new v;else if("p224"===e)t=new _;else if("p192"===e)t=new w;else{if("p25519"!==e)throw new Error("Unknown prime "+e);t=new S}return g[e]=t,t},M.prototype._verify1=function(e){i(0===e.negative,"red works only with positives"),i(e.red,"red works only with red numbers")},M.prototype._verify2=function(e,t){i(0==(e.negative|t.negative),"red works only with positives"),i(e.red&&e.red===t.red,"red works only with red numbers")},M.prototype.imod=function(e){return this.prime?this.prime.ireduce(e)._forceRed(this):e.umod(this.m)._forceRed(this)},M.prototype.neg=function(e){return e.isZero()?e.clone():this.m.sub(e)._forceRed(this)},M.prototype.add=function(e,t){this._verify2(e,t);var r=e.add(t);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},M.prototype.iadd=function(e,t){this._verify2(e,t);var r=e.iadd(t);return r.cmp(this.m)>=0&&r.isub(this.m),r},M.prototype.sub=function(e,t){this._verify2(e,t);var r=e.sub(t);return r.cmpn(0)<0&&r.iadd(this.m),r._forceRed(this)},M.prototype.isub=function(e,t){this._verify2(e,t);var r=e.isub(t);return r.cmpn(0)<0&&r.iadd(this.m),r},M.prototype.shl=function(e,t){return this._verify1(e),this.imod(e.ushln(t))},M.prototype.imul=function(e,t){return this._verify2(e,t),this.imod(e.imul(t))},M.prototype.mul=function(e,t){return this._verify2(e,t),this.imod(e.mul(t))},M.prototype.isqr=function(e){return this.imul(e,e.clone())},M.prototype.sqr=function(e){return this.mul(e,e)},M.prototype.sqrt=function(e){if(e.isZero())return e.clone();var t=this.m.andln(3);if(i(t%2==1),3===t){var r=this.m.add(new f(1)).iushrn(2);return this.pow(e,r)}for(var n=this.m.subn(1),o=0;!n.isZero()&&0===n.andln(1);)o++,n.iushrn(1);i(!n.isZero());var a=new f(1).toRed(this),s=a.redNeg(),c=this.m.subn(1).iushrn(1),h=this.m.bitLength();for(h=new f(2*h*h).toRed(this);0!==this.pow(h,c).cmp(s);)h.redIAdd(s);for(var d=this.pow(h,n),u=this.pow(e,n.addn(1).iushrn(1)),l=this.pow(e,n),p=o;0!==l.cmp(a);){for(var b=l,y=0;0!==b.cmp(a);y++)b=b.redSqr();i(y<p);var g=this.pow(d,new f(1).iushln(p-y-1));u=u.redMul(g),d=g.redSqr(),l=l.redMul(d),p=y}return u},M.prototype.invm=function(e){var t=e._invmp(this.m);return 0!==t.negative?(t.negative=0,this.imod(t).redNeg()):this.imod(t)},M.prototype.pow=function(e,t){if(t.isZero())return new f(1).toRed(this);if(0===t.cmpn(1))return e.clone();var r=new Array(16);r[0]=new f(1).toRed(this),r[1]=e;for(var i=2;i<r.length;i++)r[i]=this.mul(r[i-1],e);var n=r[0],o=0,a=0,s=t.bitLength()%26;for(0===s&&(s=26),i=t.length-1;i>=0;i--){for(var c=t.words[i],h=s-1;h>=0;h--){var d=c>>h&1;n!==r[0]&&(n=this.sqr(n)),0!==d||0!==o?(o<<=1,o|=d,(4===++a||0===i&&0===h)&&(n=this.mul(n,r[o]),a=0,o=0)):a=0}s=26}return n},M.prototype.convertTo=function(e){var t=e.umod(this.m);return t===e?t.clone():t},M.prototype.convertFrom=function(e){var t=e.clone();return t.red=null,t},f.mont=function(e){return new E(e)},n(E,M),E.prototype.convertTo=function(e){return this.imod(e.ushln(this.shift))},E.prototype.convertFrom=function(e){var t=this.imod(e.mul(this.rinv));return t.red=null,t},E.prototype.imul=function(e,t){if(e.isZero()||t.isZero())return e.words[0]=0,e.length=1,e;var r=e.imul(t),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=r.isub(i).iushrn(this.shift),f=n;return n.cmp(this.m)>=0?f=n.isub(this.m):n.cmpn(0)<0&&(f=n.iadd(this.m)),f._forceRed(this)},E.prototype.mul=function(e,t){if(e.isZero()||t.isZero())return new f(0)._forceRed(this);var r=e.mul(t),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=r.isub(i).iushrn(this.shift),o=n;return n.cmp(this.m)>=0?o=n.isub(this.m):n.cmpn(0)<0&&(o=n.iadd(this.m)),o._forceRed(this)},E.prototype.invm=function(e){return this.imod(e._invmp(this.m).mul(this.r2))._forceRed(this)}}(e,this)}).call(this,r(60)(e))},function(e,t,r){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var i=r(447),n=r(448),f=r(366);function o(){return s.TYPED_ARRAY_SUPPORT?**********:**********}function a(e,t){if(o()<t)throw new RangeError("Invalid typed array length");return s.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=s.prototype:(null===e&&(e=new s(t)),e.length=t),e}function s(e,t,r){if(!(s.TYPED_ARRAY_SUPPORT||this instanceof s))return new s(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return d(this,e)}return c(this,e,t,r)}function c(e,t,r,i){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,i){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(i||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===i?new Uint8Array(t):void 0===i?new Uint8Array(t,r):new Uint8Array(t,r,i);s.TYPED_ARRAY_SUPPORT?(e=t).__proto__=s.prototype:e=u(e,t);return e}(e,t,r,i):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!s.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var i=0|p(t,r),n=(e=a(e,i)).write(t,r);n!==i&&(e=e.slice(0,n));return e}(e,t,r):function(e,t){if(s.isBuffer(t)){var r=0|l(t.length);return 0===(e=a(e,r)).length||t.copy(e,0,0,r),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(i=t.length)!=i?a(e,0):u(e,t);if("Buffer"===t.type&&f(t.data))return u(e,t.data)}var i;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function h(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function d(e,t){if(h(t),e=a(e,t<0?0:0|l(t)),!s.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function u(e,t){var r=t.length<0?0:0|l(t.length);e=a(e,r);for(var i=0;i<r;i+=1)e[i]=255&t[i];return e}function l(e){if(e>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|e}function p(e,t){if(s.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return N(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return z(e).length;default:if(i)return N(e).length;t=(""+t).toLowerCase(),i=!0}}function b(e,t,r){var i=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return I(this,t,r);case"utf8":case"utf-8":return A(this,t,r);case"ascii":return x(this,t,r);case"latin1":case"binary":return B(this,t,r);case"base64":return k(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,t,r);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}function y(e,t,r){var i=e[t];e[t]=e[r],e[r]=i}function g(e,t,r,i,n){if(0===e.length)return-1;if("string"==typeof r?(i=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=n?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(n)return-1;r=e.length-1}else if(r<0){if(!n)return-1;r=0}if("string"==typeof t&&(t=s.from(t,i)),s.isBuffer(t))return 0===t.length?-1:m(e,t,r,i,n);if("number"==typeof t)return t&=255,s.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):m(e,[t],r,i,n);throw new TypeError("val must be string, number or Buffer")}function m(e,t,r,i,n){var f,o=1,a=e.length,s=t.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||t.length<2)return-1;o=2,a/=2,s/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(n){var h=-1;for(f=r;f<a;f++)if(c(e,f)===c(t,-1===h?0:f-h)){if(-1===h&&(h=f),f-h+1===s)return h*o}else-1!==h&&(f-=f-h),h=-1}else for(r+s>a&&(r=a-s),f=r;f>=0;f--){for(var d=!0,u=0;u<s;u++)if(c(e,f+u)!==c(t,u)){d=!1;break}if(d)return f}return-1}function v(e,t,r,i){r=Number(r)||0;var n=e.length-r;i?(i=Number(i))>n&&(i=n):i=n;var f=t.length;if(f%2!=0)throw new TypeError("Invalid hex string");i>f/2&&(i=f/2);for(var o=0;o<i;++o){var a=parseInt(t.substr(2*o,2),16);if(isNaN(a))return o;e[r+o]=a}return o}function _(e,t,r,i){return K(N(t,e.length-r),e,r,i)}function w(e,t,r,i){return K(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,i)}function S(e,t,r,i){return w(e,t,r,i)}function M(e,t,r,i){return K(z(t),e,r,i)}function E(e,t,r,i){return K(function(e,t){for(var r,i,n,f=[],o=0;o<e.length&&!((t-=2)<0);++o)r=e.charCodeAt(o),i=r>>8,n=r%256,f.push(n),f.push(i);return f}(t,e.length-r),e,r,i)}function k(e,t,r){return 0===t&&r===e.length?i.fromByteArray(e):i.fromByteArray(e.slice(t,r))}function A(e,t,r){r=Math.min(e.length,r);for(var i=[],n=t;n<r;){var f,o,a,s,c=e[n],h=null,d=c>239?4:c>223?3:c>191?2:1;if(n+d<=r)switch(d){case 1:c<128&&(h=c);break;case 2:128==(192&(f=e[n+1]))&&(s=(31&c)<<6|63&f)>127&&(h=s);break;case 3:f=e[n+1],o=e[n+2],128==(192&f)&&128==(192&o)&&(s=(15&c)<<12|(63&f)<<6|63&o)>2047&&(s<55296||s>57343)&&(h=s);break;case 4:f=e[n+1],o=e[n+2],a=e[n+3],128==(192&f)&&128==(192&o)&&128==(192&a)&&(s=(15&c)<<18|(63&f)<<12|(63&o)<<6|63&a)>65535&&s<1114112&&(h=s)}null===h?(h=65533,d=1):h>65535&&(h-=65536,i.push(h>>>10&1023|55296),h=56320|1023&h),i.push(h),n+=d}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);var r="",i=0;for(;i<t;)r+=String.fromCharCode.apply(String,e.slice(i,i+=4096));return r}(i)}t.Buffer=s,t.SlowBuffer=function(e){+e!=e&&(e=0);return s.alloc(+e)},t.INSPECT_MAX_BYTES=50,s.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=o(),s.poolSize=8192,s._augment=function(e){return e.__proto__=s.prototype,e},s.from=function(e,t,r){return c(null,e,t,r)},s.TYPED_ARRAY_SUPPORT&&(s.prototype.__proto__=Uint8Array.prototype,s.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0})),s.alloc=function(e,t,r){return function(e,t,r,i){return h(t),t<=0?a(e,t):void 0!==r?"string"==typeof i?a(e,t).fill(r,i):a(e,t).fill(r):a(e,t)}(null,e,t,r)},s.allocUnsafe=function(e){return d(null,e)},s.allocUnsafeSlow=function(e){return d(null,e)},s.isBuffer=function(e){return!(null==e||!e._isBuffer)},s.compare=function(e,t){if(!s.isBuffer(e)||!s.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,i=t.length,n=0,f=Math.min(r,i);n<f;++n)if(e[n]!==t[n]){r=e[n],i=t[n];break}return r<i?-1:i<r?1:0},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!f(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var i=s.allocUnsafe(t),n=0;for(r=0;r<e.length;++r){var o=e[r];if(!s.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(i,n),n+=o.length}return i},s.byteLength=p,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)y(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)y(this,t,t+3),y(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)y(this,t,t+7),y(this,t+1,t+6),y(this,t+2,t+5),y(this,t+3,t+4);return this},s.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?A(this,0,e):b.apply(this,arguments)},s.prototype.equals=function(e){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},s.prototype.compare=function(e,t,r,i,n){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===i&&(i=0),void 0===n&&(n=this.length),t<0||r>e.length||i<0||n>this.length)throw new RangeError("out of range index");if(i>=n&&t>=r)return 0;if(i>=n)return-1;if(t>=r)return 1;if(this===e)return 0;for(var f=(n>>>=0)-(i>>>=0),o=(r>>>=0)-(t>>>=0),a=Math.min(f,o),c=this.slice(i,n),h=e.slice(t,r),d=0;d<a;++d)if(c[d]!==h[d]){f=c[d],o=h[d];break}return f<o?-1:o<f?1:0},s.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},s.prototype.indexOf=function(e,t,r){return g(this,e,t,r,!0)},s.prototype.lastIndexOf=function(e,t,r){return g(this,e,t,r,!1)},s.prototype.write=function(e,t,r,i){if(void 0===t)i="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)i=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===i&&(i="utf8")):(i=r,r=void 0)}var n=this.length-t;if((void 0===r||r>n)&&(r=n),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var f=!1;;)switch(i){case"hex":return v(this,e,t,r);case"utf8":case"utf-8":return _(this,e,t,r);case"ascii":return w(this,e,t,r);case"latin1":case"binary":return S(this,e,t,r);case"base64":return M(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,e,t,r);default:if(f)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),f=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function x(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n)i+=String.fromCharCode(127&e[n]);return i}function B(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n)i+=String.fromCharCode(e[n]);return i}function I(e,t,r){var i=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>i)&&(r=i);for(var n="",f=t;f<r;++f)n+=q(e[f]);return n}function R(e,t,r){for(var i=e.slice(t,r),n="",f=0;f<i.length;f+=2)n+=String.fromCharCode(i[f]+256*i[f+1]);return n}function P(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function C(e,t,r,i,n,f){if(!s.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<f)throw new RangeError('"value" argument is out of bounds');if(r+i>e.length)throw new RangeError("Index out of range")}function T(e,t,r,i){t<0&&(t=65535+t+1);for(var n=0,f=Math.min(e.length-r,2);n<f;++n)e[r+n]=(t&255<<8*(i?n:1-n))>>>8*(i?n:1-n)}function j(e,t,r,i){t<0&&(t=4294967295+t+1);for(var n=0,f=Math.min(e.length-r,4);n<f;++n)e[r+n]=t>>>8*(i?n:3-n)&255}function L(e,t,r,i,n,f){if(r+i>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function O(e,t,r,i,f){return f||L(e,0,r,4),n.write(e,t,r,i,23,4),r+4}function D(e,t,r,i,f){return f||L(e,0,r,8),n.write(e,t,r,i,52,8),r+8}s.prototype.slice=function(e,t){var r,i=this.length;if((e=~~e)<0?(e+=i)<0&&(e=0):e>i&&(e=i),(t=void 0===t?i:~~t)<0?(t+=i)<0&&(t=0):t>i&&(t=i),t<e&&(t=e),s.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=s.prototype;else{var n=t-e;r=new s(n,void 0);for(var f=0;f<n;++f)r[f]=this[f+e]}return r},s.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||P(e,t,this.length);for(var i=this[e],n=1,f=0;++f<t&&(n*=256);)i+=this[e+f]*n;return i},s.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||P(e,t,this.length);for(var i=this[e+--t],n=1;t>0&&(n*=256);)i+=this[e+--t]*n;return i},s.prototype.readUInt8=function(e,t){return t||P(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return t||P(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return t||P(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return t||P(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},s.prototype.readUInt32BE=function(e,t){return t||P(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||P(e,t,this.length);for(var i=this[e],n=1,f=0;++f<t&&(n*=256);)i+=this[e+f]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*t)),i},s.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||P(e,t,this.length);for(var i=t,n=1,f=this[e+--i];i>0&&(n*=256);)f+=this[e+--i]*n;return f>=(n*=128)&&(f-=Math.pow(2,8*t)),f},s.prototype.readInt8=function(e,t){return t||P(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},s.prototype.readInt16LE=function(e,t){t||P(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},s.prototype.readInt16BE=function(e,t){t||P(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},s.prototype.readInt32LE=function(e,t){return t||P(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return t||P(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return t||P(e,4,this.length),n.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return t||P(e,4,this.length),n.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return t||P(e,8,this.length),n.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return t||P(e,8,this.length),n.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,r,i){(e=+e,t|=0,r|=0,i)||C(this,e,t,r,Math.pow(2,8*r)-1,0);var n=1,f=0;for(this[t]=255&e;++f<r&&(n*=256);)this[t+f]=e/n&255;return t+r},s.prototype.writeUIntBE=function(e,t,r,i){(e=+e,t|=0,r|=0,i)||C(this,e,t,r,Math.pow(2,8*r)-1,0);var n=r-1,f=1;for(this[t+n]=255&e;--n>=0&&(f*=256);)this[t+n]=e/f&255;return t+r},s.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,1,255,0),s.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):T(this,e,t,!0),t+2},s.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):T(this,e,t,!1),t+2},s.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):j(this,e,t,!0),t+4},s.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},s.prototype.writeIntLE=function(e,t,r,i){if(e=+e,t|=0,!i){var n=Math.pow(2,8*r-1);C(this,e,t,r,n-1,-n)}var f=0,o=1,a=0;for(this[t]=255&e;++f<r&&(o*=256);)e<0&&0===a&&0!==this[t+f-1]&&(a=1),this[t+f]=(e/o>>0)-a&255;return t+r},s.prototype.writeIntBE=function(e,t,r,i){if(e=+e,t|=0,!i){var n=Math.pow(2,8*r-1);C(this,e,t,r,n-1,-n)}var f=r-1,o=1,a=0;for(this[t+f]=255&e;--f>=0&&(o*=256);)e<0&&0===a&&0!==this[t+f+1]&&(a=1),this[t+f]=(e/o>>0)-a&255;return t+r},s.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,1,127,-128),s.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):T(this,e,t,!0),t+2},s.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):T(this,e,t,!1),t+2},s.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,**********,-2147483648),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):j(this,e,t,!0),t+4},s.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||C(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},s.prototype.writeFloatLE=function(e,t,r){return O(this,e,t,!0,r)},s.prototype.writeFloatBE=function(e,t,r){return O(this,e,t,!1,r)},s.prototype.writeDoubleLE=function(e,t,r){return D(this,e,t,!0,r)},s.prototype.writeDoubleBE=function(e,t,r){return D(this,e,t,!1,r)},s.prototype.copy=function(e,t,r,i){if(r||(r=0),i||0===i||(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<r&&(i=r),i===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-r&&(i=e.length-t+r);var n,f=i-r;if(this===e&&r<t&&t<i)for(n=f-1;n>=0;--n)e[n+t]=this[n+r];else if(f<1e3||!s.TYPED_ARRAY_SUPPORT)for(n=0;n<f;++n)e[n+t]=this[n+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+f),t);return f},s.prototype.fill=function(e,t,r,i){if("string"==typeof e){if("string"==typeof t?(i=t,t=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),1===e.length){var n=e.charCodeAt(0);n<256&&(e=n)}if(void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!s.isEncoding(i))throw new TypeError("Unknown encoding: "+i)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var f;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(f=t;f<r;++f)this[f]=e;else{var o=s.isBuffer(e)?e:N(new s(e,i).toString()),a=o.length;for(f=0;f<r-t;++f)this[f+t]=o[f%a]}return this};var U=/[^+\/0-9A-Za-z-_]/g;function q(e){return e<16?"0"+e.toString(16):e.toString(16)}function N(e,t){var r;t=t||1/0;for(var i=e.length,n=null,f=[],o=0;o<i;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!n){if(r>56319){(t-=3)>-1&&f.push(239,191,189);continue}if(o+1===i){(t-=3)>-1&&f.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&f.push(239,191,189),n=r;continue}r=65536+(n-55296<<10|r-56320)}else n&&(t-=3)>-1&&f.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;f.push(r)}else if(r<2048){if((t-=2)<0)break;f.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;f.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;f.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return f}function z(e){return i.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(U,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function K(e,t,r,i){for(var n=0;n<i&&!(n+r>=t.length||n>=e.length);++n)t[n+r]=e[n];return n}}).call(this,r(16))},function(e,t){function r(e,t){if(!e)throw new Error(t||"Assertion failed")}e.exports=r,r.equal=function(e,t,r){if(e!=t)throw new Error(r||"Assertion failed: "+e+" != "+t)}},function(e,t,r){"use strict";var i=t,n=r(276),f=r(278),o=r(396);i.assert=f,i.toArray=o.toArray,i.zero2=o.zero2,i.toHex=o.toHex,i.encode=o.encode,i.getNAF=function(e,t,r){var i=new Array(Math.max(e.bitLength(),r)+1);i.fill(0);for(var n=1<<t+1,f=e.clone(),o=0;o<i.length;o++){var a,s=f.andln(n-1);f.isOdd()?(a=s>(n>>1)-1?(n>>1)-s:s,f.isubn(a)):a=0,i[o]=a,f.iushrn(1)}return i},i.getJSF=function(e,t){var r=[[],[]];e=e.clone(),t=t.clone();for(var i,n=0,f=0;e.cmpn(-n)>0||t.cmpn(-f)>0;){var o,a,s=e.andln(3)+n&3,c=t.andln(3)+f&3;3===s&&(s=-1),3===c&&(c=-1),o=0==(1&s)?0:3!==(i=e.andln(7)+n&7)&&5!==i||2!==c?s:-s,r[0].push(o),a=0==(1&c)?0:3!==(i=t.andln(7)+f&7)&&5!==i||2!==s?c:-c,r[1].push(a),2*n===o+1&&(n=1-n),2*f===a+1&&(f=1-f),e.iushrn(1),t.iushrn(1)}return r},i.cachedProperty=function(e,t,r){var i="_"+t;e.prototype[t]=function(){return void 0!==this[i]?this[i]:this[i]=r.call(this)}},i.parseBytes=function(e){return"string"==typeof e?i.toArray(e,"hex"):e},i.intFromLE=function(e){return new n(e,"hex","le")}},function(e,t,r){"use strict";var i=r(278),n=r(275);function f(e,t){return 55296==(64512&e.charCodeAt(t))&&(!(t<0||t+1>=e.length)&&56320==(64512&e.charCodeAt(t+1)))}function o(e){return(e>>>24|e>>>8&65280|e<<8&16711680|(255&e)<<24)>>>0}function a(e){return 1===e.length?"0"+e:e}function s(e){return 7===e.length?"0"+e:6===e.length?"00"+e:5===e.length?"000"+e:4===e.length?"0000"+e:3===e.length?"00000"+e:2===e.length?"000000"+e:1===e.length?"0000000"+e:e}t.inherits=n,t.toArray=function(e,t){if(Array.isArray(e))return e.slice();if(!e)return[];var r=[];if("string"==typeof e)if(t){if("hex"===t)for((e=e.replace(/[^a-z0-9]+/gi,"")).length%2!=0&&(e="0"+e),n=0;n<e.length;n+=2)r.push(parseInt(e[n]+e[n+1],16))}else for(var i=0,n=0;n<e.length;n++){var o=e.charCodeAt(n);o<128?r[i++]=o:o<2048?(r[i++]=o>>6|192,r[i++]=63&o|128):f(e,n)?(o=65536+((1023&o)<<10)+(1023&e.charCodeAt(++n)),r[i++]=o>>18|240,r[i++]=o>>12&63|128,r[i++]=o>>6&63|128,r[i++]=63&o|128):(r[i++]=o>>12|224,r[i++]=o>>6&63|128,r[i++]=63&o|128)}else for(n=0;n<e.length;n++)r[n]=0|e[n];return r},t.toHex=function(e){for(var t="",r=0;r<e.length;r++)t+=a(e[r].toString(16));return t},t.htonl=o,t.toHex32=function(e,t){for(var r="",i=0;i<e.length;i++){var n=e[i];"little"===t&&(n=o(n)),r+=s(n.toString(16))}return r},t.zero2=a,t.zero8=s,t.join32=function(e,t,r,n){var f=r-t;i(f%4==0);for(var o=new Array(f/4),a=0,s=t;a<o.length;a++,s+=4){var c;c="big"===n?e[s]<<24|e[s+1]<<16|e[s+2]<<8|e[s+3]:e[s+3]<<24|e[s+2]<<16|e[s+1]<<8|e[s],o[a]=c>>>0}return o},t.split32=function(e,t){for(var r=new Array(4*e.length),i=0,n=0;i<e.length;i++,n+=4){var f=e[i];"big"===t?(r[n]=f>>>24,r[n+1]=f>>>16&255,r[n+2]=f>>>8&255,r[n+3]=255&f):(r[n+3]=f>>>24,r[n+2]=f>>>16&255,r[n+1]=f>>>8&255,r[n]=255&f)}return r},t.rotr32=function(e,t){return e>>>t|e<<32-t},t.rotl32=function(e,t){return e<<t|e>>>32-t},t.sum32=function(e,t){return e+t>>>0},t.sum32_3=function(e,t,r){return e+t+r>>>0},t.sum32_4=function(e,t,r,i){return e+t+r+i>>>0},t.sum32_5=function(e,t,r,i,n){return e+t+r+i+n>>>0},t.sum64=function(e,t,r,i){var n=e[t],f=i+e[t+1]>>>0,o=(f<i?1:0)+r+n;e[t]=o>>>0,e[t+1]=f},t.sum64_hi=function(e,t,r,i){return(t+i>>>0<t?1:0)+e+r>>>0},t.sum64_lo=function(e,t,r,i){return t+i>>>0},t.sum64_4_hi=function(e,t,r,i,n,f,o,a){var s=0,c=t;return s+=(c=c+i>>>0)<t?1:0,s+=(c=c+f>>>0)<f?1:0,e+r+n+o+(s+=(c=c+a>>>0)<a?1:0)>>>0},t.sum64_4_lo=function(e,t,r,i,n,f,o,a){return t+i+f+a>>>0},t.sum64_5_hi=function(e,t,r,i,n,f,o,a,s,c){var h=0,d=t;return h+=(d=d+i>>>0)<t?1:0,h+=(d=d+f>>>0)<f?1:0,h+=(d=d+a>>>0)<a?1:0,e+r+n+o+s+(h+=(d=d+c>>>0)<c?1:0)>>>0},t.sum64_5_lo=function(e,t,r,i,n,f,o,a,s,c){return t+i+f+a+c>>>0},t.rotr64_hi=function(e,t,r){return(t<<32-r|e>>>r)>>>0},t.rotr64_lo=function(e,t,r){return(e<<32-r|t>>>r)>>>0},t.shr64_hi=function(e,t,r){return e>>>r},t.shr64_lo=function(e,t,r){return(e<<32-r|t>>>r)>>>0}},function(e,t,r){var i=r(274).Buffer,n=r(456).Transform,f=r(316).StringDecoder;function o(e){n.call(this),this.hashMode="string"==typeof e,this.hashMode?this[e]=this._finalOrDigest:this.final=this._finalOrDigest,this._final&&(this.__final=this._final,this._final=null),this._decoder=null,this._encoding=null}r(275)(o,n),o.prototype.update=function(e,t,r){"string"==typeof e&&(e=i.from(e,t));var n=this._update(e);return this.hashMode?this:(r&&(n=this._toString(n,r)),n)},o.prototype.setAutoPadding=function(){},o.prototype.getAuthTag=function(){throw new Error("trying to get auth tag in unsupported state")},o.prototype.setAuthTag=function(){throw new Error("trying to set auth tag in unsupported state")},o.prototype.setAAD=function(){throw new Error("trying to set aad in unsupported state")},o.prototype._transform=function(e,t,r){var i;try{this.hashMode?this._update(e):this.push(this._update(e))}catch(e){i=e}finally{r(i)}},o.prototype._flush=function(e){var t;try{this.push(this.__final())}catch(e){t=e}e(t)},o.prototype._finalOrDigest=function(e){var t=this.__final()||i.alloc(0);return e&&(t=this._toString(t,e,!0)),t},o.prototype._toString=function(e,t,r){if(this._decoder||(this._decoder=new f(t),this._encoding=t),this._encoding!==t)throw new Error("can't switch encodings");var i=this._decoder.write(e);return r&&(i+=this._decoder.end()),i},e.exports=o},,function(e,t,r){"use strict";var i=r(298),n=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};e.exports=d;var f=Object.create(r(294));f.inherits=r(275);var o=r(369),a=r(315);f.inherits(d,o);for(var s=n(a.prototype),c=0;c<s.length;c++){var h=s[c];d.prototype[h]||(d.prototype[h]=a.prototype[h])}function d(e){if(!(this instanceof d))return new d(e);o.call(this,e),a.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",u)}function u(){this.allowHalfOpen||this._writableState.ended||i.nextTick(l,this)}function l(e){e.end()}Object.defineProperty(d.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(d.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),d.prototype._destroy=function(e,t){this.push(null),this.end(),i.nextTick(t,e)}},,,,function(e,t,r){"use strict";(function(t,i){var n=r(274).Buffer,f=t.crypto||t.msCrypto;f&&f.getRandomValues?e.exports=function(e,t){if(e>4294967295)throw new RangeError("requested too many random bytes");var r=n.allocUnsafe(e);if(e>0)if(e>65536)for(var o=0;o<e;o+=65536)f.getRandomValues(r.slice(o,o+65536));else f.getRandomValues(r);if("function"==typeof t)return i.nextTick((function(){t(null,r)}));return r}:e.exports=function(){throw new Error("Secure random number generation is not supported by this browser.\nUse Chrome, Firefox or Internet Explorer 11")}}).call(this,r(16),r(109))},function(e,t,r){var i=r(274).Buffer;function n(e,t){this._block=i.alloc(e),this._finalSize=t,this._blockSize=e,this._len=0}n.prototype.update=function(e,t){"string"==typeof e&&(t=t||"utf8",e=i.from(e,t));for(var r=this._block,n=this._blockSize,f=e.length,o=this._len,a=0;a<f;){for(var s=o%n,c=Math.min(f-a,n-s),h=0;h<c;h++)r[s+h]=e[a+h];a+=c,(o+=c)%n==0&&this._update(r)}return this._len+=f,this},n.prototype.digest=function(e){var t=this._len%this._blockSize;this._block[t]=128,this._block.fill(0,t+1),t>=this._finalSize&&(this._update(this._block),this._block.fill(0));var r=8*this._len;if(r<=4294967295)this._block.writeUInt32BE(r,this._blockSize-4);else{var i=(4294967295&r)>>>0,n=(r-i)/4294967296;this._block.writeUInt32BE(n,this._blockSize-8),this._block.writeUInt32BE(i,this._blockSize-4)}this._update(this._block);var f=this._hash();return e?f.toString(e):f},n.prototype._update=function(){throw new Error("_update must be implemented by subclass")},e.exports=n},,,,function(e,t,r){"use strict";var i=r(275),n=r(313),f=r(317),o=r(318),a=r(281);function s(e){a.call(this,"digest"),this._hash=e}i(s,a),s.prototype._update=function(e){this._hash.update(e)},s.prototype._final=function(){return this._hash.digest()},e.exports=function(e){return"md5"===(e=e.toLowerCase())?new n:"rmd160"===e||"ripemd160"===e?new f:new s(o(e))}},function(e,t,r){(t=e.exports=r(369)).Stream=t,t.Readable=t,t.Writable=r(315),t.Duplex=r(283),t.Transform=r(374),t.PassThrough=r(451)},function(e,t,r){function i(e){return Object.prototype.toString.call(e)}t.isArray=function(e){return Array.isArray?Array.isArray(e):"[object Array]"===i(e)},t.isBoolean=function(e){return"boolean"==typeof e},t.isNull=function(e){return null===e},t.isNullOrUndefined=function(e){return null==e},t.isNumber=function(e){return"number"==typeof e},t.isString=function(e){return"string"==typeof e},t.isSymbol=function(e){return"symbol"==typeof e},t.isUndefined=function(e){return void 0===e},t.isRegExp=function(e){return"[object RegExp]"===i(e)},t.isObject=function(e){return"object"==typeof e&&null!==e},t.isDate=function(e){return"[object Date]"===i(e)},t.isError=function(e){return"[object Error]"===i(e)||e instanceof Error},t.isFunction=function(e){return"function"==typeof e},t.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},t.isBuffer=r(277).Buffer.isBuffer},function(e,t,r){(function(t){e.exports=function(e,r){for(var i=Math.min(e.length,r.length),n=new t(i),f=0;f<i;++f)n[f]=e[f]^r[f];return n}}).call(this,r(277).Buffer)},function(e,t,r){"use strict";var i=r(280),n=r(278);function f(){this.pending=null,this.pendingTotal=0,this.blockSize=this.constructor.blockSize,this.outSize=this.constructor.outSize,this.hmacStrength=this.constructor.hmacStrength,this.padLength=this.constructor.padLength/8,this.endian="big",this._delta8=this.blockSize/8,this._delta32=this.blockSize/32}t.BlockHash=f,f.prototype.update=function(e,t){if(e=i.toArray(e,t),this.pending?this.pending=this.pending.concat(e):this.pending=e,this.pendingTotal+=e.length,this.pending.length>=this._delta8){var r=(e=this.pending).length%this._delta8;this.pending=e.slice(e.length-r,e.length),0===this.pending.length&&(this.pending=null),e=i.join32(e,0,e.length-r,this.endian);for(var n=0;n<e.length;n+=this._delta32)this._update(e,n,n+this._delta32)}return this},f.prototype.digest=function(e){return this.update(this._pad()),n(null===this.pending),this._digest(e)},f.prototype._pad=function(){var e=this.pendingTotal,t=this._delta8,r=t-(e+this.padLength)%t,i=new Array(r+this.padLength);i[0]=128;for(var n=1;n<r;n++)i[n]=0;if(e<<=3,"big"===this.endian){for(var f=8;f<this.padLength;f++)i[n++]=0;i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=e>>>24&255,i[n++]=e>>>16&255,i[n++]=e>>>8&255,i[n++]=255&e}else for(i[n++]=255&e,i[n++]=e>>>8&255,i[n++]=e>>>16&255,i[n++]=e>>>24&255,i[n++]=0,i[n++]=0,i[n++]=0,i[n++]=0,f=8;f<this.padLength;f++)i[n++]=0;return i}},function(e,t,r){"use strict";const i=r(275),n=r(329).Reporter,f=r(327).Buffer;function o(e,t){n.call(this,t),f.isBuffer(e)?(this.base=e,this.offset=0,this.length=e.length):this.error("Input not Buffer")}function a(e,t){if(Array.isArray(e))this.length=0,this.value=e.map((function(e){return a.isEncoderBuffer(e)||(e=new a(e,t)),this.length+=e.length,e}),this);else if("number"==typeof e){if(!(0<=e&&e<=255))return t.error("non-byte EncoderBuffer value");this.value=e,this.length=1}else if("string"==typeof e)this.value=e,this.length=f.byteLength(e);else{if(!f.isBuffer(e))return t.error("Unsupported type: "+typeof e);this.value=e,this.length=e.length}}i(o,n),t.DecoderBuffer=o,o.isDecoderBuffer=function(e){if(e instanceof o)return!0;return"object"==typeof e&&f.isBuffer(e.base)&&"DecoderBuffer"===e.constructor.name&&"number"==typeof e.offset&&"number"==typeof e.length&&"function"==typeof e.save&&"function"==typeof e.restore&&"function"==typeof e.isEmpty&&"function"==typeof e.readUInt8&&"function"==typeof e.skip&&"function"==typeof e.raw},o.prototype.save=function(){return{offset:this.offset,reporter:n.prototype.save.call(this)}},o.prototype.restore=function(e){const t=new o(this.base);return t.offset=e.offset,t.length=this.offset,this.offset=e.offset,n.prototype.restore.call(this,e.reporter),t},o.prototype.isEmpty=function(){return this.offset===this.length},o.prototype.readUInt8=function(e){return this.offset+1<=this.length?this.base.readUInt8(this.offset++,!0):this.error(e||"DecoderBuffer overrun")},o.prototype.skip=function(e,t){if(!(this.offset+e<=this.length))return this.error(t||"DecoderBuffer overrun");const r=new o(this.base);return r._reporterState=this._reporterState,r.offset=this.offset,r.length=this.offset+e,this.offset+=e,r},o.prototype.raw=function(e){return this.base.slice(e?e.offset:this.offset,this.length)},t.EncoderBuffer=a,a.isEncoderBuffer=function(e){if(e instanceof a)return!0;return"object"==typeof e&&"EncoderBuffer"===e.constructor.name&&"number"==typeof e.length&&"function"==typeof e.join},a.prototype.join=function(e,t){return e||(e=f.alloc(this.length)),t||(t=0),0===this.length||(Array.isArray(this.value)?this.value.forEach((function(r){r.join(e,t),t+=r.length})):("number"==typeof this.value?e[t]=this.value:"string"==typeof this.value?e.write(this.value,t):f.isBuffer(this.value)&&this.value.copy(e,t),t+=this.length)),e}},function(e,t,r){"use strict";(function(t){void 0===t||!t.version||0===t.version.indexOf("v0.")||0===t.version.indexOf("v1.")&&0!==t.version.indexOf("v1.8.")?e.exports={nextTick:function(e,r,i,n){if("function"!=typeof e)throw new TypeError('"callback" argument must be a function');var f,o,a=arguments.length;switch(a){case 0:case 1:return t.nextTick(e);case 2:return t.nextTick((function(){e.call(null,r)}));case 3:return t.nextTick((function(){e.call(null,r,i)}));case 4:return t.nextTick((function(){e.call(null,r,i,n)}));default:for(f=new Array(a-1),o=0;o<f.length;)f[o++]=arguments[o];return t.nextTick((function(){e.apply(null,f)}))}}}:e.exports=t}).call(this,r(109))},function(e,t,r){var i=r(274).Buffer;function n(e){i.isBuffer(e)||(e=i.from(e));for(var t=e.length/4|0,r=new Array(t),n=0;n<t;n++)r[n]=e.readUInt32BE(4*n);return r}function f(e){for(;0<e.length;e++)e[0]=0}function o(e,t,r,i,n){for(var f,o,a,s,c=r[0],h=r[1],d=r[2],u=r[3],l=e[0]^t[0],p=e[1]^t[1],b=e[2]^t[2],y=e[3]^t[3],g=4,m=1;m<n;m++)f=c[l>>>24]^h[p>>>16&255]^d[b>>>8&255]^u[255&y]^t[g++],o=c[p>>>24]^h[b>>>16&255]^d[y>>>8&255]^u[255&l]^t[g++],a=c[b>>>24]^h[y>>>16&255]^d[l>>>8&255]^u[255&p]^t[g++],s=c[y>>>24]^h[l>>>16&255]^d[p>>>8&255]^u[255&b]^t[g++],l=f,p=o,b=a,y=s;return f=(i[l>>>24]<<24|i[p>>>16&255]<<16|i[b>>>8&255]<<8|i[255&y])^t[g++],o=(i[p>>>24]<<24|i[b>>>16&255]<<16|i[y>>>8&255]<<8|i[255&l])^t[g++],a=(i[b>>>24]<<24|i[y>>>16&255]<<16|i[l>>>8&255]<<8|i[255&p])^t[g++],s=(i[y>>>24]<<24|i[l>>>16&255]<<16|i[p>>>8&255]<<8|i[255&b])^t[g++],[f>>>=0,o>>>=0,a>>>=0,s>>>=0]}var a=[0,1,2,4,8,16,32,64,128,27,54],s=function(){for(var e=new Array(256),t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;for(var r=[],i=[],n=[[],[],[],[]],f=[[],[],[],[]],o=0,a=0,s=0;s<256;++s){var c=a^a<<1^a<<2^a<<3^a<<4;c=c>>>8^255&c^99,r[o]=c,i[c]=o;var h=e[o],d=e[h],u=e[d],l=257*e[c]^16843008*c;n[0][o]=l<<24|l>>>8,n[1][o]=l<<16|l>>>16,n[2][o]=l<<8|l>>>24,n[3][o]=l,l=16843009*u^65537*d^257*h^16843008*o,f[0][c]=l<<24|l>>>8,f[1][c]=l<<16|l>>>16,f[2][c]=l<<8|l>>>24,f[3][c]=l,0===o?o=a=1:(o=h^e[e[e[u^h]]],a^=e[e[a]])}return{SBOX:r,INV_SBOX:i,SUB_MIX:n,INV_SUB_MIX:f}}();function c(e){this._key=n(e),this._reset()}c.blockSize=16,c.keySize=32,c.prototype.blockSize=c.blockSize,c.prototype.keySize=c.keySize,c.prototype._reset=function(){for(var e=this._key,t=e.length,r=t+6,i=4*(r+1),n=[],f=0;f<t;f++)n[f]=e[f];for(f=t;f<i;f++){var o=n[f-1];f%t==0?(o=o<<8|o>>>24,o=s.SBOX[o>>>24]<<24|s.SBOX[o>>>16&255]<<16|s.SBOX[o>>>8&255]<<8|s.SBOX[255&o],o^=a[f/t|0]<<24):t>6&&f%t==4&&(o=s.SBOX[o>>>24]<<24|s.SBOX[o>>>16&255]<<16|s.SBOX[o>>>8&255]<<8|s.SBOX[255&o]),n[f]=n[f-t]^o}for(var c=[],h=0;h<i;h++){var d=i-h,u=n[d-(h%4?0:4)];c[h]=h<4||d<=4?u:s.INV_SUB_MIX[0][s.SBOX[u>>>24]]^s.INV_SUB_MIX[1][s.SBOX[u>>>16&255]]^s.INV_SUB_MIX[2][s.SBOX[u>>>8&255]]^s.INV_SUB_MIX[3][s.SBOX[255&u]]}this._nRounds=r,this._keySchedule=n,this._invKeySchedule=c},c.prototype.encryptBlockRaw=function(e){return o(e=n(e),this._keySchedule,s.SUB_MIX,s.SBOX,this._nRounds)},c.prototype.encryptBlock=function(e){var t=this.encryptBlockRaw(e),r=i.allocUnsafe(16);return r.writeUInt32BE(t[0],0),r.writeUInt32BE(t[1],4),r.writeUInt32BE(t[2],8),r.writeUInt32BE(t[3],12),r},c.prototype.decryptBlock=function(e){var t=(e=n(e))[1];e[1]=e[3],e[3]=t;var r=o(e,this._invKeySchedule,s.INV_SUB_MIX,s.INV_SBOX,this._nRounds),f=i.allocUnsafe(16);return f.writeUInt32BE(r[0],0),f.writeUInt32BE(r[3],4),f.writeUInt32BE(r[2],8),f.writeUInt32BE(r[1],12),f},c.prototype.scrub=function(){f(this._keySchedule),f(this._invKeySchedule),f(this._key)},e.exports.AES=c},function(e,t,r){var i=r(274).Buffer,n=r(313);e.exports=function(e,t,r,f){if(i.isBuffer(e)||(e=i.from(e,"binary")),t&&(i.isBuffer(t)||(t=i.from(t,"binary")),8!==t.length))throw new RangeError("salt should be Buffer with 8 byte length");for(var o=r/8,a=i.alloc(o),s=i.alloc(f||0),c=i.alloc(0);o>0||f>0;){var h=new n;h.update(c),h.update(e),t&&h.update(t),c=h.digest();var d=0;if(o>0){var u=a.length-o;d=Math.min(o,c.length),c.copy(a,u,0,d),o-=d}if(d<c.length&&f>0){var l=s.length-f,p=Math.min(f,c.length-d);c.copy(s,l,d,d+p),f-=p}}return c.fill(0),{key:a,iv:s}}},function(e,t,r){"use strict";var i=r(276),n=r(279),f=n.getNAF,o=n.getJSF,a=n.assert;function s(e,t){this.type=e,this.p=new i(t.p,16),this.red=t.prime?i.red(t.prime):i.mont(this.p),this.zero=new i(0).toRed(this.red),this.one=new i(1).toRed(this.red),this.two=new i(2).toRed(this.red),this.n=t.n&&new i(t.n,16),this.g=t.g&&this.pointFromJSON(t.g,t.gRed),this._wnafT1=new Array(4),this._wnafT2=new Array(4),this._wnafT3=new Array(4),this._wnafT4=new Array(4),this._bitLength=this.n?this.n.bitLength():0;var r=this.n&&this.p.div(this.n);!r||r.cmpn(100)>0?this.redN=null:(this._maxwellTrick=!0,this.redN=this.n.toRed(this.red))}function c(e,t){this.curve=e,this.type=t,this.precomputed=null}e.exports=s,s.prototype.point=function(){throw new Error("Not implemented")},s.prototype.validate=function(){throw new Error("Not implemented")},s.prototype._fixedNafMul=function(e,t){a(e.precomputed);var r=e._getDoubles(),i=f(t,1,this._bitLength),n=(1<<r.step+1)-(r.step%2==0?2:1);n/=3;var o,s,c=[];for(o=0;o<i.length;o+=r.step){s=0;for(var h=o+r.step-1;h>=o;h--)s=(s<<1)+i[h];c.push(s)}for(var d=this.jpoint(null,null,null),u=this.jpoint(null,null,null),l=n;l>0;l--){for(o=0;o<c.length;o++)(s=c[o])===l?u=u.mixedAdd(r.points[o]):s===-l&&(u=u.mixedAdd(r.points[o].neg()));d=d.add(u)}return d.toP()},s.prototype._wnafMul=function(e,t){var r=4,i=e._getNAFPoints(r);r=i.wnd;for(var n=i.points,o=f(t,r,this._bitLength),s=this.jpoint(null,null,null),c=o.length-1;c>=0;c--){for(var h=0;c>=0&&0===o[c];c--)h++;if(c>=0&&h++,s=s.dblp(h),c<0)break;var d=o[c];a(0!==d),s="affine"===e.type?d>0?s.mixedAdd(n[d-1>>1]):s.mixedAdd(n[-d-1>>1].neg()):d>0?s.add(n[d-1>>1]):s.add(n[-d-1>>1].neg())}return"affine"===e.type?s.toP():s},s.prototype._wnafMulAdd=function(e,t,r,i,n){var a,s,c,h=this._wnafT1,d=this._wnafT2,u=this._wnafT3,l=0;for(a=0;a<i;a++){var p=(c=t[a])._getNAFPoints(e);h[a]=p.wnd,d[a]=p.points}for(a=i-1;a>=1;a-=2){var b=a-1,y=a;if(1===h[b]&&1===h[y]){var g=[t[b],null,null,t[y]];0===t[b].y.cmp(t[y].y)?(g[1]=t[b].add(t[y]),g[2]=t[b].toJ().mixedAdd(t[y].neg())):0===t[b].y.cmp(t[y].y.redNeg())?(g[1]=t[b].toJ().mixedAdd(t[y]),g[2]=t[b].add(t[y].neg())):(g[1]=t[b].toJ().mixedAdd(t[y]),g[2]=t[b].toJ().mixedAdd(t[y].neg()));var m=[-3,-1,-5,-7,0,7,5,1,3],v=o(r[b],r[y]);for(l=Math.max(v[0].length,l),u[b]=new Array(l),u[y]=new Array(l),s=0;s<l;s++){var _=0|v[0][s],w=0|v[1][s];u[b][s]=m[3*(_+1)+(w+1)],u[y][s]=0,d[b]=g}}else u[b]=f(r[b],h[b],this._bitLength),u[y]=f(r[y],h[y],this._bitLength),l=Math.max(u[b].length,l),l=Math.max(u[y].length,l)}var S=this.jpoint(null,null,null),M=this._wnafT4;for(a=l;a>=0;a--){for(var E=0;a>=0;){var k=!0;for(s=0;s<i;s++)M[s]=0|u[s][a],0!==M[s]&&(k=!1);if(!k)break;E++,a--}if(a>=0&&E++,S=S.dblp(E),a<0)break;for(s=0;s<i;s++){var A=M[s];0!==A&&(A>0?c=d[s][A-1>>1]:A<0&&(c=d[s][-A-1>>1].neg()),S="affine"===c.type?S.mixedAdd(c):S.add(c))}}for(a=0;a<i;a++)d[a]=null;return n?S:S.toP()},s.BasePoint=c,c.prototype.eq=function(){throw new Error("Not implemented")},c.prototype.validate=function(){return this.curve.validate(this)},s.prototype.decodePoint=function(e,t){e=n.toArray(e,t);var r=this.p.byteLength();if((4===e[0]||6===e[0]||7===e[0])&&e.length-1==2*r)return 6===e[0]?a(e[e.length-1]%2==0):7===e[0]&&a(e[e.length-1]%2==1),this.point(e.slice(1,1+r),e.slice(1+r,1+2*r));if((2===e[0]||3===e[0])&&e.length-1===r)return this.pointFromX(e.slice(1,1+r),3===e[0]);throw new Error("Unknown point format")},c.prototype.encodeCompressed=function(e){return this.encode(e,!0)},c.prototype._encode=function(e){var t=this.curve.p.byteLength(),r=this.getX().toArray("be",t);return e?[this.getY().isEven()?2:3].concat(r):[4].concat(r,this.getY().toArray("be",t))},c.prototype.encode=function(e,t){return n.encode(this._encode(t),e)},c.prototype.precompute=function(e){if(this.precomputed)return this;var t={doubles:null,naf:null,beta:null};return t.naf=this._getNAFPoints(8),t.doubles=this._getDoubles(4,e),t.beta=this._getBeta(),this.precomputed=t,this},c.prototype._hasDoubles=function(e){if(!this.precomputed)return!1;var t=this.precomputed.doubles;return!!t&&t.points.length>=Math.ceil((e.bitLength()+1)/t.step)},c.prototype._getDoubles=function(e,t){if(this.precomputed&&this.precomputed.doubles)return this.precomputed.doubles;for(var r=[this],i=this,n=0;n<t;n+=e){for(var f=0;f<e;f++)i=i.dbl();r.push(i)}return{step:e,points:r}},c.prototype._getNAFPoints=function(e){if(this.precomputed&&this.precomputed.naf)return this.precomputed.naf;for(var t=[this],r=(1<<e)-1,i=1===r?null:this.dbl(),n=1;n<r;n++)t[n]=t[n-1].add(i);return{wnd:e,points:t}},c.prototype._getBeta=function(){return null},c.prototype.dblp=function(e){for(var t=this,r=0;r<e;r++)t=t.dbl();return t}},function(e,t,r){var i=r(502),n=r(509),f=r(510),o=r(320),a=r(380),s=r(274).Buffer;function c(e){var t;"object"!=typeof e||s.isBuffer(e)||(t=e.passphrase,e=e.key),"string"==typeof e&&(e=s.from(e));var r,c,h=f(e,t),d=h.tag,u=h.data;switch(d){case"CERTIFICATE":c=i.certificate.decode(u,"der").tbsCertificate.subjectPublicKeyInfo;case"PUBLIC KEY":switch(c||(c=i.PublicKey.decode(u,"der")),r=c.algorithm.algorithm.join(".")){case"1.2.840.113549.1.1.1":return i.RSAPublicKey.decode(c.subjectPublicKey.data,"der");case"1.2.840.10045.2.1":return c.subjectPrivateKey=c.subjectPublicKey,{type:"ec",data:c};case"1.2.840.10040.4.1":return c.algorithm.params.pub_key=i.DSAparam.decode(c.subjectPublicKey.data,"der"),{type:"dsa",data:c.algorithm.params};default:throw new Error("unknown key id "+r)}case"ENCRYPTED PRIVATE KEY":u=function(e,t){var r=e.algorithm.decrypt.kde.kdeparams.salt,i=parseInt(e.algorithm.decrypt.kde.kdeparams.iters.toString(),10),f=n[e.algorithm.decrypt.cipher.algo.join(".")],c=e.algorithm.decrypt.cipher.iv,h=e.subjectPrivateKey,d=parseInt(f.split("-")[1],10)/8,u=a.pbkdf2Sync(t,r,i,d,"sha1"),l=o.createDecipheriv(f,u,c),p=[];return p.push(l.update(h)),p.push(l.final()),s.concat(p)}(u=i.EncryptedPrivateKey.decode(u,"der"),t);case"PRIVATE KEY":switch(r=(c=i.PrivateKey.decode(u,"der")).algorithm.algorithm.join(".")){case"1.2.840.113549.1.1.1":return i.RSAPrivateKey.decode(c.subjectPrivateKey,"der");case"1.2.840.10045.2.1":return{curve:c.algorithm.curve,privateKey:i.ECPrivateKey.decode(c.subjectPrivateKey,"der").privateKey};case"1.2.840.10040.4.1":return c.algorithm.params.priv_key=i.DSAparam.decode(c.subjectPrivateKey,"der"),{type:"dsa",params:c.algorithm.params};default:throw new Error("unknown key id "+r)}case"RSA PUBLIC KEY":return i.RSAPublicKey.decode(u,"der");case"RSA PRIVATE KEY":return i.RSAPrivateKey.decode(u,"der");case"DSA PRIVATE KEY":return{type:"dsa",params:i.DSAPrivateKey.decode(u,"der")};case"EC PRIVATE KEY":return{curve:(u=i.ECPrivateKey.decode(u,"der")).parameters.value,privateKey:u.privateKey};default:throw new Error("unknown key type "+d)}}e.exports=c,c.signature=i.signature},,,,,,,,,,,function(e,t,r){"use strict";var i=r(275),n=r(368),f=r(274).Buffer,o=new Array(16);function a(){n.call(this,64),this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878}function s(e,t){return e<<t|e>>>32-t}function c(e,t,r,i,n,f,o){return s(e+(t&r|~t&i)+n+f|0,o)+t|0}function h(e,t,r,i,n,f,o){return s(e+(t&i|r&~i)+n+f|0,o)+t|0}function d(e,t,r,i,n,f,o){return s(e+(t^r^i)+n+f|0,o)+t|0}function u(e,t,r,i,n,f,o){return s(e+(r^(t|~i))+n+f|0,o)+t|0}i(a,n),a.prototype._update=function(){for(var e=o,t=0;t<16;++t)e[t]=this._block.readInt32LE(4*t);var r=this._a,i=this._b,n=this._c,f=this._d;r=c(r,i,n,f,e[0],3614090360,7),f=c(f,r,i,n,e[1],3905402710,12),n=c(n,f,r,i,e[2],606105819,17),i=c(i,n,f,r,e[3],3250441966,22),r=c(r,i,n,f,e[4],4118548399,7),f=c(f,r,i,n,e[5],1200080426,12),n=c(n,f,r,i,e[6],2821735955,17),i=c(i,n,f,r,e[7],4249261313,22),r=c(r,i,n,f,e[8],1770035416,7),f=c(f,r,i,n,e[9],2336552879,12),n=c(n,f,r,i,e[10],4294925233,17),i=c(i,n,f,r,e[11],2304563134,22),r=c(r,i,n,f,e[12],1804603682,7),f=c(f,r,i,n,e[13],4254626195,12),n=c(n,f,r,i,e[14],2792965006,17),r=h(r,i=c(i,n,f,r,e[15],1236535329,22),n,f,e[1],4129170786,5),f=h(f,r,i,n,e[6],3225465664,9),n=h(n,f,r,i,e[11],643717713,14),i=h(i,n,f,r,e[0],3921069994,20),r=h(r,i,n,f,e[5],3593408605,5),f=h(f,r,i,n,e[10],38016083,9),n=h(n,f,r,i,e[15],3634488961,14),i=h(i,n,f,r,e[4],3889429448,20),r=h(r,i,n,f,e[9],568446438,5),f=h(f,r,i,n,e[14],3275163606,9),n=h(n,f,r,i,e[3],4107603335,14),i=h(i,n,f,r,e[8],1163531501,20),r=h(r,i,n,f,e[13],2850285829,5),f=h(f,r,i,n,e[2],4243563512,9),n=h(n,f,r,i,e[7],1735328473,14),r=d(r,i=h(i,n,f,r,e[12],2368359562,20),n,f,e[5],4294588738,4),f=d(f,r,i,n,e[8],2272392833,11),n=d(n,f,r,i,e[11],1839030562,16),i=d(i,n,f,r,e[14],4259657740,23),r=d(r,i,n,f,e[1],2763975236,4),f=d(f,r,i,n,e[4],1272893353,11),n=d(n,f,r,i,e[7],4139469664,16),i=d(i,n,f,r,e[10],3200236656,23),r=d(r,i,n,f,e[13],681279174,4),f=d(f,r,i,n,e[0],3936430074,11),n=d(n,f,r,i,e[3],3572445317,16),i=d(i,n,f,r,e[6],76029189,23),r=d(r,i,n,f,e[9],3654602809,4),f=d(f,r,i,n,e[12],3873151461,11),n=d(n,f,r,i,e[15],530742520,16),r=u(r,i=d(i,n,f,r,e[2],3299628645,23),n,f,e[0],4096336452,6),f=u(f,r,i,n,e[7],1126891415,10),n=u(n,f,r,i,e[14],2878612391,15),i=u(i,n,f,r,e[5],4237533241,21),r=u(r,i,n,f,e[12],1700485571,6),f=u(f,r,i,n,e[3],2399980690,10),n=u(n,f,r,i,e[10],4293915773,15),i=u(i,n,f,r,e[1],2240044497,21),r=u(r,i,n,f,e[8],1873313359,6),f=u(f,r,i,n,e[15],4264355552,10),n=u(n,f,r,i,e[6],2734768916,15),i=u(i,n,f,r,e[13],1309151649,21),r=u(r,i,n,f,e[4],4149444226,6),f=u(f,r,i,n,e[11],3174756917,10),n=u(n,f,r,i,e[2],718787259,15),i=u(i,n,f,r,e[9],3951481745,21),this._a=this._a+r|0,this._b=this._b+i|0,this._c=this._c+n|0,this._d=this._d+f|0},a.prototype._digest=function(){this._block[this._blockOffset++]=128,this._blockOffset>56&&(this._block.fill(0,this._blockOffset,64),this._update(),this._blockOffset=0),this._block.fill(0,this._blockOffset,56),this._block.writeUInt32LE(this._length[0],56),this._block.writeUInt32LE(this._length[1],60),this._update();var e=f.allocUnsafe(16);return e.writeInt32LE(this._a,0),e.writeInt32LE(this._b,4),e.writeInt32LE(this._c,8),e.writeInt32LE(this._d,12),e},e.exports=a},function(e,t,r){"use strict";var i,n="object"==typeof Reflect?Reflect:null,f=n&&"function"==typeof n.apply?n.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};i=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var o=Number.isNaN||function(e){return e!=e};function a(){a.init.call(this)}e.exports=a,e.exports.once=function(e,t){return new Promise((function(r,i){function n(r){e.removeListener(t,f),i(r)}function f(){"function"==typeof e.removeListener&&e.removeListener("error",n),r([].slice.call(arguments))}g(e,t,f,{once:!0}),"error"!==t&&function(e,t,r){"function"==typeof e.on&&g(e,"error",t,r)}(e,n,{once:!0})}))},a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var s=10;function c(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function h(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function d(e,t,r,i){var n,f,o,a;if(c(r),void 0===(f=e._events)?(f=e._events=Object.create(null),e._eventsCount=0):(void 0!==f.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),f=e._events),o=f[t]),void 0===o)o=f[t]=r,++e._eventsCount;else if("function"==typeof o?o=f[t]=i?[r,o]:[o,r]:i?o.unshift(r):o.push(r),(n=h(e))>0&&o.length>n&&!o.warned){o.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=e,s.type=t,s.count=o.length,a=s,console&&console.warn&&console.warn(a)}return e}function u(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function l(e,t,r){var i={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},n=u.bind(i);return n.listener=r,i.wrapFn=n,n}function p(e,t,r){var i=e._events;if(void 0===i)return[];var n=i[t];return void 0===n?[]:"function"==typeof n?r?[n.listener||n]:[n]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(n):y(n,n.length)}function b(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function y(e,t){for(var r=new Array(t),i=0;i<t;++i)r[i]=e[i];return r}function g(e,t,r,i){if("function"==typeof e.on)i.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function n(f){i.once&&e.removeEventListener(t,n),r(f)}))}}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");s=e}}),a.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},a.prototype.getMaxListeners=function(){return h(this)},a.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var i="error"===e,n=this._events;if(void 0!==n)i=i&&void 0===n.error;else if(!i)return!1;if(i){var o;if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var a=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw a.context=o,a}var s=n[e];if(void 0===s)return!1;if("function"==typeof s)f(s,this,t);else{var c=s.length,h=y(s,c);for(r=0;r<c;++r)f(h[r],this,t)}return!0},a.prototype.addListener=function(e,t){return d(this,e,t,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(e,t){return d(this,e,t,!0)},a.prototype.once=function(e,t){return c(t),this.on(e,l(this,e,t)),this},a.prototype.prependOnceListener=function(e,t){return c(t),this.prependListener(e,l(this,e,t)),this},a.prototype.removeListener=function(e,t){var r,i,n,f,o;if(c(t),void 0===(i=this._events))return this;if(void 0===(r=i[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(n=-1,f=r.length-1;f>=0;f--)if(r[f]===t||r[f].listener===t){o=r[f].listener,n=f;break}if(n<0)return this;0===n?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,n),1===r.length&&(i[e]=r[0]),void 0!==i.removeListener&&this.emit("removeListener",e,o||t)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(e){var t,r,i;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var n,f=Object.keys(r);for(i=0;i<f.length;++i)"removeListener"!==(n=f[i])&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(i=t.length-1;i>=0;i--)this.removeListener(e,t[i]);return this},a.prototype.listeners=function(e){return p(this,e,!0)},a.prototype.rawListeners=function(e){return p(this,e,!1)},a.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):b.call(e,t)},a.prototype.listenerCount=b,a.prototype.eventNames=function(){return this._eventsCount>0?i(this._events):[]}},function(e,t,r){"use strict";(function(t,i,n){var f=r(298);function o(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,r){var i=e.entry;e.entry=null;for(;i;){var n=i.callback;t.pendingcb--,n(r),i=i.next}t.corkedRequestsFree.next=e}(t,e)}}e.exports=m;var a,s=!t.browser&&["v0.10","v0.9."].indexOf(t.version.slice(0,5))>-1?i:f.nextTick;m.WritableState=g;var c=Object.create(r(294));c.inherits=r(275);var h={deprecate:r(450)},d=r(370),u=r(274).Buffer,l=(void 0!==n?n:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var p,b=r(373);function y(){}function g(e,t){a=a||r(283),e=e||{};var i=t instanceof a;this.objectMode=!!e.objectMode,i&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var n=e.highWaterMark,c=e.writableHighWaterMark,h=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:i&&(c||0===c)?c:h,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var d=!1===e.decodeStrings;this.decodeStrings=!d,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,i=r.sync,n=r.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(r),t)!function(e,t,r,i,n){--t.pendingcb,r?(f.nextTick(n,i),f.nextTick(E,e,t),e._writableState.errorEmitted=!0,e.emit("error",i)):(n(i),e._writableState.errorEmitted=!0,e.emit("error",i),E(e,t))}(e,r,i,t,n);else{var o=S(r);o||r.corked||r.bufferProcessing||!r.bufferedRequest||w(e,r),i?s(_,e,r,o,n):_(e,r,o,n)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new o(this)}function m(e){if(a=a||r(283),!(p.call(m,this)||this instanceof a))return new m(e);this._writableState=new g(e,this),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),d.call(this)}function v(e,t,r,i,n,f,o){t.writelen=i,t.writecb=o,t.writing=!0,t.sync=!0,r?e._writev(n,t.onwrite):e._write(n,f,t.onwrite),t.sync=!1}function _(e,t,r,i){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,i(),E(e,t)}function w(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var i=t.bufferedRequestCount,n=new Array(i),f=t.corkedRequestsFree;f.entry=r;for(var a=0,s=!0;r;)n[a]=r,r.isBuf||(s=!1),r=r.next,a+=1;n.allBuffers=s,v(e,t,!0,t.length,n,"",f.finish),t.pendingcb++,t.lastBufferedRequest=null,f.next?(t.corkedRequestsFree=f.next,f.next=null):t.corkedRequestsFree=new o(t),t.bufferedRequestCount=0}else{for(;r;){var c=r.chunk,h=r.encoding,d=r.callback;if(v(e,t,!1,t.objectMode?1:c.length,c,h,d),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function S(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function M(e,t){e._final((function(r){t.pendingcb--,r&&e.emit("error",r),t.prefinished=!0,e.emit("prefinish"),E(e,t)}))}function E(e,t){var r=S(t);return r&&(!function(e,t){t.prefinished||t.finalCalled||("function"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,f.nextTick(M,e,t)):(t.prefinished=!0,e.emit("prefinish")))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),r}c.inherits(m,d),g.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(g.prototype,"buffer",{get:h.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(p=Function.prototype[Symbol.hasInstance],Object.defineProperty(m,Symbol.hasInstance,{value:function(e){return!!p.call(this,e)||this===m&&(e&&e._writableState instanceof g)}})):p=function(e){return e instanceof this},m.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},m.prototype.write=function(e,t,r){var i,n=this._writableState,o=!1,a=!n.objectMode&&(i=e,u.isBuffer(i)||i instanceof l);return a&&!u.isBuffer(e)&&(e=function(e){return u.from(e)}(e)),"function"==typeof t&&(r=t,t=null),a?t="buffer":t||(t=n.defaultEncoding),"function"!=typeof r&&(r=y),n.ended?function(e,t){var r=new Error("write after end");e.emit("error",r),f.nextTick(t,r)}(this,r):(a||function(e,t,r,i){var n=!0,o=!1;return null===r?o=new TypeError("May not write null values to stream"):"string"==typeof r||void 0===r||t.objectMode||(o=new TypeError("Invalid non-string/buffer chunk")),o&&(e.emit("error",o),f.nextTick(i,o),n=!1),n}(this,n,e,r))&&(n.pendingcb++,o=function(e,t,r,i,n,f){if(!r){var o=function(e,t,r){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=u.from(t,r));return t}(t,i,n);i!==o&&(r=!0,n="buffer",i=o)}var a=t.objectMode?1:i.length;t.length+=a;var s=t.length<t.highWaterMark;s||(t.needDrain=!0);if(t.writing||t.corked){var c=t.lastBufferedRequest;t.lastBufferedRequest={chunk:i,encoding:n,isBuf:r,callback:f,next:null},c?c.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else v(e,t,!1,a,i,n,f);return s}(this,n,a,e,t,r)),o},m.prototype.cork=function(){this._writableState.corked++},m.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||w(this,e))},m.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(m.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),m.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))},m.prototype._writev=null,m.prototype.end=function(e,t,r){var i=this._writableState;"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),i.corked&&(i.corked=1,this.uncork()),i.ending||function(e,t,r){t.ending=!0,E(e,t),r&&(t.finished?f.nextTick(r):e.once("finish",r));t.ended=!0,e.writable=!1}(this,i,r)},Object.defineProperty(m.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),m.prototype.destroy=b.destroy,m.prototype._undestroy=b.undestroy,m.prototype._destroy=function(e,t){this.end(),t(e)}}).call(this,r(109),r(126).setImmediate,r(16))},function(e,t,r){"use strict";var i=r(274).Buffer,n=i.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function f(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(i.isEncoding===n||!n(e)))throw new Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=s,this.end=c,t=4;break;case"utf8":this.fillLast=a,t=4;break;case"base64":this.text=h,this.end=d,t=3;break;default:return this.write=u,void(this.end=l)}this.lastNeed=0,this.lastTotal=0,this.lastChar=i.allocUnsafe(t)}function o(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function a(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function s(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var i=r.charCodeAt(r.length-1);if(i>=55296&&i<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function c(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function h(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function d(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function u(e){return e.toString(this.encoding)}function l(e){return e&&e.length?this.write(e):""}t.StringDecoder=f,f.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},f.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},f.prototype.text=function(e,t){var r=function(e,t,r){var i=t.length-1;if(i<r)return 0;var n=o(t[i]);if(n>=0)return n>0&&(e.lastNeed=n-1),n;if(--i<r||-2===n)return 0;if((n=o(t[i]))>=0)return n>0&&(e.lastNeed=n-2),n;if(--i<r||-2===n)return 0;if((n=o(t[i]))>=0)return n>0&&(2===n?n=0:e.lastNeed=n-3),n;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var i=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,i),e.toString("utf8",t,i)},f.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},function(e,t,r){"use strict";var i=r(277).Buffer,n=r(275),f=r(368),o=new Array(16),a=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],s=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],c=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],h=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11],d=[0,1518500249,1859775393,2400959708,2840853838],u=[1352829926,1548603684,1836072691,2053994217,0];function l(){f.call(this,64),this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520}function p(e,t){return e<<t|e>>>32-t}function b(e,t,r,i,n,f,o,a){return p(e+(t^r^i)+f+o|0,a)+n|0}function y(e,t,r,i,n,f,o,a){return p(e+(t&r|~t&i)+f+o|0,a)+n|0}function g(e,t,r,i,n,f,o,a){return p(e+((t|~r)^i)+f+o|0,a)+n|0}function m(e,t,r,i,n,f,o,a){return p(e+(t&i|r&~i)+f+o|0,a)+n|0}function v(e,t,r,i,n,f,o,a){return p(e+(t^(r|~i))+f+o|0,a)+n|0}n(l,f),l.prototype._update=function(){for(var e=o,t=0;t<16;++t)e[t]=this._block.readInt32LE(4*t);for(var r=0|this._a,i=0|this._b,n=0|this._c,f=0|this._d,l=0|this._e,_=0|this._a,w=0|this._b,S=0|this._c,M=0|this._d,E=0|this._e,k=0;k<80;k+=1){var A,x;k<16?(A=b(r,i,n,f,l,e[a[k]],d[0],c[k]),x=v(_,w,S,M,E,e[s[k]],u[0],h[k])):k<32?(A=y(r,i,n,f,l,e[a[k]],d[1],c[k]),x=m(_,w,S,M,E,e[s[k]],u[1],h[k])):k<48?(A=g(r,i,n,f,l,e[a[k]],d[2],c[k]),x=g(_,w,S,M,E,e[s[k]],u[2],h[k])):k<64?(A=m(r,i,n,f,l,e[a[k]],d[3],c[k]),x=y(_,w,S,M,E,e[s[k]],u[3],h[k])):(A=v(r,i,n,f,l,e[a[k]],d[4],c[k]),x=b(_,w,S,M,E,e[s[k]],u[4],h[k])),r=l,l=f,f=p(n,10),n=i,i=A,_=E,E=M,M=p(S,10),S=w,w=x}var B=this._b+n+M|0;this._b=this._c+f+E|0,this._c=this._d+l+_|0,this._d=this._e+r+w|0,this._e=this._a+i+S|0,this._a=B},l.prototype._digest=function(){this._block[this._blockOffset++]=128,this._blockOffset>56&&(this._block.fill(0,this._blockOffset,64),this._update(),this._blockOffset=0),this._block.fill(0,this._blockOffset,56),this._block.writeUInt32LE(this._length[0],56),this._block.writeUInt32LE(this._length[1],60),this._update();var e=i.alloc?i.alloc(20):new i(20);return e.writeInt32LE(this._a,0),e.writeInt32LE(this._b,4),e.writeInt32LE(this._c,8),e.writeInt32LE(this._d,12),e.writeInt32LE(this._e,16),e},e.exports=l},function(e,t,r){(t=e.exports=function(e){e=e.toLowerCase();var r=t[e];if(!r)throw new Error(e+" is not supported (we accept pull requests)");return new r}).sha=r(452),t.sha1=r(453),t.sha224=r(454),t.sha256=r(375),t.sha384=r(455),t.sha512=r(376)},function(e,t,r){"use strict";var i=r(278);function n(e){this.options=e,this.type=this.options.type,this.blockSize=8,this._init(),this.buffer=new Array(this.blockSize),this.bufferOff=0,this.padding=!1!==e.padding}e.exports=n,n.prototype._init=function(){},n.prototype.update=function(e){return 0===e.length?[]:"decrypt"===this.type?this._updateDecrypt(e):this._updateEncrypt(e)},n.prototype._buffer=function(e,t){for(var r=Math.min(this.buffer.length-this.bufferOff,e.length-t),i=0;i<r;i++)this.buffer[this.bufferOff+i]=e[t+i];return this.bufferOff+=r,r},n.prototype._flushBuffer=function(e,t){return this._update(this.buffer,0,e,t),this.bufferOff=0,this.blockSize},n.prototype._updateEncrypt=function(e){var t=0,r=0,i=(this.bufferOff+e.length)/this.blockSize|0,n=new Array(i*this.blockSize);0!==this.bufferOff&&(t+=this._buffer(e,t),this.bufferOff===this.buffer.length&&(r+=this._flushBuffer(n,r)));for(var f=e.length-(e.length-t)%this.blockSize;t<f;t+=this.blockSize)this._update(e,t,n,r),r+=this.blockSize;for(;t<e.length;t++,this.bufferOff++)this.buffer[this.bufferOff]=e[t];return n},n.prototype._updateDecrypt=function(e){for(var t=0,r=0,i=Math.ceil((this.bufferOff+e.length)/this.blockSize)-1,n=new Array(i*this.blockSize);i>0;i--)t+=this._buffer(e,t),r+=this._flushBuffer(n,r);return t+=this._buffer(e,t),n},n.prototype.final=function(e){var t,r;return e&&(t=this.update(e)),r="encrypt"===this.type?this._finalEncrypt():this._finalDecrypt(),t?t.concat(r):r},n.prototype._pad=function(e,t){if(0===t)return!1;for(;t<e.length;)e[t++]=0;return!0},n.prototype._finalEncrypt=function(){if(!this._pad(this.buffer,this.bufferOff))return[];var e=new Array(this.blockSize);return this._update(this.buffer,0,e,0),e},n.prototype._unpad=function(e){return e},n.prototype._finalDecrypt=function(){i.equal(this.bufferOff,this.blockSize,"Not enough data to decrypt");var e=new Array(this.blockSize);return this._flushBuffer(e,0),this._unpad(e)}},function(e,t,r){var i=r(469),n=r(477),f=r(389);t.createCipher=t.Cipher=i.createCipher,t.createCipheriv=t.Cipheriv=i.createCipheriv,t.createDecipher=t.Decipher=n.createDecipher,t.createDecipheriv=t.Decipheriv=n.createDecipheriv,t.listCiphers=t.getCiphers=function(){return Object.keys(f)}},function(e,t,r){var i={ECB:r(470),CBC:r(471),CFB:r(472),CFB8:r(473),CFB1:r(474),OFB:r(475),CTR:r(387),GCM:r(387)},n=r(389);for(var f in n)n[f].module=i[n[f].mode];e.exports=n},function(e,t,r){var i;function n(e){this.rand=e}if(e.exports=function(e){return i||(i=new n(null)),i.generate(e)},e.exports.Rand=n,n.prototype.generate=function(e){return this._rand(e)},n.prototype._rand=function(e){if(this.rand.getBytes)return this.rand.getBytes(e);for(var t=new Uint8Array(e),r=0;r<t.length;r++)t[r]=this.rand.getByte();return t},"object"==typeof self)self.crypto&&self.crypto.getRandomValues?n.prototype._rand=function(e){var t=new Uint8Array(e);return self.crypto.getRandomValues(t),t}:self.msCrypto&&self.msCrypto.getRandomValues?n.prototype._rand=function(e){var t=new Uint8Array(e);return self.msCrypto.getRandomValues(t),t}:"object"==typeof window&&(n.prototype._rand=function(){throw new Error("Not implemented yet")});else try{var f=r(395);if("function"!=typeof f.randomBytes)throw new Error("Not supported");n.prototype._rand=function(e){return f.randomBytes(e)}}catch(e){}},function(e,t,r){(function(t){var i=r(276),n=r(287);function f(e){var t,r=e.modulus.byteLength();do{t=new i(n(r))}while(t.cmp(e.modulus)>=0||!t.umod(e.prime1)||!t.umod(e.prime2));return t}function o(e,r){var n=function(e){var t=f(e);return{blinder:t.toRed(i.mont(e.modulus)).redPow(new i(e.publicExponent)).fromRed(),unblinder:t.invm(e.modulus)}}(r),o=r.modulus.byteLength(),a=new i(e).mul(n.blinder).umod(r.modulus),s=a.toRed(i.mont(r.prime1)),c=a.toRed(i.mont(r.prime2)),h=r.coefficient,d=r.prime1,u=r.prime2,l=s.redPow(r.exponent1).fromRed(),p=c.redPow(r.exponent2).fromRed(),b=l.isub(p).imul(h).umod(d).imul(u);return p.iadd(b).imul(n.unblinder).umod(r.modulus).toArrayLike(t,"be",o)}o.getr=f,e.exports=o}).call(this,r(277).Buffer)},function(e,t,r){"use strict";var i=t;i.version=r(484).version,i.utils=r(279),i.rand=r(322),i.curve=r(397),i.curves=r(325),i.ec=r(495),i.eddsa=r(499)},function(e,t,r){"use strict";var i,n=t,f=r(326),o=r(397),a=r(279).assert;function s(e){"short"===e.type?this.curve=new o.short(e):"edwards"===e.type?this.curve=new o.edwards(e):this.curve=new o.mont(e),this.g=this.curve.g,this.n=this.curve.n,this.hash=e.hash,a(this.g.validate(),"Invalid curve"),a(this.g.mul(this.n).isInfinity(),"Invalid curve, G*N != O")}function c(e,t){Object.defineProperty(n,e,{configurable:!0,enumerable:!0,get:function(){var r=new s(t);return Object.defineProperty(n,e,{configurable:!0,enumerable:!0,value:r}),r}})}n.PresetCurve=s,c("p192",{type:"short",prime:"p192",p:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff",a:"ffffffff ffffffff ffffffff fffffffe ffffffff fffffffc",b:"64210519 e59c80e7 0fa7e9ab 72243049 feb8deec c146b9b1",n:"ffffffff ffffffff ffffffff 99def836 146bc9b1 b4d22831",hash:f.sha256,gRed:!1,g:["188da80e b03090f6 7cbf20eb 43a18800 f4ff0afd 82ff1012","07192b95 ffc8da78 631011ed 6b24cdd5 73f977a1 1e794811"]}),c("p224",{type:"short",prime:"p224",p:"ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001",a:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff fffffffe",b:"b4050a85 0c04b3ab f5413256 5044b0b7 d7bfd8ba 270b3943 2355ffb4",n:"ffffffff ffffffff ffffffff ffff16a2 e0b8f03e 13dd2945 5c5c2a3d",hash:f.sha256,gRed:!1,g:["b70e0cbd 6bb4bf7f 321390b9 4a03c1d3 56c21122 343280d6 115c1d21","bd376388 b5f723fb 4c22dfe6 cd4375a0 5a074764 44d58199 85007e34"]}),c("p256",{type:"short",prime:null,p:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff ffffffff",a:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff fffffffc",b:"5ac635d8 aa3a93e7 b3ebbd55 769886bc 651d06b0 cc53b0f6 3bce3c3e 27d2604b",n:"ffffffff 00000000 ffffffff ffffffff bce6faad a7179e84 f3b9cac2 fc632551",hash:f.sha256,gRed:!1,g:["6b17d1f2 e12c4247 f8bce6e5 63a440f2 77037d81 2deb33a0 f4a13945 d898c296","4fe342e2 fe1a7f9b 8ee7eb4a 7c0f9e16 2bce3357 6b315ece cbb64068 37bf51f5"]}),c("p384",{type:"short",prime:null,p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 ffffffff",a:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 fffffffc",b:"b3312fa7 e23ee7e4 988e056b e3f82d19 181d9c6e fe814112 0314088f 5013875a c656398d 8a2ed19d 2a85c8ed d3ec2aef",n:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff c7634d81 f4372ddf 581a0db2 48b0a77a ecec196a ccc52973",hash:f.sha384,gRed:!1,g:["aa87ca22 be8b0537 8eb1c71e f320ad74 6e1d3b62 8ba79b98 59f741e0 82542a38 5502f25d bf55296c 3a545e38 72760ab7","3617de4a 96262c6f 5d9e98bf 9292dc29 f8f41dbd 289a147c e9da3113 b5f0b8c0 0a60b1ce 1d7e819d 7a431d7c 90ea0e5f"]}),c("p521",{type:"short",prime:null,p:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff",a:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffc",b:"00000051 953eb961 8e1c9a1f 929a21a0 b68540ee a2da725b 99b315f3 b8b48991 8ef109e1 56193951 ec7e937b 1652c0bd 3bb1bf07 3573df88 3d2c34f1 ef451fd4 6b503f00",n:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffa 51868783 bf2f966b 7fcc0148 f709a5d0 3bb5c9b8 899c47ae bb6fb71e 91386409",hash:f.sha512,gRed:!1,g:["000000c6 858e06b7 0404e9cd 9e3ecb66 2395b442 9c648139 053fb521 f828af60 6b4d3dba a14b5e77 efe75928 fe1dc127 a2ffa8de 3348b3c1 856a429b f97e7e31 c2e5bd66","00000118 39296a78 9a3bc004 5c8a5fb4 2c7d1bd9 98f54449 579b4468 17afbd17 273e662c 97ee7299 5ef42640 c550b901 3fad0761 353c7086 a272c240 88be9476 9fd16650"]}),c("curve25519",{type:"mont",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"76d06",b:"1",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:f.sha256,gRed:!1,g:["9"]}),c("ed25519",{type:"edwards",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"-1",c:"1",d:"52036cee2b6ffe73 8cc740797779e898 00700a4d4141d8ab 75eb4dca135978a3",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:f.sha256,gRed:!1,g:["216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a","6666666666666666666666666666666666666666666666666666666666666658"]});try{i=r(494)}catch(e){i=void 0}c("secp256k1",{type:"short",prime:"k256",p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f",a:"0",b:"7",n:"ffffffff ffffffff ffffffff fffffffe baaedce6 af48a03b bfd25e8c d0364141",h:"1",hash:f.sha256,beta:"7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee",lambda:"5363ad4cc05c30e0a5261c028812645a122e22ea20816678df02967c1b23bd72",basis:[{a:"3086d221a7d46bcde86c90e49284eb15",b:"-e4437ed6010e88286f547fa90abfe4c3"},{a:"114ca50f7a8e2f3f657c1108d9d44cfd8",b:"3086d221a7d46bcde86c90e49284eb15"}],gRed:!1,g:["79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798","483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8",i]})},function(e,t,r){var i=t;i.utils=r(280),i.common=r(296),i.sha=r(488),i.ripemd=r(492),i.hmac=r(493),i.sha1=i.sha.sha1,i.sha256=i.sha.sha256,i.sha224=i.sha.sha224,i.sha384=i.sha.sha384,i.sha512=i.sha.sha512,i.ripemd160=i.ripemd.ripemd160},function(e,t,r){"use strict";(function(t){var i,n=r(277),f=n.Buffer,o={};for(i in n)n.hasOwnProperty(i)&&"SlowBuffer"!==i&&"Buffer"!==i&&(o[i]=n[i]);var a=o.Buffer={};for(i in f)f.hasOwnProperty(i)&&"allocUnsafe"!==i&&"allocUnsafeSlow"!==i&&(a[i]=f[i]);if(o.Buffer.prototype=f.prototype,a.from&&a.from!==Uint8Array.from||(a.from=function(e,t,r){if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type '+typeof e);if(e&&void 0===e.length)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);return f(e,t,r)}),a.alloc||(a.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError('The "size" argument must be of type number. Received type '+typeof e);if(e<0||e>=2*(1<<30))throw new RangeError('The value "'+e+'" is invalid for option "size"');var i=f(e);return t&&0!==t.length?"string"==typeof r?i.fill(t,r):i.fill(t):i.fill(0),i}),!o.kStringMaxLength)try{o.kStringMaxLength=t.binding("buffer").kStringMaxLength}catch(e){}o.constants||(o.constants={MAX_LENGTH:o.kMaxLength},o.kStringMaxLength&&(o.constants.MAX_STRING_LENGTH=o.kStringMaxLength)),e.exports=o}).call(this,r(109))},function(e,t,r){"use strict";const i=r(329).Reporter,n=r(297).EncoderBuffer,f=r(297).DecoderBuffer,o=r(278),a=["seq","seqof","set","setof","objid","bool","gentime","utctime","null_","enum","int","objDesc","bitstr","bmpstr","charstr","genstr","graphstr","ia5str","iso646str","numstr","octstr","printstr","t61str","unistr","utf8str","videostr"],s=["key","obj","use","optional","explicit","implicit","def","choice","any","contains"].concat(a);function c(e,t,r){const i={};this._baseState=i,i.name=r,i.enc=e,i.parent=t||null,i.children=null,i.tag=null,i.args=null,i.reverseArgs=null,i.choice=null,i.optional=!1,i.any=!1,i.obj=!1,i.use=null,i.useDecoder=null,i.key=null,i.default=null,i.explicit=null,i.implicit=null,i.contains=null,i.parent||(i.children=[],this._wrap())}e.exports=c;const h=["enc","parent","children","tag","args","reverseArgs","choice","optional","any","obj","use","alteredUse","key","default","explicit","implicit","contains"];c.prototype.clone=function(){const e=this._baseState,t={};h.forEach((function(r){t[r]=e[r]}));const r=new this.constructor(t.parent);return r._baseState=t,r},c.prototype._wrap=function(){const e=this._baseState;s.forEach((function(t){this[t]=function(){const r=new this.constructor(this);return e.children.push(r),r[t].apply(r,arguments)}}),this)},c.prototype._init=function(e){const t=this._baseState;o(null===t.parent),e.call(this),t.children=t.children.filter((function(e){return e._baseState.parent===this}),this),o.equal(t.children.length,1,"Root node can have only one child")},c.prototype._useArgs=function(e){const t=this._baseState,r=e.filter((function(e){return e instanceof this.constructor}),this);e=e.filter((function(e){return!(e instanceof this.constructor)}),this),0!==r.length&&(o(null===t.children),t.children=r,r.forEach((function(e){e._baseState.parent=this}),this)),0!==e.length&&(o(null===t.args),t.args=e,t.reverseArgs=e.map((function(e){if("object"!=typeof e||e.constructor!==Object)return e;const t={};return Object.keys(e).forEach((function(r){r==(0|r)&&(r|=0);const i=e[r];t[i]=r})),t})))},["_peekTag","_decodeTag","_use","_decodeStr","_decodeObjid","_decodeTime","_decodeNull","_decodeInt","_decodeBool","_decodeList","_encodeComposite","_encodeStr","_encodeObjid","_encodeTime","_encodeNull","_encodeInt","_encodeBool"].forEach((function(e){c.prototype[e]=function(){const t=this._baseState;throw new Error(e+" not implemented for encoding: "+t.enc)}})),a.forEach((function(e){c.prototype[e]=function(){const t=this._baseState,r=Array.prototype.slice.call(arguments);return o(null===t.tag),t.tag=e,this._useArgs(r),this}})),c.prototype.use=function(e){o(e);const t=this._baseState;return o(null===t.use),t.use=e,this},c.prototype.optional=function(){return this._baseState.optional=!0,this},c.prototype.def=function(e){const t=this._baseState;return o(null===t.default),t.default=e,t.optional=!0,this},c.prototype.explicit=function(e){const t=this._baseState;return o(null===t.explicit&&null===t.implicit),t.explicit=e,this},c.prototype.implicit=function(e){const t=this._baseState;return o(null===t.explicit&&null===t.implicit),t.implicit=e,this},c.prototype.obj=function(){const e=this._baseState,t=Array.prototype.slice.call(arguments);return e.obj=!0,0!==t.length&&this._useArgs(t),this},c.prototype.key=function(e){const t=this._baseState;return o(null===t.key),t.key=e,this},c.prototype.any=function(){return this._baseState.any=!0,this},c.prototype.choice=function(e){const t=this._baseState;return o(null===t.choice),t.choice=e,this._useArgs(Object.keys(e).map((function(t){return e[t]}))),this},c.prototype.contains=function(e){const t=this._baseState;return o(null===t.use),t.contains=e,this},c.prototype._decode=function(e,t){const r=this._baseState;if(null===r.parent)return e.wrapResult(r.children[0]._decode(e,t));let i,n=r.default,o=!0,a=null;if(null!==r.key&&(a=e.enterKey(r.key)),r.optional){let i=null;if(null!==r.explicit?i=r.explicit:null!==r.implicit?i=r.implicit:null!==r.tag&&(i=r.tag),null!==i||r.any){if(o=this._peekTag(e,i,r.any),e.isError(o))return o}else{const i=e.save();try{null===r.choice?this._decodeGeneric(r.tag,e,t):this._decodeChoice(e,t),o=!0}catch(e){o=!1}e.restore(i)}}if(r.obj&&o&&(i=e.enterObject()),o){if(null!==r.explicit){const t=this._decodeTag(e,r.explicit);if(e.isError(t))return t;e=t}const i=e.offset;if(null===r.use&&null===r.choice){let t;r.any&&(t=e.save());const i=this._decodeTag(e,null!==r.implicit?r.implicit:r.tag,r.any);if(e.isError(i))return i;r.any?n=e.raw(t):e=i}if(t&&t.track&&null!==r.tag&&t.track(e.path(),i,e.length,"tagged"),t&&t.track&&null!==r.tag&&t.track(e.path(),e.offset,e.length,"content"),r.any||(n=null===r.choice?this._decodeGeneric(r.tag,e,t):this._decodeChoice(e,t)),e.isError(n))return n;if(r.any||null!==r.choice||null===r.children||r.children.forEach((function(r){r._decode(e,t)})),r.contains&&("octstr"===r.tag||"bitstr"===r.tag)){const i=new f(n);n=this._getUse(r.contains,e._reporterState.obj)._decode(i,t)}}return r.obj&&o&&(n=e.leaveObject(i)),null===r.key||null===n&&!0!==o?null!==a&&e.exitKey(a):e.leaveKey(a,r.key,n),n},c.prototype._decodeGeneric=function(e,t,r){const i=this._baseState;return"seq"===e||"set"===e?null:"seqof"===e||"setof"===e?this._decodeList(t,e,i.args[0],r):/str$/.test(e)?this._decodeStr(t,e,r):"objid"===e&&i.args?this._decodeObjid(t,i.args[0],i.args[1],r):"objid"===e?this._decodeObjid(t,null,null,r):"gentime"===e||"utctime"===e?this._decodeTime(t,e,r):"null_"===e?this._decodeNull(t,r):"bool"===e?this._decodeBool(t,r):"objDesc"===e?this._decodeStr(t,e,r):"int"===e||"enum"===e?this._decodeInt(t,i.args&&i.args[0],r):null!==i.use?this._getUse(i.use,t._reporterState.obj)._decode(t,r):t.error("unknown tag: "+e)},c.prototype._getUse=function(e,t){const r=this._baseState;return r.useDecoder=this._use(e,t),o(null===r.useDecoder._baseState.parent),r.useDecoder=r.useDecoder._baseState.children[0],r.implicit!==r.useDecoder._baseState.implicit&&(r.useDecoder=r.useDecoder.clone(),r.useDecoder._baseState.implicit=r.implicit),r.useDecoder},c.prototype._decodeChoice=function(e,t){const r=this._baseState;let i=null,n=!1;return Object.keys(r.choice).some((function(f){const o=e.save(),a=r.choice[f];try{const r=a._decode(e,t);if(e.isError(r))return!1;i={type:f,value:r},n=!0}catch(t){return e.restore(o),!1}return!0}),this),n?i:e.error("Choice not matched")},c.prototype._createEncoderBuffer=function(e){return new n(e,this.reporter)},c.prototype._encode=function(e,t,r){const i=this._baseState;if(null!==i.default&&i.default===e)return;const n=this._encodeValue(e,t,r);return void 0===n||this._skipDefault(n,t,r)?void 0:n},c.prototype._encodeValue=function(e,t,r){const n=this._baseState;if(null===n.parent)return n.children[0]._encode(e,t||new i);let f=null;if(this.reporter=t,n.optional&&void 0===e){if(null===n.default)return;e=n.default}let o=null,a=!1;if(n.any)f=this._createEncoderBuffer(e);else if(n.choice)f=this._encodeChoice(e,t);else if(n.contains)o=this._getUse(n.contains,r)._encode(e,t),a=!0;else if(n.children)o=n.children.map((function(r){if("null_"===r._baseState.tag)return r._encode(null,t,e);if(null===r._baseState.key)return t.error("Child should have a key");const i=t.enterKey(r._baseState.key);if("object"!=typeof e)return t.error("Child expected, but input is not object");const n=r._encode(e[r._baseState.key],t,e);return t.leaveKey(i),n}),this).filter((function(e){return e})),o=this._createEncoderBuffer(o);else if("seqof"===n.tag||"setof"===n.tag){if(!n.args||1!==n.args.length)return t.error("Too many args for : "+n.tag);if(!Array.isArray(e))return t.error("seqof/setof, but data is not Array");const r=this.clone();r._baseState.implicit=null,o=this._createEncoderBuffer(e.map((function(r){const i=this._baseState;return this._getUse(i.args[0],e)._encode(r,t)}),r))}else null!==n.use?f=this._getUse(n.use,r)._encode(e,t):(o=this._encodePrimitive(n.tag,e),a=!0);if(!n.any&&null===n.choice){const e=null!==n.implicit?n.implicit:n.tag,r=null===n.implicit?"universal":"context";null===e?null===n.use&&t.error("Tag could be omitted only for .use()"):null===n.use&&(f=this._encodeComposite(e,a,r,o))}return null!==n.explicit&&(f=this._encodeComposite(n.explicit,!1,"context",f)),f},c.prototype._encodeChoice=function(e,t){const r=this._baseState,i=r.choice[e.type];return i||o(!1,e.type+" not found in "+JSON.stringify(Object.keys(r.choice))),i._encode(e.value,t)},c.prototype._encodePrimitive=function(e,t){const r=this._baseState;if(/str$/.test(e))return this._encodeStr(t,e);if("objid"===e&&r.args)return this._encodeObjid(t,r.reverseArgs[0],r.args[1]);if("objid"===e)return this._encodeObjid(t,null,null);if("gentime"===e||"utctime"===e)return this._encodeTime(t,e);if("null_"===e)return this._encodeNull();if("int"===e||"enum"===e)return this._encodeInt(t,r.args&&r.reverseArgs[0]);if("bool"===e)return this._encodeBool(t);if("objDesc"===e)return this._encodeStr(t,e);throw new Error("Unsupported tag: "+e)},c.prototype._isNumstr=function(e){return/^[0-9 ]*$/.test(e)},c.prototype._isPrintstr=function(e){return/^[A-Za-z0-9 '()+,-./:=?]*$/.test(e)}},function(e,t,r){"use strict";const i=r(275);function n(e){this._reporterState={obj:null,path:[],options:e||{},errors:[]}}function f(e,t){this.path=e,this.rethrow(t)}t.Reporter=n,n.prototype.isError=function(e){return e instanceof f},n.prototype.save=function(){const e=this._reporterState;return{obj:e.obj,pathLen:e.path.length}},n.prototype.restore=function(e){const t=this._reporterState;t.obj=e.obj,t.path=t.path.slice(0,e.pathLen)},n.prototype.enterKey=function(e){return this._reporterState.path.push(e)},n.prototype.exitKey=function(e){const t=this._reporterState;t.path=t.path.slice(0,e-1)},n.prototype.leaveKey=function(e,t,r){const i=this._reporterState;this.exitKey(e),null!==i.obj&&(i.obj[t]=r)},n.prototype.path=function(){return this._reporterState.path.join("/")},n.prototype.enterObject=function(){const e=this._reporterState,t=e.obj;return e.obj={},t},n.prototype.leaveObject=function(e){const t=this._reporterState,r=t.obj;return t.obj=e,r},n.prototype.error=function(e){let t;const r=this._reporterState,i=e instanceof f;if(t=i?e:new f(r.path.map((function(e){return"["+JSON.stringify(e)+"]"})).join(""),e.message||e,e.stack),!r.options.partial)throw t;return i||r.errors.push(t),t},n.prototype.wrapResult=function(e){const t=this._reporterState;return t.options.partial?{result:this.isError(e)?null:e,errors:t.errors}:e},i(f,Error),f.prototype.rethrow=function(e){if(this.message=e+" at: "+(this.path||"(shallow)"),Error.captureStackTrace&&Error.captureStackTrace(this,f),!this.stack)try{throw new Error(this.message)}catch(e){this.stack=e.stack}return this}},function(e,t,r){"use strict";function i(e){const t={};return Object.keys(e).forEach((function(r){(0|r)==r&&(r|=0);const i=e[r];t[i]=r})),t}t.tagClass={0:"universal",1:"application",2:"context",3:"private"},t.tagClassByName=i(t.tagClass),t.tag={0:"end",1:"bool",2:"int",3:"bitstr",4:"octstr",5:"null_",6:"objid",7:"objDesc",8:"external",9:"real",10:"enum",11:"embed",12:"utf8str",13:"relativeOid",16:"seq",17:"set",18:"numstr",19:"printstr",20:"t61str",21:"videostr",22:"ia5str",23:"utctime",24:"gentime",25:"graphstr",26:"iso646str",27:"genstr",28:"unistr",29:"charstr",30:"bmpstr"},t.tagByName=i(t.tag)},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},function(e,t,r){"use strict";t.randomBytes=t.rng=t.pseudoRandomBytes=t.prng=r(287),t.createHash=t.Hash=r(292),t.createHmac=t.Hmac=r(377);var i=r(462),n=Object.keys(i),f=["sha1","sha224","sha256","sha384","sha512","md5","rmd160"].concat(n);t.getHashes=function(){return f};var o=r(380);t.pbkdf2=o.pbkdf2,t.pbkdf2Sync=o.pbkdf2Sync;var a=r(464);t.Cipher=a.Cipher,t.createCipher=a.createCipher,t.Cipheriv=a.Cipheriv,t.createCipheriv=a.createCipheriv,t.Decipher=a.Decipher,t.createDecipher=a.createDecipher,t.Decipheriv=a.Decipheriv,t.createDecipheriv=a.createDecipheriv,t.getCiphers=a.getCiphers,t.listCiphers=a.listCiphers;var s=r(479);t.DiffieHellmanGroup=s.DiffieHellmanGroup,t.createDiffieHellmanGroup=s.createDiffieHellmanGroup,t.getDiffieHellman=s.getDiffieHellman,t.createDiffieHellman=s.createDiffieHellman,t.DiffieHellman=s.DiffieHellman;var c=r(482);t.createSign=c.createSign,t.Sign=c.Sign,t.createVerify=c.createVerify,t.Verify=c.Verify,t.createECDH=r(512);var h=r(513);t.publicEncrypt=h.publicEncrypt,t.privateEncrypt=h.privateEncrypt,t.publicDecrypt=h.publicDecrypt,t.privateDecrypt=h.privateDecrypt;var d=r(516);t.randomFill=d.randomFill,t.randomFillSync=d.randomFillSync,t.createCredentials=function(){throw new Error(["sorry, createCredentials is not implemented yet","we accept pull requests","https://github.com/crypto-browserify/crypto-browserify"].join("\n"))},t.constants={DH_CHECK_P_NOT_SAFE_PRIME:2,DH_CHECK_P_NOT_PRIME:1,DH_UNABLE_TO_CHECK_GENERATOR:4,DH_NOT_SUITABLE_GENERATOR:8,NPN_ENABLED:1,ALPN_ENABLED:1,RSA_PKCS1_PADDING:1,RSA_SSLV23_PADDING:2,RSA_NO_PADDING:3,RSA_PKCS1_OAEP_PADDING:4,RSA_X931_PADDING:5,RSA_PKCS1_PSS_PADDING:6,POINT_CONVERSION_COMPRESSED:2,POINT_CONVERSION_UNCOMPRESSED:4,POINT_CONVERSION_HYBRID:6}},function(e,t,r){"use strict";var i=r(274).Buffer,n=r(293).Transform;function f(e){n.call(this),this._block=i.allocUnsafe(e),this._blockSize=e,this._blockOffset=0,this._length=[0,0,0,0],this._finalized=!1}r(275)(f,n),f.prototype._transform=function(e,t,r){var i=null;try{this.update(e,t)}catch(e){i=e}r(i)},f.prototype._flush=function(e){var t=null;try{this.push(this.digest())}catch(e){t=e}e(t)},f.prototype.update=function(e,t){if(function(e,t){if(!i.isBuffer(e)&&"string"!=typeof e)throw new TypeError(t+" must be a string or a buffer")}(e,"Data"),this._finalized)throw new Error("Digest already called");i.isBuffer(e)||(e=i.from(e,t));for(var r=this._block,n=0;this._blockOffset+e.length-n>=this._blockSize;){for(var f=this._blockOffset;f<this._blockSize;)r[f++]=e[n++];this._update(),this._blockOffset=0}for(;n<e.length;)r[this._blockOffset++]=e[n++];for(var o=0,a=8*e.length;a>0;++o)this._length[o]+=a,(a=this._length[o]/4294967296|0)>0&&(this._length[o]-=4294967296*a);return this},f.prototype._update=function(){throw new Error("_update is not implemented")},f.prototype.digest=function(e){if(this._finalized)throw new Error("Digest already called");this._finalized=!0;var t=this._digest();void 0!==e&&(t=t.toString(e)),this._block.fill(0),this._blockOffset=0;for(var r=0;r<4;++r)this._length[r]=0;return t},f.prototype._digest=function(){throw new Error("_digest is not implemented")},e.exports=f},function(e,t,r){"use strict";(function(t,i){var n=r(298);e.exports=v;var f,o=r(366);v.ReadableState=m;r(314).EventEmitter;var a=function(e,t){return e.listeners(t).length},s=r(370),c=r(274).Buffer,h=(void 0!==t?t:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var d=Object.create(r(294));d.inherits=r(275);var u=r(371),l=void 0;l=u&&u.debuglog?u.debuglog("stream"):function(){};var p,b=r(449),y=r(373);d.inherits(v,s);var g=["error","close","destroy","pause","resume"];function m(e,t){e=e||{};var i=t instanceof(f=f||r(283));this.objectMode=!!e.objectMode,i&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var n=e.highWaterMark,o=e.readableHighWaterMark,a=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:i&&(o||0===o)?o:a,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new b,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(p||(p=r(316).StringDecoder),this.decoder=new p(e.encoding),this.encoding=e.encoding)}function v(e){if(f=f||r(283),!(this instanceof v))return new v(e);this._readableState=new m(e,this),this.readable=!0,e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy)),s.call(this)}function _(e,t,r,i,n){var f,o=e._readableState;null===t?(o.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,M(e)}(e,o)):(n||(f=function(e,t){var r;i=t,c.isBuffer(i)||i instanceof h||"string"==typeof t||void 0===t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk"));var i;return r}(o,t)),f?e.emit("error",f):o.objectMode||t&&t.length>0?("string"==typeof t||o.objectMode||Object.getPrototypeOf(t)===c.prototype||(t=function(e){return c.from(e)}(t)),i?o.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):w(e,o,t,!0):o.ended?e.emit("error",new Error("stream.push() after EOF")):(o.reading=!1,o.decoder&&!r?(t=o.decoder.write(t),o.objectMode||0!==t.length?w(e,o,t,!1):k(e,o)):w(e,o,t,!1))):i||(o.reading=!1));return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(o)}function w(e,t,r,i){t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,i?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&M(e)),k(e,t)}Object.defineProperty(v.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),v.prototype.destroy=y.destroy,v.prototype._undestroy=y.undestroy,v.prototype._destroy=function(e,t){this.push(null),t(e)},v.prototype.push=function(e,t){var r,i=this._readableState;return i.objectMode?r=!0:"string"==typeof e&&((t=t||i.defaultEncoding)!==i.encoding&&(e=c.from(e,t),t=""),r=!0),_(this,e,t,!1,r)},v.prototype.unshift=function(e){return _(this,e,null,!0,!1)},v.prototype.isPaused=function(){return!1===this._readableState.flowing},v.prototype.setEncoding=function(e){return p||(p=r(316).StringDecoder),this._readableState.decoder=new p(e),this._readableState.encoding=e,this};function S(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=8388608?e=8388608:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function M(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(l("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?n.nextTick(E,e):E(e))}function E(e){l("emit readable"),e.emit("readable"),I(e)}function k(e,t){t.readingMore||(t.readingMore=!0,n.nextTick(A,e,t))}function A(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(l("maybeReadMore read 0"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function x(e){l("readable nexttick read 0"),e.read(0)}function B(e,t){t.reading||(l("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),I(e),t.flowing&&!t.reading&&e.read(0)}function I(e){var t=e._readableState;for(l("flow",t.flowing);t.flowing&&null!==e.read(););}function R(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=function(e,t,r){var i;e<t.head.data.length?(i=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):i=e===t.head.data.length?t.shift():r?function(e,t){var r=t.head,i=1,n=r.data;e-=n.length;for(;r=r.next;){var f=r.data,o=e>f.length?f.length:e;if(o===f.length?n+=f:n+=f.slice(0,e),0===(e-=o)){o===f.length?(++i,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=f.slice(o));break}++i}return t.length-=i,n}(e,t):function(e,t){var r=c.allocUnsafe(e),i=t.head,n=1;i.data.copy(r),e-=i.data.length;for(;i=i.next;){var f=i.data,o=e>f.length?f.length:e;if(f.copy(r,r.length-e,0,o),0===(e-=o)){o===f.length?(++n,i.next?t.head=i.next:t.head=t.tail=null):(t.head=i,i.data=f.slice(o));break}++n}return t.length-=n,r}(e,t);return i}(e,t.buffer,t.decoder),r);var r}function P(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,n.nextTick(C,t,e))}function C(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function T(e,t){for(var r=0,i=e.length;r<i;r++)if(e[r]===t)return r;return-1}v.prototype.read=function(e){l("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return l("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?P(this):M(this),null;if(0===(e=S(e,t))&&t.ended)return 0===t.length&&P(this),null;var i,n=t.needReadable;return l("need readable",n),(0===t.length||t.length-e<t.highWaterMark)&&l("length less than watermark",n=!0),t.ended||t.reading?l("reading or ended",n=!1):n&&(l("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=S(r,t))),null===(i=e>0?R(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&P(this)),null!==i&&this.emit("data",i),i},v.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},v.prototype.pipe=function(e,t){var r=this,f=this._readableState;switch(f.pipesCount){case 0:f.pipes=e;break;case 1:f.pipes=[f.pipes,e];break;default:f.pipes.push(e)}f.pipesCount+=1,l("pipe count=%d opts=%j",f.pipesCount,t);var s=(!t||!1!==t.end)&&e!==i.stdout&&e!==i.stderr?h:v;function c(t,i){l("onunpipe"),t===r&&i&&!1===i.hasUnpiped&&(i.hasUnpiped=!0,l("cleanup"),e.removeListener("close",g),e.removeListener("finish",m),e.removeListener("drain",d),e.removeListener("error",y),e.removeListener("unpipe",c),r.removeListener("end",h),r.removeListener("end",v),r.removeListener("data",b),u=!0,!f.awaitDrain||e._writableState&&!e._writableState.needDrain||d())}function h(){l("onend"),e.end()}f.endEmitted?n.nextTick(s):r.once("end",s),e.on("unpipe",c);var d=function(e){return function(){var t=e._readableState;l("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&a(e,"data")&&(t.flowing=!0,I(e))}}(r);e.on("drain",d);var u=!1;var p=!1;function b(t){l("ondata"),p=!1,!1!==e.write(t)||p||((1===f.pipesCount&&f.pipes===e||f.pipesCount>1&&-1!==T(f.pipes,e))&&!u&&(l("false write response, pause",f.awaitDrain),f.awaitDrain++,p=!0),r.pause())}function y(t){l("onerror",t),v(),e.removeListener("error",y),0===a(e,"error")&&e.emit("error",t)}function g(){e.removeListener("finish",m),v()}function m(){l("onfinish"),e.removeListener("close",g),v()}function v(){l("unpipe"),r.unpipe(e)}return r.on("data",b),function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?o(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",y),e.once("close",g),e.once("finish",m),e.emit("pipe",r),f.flowing||(l("pipe resume"),r.resume()),e},v.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var i=t.pipes,n=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var f=0;f<n;f++)i[f].emit("unpipe",this,{hasUnpiped:!1});return this}var o=T(t.pipes,e);return-1===o||(t.pipes.splice(o,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},v.prototype.on=function(e,t){var r=s.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var i=this._readableState;i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.emittedReadable=!1,i.reading?i.length&&M(this):n.nextTick(x,this))}return r},v.prototype.addListener=v.prototype.on,v.prototype.resume=function(){var e=this._readableState;return e.flowing||(l("resume"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,n.nextTick(B,e,t))}(this,e)),this},v.prototype.pause=function(){return l("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(l("pause"),this._readableState.flowing=!1,this.emit("pause")),this},v.prototype.wrap=function(e){var t=this,r=this._readableState,i=!1;for(var n in e.on("end",(function(){if(l("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on("data",(function(n){(l("wrapped data"),r.decoder&&(n=r.decoder.write(n)),r.objectMode&&null==n)||(r.objectMode||n&&n.length)&&(t.push(n)||(i=!0,e.pause()))})),e)void 0===this[n]&&"function"==typeof e[n]&&(this[n]=function(t){return function(){return e[t].apply(e,arguments)}}(n));for(var f=0;f<g.length;f++)e.on(g[f],this.emit.bind(this,g[f]));return this._read=function(t){l("wrapped _read",t),i&&(i=!1,e.resume())},this},Object.defineProperty(v.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),v._fromList=R}).call(this,r(16),r(109))},function(e,t,r){e.exports=r(314).EventEmitter},,,function(e,t,r){"use strict";var i=r(298);function n(e,t){e.emit("error",t)}e.exports={destroy:function(e,t){var r=this,f=this._readableState&&this._readableState.destroyed,o=this._writableState&&this._writableState.destroyed;return f||o?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,i.nextTick(n,this,e)):i.nextTick(n,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,(function(e){!t&&e?r._writableState?r._writableState.errorEmitted||(r._writableState.errorEmitted=!0,i.nextTick(n,r,e)):i.nextTick(n,r,e):t&&t(e)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},function(e,t,r){"use strict";e.exports=o;var i=r(283),n=Object.create(r(294));function f(e,t){var r=this._transformState;r.transforming=!1;var i=r.writecb;if(!i)return this.emit("error",new Error("write callback called multiple times"));r.writechunk=null,r.writecb=null,null!=t&&this.push(t),i(e);var n=this._readableState;n.reading=!1,(n.needReadable||n.length<n.highWaterMark)&&this._read(n.highWaterMark)}function o(e){if(!(this instanceof o))return new o(e);i.call(this,e),this._transformState={afterTransform:f.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",a)}function a(){var e=this;"function"==typeof this._flush?this._flush((function(t,r){s(e,t,r)})):s(this,null,null)}function s(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(e._transformState.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}n.inherits=r(275),n.inherits(o,i),o.prototype.push=function(e,t){return this._transformState.needTransform=!1,i.prototype.push.call(this,e,t)},o.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")},o.prototype._write=function(e,t,r){var i=this._transformState;if(i.writecb=r,i.writechunk=e,i.writeencoding=t,!i.transforming){var n=this._readableState;(i.needTransform||n.needReadable||n.length<n.highWaterMark)&&this._read(n.highWaterMark)}},o.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},o.prototype._destroy=function(e,t){var r=this;i.prototype._destroy.call(this,e,(function(e){t(e),r.emit("close")}))}},function(e,t,r){var i=r(275),n=r(288),f=r(274).Buffer,o=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],a=new Array(64);function s(){this.init(),this._w=a,n.call(this,64,56)}function c(e,t,r){return r^e&(t^r)}function h(e,t,r){return e&t|r&(e|t)}function d(e){return(e>>>2|e<<30)^(e>>>13|e<<19)^(e>>>22|e<<10)}function u(e){return(e>>>6|e<<26)^(e>>>11|e<<21)^(e>>>25|e<<7)}function l(e){return(e>>>7|e<<25)^(e>>>18|e<<14)^e>>>3}i(s,n),s.prototype.init=function(){return this._a=1779033703,this._b=3144134277,this._c=1013904242,this._d=2773480762,this._e=1359893119,this._f=2600822924,this._g=528734635,this._h=1541459225,this},s.prototype._update=function(e){for(var t,r=this._w,i=0|this._a,n=0|this._b,f=0|this._c,a=0|this._d,s=0|this._e,p=0|this._f,b=0|this._g,y=0|this._h,g=0;g<16;++g)r[g]=e.readInt32BE(4*g);for(;g<64;++g)r[g]=0|(((t=r[g-2])>>>17|t<<15)^(t>>>19|t<<13)^t>>>10)+r[g-7]+l(r[g-15])+r[g-16];for(var m=0;m<64;++m){var v=y+u(s)+c(s,p,b)+o[m]+r[m]|0,_=d(i)+h(i,n,f)|0;y=b,b=p,p=s,s=a+v|0,a=f,f=n,n=i,i=v+_|0}this._a=i+this._a|0,this._b=n+this._b|0,this._c=f+this._c|0,this._d=a+this._d|0,this._e=s+this._e|0,this._f=p+this._f|0,this._g=b+this._g|0,this._h=y+this._h|0},s.prototype._hash=function(){var e=f.allocUnsafe(32);return e.writeInt32BE(this._a,0),e.writeInt32BE(this._b,4),e.writeInt32BE(this._c,8),e.writeInt32BE(this._d,12),e.writeInt32BE(this._e,16),e.writeInt32BE(this._f,20),e.writeInt32BE(this._g,24),e.writeInt32BE(this._h,28),e},e.exports=s},function(e,t,r){var i=r(275),n=r(288),f=r(274).Buffer,o=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591],a=new Array(160);function s(){this.init(),this._w=a,n.call(this,128,112)}function c(e,t,r){return r^e&(t^r)}function h(e,t,r){return e&t|r&(e|t)}function d(e,t){return(e>>>28|t<<4)^(t>>>2|e<<30)^(t>>>7|e<<25)}function u(e,t){return(e>>>14|t<<18)^(e>>>18|t<<14)^(t>>>9|e<<23)}function l(e,t){return(e>>>1|t<<31)^(e>>>8|t<<24)^e>>>7}function p(e,t){return(e>>>1|t<<31)^(e>>>8|t<<24)^(e>>>7|t<<25)}function b(e,t){return(e>>>19|t<<13)^(t>>>29|e<<3)^e>>>6}function y(e,t){return(e>>>19|t<<13)^(t>>>29|e<<3)^(e>>>6|t<<26)}function g(e,t){return e>>>0<t>>>0?1:0}i(s,n),s.prototype.init=function(){return this._ah=1779033703,this._bh=3144134277,this._ch=1013904242,this._dh=2773480762,this._eh=1359893119,this._fh=2600822924,this._gh=528734635,this._hh=1541459225,this._al=4089235720,this._bl=2227873595,this._cl=4271175723,this._dl=1595750129,this._el=2917565137,this._fl=725511199,this._gl=4215389547,this._hl=327033209,this},s.prototype._update=function(e){for(var t=this._w,r=0|this._ah,i=0|this._bh,n=0|this._ch,f=0|this._dh,a=0|this._eh,s=0|this._fh,m=0|this._gh,v=0|this._hh,_=0|this._al,w=0|this._bl,S=0|this._cl,M=0|this._dl,E=0|this._el,k=0|this._fl,A=0|this._gl,x=0|this._hl,B=0;B<32;B+=2)t[B]=e.readInt32BE(4*B),t[B+1]=e.readInt32BE(4*B+4);for(;B<160;B+=2){var I=t[B-30],R=t[B-30+1],P=l(I,R),C=p(R,I),T=b(I=t[B-4],R=t[B-4+1]),j=y(R,I),L=t[B-14],O=t[B-14+1],D=t[B-32],U=t[B-32+1],q=C+O|0,N=P+L+g(q,C)|0;N=(N=N+T+g(q=q+j|0,j)|0)+D+g(q=q+U|0,U)|0,t[B]=N,t[B+1]=q}for(var z=0;z<160;z+=2){N=t[z],q=t[z+1];var K=h(r,i,n),F=h(_,w,S),H=d(r,_),Y=d(_,r),W=u(a,E),V=u(E,a),X=o[z],J=o[z+1],G=c(a,s,m),Z=c(E,k,A),$=x+V|0,Q=v+W+g($,x)|0;Q=(Q=(Q=Q+G+g($=$+Z|0,Z)|0)+X+g($=$+J|0,J)|0)+N+g($=$+q|0,q)|0;var ee=Y+F|0,te=H+K+g(ee,Y)|0;v=m,x=A,m=s,A=k,s=a,k=E,a=f+Q+g(E=M+$|0,M)|0,f=n,M=S,n=i,S=w,i=r,w=_,r=Q+te+g(_=$+ee|0,$)|0}this._al=this._al+_|0,this._bl=this._bl+w|0,this._cl=this._cl+S|0,this._dl=this._dl+M|0,this._el=this._el+E|0,this._fl=this._fl+k|0,this._gl=this._gl+A|0,this._hl=this._hl+x|0,this._ah=this._ah+r+g(this._al,_)|0,this._bh=this._bh+i+g(this._bl,w)|0,this._ch=this._ch+n+g(this._cl,S)|0,this._dh=this._dh+f+g(this._dl,M)|0,this._eh=this._eh+a+g(this._el,E)|0,this._fh=this._fh+s+g(this._fl,k)|0,this._gh=this._gh+m+g(this._gl,A)|0,this._hh=this._hh+v+g(this._hl,x)|0},s.prototype._hash=function(){var e=f.allocUnsafe(64);function t(t,r,i){e.writeInt32BE(t,i),e.writeInt32BE(r,i+4)}return t(this._ah,this._al,0),t(this._bh,this._bl,8),t(this._ch,this._cl,16),t(this._dh,this._dl,24),t(this._eh,this._el,32),t(this._fh,this._fl,40),t(this._gh,this._gl,48),t(this._hh,this._hl,56),e},e.exports=s},function(e,t,r){"use strict";var i=r(275),n=r(461),f=r(281),o=r(274).Buffer,a=r(378),s=r(317),c=r(318),h=o.alloc(128);function d(e,t){f.call(this,"digest"),"string"==typeof t&&(t=o.from(t));var r="sha512"===e||"sha384"===e?128:64;(this._alg=e,this._key=t,t.length>r)?t=("rmd160"===e?new s:c(e)).update(t).digest():t.length<r&&(t=o.concat([t,h],r));for(var i=this._ipad=o.allocUnsafe(r),n=this._opad=o.allocUnsafe(r),a=0;a<r;a++)i[a]=54^t[a],n[a]=92^t[a];this._hash="rmd160"===e?new s:c(e),this._hash.update(i)}i(d,f),d.prototype._update=function(e){this._hash.update(e)},d.prototype._final=function(){var e=this._hash.digest();return("rmd160"===this._alg?new s:c(this._alg)).update(this._opad).update(e).digest()},e.exports=function(e,t){return"rmd160"===(e=e.toLowerCase())||"ripemd160"===e?new d("rmd160",t):"md5"===e?new n(a,t):new d(e,t)}},function(e,t,r){var i=r(313);e.exports=function(e){return(new i).update(e).digest()}},function(e){e.exports=JSON.parse('{"sha224WithRSAEncryption":{"sign":"rsa","hash":"sha224","id":"302d300d06096086480165030402040500041c"},"RSA-SHA224":{"sign":"ecdsa/rsa","hash":"sha224","id":"302d300d06096086480165030402040500041c"},"sha256WithRSAEncryption":{"sign":"rsa","hash":"sha256","id":"3031300d060960864801650304020105000420"},"RSA-SHA256":{"sign":"ecdsa/rsa","hash":"sha256","id":"3031300d060960864801650304020105000420"},"sha384WithRSAEncryption":{"sign":"rsa","hash":"sha384","id":"3041300d060960864801650304020205000430"},"RSA-SHA384":{"sign":"ecdsa/rsa","hash":"sha384","id":"3041300d060960864801650304020205000430"},"sha512WithRSAEncryption":{"sign":"rsa","hash":"sha512","id":"3051300d060960864801650304020305000440"},"RSA-SHA512":{"sign":"ecdsa/rsa","hash":"sha512","id":"3051300d060960864801650304020305000440"},"RSA-SHA1":{"sign":"rsa","hash":"sha1","id":"3021300906052b0e03021a05000414"},"ecdsa-with-SHA1":{"sign":"ecdsa","hash":"sha1","id":""},"sha256":{"sign":"ecdsa","hash":"sha256","id":""},"sha224":{"sign":"ecdsa","hash":"sha224","id":""},"sha384":{"sign":"ecdsa","hash":"sha384","id":""},"sha512":{"sign":"ecdsa","hash":"sha512","id":""},"DSA-SHA":{"sign":"dsa","hash":"sha1","id":""},"DSA-SHA1":{"sign":"dsa","hash":"sha1","id":""},"DSA":{"sign":"dsa","hash":"sha1","id":""},"DSA-WITH-SHA224":{"sign":"dsa","hash":"sha224","id":""},"DSA-SHA224":{"sign":"dsa","hash":"sha224","id":""},"DSA-WITH-SHA256":{"sign":"dsa","hash":"sha256","id":""},"DSA-SHA256":{"sign":"dsa","hash":"sha256","id":""},"DSA-WITH-SHA384":{"sign":"dsa","hash":"sha384","id":""},"DSA-SHA384":{"sign":"dsa","hash":"sha384","id":""},"DSA-WITH-SHA512":{"sign":"dsa","hash":"sha512","id":""},"DSA-SHA512":{"sign":"dsa","hash":"sha512","id":""},"DSA-RIPEMD160":{"sign":"dsa","hash":"rmd160","id":""},"ripemd160WithRSA":{"sign":"rsa","hash":"rmd160","id":"3021300906052b2403020105000414"},"RSA-RIPEMD160":{"sign":"rsa","hash":"rmd160","id":"3021300906052b2403020105000414"},"md5WithRSAEncryption":{"sign":"rsa","hash":"md5","id":"3020300c06082a864886f70d020505000410"},"RSA-MD5":{"sign":"rsa","hash":"md5","id":"3020300c06082a864886f70d020505000410"}}')},function(e,t,r){t.pbkdf2=r(463),t.pbkdf2Sync=r(383)},function(e,t){var r=Math.pow(2,30)-1;e.exports=function(e,t){if("number"!=typeof e)throw new TypeError("Iterations not a number");if(e<0)throw new TypeError("Bad iterations");if("number"!=typeof t)throw new TypeError("Key length not a number");if(t<0||t>r||t!=t)throw new TypeError("Bad key length")}},function(e,t,r){(function(t,r){var i;if(t.process&&t.process.browser)i="utf-8";else if(t.process&&t.process.version){i=parseInt(r.version.split(".")[0].slice(1),10)>=6?"utf-8":"binary"}else i="utf-8";e.exports=i}).call(this,r(16),r(109))},function(e,t,r){var i=r(378),n=r(317),f=r(318),o=r(274).Buffer,a=r(381),s=r(382),c=r(384),h=o.alloc(128),d={md5:16,sha1:20,sha224:28,sha256:32,sha384:48,sha512:64,rmd160:20,ripemd160:20};function u(e,t,r){var a=function(e){function t(t){return f(e).update(t).digest()}return"rmd160"===e||"ripemd160"===e?function(e){return(new n).update(e).digest()}:"md5"===e?i:t}(e),s="sha512"===e||"sha384"===e?128:64;t.length>s?t=a(t):t.length<s&&(t=o.concat([t,h],s));for(var c=o.allocUnsafe(s+d[e]),u=o.allocUnsafe(s+d[e]),l=0;l<s;l++)c[l]=54^t[l],u[l]=92^t[l];var p=o.allocUnsafe(s+r+4);c.copy(p,0,0,s),this.ipad1=p,this.ipad2=c,this.opad=u,this.alg=e,this.blocksize=s,this.hash=a,this.size=d[e]}u.prototype.run=function(e,t){return e.copy(t,this.blocksize),this.hash(t).copy(this.opad,this.blocksize),this.hash(this.opad)},e.exports=function(e,t,r,i,n){a(r,i);var f=new u(n=n||"sha1",e=c(e,s,"Password"),(t=c(t,s,"Salt")).length),h=o.allocUnsafe(i),l=o.allocUnsafe(t.length+4);t.copy(l,0,0,t.length);for(var p=0,b=d[n],y=Math.ceil(i/b),g=1;g<=y;g++){l.writeUInt32BE(g,t.length);for(var m=f.run(l,f.ipad1),v=m,_=1;_<r;_++){v=f.run(v,f.ipad2);for(var w=0;w<b;w++)m[w]^=v[w]}m.copy(h,p),p+=b}return h}},function(e,t,r){var i=r(274).Buffer;e.exports=function(e,t,r){if(i.isBuffer(e))return e;if("string"==typeof e)return i.from(e,t);if(ArrayBuffer.isView(e))return i.from(e.buffer);throw new TypeError(r+" must be a string, a Buffer, a typed array or a DataView")}},function(e,t,r){"use strict";t.readUInt32BE=function(e,t){return(e[0+t]<<24|e[1+t]<<16|e[2+t]<<8|e[3+t])>>>0},t.writeUInt32BE=function(e,t,r){e[0+r]=t>>>24,e[1+r]=t>>>16&255,e[2+r]=t>>>8&255,e[3+r]=255&t},t.ip=function(e,t,r,i){for(var n=0,f=0,o=6;o>=0;o-=2){for(var a=0;a<=24;a+=8)n<<=1,n|=t>>>a+o&1;for(a=0;a<=24;a+=8)n<<=1,n|=e>>>a+o&1}for(o=6;o>=0;o-=2){for(a=1;a<=25;a+=8)f<<=1,f|=t>>>a+o&1;for(a=1;a<=25;a+=8)f<<=1,f|=e>>>a+o&1}r[i+0]=n>>>0,r[i+1]=f>>>0},t.rip=function(e,t,r,i){for(var n=0,f=0,o=0;o<4;o++)for(var a=24;a>=0;a-=8)n<<=1,n|=t>>>a+o&1,n<<=1,n|=e>>>a+o&1;for(o=4;o<8;o++)for(a=24;a>=0;a-=8)f<<=1,f|=t>>>a+o&1,f<<=1,f|=e>>>a+o&1;r[i+0]=n>>>0,r[i+1]=f>>>0},t.pc1=function(e,t,r,i){for(var n=0,f=0,o=7;o>=5;o--){for(var a=0;a<=24;a+=8)n<<=1,n|=t>>a+o&1;for(a=0;a<=24;a+=8)n<<=1,n|=e>>a+o&1}for(a=0;a<=24;a+=8)n<<=1,n|=t>>a+o&1;for(o=1;o<=3;o++){for(a=0;a<=24;a+=8)f<<=1,f|=t>>a+o&1;for(a=0;a<=24;a+=8)f<<=1,f|=e>>a+o&1}for(a=0;a<=24;a+=8)f<<=1,f|=e>>a+o&1;r[i+0]=n>>>0,r[i+1]=f>>>0},t.r28shl=function(e,t){return e<<t&268435455|e>>>28-t};var i=[14,11,17,4,27,23,25,0,13,22,7,18,5,9,16,24,2,20,12,21,1,8,15,26,15,4,25,19,9,1,26,16,5,11,23,8,12,7,17,0,22,3,10,14,6,20,27,24];t.pc2=function(e,t,r,n){for(var f=0,o=0,a=i.length>>>1,s=0;s<a;s++)f<<=1,f|=e>>>i[s]&1;for(s=a;s<i.length;s++)o<<=1,o|=t>>>i[s]&1;r[n+0]=f>>>0,r[n+1]=o>>>0},t.expand=function(e,t,r){var i=0,n=0;i=(1&e)<<5|e>>>27;for(var f=23;f>=15;f-=4)i<<=6,i|=e>>>f&63;for(f=11;f>=3;f-=4)n|=e>>>f&63,n<<=6;n|=(31&e)<<1|e>>>31,t[r+0]=i>>>0,t[r+1]=n>>>0};var n=[14,0,4,15,13,7,1,4,2,14,15,2,11,13,8,1,3,10,10,6,6,12,12,11,5,9,9,5,0,3,7,8,4,15,1,12,14,8,8,2,13,4,6,9,2,1,11,7,15,5,12,11,9,3,7,14,3,10,10,0,5,6,0,13,15,3,1,13,8,4,14,7,6,15,11,2,3,8,4,14,9,12,7,0,2,1,13,10,12,6,0,9,5,11,10,5,0,13,14,8,7,10,11,1,10,3,4,15,13,4,1,2,5,11,8,6,12,7,6,12,9,0,3,5,2,14,15,9,10,13,0,7,9,0,14,9,6,3,3,4,15,6,5,10,1,2,13,8,12,5,7,14,11,12,4,11,2,15,8,1,13,1,6,10,4,13,9,0,8,6,15,9,3,8,0,7,11,4,1,15,2,14,12,3,5,11,10,5,14,2,7,12,7,13,13,8,14,11,3,5,0,6,6,15,9,0,10,3,1,4,2,7,8,2,5,12,11,1,12,10,4,14,15,9,10,3,6,15,9,0,0,6,12,10,11,1,7,13,13,8,15,9,1,4,3,5,14,11,5,12,2,7,8,2,4,14,2,14,12,11,4,2,1,12,7,4,10,7,11,13,6,1,8,5,5,0,3,15,15,10,13,3,0,9,14,8,9,6,4,11,2,8,1,12,11,7,10,1,13,14,7,2,8,13,15,6,9,15,12,0,5,9,6,10,3,4,0,5,14,3,12,10,1,15,10,4,15,2,9,7,2,12,6,9,8,5,0,6,13,1,3,13,4,14,14,0,7,11,5,3,11,8,9,4,14,3,15,2,5,12,2,9,8,5,12,15,3,10,7,11,0,14,4,1,10,7,1,6,13,0,11,8,6,13,4,13,11,0,2,11,14,7,15,4,0,9,8,1,13,10,3,14,12,3,9,5,7,12,5,2,10,15,6,8,1,6,1,6,4,11,11,13,13,8,12,1,3,4,7,10,14,7,10,9,15,5,6,0,8,15,0,14,5,2,9,3,2,12,13,1,2,15,8,13,4,8,6,10,15,3,11,7,1,4,10,12,9,5,3,6,14,11,5,0,0,14,12,9,7,2,7,2,11,1,4,14,1,7,9,4,12,10,14,8,2,13,0,15,6,12,10,9,13,0,15,3,3,5,5,6,8,11];t.substitute=function(e,t){for(var r=0,i=0;i<4;i++){r<<=4,r|=n[64*i+(e>>>18-6*i&63)]}for(i=0;i<4;i++){r<<=4,r|=n[256+64*i+(t>>>18-6*i&63)]}return r>>>0};var f=[16,25,12,11,3,20,4,15,31,17,9,6,27,14,1,22,30,24,8,18,0,5,29,23,13,19,2,26,10,21,28,7];t.permute=function(e){for(var t=0,r=0;r<f.length;r++)t<<=1,t|=e>>>f[r]&1;return t>>>0},t.padSplit=function(e,t,r){for(var i=e.toString(2);i.length<t;)i="0"+i;for(var n=[],f=0;f<t;f+=r)n.push(i.slice(f,f+r));return n.join(" ")}},function(e,t,r){"use strict";var i=r(278),n=r(275),f=r(385),o=r(319);function a(){this.tmp=new Array(2),this.keys=null}function s(e){o.call(this,e);var t=new a;this._desState=t,this.deriveKeys(t,e.key)}n(s,o),e.exports=s,s.create=function(e){return new s(e)};var c=[1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1];s.prototype.deriveKeys=function(e,t){e.keys=new Array(32),i.equal(t.length,this.blockSize,"Invalid key length");var r=f.readUInt32BE(t,0),n=f.readUInt32BE(t,4);f.pc1(r,n,e.tmp,0),r=e.tmp[0],n=e.tmp[1];for(var o=0;o<e.keys.length;o+=2){var a=c[o>>>1];r=f.r28shl(r,a),n=f.r28shl(n,a),f.pc2(r,n,e.keys,o)}},s.prototype._update=function(e,t,r,i){var n=this._desState,o=f.readUInt32BE(e,t),a=f.readUInt32BE(e,t+4);f.ip(o,a,n.tmp,0),o=n.tmp[0],a=n.tmp[1],"encrypt"===this.type?this._encrypt(n,o,a,n.tmp,0):this._decrypt(n,o,a,n.tmp,0),o=n.tmp[0],a=n.tmp[1],f.writeUInt32BE(r,o,i),f.writeUInt32BE(r,a,i+4)},s.prototype._pad=function(e,t){if(!1===this.padding)return!1;for(var r=e.length-t,i=t;i<e.length;i++)e[i]=r;return!0},s.prototype._unpad=function(e){if(!1===this.padding)return e;for(var t=e[e.length-1],r=e.length-t;r<e.length;r++)i.equal(e[r],t);return e.slice(0,e.length-t)},s.prototype._encrypt=function(e,t,r,i,n){for(var o=t,a=r,s=0;s<e.keys.length;s+=2){var c=e.keys[s],h=e.keys[s+1];f.expand(a,e.tmp,0),c^=e.tmp[0],h^=e.tmp[1];var d=f.substitute(c,h),u=a;a=(o^f.permute(d))>>>0,o=u}f.rip(a,o,i,n)},s.prototype._decrypt=function(e,t,r,i,n){for(var o=r,a=t,s=e.keys.length-2;s>=0;s-=2){var c=e.keys[s],h=e.keys[s+1];f.expand(o,e.tmp,0),c^=e.tmp[0],h^=e.tmp[1];var d=f.substitute(c,h),u=o;o=(a^f.permute(d))>>>0,a=u}f.rip(o,a,i,n)}},function(e,t,r){var i=r(295),n=r(274).Buffer,f=r(388);function o(e){var t=e._cipher.encryptBlockRaw(e._prev);return f(e._prev),t}t.encrypt=function(e,t){var r=Math.ceil(t.length/16),f=e._cache.length;e._cache=n.concat([e._cache,n.allocUnsafe(16*r)]);for(var a=0;a<r;a++){var s=o(e),c=f+16*a;e._cache.writeUInt32BE(s[0],c+0),e._cache.writeUInt32BE(s[1],c+4),e._cache.writeUInt32BE(s[2],c+8),e._cache.writeUInt32BE(s[3],c+12)}var h=e._cache.slice(0,t.length);return e._cache=e._cache.slice(t.length),i(t,h)}},function(e,t){e.exports=function(e){for(var t,r=e.length;r--;){if(255!==(t=e.readUInt8(r))){t++,e.writeUInt8(t,r);break}e.writeUInt8(0,r)}}},function(e){e.exports=JSON.parse('{"aes-128-ecb":{"cipher":"AES","key":128,"iv":0,"mode":"ECB","type":"block"},"aes-192-ecb":{"cipher":"AES","key":192,"iv":0,"mode":"ECB","type":"block"},"aes-256-ecb":{"cipher":"AES","key":256,"iv":0,"mode":"ECB","type":"block"},"aes-128-cbc":{"cipher":"AES","key":128,"iv":16,"mode":"CBC","type":"block"},"aes-192-cbc":{"cipher":"AES","key":192,"iv":16,"mode":"CBC","type":"block"},"aes-256-cbc":{"cipher":"AES","key":256,"iv":16,"mode":"CBC","type":"block"},"aes128":{"cipher":"AES","key":128,"iv":16,"mode":"CBC","type":"block"},"aes192":{"cipher":"AES","key":192,"iv":16,"mode":"CBC","type":"block"},"aes256":{"cipher":"AES","key":256,"iv":16,"mode":"CBC","type":"block"},"aes-128-cfb":{"cipher":"AES","key":128,"iv":16,"mode":"CFB","type":"stream"},"aes-192-cfb":{"cipher":"AES","key":192,"iv":16,"mode":"CFB","type":"stream"},"aes-256-cfb":{"cipher":"AES","key":256,"iv":16,"mode":"CFB","type":"stream"},"aes-128-cfb8":{"cipher":"AES","key":128,"iv":16,"mode":"CFB8","type":"stream"},"aes-192-cfb8":{"cipher":"AES","key":192,"iv":16,"mode":"CFB8","type":"stream"},"aes-256-cfb8":{"cipher":"AES","key":256,"iv":16,"mode":"CFB8","type":"stream"},"aes-128-cfb1":{"cipher":"AES","key":128,"iv":16,"mode":"CFB1","type":"stream"},"aes-192-cfb1":{"cipher":"AES","key":192,"iv":16,"mode":"CFB1","type":"stream"},"aes-256-cfb1":{"cipher":"AES","key":256,"iv":16,"mode":"CFB1","type":"stream"},"aes-128-ofb":{"cipher":"AES","key":128,"iv":16,"mode":"OFB","type":"stream"},"aes-192-ofb":{"cipher":"AES","key":192,"iv":16,"mode":"OFB","type":"stream"},"aes-256-ofb":{"cipher":"AES","key":256,"iv":16,"mode":"OFB","type":"stream"},"aes-128-ctr":{"cipher":"AES","key":128,"iv":16,"mode":"CTR","type":"stream"},"aes-192-ctr":{"cipher":"AES","key":192,"iv":16,"mode":"CTR","type":"stream"},"aes-256-ctr":{"cipher":"AES","key":256,"iv":16,"mode":"CTR","type":"stream"},"aes-128-gcm":{"cipher":"AES","key":128,"iv":12,"mode":"GCM","type":"auth"},"aes-192-gcm":{"cipher":"AES","key":192,"iv":12,"mode":"GCM","type":"auth"},"aes-256-gcm":{"cipher":"AES","key":256,"iv":12,"mode":"GCM","type":"auth"}}')},function(e,t,r){var i=r(299),n=r(274).Buffer,f=r(281),o=r(275),a=r(476),s=r(295),c=r(388);function h(e,t,r,o){f.call(this);var s=n.alloc(4,0);this._cipher=new i.AES(t);var h=this._cipher.encryptBlock(s);this._ghash=new a(h),r=function(e,t,r){if(12===t.length)return e._finID=n.concat([t,n.from([0,0,0,1])]),n.concat([t,n.from([0,0,0,2])]);var i=new a(r),f=t.length,o=f%16;i.update(t),o&&(o=16-o,i.update(n.alloc(o,0))),i.update(n.alloc(8,0));var s=8*f,h=n.alloc(8);h.writeUIntBE(s,0,8),i.update(h),e._finID=i.state;var d=n.from(e._finID);return c(d),d}(this,r,h),this._prev=n.from(r),this._cache=n.allocUnsafe(0),this._secCache=n.allocUnsafe(0),this._decrypt=o,this._alen=0,this._len=0,this._mode=e,this._authTag=null,this._called=!1}o(h,f),h.prototype._update=function(e){if(!this._called&&this._alen){var t=16-this._alen%16;t<16&&(t=n.alloc(t,0),this._ghash.update(t))}this._called=!0;var r=this._mode.encrypt(this,e);return this._decrypt?this._ghash.update(e):this._ghash.update(r),this._len+=e.length,r},h.prototype._final=function(){if(this._decrypt&&!this._authTag)throw new Error("Unsupported state or unable to authenticate data");var e=s(this._ghash.final(8*this._alen,8*this._len),this._cipher.encryptBlock(this._finID));if(this._decrypt&&function(e,t){var r=0;e.length!==t.length&&r++;for(var i=Math.min(e.length,t.length),n=0;n<i;++n)r+=e[n]^t[n];return r}(e,this._authTag))throw new Error("Unsupported state or unable to authenticate data");this._authTag=e,this._cipher.scrub()},h.prototype.getAuthTag=function(){if(this._decrypt||!n.isBuffer(this._authTag))throw new Error("Attempting to get auth tag in unsupported state");return this._authTag},h.prototype.setAuthTag=function(e){if(!this._decrypt)throw new Error("Attempting to set auth tag in unsupported state");this._authTag=e},h.prototype.setAAD=function(e){if(this._called)throw new Error("Attempting to set AAD in unsupported state");this._ghash.update(e),this._alen+=e.length},e.exports=h},function(e,t,r){var i=r(299),n=r(274).Buffer,f=r(281);function o(e,t,r,o){f.call(this),this._cipher=new i.AES(t),this._prev=n.from(r),this._cache=n.allocUnsafe(0),this._secCache=n.allocUnsafe(0),this._decrypt=o,this._mode=e}r(275)(o,f),o.prototype._update=function(e){return this._mode.encrypt(this,e,this._decrypt)},o.prototype._final=function(){this._cipher.scrub()},e.exports=o},function(e,t,r){var i=r(287);e.exports=m,m.simpleSieve=y,m.fermatTest=g;var n=r(276),f=new n(24),o=new(r(394)),a=new n(1),s=new n(2),c=new n(5),h=(new n(16),new n(8),new n(10)),d=new n(3),u=(new n(7),new n(11)),l=new n(4),p=(new n(12),null);function b(){if(null!==p)return p;var e=[];e[0]=2;for(var t=1,r=3;r<1048576;r+=2){for(var i=Math.ceil(Math.sqrt(r)),n=0;n<t&&e[n]<=i&&r%e[n]!=0;n++);t!==n&&e[n]<=i||(e[t++]=r)}return p=e,e}function y(e){for(var t=b(),r=0;r<t.length;r++)if(0===e.modn(t[r]))return 0===e.cmpn(t[r]);return!0}function g(e){var t=n.mont(e);return 0===s.toRed(t).redPow(e.subn(1)).fromRed().cmpn(1)}function m(e,t){if(e<16)return new n(2===t||5===t?[140,123]:[140,39]);var r,p;for(t=new n(t);;){for(r=new n(i(Math.ceil(e/8)));r.bitLength()>e;)r.ishrn(1);if(r.isEven()&&r.iadd(a),r.testn(1)||r.iadd(s),t.cmp(s)){if(!t.cmp(c))for(;r.mod(h).cmp(d);)r.iadd(l)}else for(;r.mod(f).cmp(u);)r.iadd(l);if(y(p=r.shrn(1))&&y(r)&&g(p)&&g(r)&&o.test(p)&&o.test(r))return r}}},,function(e,t,r){var i=r(276),n=r(322);function f(e){this.rand=e||new n.Rand}e.exports=f,f.create=function(e){return new f(e)},f.prototype._randbelow=function(e){var t=e.bitLength(),r=Math.ceil(t/8);do{var n=new i(this.rand.generate(r))}while(n.cmp(e)>=0);return n},f.prototype._randrange=function(e,t){var r=t.sub(e);return e.add(this._randbelow(r))},f.prototype.test=function(e,t,r){var n=e.bitLength(),f=i.mont(e),o=new i(1).toRed(f);t||(t=Math.max(1,n/48|0));for(var a=e.subn(1),s=0;!a.testn(s);s++);for(var c=e.shrn(s),h=a.toRed(f);t>0;t--){var d=this._randrange(new i(2),a);r&&r(d);var u=d.toRed(f).redPow(c);if(0!==u.cmp(o)&&0!==u.cmp(h)){for(var l=1;l<s;l++){if(0===(u=u.redSqr()).cmp(o))return!1;if(0===u.cmp(h))break}if(l===s)return!1}}return!0},f.prototype.getDivisor=function(e,t){var r=e.bitLength(),n=i.mont(e),f=new i(1).toRed(n);t||(t=Math.max(1,r/48|0));for(var o=e.subn(1),a=0;!o.testn(a);a++);for(var s=e.shrn(a),c=o.toRed(n);t>0;t--){var h=this._randrange(new i(2),o),d=e.gcd(h);if(0!==d.cmpn(1))return d;var u=h.toRed(n).redPow(s);if(0!==u.cmp(f)&&0!==u.cmp(c)){for(var l=1;l<a;l++){if(0===(u=u.redSqr()).cmp(f))return u.fromRed().subn(1).gcd(e);if(0===u.cmp(c))break}if(l===a)return(u=u.redSqr()).fromRed().subn(1).gcd(e)}}return!1}},,function(e,t,r){"use strict";var i=t;function n(e){return 1===e.length?"0"+e:e}function f(e){for(var t="",r=0;r<e.length;r++)t+=n(e[r].toString(16));return t}i.toArray=function(e,t){if(Array.isArray(e))return e.slice();if(!e)return[];var r=[];if("string"!=typeof e){for(var i=0;i<e.length;i++)r[i]=0|e[i];return r}if("hex"===t){(e=e.replace(/[^a-z0-9]+/gi,"")).length%2!=0&&(e="0"+e);for(i=0;i<e.length;i+=2)r.push(parseInt(e[i]+e[i+1],16))}else for(i=0;i<e.length;i++){var n=e.charCodeAt(i),f=n>>8,o=255&n;f?r.push(f,o):r.push(o)}return r},i.zero2=n,i.toHex=f,i.encode=function(e,t){return"hex"===t?f(e):e}},function(e,t,r){"use strict";var i=t;i.base=r(301),i.short=r(485),i.mont=r(486),i.edwards=r(487)},function(e,t,r){"use strict";var i=r(280).rotr32;function n(e,t,r){return e&t^~e&r}function f(e,t,r){return e&t^e&r^t&r}function o(e,t,r){return e^t^r}t.ft_1=function(e,t,r,i){return 0===e?n(t,r,i):1===e||3===e?o(t,r,i):2===e?f(t,r,i):void 0},t.ch32=n,t.maj32=f,t.p32=o,t.s0_256=function(e){return i(e,2)^i(e,13)^i(e,22)},t.s1_256=function(e){return i(e,6)^i(e,11)^i(e,25)},t.g0_256=function(e){return i(e,7)^i(e,18)^e>>>3},t.g1_256=function(e){return i(e,17)^i(e,19)^e>>>10}},function(e,t,r){"use strict";var i=r(280),n=r(296),f=r(398),o=r(278),a=i.sum32,s=i.sum32_4,c=i.sum32_5,h=f.ch32,d=f.maj32,u=f.s0_256,l=f.s1_256,p=f.g0_256,b=f.g1_256,y=n.BlockHash,g=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function m(){if(!(this instanceof m))return new m;y.call(this),this.h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],this.k=g,this.W=new Array(64)}i.inherits(m,y),e.exports=m,m.blockSize=512,m.outSize=256,m.hmacStrength=192,m.padLength=64,m.prototype._update=function(e,t){for(var r=this.W,i=0;i<16;i++)r[i]=e[t+i];for(;i<r.length;i++)r[i]=s(b(r[i-2]),r[i-7],p(r[i-15]),r[i-16]);var n=this.h[0],f=this.h[1],y=this.h[2],g=this.h[3],m=this.h[4],v=this.h[5],_=this.h[6],w=this.h[7];for(o(this.k.length===r.length),i=0;i<r.length;i++){var S=c(w,l(m),h(m,v,_),this.k[i],r[i]),M=a(u(n),d(n,f,y));w=_,_=v,v=m,m=a(g,S),g=y,y=f,f=n,n=a(S,M)}this.h[0]=a(this.h[0],n),this.h[1]=a(this.h[1],f),this.h[2]=a(this.h[2],y),this.h[3]=a(this.h[3],g),this.h[4]=a(this.h[4],m),this.h[5]=a(this.h[5],v),this.h[6]=a(this.h[6],_),this.h[7]=a(this.h[7],w)},m.prototype._digest=function(e){return"hex"===e?i.toHex32(this.h,"big"):i.split32(this.h,"big")}},function(e,t,r){"use strict";var i=r(280),n=r(296),f=r(278),o=i.rotr64_hi,a=i.rotr64_lo,s=i.shr64_hi,c=i.shr64_lo,h=i.sum64,d=i.sum64_hi,u=i.sum64_lo,l=i.sum64_4_hi,p=i.sum64_4_lo,b=i.sum64_5_hi,y=i.sum64_5_lo,g=n.BlockHash,m=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function v(){if(!(this instanceof v))return new v;g.call(this),this.h=[1779033703,4089235720,3144134277,2227873595,1013904242,4271175723,2773480762,1595750129,1359893119,2917565137,2600822924,725511199,528734635,4215389547,1541459225,327033209],this.k=m,this.W=new Array(160)}function _(e,t,r,i,n){var f=e&r^~e&n;return f<0&&(f+=4294967296),f}function w(e,t,r,i,n,f){var o=t&i^~t&f;return o<0&&(o+=4294967296),o}function S(e,t,r,i,n){var f=e&r^e&n^r&n;return f<0&&(f+=4294967296),f}function M(e,t,r,i,n,f){var o=t&i^t&f^i&f;return o<0&&(o+=4294967296),o}function E(e,t){var r=o(e,t,28)^o(t,e,2)^o(t,e,7);return r<0&&(r+=4294967296),r}function k(e,t){var r=a(e,t,28)^a(t,e,2)^a(t,e,7);return r<0&&(r+=4294967296),r}function A(e,t){var r=o(e,t,14)^o(e,t,18)^o(t,e,9);return r<0&&(r+=4294967296),r}function x(e,t){var r=a(e,t,14)^a(e,t,18)^a(t,e,9);return r<0&&(r+=4294967296),r}function B(e,t){var r=o(e,t,1)^o(e,t,8)^s(e,t,7);return r<0&&(r+=4294967296),r}function I(e,t){var r=a(e,t,1)^a(e,t,8)^c(e,t,7);return r<0&&(r+=4294967296),r}function R(e,t){var r=o(e,t,19)^o(t,e,29)^s(e,t,6);return r<0&&(r+=4294967296),r}function P(e,t){var r=a(e,t,19)^a(t,e,29)^c(e,t,6);return r<0&&(r+=4294967296),r}i.inherits(v,g),e.exports=v,v.blockSize=1024,v.outSize=512,v.hmacStrength=192,v.padLength=128,v.prototype._prepareBlock=function(e,t){for(var r=this.W,i=0;i<32;i++)r[i]=e[t+i];for(;i<r.length;i+=2){var n=R(r[i-4],r[i-3]),f=P(r[i-4],r[i-3]),o=r[i-14],a=r[i-13],s=B(r[i-30],r[i-29]),c=I(r[i-30],r[i-29]),h=r[i-32],d=r[i-31];r[i]=l(n,f,o,a,s,c,h,d),r[i+1]=p(n,f,o,a,s,c,h,d)}},v.prototype._update=function(e,t){this._prepareBlock(e,t);var r=this.W,i=this.h[0],n=this.h[1],o=this.h[2],a=this.h[3],s=this.h[4],c=this.h[5],l=this.h[6],p=this.h[7],g=this.h[8],m=this.h[9],v=this.h[10],B=this.h[11],I=this.h[12],R=this.h[13],P=this.h[14],C=this.h[15];f(this.k.length===r.length);for(var T=0;T<r.length;T+=2){var j=P,L=C,O=A(g,m),D=x(g,m),U=_(g,m,v,B,I),q=w(g,m,v,B,I,R),N=this.k[T],z=this.k[T+1],K=r[T],F=r[T+1],H=b(j,L,O,D,U,q,N,z,K,F),Y=y(j,L,O,D,U,q,N,z,K,F);j=E(i,n),L=k(i,n),O=S(i,n,o,a,s),D=M(i,n,o,a,s,c);var W=d(j,L,O,D),V=u(j,L,O,D);P=I,C=R,I=v,R=B,v=g,B=m,g=d(l,p,H,Y),m=u(p,p,H,Y),l=s,p=c,s=o,c=a,o=i,a=n,i=d(H,Y,W,V),n=u(H,Y,W,V)}h(this.h,0,i,n),h(this.h,2,o,a),h(this.h,4,s,c),h(this.h,6,l,p),h(this.h,8,g,m),h(this.h,10,v,B),h(this.h,12,I,R),h(this.h,14,P,C)},v.prototype._digest=function(e){return"hex"===e?i.toHex32(this.h,"big"):i.split32(this.h,"big")}},function(e,t,r){"use strict";const i=t;i.bignum=r(276),i.define=r(503).define,i.base=r(506),i.constants=r(507),i.decoders=r(404),i.encoders=r(402)},function(e,t,r){"use strict";const i=t;i.der=r(403),i.pem=r(504)},function(e,t,r){"use strict";const i=r(275),n=r(327).Buffer,f=r(328),o=r(330);function a(e){this.enc="der",this.name=e.name,this.entity=e,this.tree=new s,this.tree._init(e.body)}function s(e){f.call(this,"der",e)}function c(e){return e<10?"0"+e:e}e.exports=a,a.prototype.encode=function(e,t){return this.tree._encode(e,t).join()},i(s,f),s.prototype._encodeComposite=function(e,t,r,i){const f=function(e,t,r,i){let n;"seqof"===e?e="seq":"setof"===e&&(e="set");if(o.tagByName.hasOwnProperty(e))n=o.tagByName[e];else{if("number"!=typeof e||(0|e)!==e)return i.error("Unknown tag: "+e);n=e}if(n>=31)return i.error("Multi-octet tag encoding unsupported");t||(n|=32);return n|=o.tagClassByName[r||"universal"]<<6,n}(e,t,r,this.reporter);if(i.length<128){const e=n.alloc(2);return e[0]=f,e[1]=i.length,this._createEncoderBuffer([e,i])}let a=1;for(let e=i.length;e>=256;e>>=8)a++;const s=n.alloc(2+a);s[0]=f,s[1]=128|a;for(let e=1+a,t=i.length;t>0;e--,t>>=8)s[e]=255&t;return this._createEncoderBuffer([s,i])},s.prototype._encodeStr=function(e,t){if("bitstr"===t)return this._createEncoderBuffer([0|e.unused,e.data]);if("bmpstr"===t){const t=n.alloc(2*e.length);for(let r=0;r<e.length;r++)t.writeUInt16BE(e.charCodeAt(r),2*r);return this._createEncoderBuffer(t)}return"numstr"===t?this._isNumstr(e)?this._createEncoderBuffer(e):this.reporter.error("Encoding of string type: numstr supports only digits and space"):"printstr"===t?this._isPrintstr(e)?this._createEncoderBuffer(e):this.reporter.error("Encoding of string type: printstr supports only latin upper and lower case letters, digits, space, apostrophe, left and rigth parenthesis, plus sign, comma, hyphen, dot, slash, colon, equal sign, question mark"):/str$/.test(t)||"objDesc"===t?this._createEncoderBuffer(e):this.reporter.error("Encoding of string type: "+t+" unsupported")},s.prototype._encodeObjid=function(e,t,r){if("string"==typeof e){if(!t)return this.reporter.error("string objid given, but no values map found");if(!t.hasOwnProperty(e))return this.reporter.error("objid not found in values map");e=t[e].split(/[\s.]+/g);for(let t=0;t<e.length;t++)e[t]|=0}else if(Array.isArray(e)){e=e.slice();for(let t=0;t<e.length;t++)e[t]|=0}if(!Array.isArray(e))return this.reporter.error("objid() should be either array or string, got: "+JSON.stringify(e));if(!r){if(e[1]>=40)return this.reporter.error("Second objid identifier OOB");e.splice(0,2,40*e[0]+e[1])}let i=0;for(let t=0;t<e.length;t++){let r=e[t];for(i++;r>=128;r>>=7)i++}const f=n.alloc(i);let o=f.length-1;for(let t=e.length-1;t>=0;t--){let r=e[t];for(f[o--]=127&r;(r>>=7)>0;)f[o--]=128|127&r}return this._createEncoderBuffer(f)},s.prototype._encodeTime=function(e,t){let r;const i=new Date(e);return"gentime"===t?r=[c(i.getUTCFullYear()),c(i.getUTCMonth()+1),c(i.getUTCDate()),c(i.getUTCHours()),c(i.getUTCMinutes()),c(i.getUTCSeconds()),"Z"].join(""):"utctime"===t?r=[c(i.getUTCFullYear()%100),c(i.getUTCMonth()+1),c(i.getUTCDate()),c(i.getUTCHours()),c(i.getUTCMinutes()),c(i.getUTCSeconds()),"Z"].join(""):this.reporter.error("Encoding "+t+" time is not supported yet"),this._encodeStr(r,"octstr")},s.prototype._encodeNull=function(){return this._createEncoderBuffer("")},s.prototype._encodeInt=function(e,t){if("string"==typeof e){if(!t)return this.reporter.error("String int or enum given, but no values map");if(!t.hasOwnProperty(e))return this.reporter.error("Values map doesn't contain: "+JSON.stringify(e));e=t[e]}if("number"!=typeof e&&!n.isBuffer(e)){const t=e.toArray();!e.sign&&128&t[0]&&t.unshift(0),e=n.from(t)}if(n.isBuffer(e)){let t=e.length;0===e.length&&t++;const r=n.alloc(t);return e.copy(r),0===e.length&&(r[0]=0),this._createEncoderBuffer(r)}if(e<128)return this._createEncoderBuffer(e);if(e<256)return this._createEncoderBuffer([0,e]);let r=1;for(let t=e;t>=256;t>>=8)r++;const i=new Array(r);for(let t=i.length-1;t>=0;t--)i[t]=255&e,e>>=8;return 128&i[0]&&i.unshift(0),this._createEncoderBuffer(n.from(i))},s.prototype._encodeBool=function(e){return this._createEncoderBuffer(e?255:0)},s.prototype._use=function(e,t){return"function"==typeof e&&(e=e(t)),e._getEncoder("der").tree},s.prototype._skipDefault=function(e,t,r){const i=this._baseState;let n;if(null===i.default)return!1;const f=e.join();if(void 0===i.defaultBuffer&&(i.defaultBuffer=this._encodeValue(i.default,t,r).join()),f.length!==i.defaultBuffer.length)return!1;for(n=0;n<f.length;n++)if(f[n]!==i.defaultBuffer[n])return!1;return!0}},function(e,t,r){"use strict";const i=t;i.der=r(405),i.pem=r(505)},function(e,t,r){"use strict";const i=r(275),n=r(276),f=r(297).DecoderBuffer,o=r(328),a=r(330);function s(e){this.enc="der",this.name=e.name,this.entity=e,this.tree=new c,this.tree._init(e.body)}function c(e){o.call(this,"der",e)}function h(e,t){let r=e.readUInt8(t);if(e.isError(r))return r;const i=a.tagClass[r>>6],n=0==(32&r);if(31==(31&r)){let i=r;for(r=0;128==(128&i);){if(i=e.readUInt8(t),e.isError(i))return i;r<<=7,r|=127&i}}else r&=31;return{cls:i,primitive:n,tag:r,tagStr:a.tag[r]}}function d(e,t,r){let i=e.readUInt8(r);if(e.isError(i))return i;if(!t&&128===i)return null;if(0==(128&i))return i;const n=127&i;if(n>4)return e.error("length octect is too long");i=0;for(let t=0;t<n;t++){i<<=8;const t=e.readUInt8(r);if(e.isError(t))return t;i|=t}return i}e.exports=s,s.prototype.decode=function(e,t){return f.isDecoderBuffer(e)||(e=new f(e,t)),this.tree._decode(e,t)},i(c,o),c.prototype._peekTag=function(e,t,r){if(e.isEmpty())return!1;const i=e.save(),n=h(e,'Failed to peek tag: "'+t+'"');return e.isError(n)?n:(e.restore(i),n.tag===t||n.tagStr===t||n.tagStr+"of"===t||r)},c.prototype._decodeTag=function(e,t,r){const i=h(e,'Failed to decode tag of "'+t+'"');if(e.isError(i))return i;let n=d(e,i.primitive,'Failed to get length of "'+t+'"');if(e.isError(n))return n;if(!r&&i.tag!==t&&i.tagStr!==t&&i.tagStr+"of"!==t)return e.error('Failed to match tag: "'+t+'"');if(i.primitive||null!==n)return e.skip(n,'Failed to match body of: "'+t+'"');const f=e.save(),o=this._skipUntilEnd(e,'Failed to skip indefinite length body: "'+this.tag+'"');return e.isError(o)?o:(n=e.offset-f.offset,e.restore(f),e.skip(n,'Failed to match body of: "'+t+'"'))},c.prototype._skipUntilEnd=function(e,t){for(;;){const r=h(e,t);if(e.isError(r))return r;const i=d(e,r.primitive,t);if(e.isError(i))return i;let n;if(n=r.primitive||null!==i?e.skip(i):this._skipUntilEnd(e,t),e.isError(n))return n;if("end"===r.tagStr)break}},c.prototype._decodeList=function(e,t,r,i){const n=[];for(;!e.isEmpty();){const t=this._peekTag(e,"end");if(e.isError(t))return t;const f=r.decode(e,"der",i);if(e.isError(f)&&t)break;n.push(f)}return n},c.prototype._decodeStr=function(e,t){if("bitstr"===t){const t=e.readUInt8();return e.isError(t)?t:{unused:t,data:e.raw()}}if("bmpstr"===t){const t=e.raw();if(t.length%2==1)return e.error("Decoding of string type: bmpstr length mismatch");let r="";for(let e=0;e<t.length/2;e++)r+=String.fromCharCode(t.readUInt16BE(2*e));return r}if("numstr"===t){const t=e.raw().toString("ascii");return this._isNumstr(t)?t:e.error("Decoding of string type: numstr unsupported characters")}if("octstr"===t)return e.raw();if("objDesc"===t)return e.raw();if("printstr"===t){const t=e.raw().toString("ascii");return this._isPrintstr(t)?t:e.error("Decoding of string type: printstr unsupported characters")}return/str$/.test(t)?e.raw().toString():e.error("Decoding of string type: "+t+" unsupported")},c.prototype._decodeObjid=function(e,t,r){let i;const n=[];let f=0,o=0;for(;!e.isEmpty();)o=e.readUInt8(),f<<=7,f|=127&o,0==(128&o)&&(n.push(f),f=0);128&o&&n.push(f);const a=n[0]/40|0,s=n[0]%40;if(i=r?n:[a,s].concat(n.slice(1)),t){let e=t[i.join(" ")];void 0===e&&(e=t[i.join(".")]),void 0!==e&&(i=e)}return i},c.prototype._decodeTime=function(e,t){const r=e.raw().toString();let i,n,f,o,a,s;if("gentime"===t)i=0|r.slice(0,4),n=0|r.slice(4,6),f=0|r.slice(6,8),o=0|r.slice(8,10),a=0|r.slice(10,12),s=0|r.slice(12,14);else{if("utctime"!==t)return e.error("Decoding "+t+" time is not supported yet");i=0|r.slice(0,2),n=0|r.slice(2,4),f=0|r.slice(4,6),o=0|r.slice(6,8),a=0|r.slice(8,10),s=0|r.slice(10,12),i=i<70?2e3+i:1900+i}return Date.UTC(i,n-1,f,o,a,s,0)},c.prototype._decodeNull=function(){return null},c.prototype._decodeBool=function(e){const t=e.readUInt8();return e.isError(t)?t:0!==t},c.prototype._decodeInt=function(e,t){const r=e.raw();let i=new n(r);return t&&(i=t[i.toString(10)]||i),i},c.prototype._use=function(e,t){return"function"==typeof e&&(e=e(t)),e._getDecoder("der").tree}},function(e){e.exports=JSON.parse('{"1.3.132.0.10":"secp256k1","1.3.132.0.33":"p224","1.2.840.10045.3.1.1":"p192","1.2.840.10045.3.1.7":"p256","1.3.132.0.34":"p384","1.3.132.0.35":"p521"}')},function(e,t,r){var i=r(292),n=r(274).Buffer;function f(e){var t=n.allocUnsafe(4);return t.writeUInt32BE(e,0),t}e.exports=function(e,t){for(var r,o=n.alloc(0),a=0;o.length<t;)r=f(a++),o=n.concat([o,i("sha1").update(e).update(r).digest()]);return o.slice(0,t)}},function(e,t){e.exports=function(e,t){for(var r=e.length,i=-1;++i<r;)e[i]^=t[i];return e}},function(e,t,r){var i=r(276),n=r(274).Buffer;e.exports=function(e,t){return n.from(e.toRed(i.mont(t.modulus)).redPow(new i(t.publicExponent)).fromRed().toArray())}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";t.byteLength=function(e){var t=c(e),r=t[0],i=t[1];return 3*(r+i)/4-i},t.toByteArray=function(e){var t,r,i=c(e),o=i[0],a=i[1],s=new f(function(e,t,r){return 3*(t+r)/4-r}(0,o,a)),h=0,d=a>0?o-4:o;for(r=0;r<d;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],s[h++]=t>>16&255,s[h++]=t>>8&255,s[h++]=255&t;2===a&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,s[h++]=255&t);1===a&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,s[h++]=t>>8&255,s[h++]=255&t);return s},t.fromByteArray=function(e){for(var t,r=e.length,n=r%3,f=[],o=0,a=r-n;o<a;o+=16383)f.push(h(e,o,o+16383>a?a:o+16383));1===n?(t=e[r-1],f.push(i[t>>2]+i[t<<4&63]+"==")):2===n&&(t=(e[r-2]<<8)+e[r-1],f.push(i[t>>10]+i[t>>4&63]+i[t<<2&63]+"="));return f.join("")};for(var i=[],n=[],f="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,s=o.length;a<s;++a)i[a]=o[a],n[o.charCodeAt(a)]=a;function c(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function h(e,t,r){for(var n,f,o=[],a=t;a<r;a+=3)n=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),o.push(i[(f=n)>>18&63]+i[f>>12&63]+i[f>>6&63]+i[63&f]);return o.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},function(e,t){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
t.read=function(e,t,r,i,n){var f,o,a=8*n-i-1,s=(1<<a)-1,c=s>>1,h=-7,d=r?n-1:0,u=r?-1:1,l=e[t+d];for(d+=u,f=l&(1<<-h)-1,l>>=-h,h+=a;h>0;f=256*f+e[t+d],d+=u,h-=8);for(o=f&(1<<-h)-1,f>>=-h,h+=i;h>0;o=256*o+e[t+d],d+=u,h-=8);if(0===f)f=1-c;else{if(f===s)return o?NaN:1/0*(l?-1:1);o+=Math.pow(2,i),f-=c}return(l?-1:1)*o*Math.pow(2,f-i)},t.write=function(e,t,r,i,n,f){var o,a,s,c=8*f-n-1,h=(1<<c)-1,d=h>>1,u=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,l=i?0:f-1,p=i?1:-1,b=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,o=h):(o=Math.floor(Math.log(t)/Math.LN2),t*(s=Math.pow(2,-o))<1&&(o--,s*=2),(t+=o+d>=1?u/s:u*Math.pow(2,1-d))*s>=2&&(o++,s/=2),o+d>=h?(a=0,o=h):o+d>=1?(a=(t*s-1)*Math.pow(2,n),o+=d):(a=t*Math.pow(2,d-1)*Math.pow(2,n),o=0));n>=8;e[r+l]=255&a,l+=p,a/=256,n-=8);for(o=o<<n|a,c+=n;c>0;e[r+l]=255&o,l+=p,o/=256,c-=8);e[r+l-p]|=128*b}},function(e,t,r){"use strict";var i=r(274).Buffer,n=r(372);e.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return i.alloc(0);for(var t,r,n,f=i.allocUnsafe(e>>>0),o=this.head,a=0;o;)t=o.data,r=f,n=a,t.copy(r,n),a+=o.data.length,o=o.next;return f},e}(),n&&n.inspect&&n.inspect.custom&&(e.exports.prototype[n.inspect.custom]=function(){var e=n.inspect({length:this.length});return this.constructor.name+" "+e})},function(e,t,r){(function(t){function r(e){try{if(!t.localStorage)return!1}catch(e){return!1}var r=t.localStorage[e];return null!=r&&"true"===String(r).toLowerCase()}e.exports=function(e,t){if(r("noDeprecation"))return e;var i=!1;return function(){if(!i){if(r("throwDeprecation"))throw new Error(t);r("traceDeprecation")?console.trace(t):console.warn(t),i=!0}return e.apply(this,arguments)}}}).call(this,r(16))},function(e,t,r){"use strict";e.exports=f;var i=r(374),n=Object.create(r(294));function f(e){if(!(this instanceof f))return new f(e);i.call(this,e)}n.inherits=r(275),n.inherits(f,i),f.prototype._transform=function(e,t,r){r(null,e)}},function(e,t,r){var i=r(275),n=r(288),f=r(274).Buffer,o=[1518500249,1859775393,-1894007588,-899497514],a=new Array(80);function s(){this.init(),this._w=a,n.call(this,64,56)}function c(e){return e<<30|e>>>2}function h(e,t,r,i){return 0===e?t&r|~t&i:2===e?t&r|t&i|r&i:t^r^i}i(s,n),s.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this},s.prototype._update=function(e){for(var t,r=this._w,i=0|this._a,n=0|this._b,f=0|this._c,a=0|this._d,s=0|this._e,d=0;d<16;++d)r[d]=e.readInt32BE(4*d);for(;d<80;++d)r[d]=r[d-3]^r[d-8]^r[d-14]^r[d-16];for(var u=0;u<80;++u){var l=~~(u/20),p=0|((t=i)<<5|t>>>27)+h(l,n,f,a)+s+r[u]+o[l];s=a,a=f,f=c(n),n=i,i=p}this._a=i+this._a|0,this._b=n+this._b|0,this._c=f+this._c|0,this._d=a+this._d|0,this._e=s+this._e|0},s.prototype._hash=function(){var e=f.allocUnsafe(20);return e.writeInt32BE(0|this._a,0),e.writeInt32BE(0|this._b,4),e.writeInt32BE(0|this._c,8),e.writeInt32BE(0|this._d,12),e.writeInt32BE(0|this._e,16),e},e.exports=s},function(e,t,r){var i=r(275),n=r(288),f=r(274).Buffer,o=[1518500249,1859775393,-1894007588,-899497514],a=new Array(80);function s(){this.init(),this._w=a,n.call(this,64,56)}function c(e){return e<<5|e>>>27}function h(e){return e<<30|e>>>2}function d(e,t,r,i){return 0===e?t&r|~t&i:2===e?t&r|t&i|r&i:t^r^i}i(s,n),s.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this},s.prototype._update=function(e){for(var t,r=this._w,i=0|this._a,n=0|this._b,f=0|this._c,a=0|this._d,s=0|this._e,u=0;u<16;++u)r[u]=e.readInt32BE(4*u);for(;u<80;++u)r[u]=(t=r[u-3]^r[u-8]^r[u-14]^r[u-16])<<1|t>>>31;for(var l=0;l<80;++l){var p=~~(l/20),b=c(i)+d(p,n,f,a)+s+r[l]+o[p]|0;s=a,a=f,f=h(n),n=i,i=b}this._a=i+this._a|0,this._b=n+this._b|0,this._c=f+this._c|0,this._d=a+this._d|0,this._e=s+this._e|0},s.prototype._hash=function(){var e=f.allocUnsafe(20);return e.writeInt32BE(0|this._a,0),e.writeInt32BE(0|this._b,4),e.writeInt32BE(0|this._c,8),e.writeInt32BE(0|this._d,12),e.writeInt32BE(0|this._e,16),e},e.exports=s},function(e,t,r){var i=r(275),n=r(375),f=r(288),o=r(274).Buffer,a=new Array(64);function s(){this.init(),this._w=a,f.call(this,64,56)}i(s,n),s.prototype.init=function(){return this._a=3238371032,this._b=914150663,this._c=812702999,this._d=4144912697,this._e=4290775857,this._f=1750603025,this._g=1694076839,this._h=3204075428,this},s.prototype._hash=function(){var e=o.allocUnsafe(28);return e.writeInt32BE(this._a,0),e.writeInt32BE(this._b,4),e.writeInt32BE(this._c,8),e.writeInt32BE(this._d,12),e.writeInt32BE(this._e,16),e.writeInt32BE(this._f,20),e.writeInt32BE(this._g,24),e},e.exports=s},function(e,t,r){var i=r(275),n=r(376),f=r(288),o=r(274).Buffer,a=new Array(160);function s(){this.init(),this._w=a,f.call(this,128,112)}i(s,n),s.prototype.init=function(){return this._ah=3418070365,this._bh=1654270250,this._ch=2438529370,this._dh=355462360,this._eh=1731405415,this._fh=2394180231,this._gh=3675008525,this._hh=1203062813,this._al=3238371032,this._bl=914150663,this._cl=812702999,this._dl=4144912697,this._el=4290775857,this._fl=1750603025,this._gl=1694076839,this._hl=3204075428,this},s.prototype._hash=function(){var e=o.allocUnsafe(48);function t(t,r,i){e.writeInt32BE(t,i),e.writeInt32BE(r,i+4)}return t(this._ah,this._al,0),t(this._bh,this._bl,8),t(this._ch,this._cl,16),t(this._dh,this._dl,24),t(this._eh,this._el,32),t(this._fh,this._fl,40),e},e.exports=s},function(e,t,r){e.exports=n;var i=r(314).EventEmitter;function n(){i.call(this)}r(275)(n,i),n.Readable=r(293),n.Writable=r(457),n.Duplex=r(458),n.Transform=r(459),n.PassThrough=r(460),n.Stream=n,n.prototype.pipe=function(e,t){var r=this;function n(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function f(){r.readable&&r.resume&&r.resume()}r.on("data",n),e.on("drain",f),e._isStdio||t&&!1===t.end||(r.on("end",a),r.on("close",s));var o=!1;function a(){o||(o=!0,e.end())}function s(){o||(o=!0,"function"==typeof e.destroy&&e.destroy())}function c(e){if(h(),0===i.listenerCount(this,"error"))throw e}function h(){r.removeListener("data",n),e.removeListener("drain",f),r.removeListener("end",a),r.removeListener("close",s),r.removeListener("error",c),e.removeListener("error",c),r.removeListener("end",h),r.removeListener("close",h),e.removeListener("close",h)}return r.on("error",c),e.on("error",c),r.on("end",h),r.on("close",h),e.on("close",h),e.emit("pipe",r),e}},function(e,t,r){e.exports=r(315)},function(e,t,r){e.exports=r(283)},function(e,t,r){e.exports=r(293).Transform},function(e,t,r){e.exports=r(293).PassThrough},function(e,t,r){"use strict";var i=r(275),n=r(274).Buffer,f=r(281),o=n.alloc(128);function a(e,t){f.call(this,"digest"),"string"==typeof t&&(t=n.from(t)),this._alg=e,this._key=t,t.length>64?t=e(t):t.length<64&&(t=n.concat([t,o],64));for(var r=this._ipad=n.allocUnsafe(64),i=this._opad=n.allocUnsafe(64),a=0;a<64;a++)r[a]=54^t[a],i[a]=92^t[a];this._hash=[r]}i(a,f),a.prototype._update=function(e){this._hash.push(e)},a.prototype._final=function(){var e=this._alg(n.concat(this._hash));return this._alg(n.concat([this._opad,e]))},e.exports=a},function(e,t,r){"use strict";e.exports=r(379)},function(e,t,r){(function(t){var i,n,f=r(274).Buffer,o=r(381),a=r(382),s=r(383),c=r(384),h=t.crypto&&t.crypto.subtle,d={sha:"SHA-1","sha-1":"SHA-1",sha1:"SHA-1",sha256:"SHA-256","sha-256":"SHA-256",sha384:"SHA-384","sha-384":"SHA-384","sha-512":"SHA-512",sha512:"SHA-512"},u=[];function l(){return n||(n=t.process&&t.process.nextTick?t.process.nextTick:t.queueMicrotask?t.queueMicrotask:t.setImmediate?t.setImmediate:t.setTimeout)}function p(e,t,r,i,n){return h.importKey("raw",e,{name:"PBKDF2"},!1,["deriveBits"]).then((function(e){return h.deriveBits({name:"PBKDF2",salt:t,iterations:r,hash:{name:n}},e,i<<3)})).then((function(e){return f.from(e)}))}e.exports=function(e,r,n,b,y,g){"function"==typeof y&&(g=y,y=void 0);var m=d[(y=y||"sha1").toLowerCase()];if(m&&"function"==typeof t.Promise){if(o(n,b),e=c(e,a,"Password"),r=c(r,a,"Salt"),"function"!=typeof g)throw new Error("No callback provided to pbkdf2");!function(e,t){e.then((function(e){l()((function(){t(null,e)}))}),(function(e){l()((function(){t(e)}))}))}(function(e){if(t.process&&!t.process.browser)return Promise.resolve(!1);if(!h||!h.importKey||!h.deriveBits)return Promise.resolve(!1);if(void 0!==u[e])return u[e];var r=p(i=i||f.alloc(8),i,10,128,e).then((function(){return!0})).catch((function(){return!1}));return u[e]=r,r}(m).then((function(t){return t?p(e,r,n,b,m):s(e,r,n,b,y)})),g)}else l()((function(){var t;try{t=s(e,r,n,b,y)}catch(e){return g(e)}g(null,t)}))}}).call(this,r(16))},function(e,t,r){var i=r(465),n=r(320),f=r(321),o=r(478),a=r(300);function s(e,t,r){if(e=e.toLowerCase(),f[e])return n.createCipheriv(e,t,r);if(o[e])return new i({key:t,iv:r,mode:e});throw new TypeError("invalid suite type")}function c(e,t,r){if(e=e.toLowerCase(),f[e])return n.createDecipheriv(e,t,r);if(o[e])return new i({key:t,iv:r,mode:e,decrypt:!0});throw new TypeError("invalid suite type")}t.createCipher=t.Cipher=function(e,t){var r,i;if(e=e.toLowerCase(),f[e])r=f[e].key,i=f[e].iv;else{if(!o[e])throw new TypeError("invalid suite type");r=8*o[e].key,i=o[e].iv}var n=a(t,!1,r,i);return s(e,n.key,n.iv)},t.createCipheriv=t.Cipheriv=s,t.createDecipher=t.Decipher=function(e,t){var r,i;if(e=e.toLowerCase(),f[e])r=f[e].key,i=f[e].iv;else{if(!o[e])throw new TypeError("invalid suite type");r=8*o[e].key,i=o[e].iv}var n=a(t,!1,r,i);return c(e,n.key,n.iv)},t.createDecipheriv=t.Decipheriv=c,t.listCiphers=t.getCiphers=function(){return Object.keys(o).concat(n.getCiphers())}},function(e,t,r){var i=r(281),n=r(466),f=r(275),o=r(274).Buffer,a={"des-ede3-cbc":n.CBC.instantiate(n.EDE),"des-ede3":n.EDE,"des-ede-cbc":n.CBC.instantiate(n.EDE),"des-ede":n.EDE,"des-cbc":n.CBC.instantiate(n.DES),"des-ecb":n.DES};function s(e){i.call(this);var t,r=e.mode.toLowerCase(),n=a[r];t=e.decrypt?"decrypt":"encrypt";var f=e.key;o.isBuffer(f)||(f=o.from(f)),"des-ede"!==r&&"des-ede-cbc"!==r||(f=o.concat([f,f.slice(0,8)]));var s=e.iv;o.isBuffer(s)||(s=o.from(s)),this._des=n.create({key:f,iv:s,type:t})}a.des=a["des-cbc"],a.des3=a["des-ede3-cbc"],e.exports=s,f(s,i),s.prototype._update=function(e){return o.from(this._des.update(e))},s.prototype._final=function(){return o.from(this._des.final())}},function(e,t,r){"use strict";t.utils=r(385),t.Cipher=r(319),t.DES=r(386),t.CBC=r(467),t.EDE=r(468)},function(e,t,r){"use strict";var i=r(278),n=r(275),f={};function o(e){i.equal(e.length,8,"Invalid IV length"),this.iv=new Array(8);for(var t=0;t<this.iv.length;t++)this.iv[t]=e[t]}t.instantiate=function(e){function t(t){e.call(this,t),this._cbcInit()}n(t,e);for(var r=Object.keys(f),i=0;i<r.length;i++){var o=r[i];t.prototype[o]=f[o]}return t.create=function(e){return new t(e)},t},f._cbcInit=function(){var e=new o(this.options.iv);this._cbcState=e},f._update=function(e,t,r,i){var n=this._cbcState,f=this.constructor.super_.prototype,o=n.iv;if("encrypt"===this.type){for(var a=0;a<this.blockSize;a++)o[a]^=e[t+a];f._update.call(this,o,0,r,i);for(a=0;a<this.blockSize;a++)o[a]=r[i+a]}else{f._update.call(this,e,t,r,i);for(a=0;a<this.blockSize;a++)r[i+a]^=o[a];for(a=0;a<this.blockSize;a++)o[a]=e[t+a]}}},function(e,t,r){"use strict";var i=r(278),n=r(275),f=r(319),o=r(386);function a(e,t){i.equal(t.length,24,"Invalid key length");var r=t.slice(0,8),n=t.slice(8,16),f=t.slice(16,24);this.ciphers="encrypt"===e?[o.create({type:"encrypt",key:r}),o.create({type:"decrypt",key:n}),o.create({type:"encrypt",key:f})]:[o.create({type:"decrypt",key:f}),o.create({type:"encrypt",key:n}),o.create({type:"decrypt",key:r})]}function s(e){f.call(this,e);var t=new a(this.type,this.options.key);this._edeState=t}n(s,f),e.exports=s,s.create=function(e){return new s(e)},s.prototype._update=function(e,t,r,i){var n=this._edeState;n.ciphers[0]._update(e,t,r,i),n.ciphers[1]._update(r,i,r,i),n.ciphers[2]._update(r,i,r,i)},s.prototype._pad=o.prototype._pad,s.prototype._unpad=o.prototype._unpad},function(e,t,r){var i=r(321),n=r(390),f=r(274).Buffer,o=r(391),a=r(281),s=r(299),c=r(300);function h(e,t,r){a.call(this),this._cache=new u,this._cipher=new s.AES(t),this._prev=f.from(r),this._mode=e,this._autopadding=!0}r(275)(h,a),h.prototype._update=function(e){var t,r;this._cache.add(e);for(var i=[];t=this._cache.get();)r=this._mode.encrypt(this,t),i.push(r);return f.concat(i)};var d=f.alloc(16,16);function u(){this.cache=f.allocUnsafe(0)}function l(e,t,r){var a=i[e.toLowerCase()];if(!a)throw new TypeError("invalid suite type");if("string"==typeof t&&(t=f.from(t)),t.length!==a.key/8)throw new TypeError("invalid key length "+t.length);if("string"==typeof r&&(r=f.from(r)),"GCM"!==a.mode&&r.length!==a.iv)throw new TypeError("invalid iv length "+r.length);return"stream"===a.type?new o(a.module,t,r):"auth"===a.type?new n(a.module,t,r):new h(a.module,t,r)}h.prototype._final=function(){var e=this._cache.flush();if(this._autopadding)return e=this._mode.encrypt(this,e),this._cipher.scrub(),e;if(!e.equals(d))throw this._cipher.scrub(),new Error("data not multiple of block length")},h.prototype.setAutoPadding=function(e){return this._autopadding=!!e,this},u.prototype.add=function(e){this.cache=f.concat([this.cache,e])},u.prototype.get=function(){if(this.cache.length>15){var e=this.cache.slice(0,16);return this.cache=this.cache.slice(16),e}return null},u.prototype.flush=function(){for(var e=16-this.cache.length,t=f.allocUnsafe(e),r=-1;++r<e;)t.writeUInt8(e,r);return f.concat([this.cache,t])},t.createCipheriv=l,t.createCipher=function(e,t){var r=i[e.toLowerCase()];if(!r)throw new TypeError("invalid suite type");var n=c(t,!1,r.key,r.iv);return l(e,n.key,n.iv)}},function(e,t){t.encrypt=function(e,t){return e._cipher.encryptBlock(t)},t.decrypt=function(e,t){return e._cipher.decryptBlock(t)}},function(e,t,r){var i=r(295);t.encrypt=function(e,t){var r=i(t,e._prev);return e._prev=e._cipher.encryptBlock(r),e._prev},t.decrypt=function(e,t){var r=e._prev;e._prev=t;var n=e._cipher.decryptBlock(t);return i(n,r)}},function(e,t,r){var i=r(274).Buffer,n=r(295);function f(e,t,r){var f=t.length,o=n(t,e._cache);return e._cache=e._cache.slice(f),e._prev=i.concat([e._prev,r?t:o]),o}t.encrypt=function(e,t,r){for(var n,o=i.allocUnsafe(0);t.length;){if(0===e._cache.length&&(e._cache=e._cipher.encryptBlock(e._prev),e._prev=i.allocUnsafe(0)),!(e._cache.length<=t.length)){o=i.concat([o,f(e,t,r)]);break}n=e._cache.length,o=i.concat([o,f(e,t.slice(0,n),r)]),t=t.slice(n)}return o}},function(e,t,r){var i=r(274).Buffer;function n(e,t,r){var n=e._cipher.encryptBlock(e._prev)[0]^t;return e._prev=i.concat([e._prev.slice(1),i.from([r?t:n])]),n}t.encrypt=function(e,t,r){for(var f=t.length,o=i.allocUnsafe(f),a=-1;++a<f;)o[a]=n(e,t[a],r);return o}},function(e,t,r){var i=r(274).Buffer;function n(e,t,r){for(var i,n,o=-1,a=0;++o<8;)i=t&1<<7-o?128:0,a+=(128&(n=e._cipher.encryptBlock(e._prev)[0]^i))>>o%8,e._prev=f(e._prev,r?i:n);return a}function f(e,t){var r=e.length,n=-1,f=i.allocUnsafe(e.length);for(e=i.concat([e,i.from([t])]);++n<r;)f[n]=e[n]<<1|e[n+1]>>7;return f}t.encrypt=function(e,t,r){for(var f=t.length,o=i.allocUnsafe(f),a=-1;++a<f;)o[a]=n(e,t[a],r);return o}},function(e,t,r){(function(e){var i=r(295);function n(e){return e._prev=e._cipher.encryptBlock(e._prev),e._prev}t.encrypt=function(t,r){for(;t._cache.length<r.length;)t._cache=e.concat([t._cache,n(t)]);var f=t._cache.slice(0,r.length);return t._cache=t._cache.slice(r.length),i(r,f)}}).call(this,r(277).Buffer)},function(e,t,r){var i=r(274).Buffer,n=i.alloc(16,0);function f(e){var t=i.allocUnsafe(16);return t.writeUInt32BE(e[0]>>>0,0),t.writeUInt32BE(e[1]>>>0,4),t.writeUInt32BE(e[2]>>>0,8),t.writeUInt32BE(e[3]>>>0,12),t}function o(e){this.h=e,this.state=i.alloc(16,0),this.cache=i.allocUnsafe(0)}o.prototype.ghash=function(e){for(var t=-1;++t<e.length;)this.state[t]^=e[t];this._multiply()},o.prototype._multiply=function(){for(var e,t,r,i=[(e=this.h).readUInt32BE(0),e.readUInt32BE(4),e.readUInt32BE(8),e.readUInt32BE(12)],n=[0,0,0,0],o=-1;++o<128;){for(0!=(this.state[~~(o/8)]&1<<7-o%8)&&(n[0]^=i[0],n[1]^=i[1],n[2]^=i[2],n[3]^=i[3]),r=0!=(1&i[3]),t=3;t>0;t--)i[t]=i[t]>>>1|(1&i[t-1])<<31;i[0]=i[0]>>>1,r&&(i[0]=i[0]^225<<24)}this.state=f(n)},o.prototype.update=function(e){var t;for(this.cache=i.concat([this.cache,e]);this.cache.length>=16;)t=this.cache.slice(0,16),this.cache=this.cache.slice(16),this.ghash(t)},o.prototype.final=function(e,t){return this.cache.length&&this.ghash(i.concat([this.cache,n],16)),this.ghash(f([0,e,0,t])),this.state},e.exports=o},function(e,t,r){var i=r(390),n=r(274).Buffer,f=r(321),o=r(391),a=r(281),s=r(299),c=r(300);function h(e,t,r){a.call(this),this._cache=new d,this._last=void 0,this._cipher=new s.AES(t),this._prev=n.from(r),this._mode=e,this._autopadding=!0}function d(){this.cache=n.allocUnsafe(0)}function u(e,t,r){var a=f[e.toLowerCase()];if(!a)throw new TypeError("invalid suite type");if("string"==typeof r&&(r=n.from(r)),"GCM"!==a.mode&&r.length!==a.iv)throw new TypeError("invalid iv length "+r.length);if("string"==typeof t&&(t=n.from(t)),t.length!==a.key/8)throw new TypeError("invalid key length "+t.length);return"stream"===a.type?new o(a.module,t,r,!0):"auth"===a.type?new i(a.module,t,r,!0):new h(a.module,t,r)}r(275)(h,a),h.prototype._update=function(e){var t,r;this._cache.add(e);for(var i=[];t=this._cache.get(this._autopadding);)r=this._mode.decrypt(this,t),i.push(r);return n.concat(i)},h.prototype._final=function(){var e=this._cache.flush();if(this._autopadding)return function(e){var t=e[15];if(t<1||t>16)throw new Error("unable to decrypt data");var r=-1;for(;++r<t;)if(e[r+(16-t)]!==t)throw new Error("unable to decrypt data");if(16===t)return;return e.slice(0,16-t)}(this._mode.decrypt(this,e));if(e)throw new Error("data not multiple of block length")},h.prototype.setAutoPadding=function(e){return this._autopadding=!!e,this},d.prototype.add=function(e){this.cache=n.concat([this.cache,e])},d.prototype.get=function(e){var t;if(e){if(this.cache.length>16)return t=this.cache.slice(0,16),this.cache=this.cache.slice(16),t}else if(this.cache.length>=16)return t=this.cache.slice(0,16),this.cache=this.cache.slice(16),t;return null},d.prototype.flush=function(){if(this.cache.length)return this.cache},t.createDecipher=function(e,t){var r=f[e.toLowerCase()];if(!r)throw new TypeError("invalid suite type");var i=c(t,!1,r.key,r.iv);return u(e,i.key,i.iv)},t.createDecipheriv=u},function(e,t){t["des-ecb"]={key:8,iv:0},t["des-cbc"]=t.des={key:8,iv:8},t["des-ede3-cbc"]=t.des3={key:24,iv:8},t["des-ede3"]={key:24,iv:0},t["des-ede-cbc"]={key:16,iv:8},t["des-ede"]={key:16,iv:0}},function(e,t,r){(function(e){var i=r(392),n=r(480),f=r(481);var o={binary:!0,hex:!0,base64:!0};t.DiffieHellmanGroup=t.createDiffieHellmanGroup=t.getDiffieHellman=function(t){var r=new e(n[t].prime,"hex"),i=new e(n[t].gen,"hex");return new f(r,i)},t.createDiffieHellman=t.DiffieHellman=function t(r,n,a,s){return e.isBuffer(n)||void 0===o[n]?t(r,"binary",n,a):(n=n||"binary",s=s||"binary",a=a||new e([2]),e.isBuffer(a)||(a=new e(a,s)),"number"==typeof r?new f(i(r,a),a,!0):(e.isBuffer(r)||(r=new e(r,n)),new f(r,a,!0)))}}).call(this,r(277).Buffer)},function(e){e.exports=JSON.parse('{"modp1":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a63a3620ffffffffffffffff"},"modp2":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece65381ffffffffffffffff"},"modp5":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca237327ffffffffffffffff"},"modp14":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aacaa68ffffffffffffffff"},"modp15":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aaac42dad33170d04507a33a85521abdf1cba64ecfb850458dbef0a8aea71575d060c7db3970f85a6e1e4c7abf5ae8cdb0933d71e8c94e04a25619dcee3d2261ad2ee6bf12ffa06d98a0864d87602733ec86a64521f2b18177b200cbbe117577a615d6c770988c0bad946e208e24fa074e5ab3143db5bfce0fd108e4b82d120a93ad2caffffffffffffffff"},"modp16":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aaac42dad33170d04507a33a85521abdf1cba64ecfb850458dbef0a8aea71575d060c7db3970f85a6e1e4c7abf5ae8cdb0933d71e8c94e04a25619dcee3d2261ad2ee6bf12ffa06d98a0864d87602733ec86a64521f2b18177b200cbbe117577a615d6c770988c0bad946e208e24fa074e5ab3143db5bfce0fd108e4b82d120a92108011a723c12a787e6d788719a10bdba5b2699c327186af4e23c1a946834b6150bda2583e9ca2ad44ce8dbbbc2db04de8ef92e8efc141fbecaa6287c59474e6bc05d99b2964fa090c3a2233ba186515be7ed1f612970cee2d7afb81bdd762170481cd0069127d5b05aa993b4ea988d8fddc186ffb7dc90a6c08f4df435c934063199ffffffffffffffff"},"modp17":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aaac42dad33170d04507a33a85521abdf1cba64ecfb850458dbef0a8aea71575d060c7db3970f85a6e1e4c7abf5ae8cdb0933d71e8c94e04a25619dcee3d2261ad2ee6bf12ffa06d98a0864d87602733ec86a64521f2b18177b200cbbe117577a615d6c770988c0bad946e208e24fa074e5ab3143db5bfce0fd108e4b82d120a92108011a723c12a787e6d788719a10bdba5b2699c327186af4e23c1a946834b6150bda2583e9ca2ad44ce8dbbbc2db04de8ef92e8efc141fbecaa6287c59474e6bc05d99b2964fa090c3a2233ba186515be7ed1f612970cee2d7afb81bdd762170481cd0069127d5b05aa993b4ea988d8fddc186ffb7dc90a6c08f4df435c93402849236c3fab4d27c7026c1d4dcb2602646dec9751e763dba37bdf8ff9406ad9e530ee5db382f413001aeb06a53ed9027d831179727b0865a8918da3edbebcf9b14ed44ce6cbaced4bb1bdb7f1447e6cc254b332051512bd7af426fb8f401378cd2bf5983ca01c64b92ecf032ea15d1721d03f482d7ce6e74fef6d55e702f46980c82b5a84031900b1c9e59e7c97fbec7e8f323a97a7e36cc88be0f1d45b7ff585ac54bd407b22b4154aacc8f6d7ebf48e1d814cc5ed20f8037e0a79715eef29be32806a1d58bb7c5da76f550aa3d8a1fbff0eb19ccb1a313d55cda56c9ec2ef29632387fe8d76e3c0468043e8f663f4860ee12bf2d5b0b7474d6e694f91e6dcc4024ffffffffffffffff"},"modp18":{"gen":"02","prime":"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"}}')},function(e,t,r){(function(t){var i=r(276),n=new(r(394)),f=new i(24),o=new i(11),a=new i(10),s=new i(3),c=new i(7),h=r(392),d=r(287);function u(e,r){return r=r||"utf8",t.isBuffer(e)||(e=new t(e,r)),this._pub=new i(e),this}function l(e,r){return r=r||"utf8",t.isBuffer(e)||(e=new t(e,r)),this._priv=new i(e),this}e.exports=b;var p={};function b(e,t,r){this.setGenerator(t),this.__prime=new i(e),this._prime=i.mont(this.__prime),this._primeLen=e.length,this._pub=void 0,this._priv=void 0,this._primeCode=void 0,r?(this.setPublicKey=u,this.setPrivateKey=l):this._primeCode=8}function y(e,r){var i=new t(e.toArray());return r?i.toString(r):i}Object.defineProperty(b.prototype,"verifyError",{enumerable:!0,get:function(){return"number"!=typeof this._primeCode&&(this._primeCode=function(e,t){var r=t.toString("hex"),i=[r,e.toString(16)].join("_");if(i in p)return p[i];var d,u=0;if(e.isEven()||!h.simpleSieve||!h.fermatTest(e)||!n.test(e))return u+=1,u+="02"===r||"05"===r?8:4,p[i]=u,u;switch(n.test(e.shrn(1))||(u+=2),r){case"02":e.mod(f).cmp(o)&&(u+=8);break;case"05":(d=e.mod(a)).cmp(s)&&d.cmp(c)&&(u+=8);break;default:u+=4}return p[i]=u,u}(this.__prime,this.__gen)),this._primeCode}}),b.prototype.generateKeys=function(){return this._priv||(this._priv=new i(d(this._primeLen))),this._pub=this._gen.toRed(this._prime).redPow(this._priv).fromRed(),this.getPublicKey()},b.prototype.computeSecret=function(e){var r=(e=(e=new i(e)).toRed(this._prime)).redPow(this._priv).fromRed(),n=new t(r.toArray()),f=this.getPrime();if(n.length<f.length){var o=new t(f.length-n.length);o.fill(0),n=t.concat([o,n])}return n},b.prototype.getPublicKey=function(e){return y(this._pub,e)},b.prototype.getPrivateKey=function(e){return y(this._priv,e)},b.prototype.getPrime=function(e){return y(this.__prime,e)},b.prototype.getGenerator=function(e){return y(this._gen,e)},b.prototype.setGenerator=function(e,r){return r=r||"utf8",t.isBuffer(e)||(e=new t(e,r)),this.__gen=e,this._gen=new i(e),this}}).call(this,r(277).Buffer)},function(e,t,r){"use strict";var i=r(274).Buffer,n=r(292),f=r(293),o=r(275),a=r(483),s=r(511),c=r(379);function h(e){f.Writable.call(this);var t=c[e];if(!t)throw new Error("Unknown message digest");this._hashType=t.hash,this._hash=n(t.hash),this._tag=t.id,this._signType=t.sign}function d(e){f.Writable.call(this);var t=c[e];if(!t)throw new Error("Unknown message digest");this._hash=n(t.hash),this._tag=t.id,this._signType=t.sign}function u(e){return new h(e)}function l(e){return new d(e)}Object.keys(c).forEach((function(e){c[e].id=i.from(c[e].id,"hex"),c[e.toLowerCase()]=c[e]})),o(h,f.Writable),h.prototype._write=function(e,t,r){this._hash.update(e),r()},h.prototype.update=function(e,t){return this._hash.update("string"==typeof e?i.from(e,t):e),this},h.prototype.sign=function(e,t){this.end();var r=this._hash.digest(),i=a(r,e,this._hashType,this._signType,this._tag);return t?i.toString(t):i},o(d,f.Writable),d.prototype._write=function(e,t,r){this._hash.update(e),r()},d.prototype.update=function(e,t){return this._hash.update("string"==typeof e?i.from(e,t):e),this},d.prototype.verify=function(e,t,r){var n="string"==typeof t?i.from(t,r):t;this.end();var f=this._hash.digest();return s(n,f,e,this._signType,this._tag)},e.exports={Sign:u,Verify:l,createSign:u,createVerify:l}},function(e,t,r){"use strict";var i=r(274).Buffer,n=r(377),f=r(323),o=r(324).ec,a=r(276),s=r(302),c=r(406);function h(e,t,r,f){if((e=i.from(e.toArray())).length<t.byteLength()){var o=i.alloc(t.byteLength()-e.length);e=i.concat([o,e])}var a=r.length,s=function(e,t){e=(e=d(e,t)).mod(t);var r=i.from(e.toArray());if(r.length<t.byteLength()){var n=i.alloc(t.byteLength()-r.length);r=i.concat([n,r])}return r}(r,t),c=i.alloc(a);c.fill(1);var h=i.alloc(a);return h=n(f,h).update(c).update(i.from([0])).update(e).update(s).digest(),c=n(f,h).update(c).digest(),{k:h=n(f,h).update(c).update(i.from([1])).update(e).update(s).digest(),v:c=n(f,h).update(c).digest()}}function d(e,t){var r=new a(e),i=(e.length<<3)-t.bitLength();return i>0&&r.ishrn(i),r}function u(e,t,r){var f,o;do{for(f=i.alloc(0);8*f.length<e.bitLength();)t.v=n(r,t.k).update(t.v).digest(),f=i.concat([f,t.v]);o=d(f,e),t.k=n(r,t.k).update(t.v).update(i.from([0])).digest(),t.v=n(r,t.k).update(t.v).digest()}while(-1!==o.cmp(e));return o}function l(e,t,r,i){return e.toRed(a.mont(r)).redPow(t).fromRed().mod(i)}e.exports=function(e,t,r,n,p){var b=s(t);if(b.curve){if("ecdsa"!==n&&"ecdsa/rsa"!==n)throw new Error("wrong private key type");return function(e,t){var r=c[t.curve.join(".")];if(!r)throw new Error("unknown curve "+t.curve.join("."));var n=new o(r).keyFromPrivate(t.privateKey).sign(e);return i.from(n.toDER())}(e,b)}if("dsa"===b.type){if("dsa"!==n)throw new Error("wrong private key type");return function(e,t,r){var n,f=t.params.priv_key,o=t.params.p,s=t.params.q,c=t.params.g,p=new a(0),b=d(e,s).mod(s),y=!1,g=h(f,s,e,r);for(;!1===y;)n=u(s,g,r),p=l(c,n,o,s),0===(y=n.invm(s).imul(b.add(f.mul(p))).mod(s)).cmpn(0)&&(y=!1,p=new a(0));return function(e,t){e=e.toArray(),t=t.toArray(),128&e[0]&&(e=[0].concat(e));128&t[0]&&(t=[0].concat(t));var r=[48,e.length+t.length+4,2,e.length];return r=r.concat(e,[2,t.length],t),i.from(r)}(p,y)}(e,b,r)}if("rsa"!==n&&"ecdsa/rsa"!==n)throw new Error("wrong private key type");if(void 0!==t.padding&&1!==t.padding)throw new Error("illegal or unsupported padding mode");e=i.concat([p,e]);for(var y=b.modulus.byteLength(),g=[0,1];e.length+g.length+1<y;)g.push(255);g.push(0);for(var m=-1;++m<e.length;)g.push(e[m]);return f(g,b)},e.exports.getKey=h,e.exports.makeKey=u},function(e){e.exports=JSON.parse('{"name":"elliptic","version":"6.5.4","description":"EC cryptography","main":"lib/elliptic.js","files":["lib"],"scripts":{"lint":"eslint lib test","lint:fix":"npm run lint -- --fix","unit":"istanbul test _mocha --reporter=spec test/index.js","test":"npm run lint && npm run unit","version":"grunt dist && git add dist/"},"repository":{"type":"git","url":"**************:indutny/elliptic"},"keywords":["EC","Elliptic","curve","Cryptography"],"author":"Fedor Indutny <<EMAIL>>","license":"MIT","bugs":{"url":"https://github.com/indutny/elliptic/issues"},"homepage":"https://github.com/indutny/elliptic","devDependencies":{"brfs":"^2.0.2","coveralls":"^3.1.0","eslint":"^7.6.0","grunt":"^1.2.1","grunt-browserify":"^5.3.0","grunt-cli":"^1.3.2","grunt-contrib-connect":"^3.0.0","grunt-contrib-copy":"^1.0.0","grunt-contrib-uglify":"^5.0.0","grunt-mocha-istanbul":"^5.0.2","grunt-saucelabs":"^9.0.1","istanbul":"^0.4.5","mocha":"^8.0.1"},"dependencies":{"bn.js":"^4.11.9","brorand":"^1.1.0","hash.js":"^1.0.0","hmac-drbg":"^1.0.1","inherits":"^2.0.4","minimalistic-assert":"^1.0.1","minimalistic-crypto-utils":"^1.0.1"}}')},function(e,t,r){"use strict";var i=r(279),n=r(276),f=r(275),o=r(301),a=i.assert;function s(e){o.call(this,"short",e),this.a=new n(e.a,16).toRed(this.red),this.b=new n(e.b,16).toRed(this.red),this.tinv=this.two.redInvm(),this.zeroA=0===this.a.fromRed().cmpn(0),this.threeA=0===this.a.fromRed().sub(this.p).cmpn(-3),this.endo=this._getEndomorphism(e),this._endoWnafT1=new Array(4),this._endoWnafT2=new Array(4)}function c(e,t,r,i){o.BasePoint.call(this,e,"affine"),null===t&&null===r?(this.x=null,this.y=null,this.inf=!0):(this.x=new n(t,16),this.y=new n(r,16),i&&(this.x.forceRed(this.curve.red),this.y.forceRed(this.curve.red)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.inf=!1)}function h(e,t,r,i){o.BasePoint.call(this,e,"jacobian"),null===t&&null===r&&null===i?(this.x=this.curve.one,this.y=this.curve.one,this.z=new n(0)):(this.x=new n(t,16),this.y=new n(r,16),this.z=new n(i,16)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)),this.zOne=this.z===this.curve.one}f(s,o),e.exports=s,s.prototype._getEndomorphism=function(e){if(this.zeroA&&this.g&&this.n&&1===this.p.modn(3)){var t,r;if(e.beta)t=new n(e.beta,16).toRed(this.red);else{var i=this._getEndoRoots(this.p);t=(t=i[0].cmp(i[1])<0?i[0]:i[1]).toRed(this.red)}if(e.lambda)r=new n(e.lambda,16);else{var f=this._getEndoRoots(this.n);0===this.g.mul(f[0]).x.cmp(this.g.x.redMul(t))?r=f[0]:(r=f[1],a(0===this.g.mul(r).x.cmp(this.g.x.redMul(t))))}return{beta:t,lambda:r,basis:e.basis?e.basis.map((function(e){return{a:new n(e.a,16),b:new n(e.b,16)}})):this._getEndoBasis(r)}}},s.prototype._getEndoRoots=function(e){var t=e===this.p?this.red:n.mont(e),r=new n(2).toRed(t).redInvm(),i=r.redNeg(),f=new n(3).toRed(t).redNeg().redSqrt().redMul(r);return[i.redAdd(f).fromRed(),i.redSub(f).fromRed()]},s.prototype._getEndoBasis=function(e){for(var t,r,i,f,o,a,s,c,h,d=this.n.ushrn(Math.floor(this.n.bitLength()/2)),u=e,l=this.n.clone(),p=new n(1),b=new n(0),y=new n(0),g=new n(1),m=0;0!==u.cmpn(0);){var v=l.div(u);c=l.sub(v.mul(u)),h=y.sub(v.mul(p));var _=g.sub(v.mul(b));if(!i&&c.cmp(d)<0)t=s.neg(),r=p,i=c.neg(),f=h;else if(i&&2==++m)break;s=c,l=u,u=c,y=p,p=h,g=b,b=_}o=c.neg(),a=h;var w=i.sqr().add(f.sqr());return o.sqr().add(a.sqr()).cmp(w)>=0&&(o=t,a=r),i.negative&&(i=i.neg(),f=f.neg()),o.negative&&(o=o.neg(),a=a.neg()),[{a:i,b:f},{a:o,b:a}]},s.prototype._endoSplit=function(e){var t=this.endo.basis,r=t[0],i=t[1],n=i.b.mul(e).divRound(this.n),f=r.b.neg().mul(e).divRound(this.n),o=n.mul(r.a),a=f.mul(i.a),s=n.mul(r.b),c=f.mul(i.b);return{k1:e.sub(o).sub(a),k2:s.add(c).neg()}},s.prototype.pointFromX=function(e,t){(e=new n(e,16)).red||(e=e.toRed(this.red));var r=e.redSqr().redMul(e).redIAdd(e.redMul(this.a)).redIAdd(this.b),i=r.redSqrt();if(0!==i.redSqr().redSub(r).cmp(this.zero))throw new Error("invalid point");var f=i.fromRed().isOdd();return(t&&!f||!t&&f)&&(i=i.redNeg()),this.point(e,i)},s.prototype.validate=function(e){if(e.inf)return!0;var t=e.x,r=e.y,i=this.a.redMul(t),n=t.redSqr().redMul(t).redIAdd(i).redIAdd(this.b);return 0===r.redSqr().redISub(n).cmpn(0)},s.prototype._endoWnafMulAdd=function(e,t,r){for(var i=this._endoWnafT1,n=this._endoWnafT2,f=0;f<e.length;f++){var o=this._endoSplit(t[f]),a=e[f],s=a._getBeta();o.k1.negative&&(o.k1.ineg(),a=a.neg(!0)),o.k2.negative&&(o.k2.ineg(),s=s.neg(!0)),i[2*f]=a,i[2*f+1]=s,n[2*f]=o.k1,n[2*f+1]=o.k2}for(var c=this._wnafMulAdd(1,i,n,2*f,r),h=0;h<2*f;h++)i[h]=null,n[h]=null;return c},f(c,o.BasePoint),s.prototype.point=function(e,t,r){return new c(this,e,t,r)},s.prototype.pointFromJSON=function(e,t){return c.fromJSON(this,e,t)},c.prototype._getBeta=function(){if(this.curve.endo){var e=this.precomputed;if(e&&e.beta)return e.beta;var t=this.curve.point(this.x.redMul(this.curve.endo.beta),this.y);if(e){var r=this.curve,i=function(e){return r.point(e.x.redMul(r.endo.beta),e.y)};e.beta=t,t.precomputed={beta:null,naf:e.naf&&{wnd:e.naf.wnd,points:e.naf.points.map(i)},doubles:e.doubles&&{step:e.doubles.step,points:e.doubles.points.map(i)}}}return t}},c.prototype.toJSON=function(){return this.precomputed?[this.x,this.y,this.precomputed&&{doubles:this.precomputed.doubles&&{step:this.precomputed.doubles.step,points:this.precomputed.doubles.points.slice(1)},naf:this.precomputed.naf&&{wnd:this.precomputed.naf.wnd,points:this.precomputed.naf.points.slice(1)}}]:[this.x,this.y]},c.fromJSON=function(e,t,r){"string"==typeof t&&(t=JSON.parse(t));var i=e.point(t[0],t[1],r);if(!t[2])return i;function n(t){return e.point(t[0],t[1],r)}var f=t[2];return i.precomputed={beta:null,doubles:f.doubles&&{step:f.doubles.step,points:[i].concat(f.doubles.points.map(n))},naf:f.naf&&{wnd:f.naf.wnd,points:[i].concat(f.naf.points.map(n))}},i},c.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" y: "+this.y.fromRed().toString(16,2)+">"},c.prototype.isInfinity=function(){return this.inf},c.prototype.add=function(e){if(this.inf)return e;if(e.inf)return this;if(this.eq(e))return this.dbl();if(this.neg().eq(e))return this.curve.point(null,null);if(0===this.x.cmp(e.x))return this.curve.point(null,null);var t=this.y.redSub(e.y);0!==t.cmpn(0)&&(t=t.redMul(this.x.redSub(e.x).redInvm()));var r=t.redSqr().redISub(this.x).redISub(e.x),i=t.redMul(this.x.redSub(r)).redISub(this.y);return this.curve.point(r,i)},c.prototype.dbl=function(){if(this.inf)return this;var e=this.y.redAdd(this.y);if(0===e.cmpn(0))return this.curve.point(null,null);var t=this.curve.a,r=this.x.redSqr(),i=e.redInvm(),n=r.redAdd(r).redIAdd(r).redIAdd(t).redMul(i),f=n.redSqr().redISub(this.x.redAdd(this.x)),o=n.redMul(this.x.redSub(f)).redISub(this.y);return this.curve.point(f,o)},c.prototype.getX=function(){return this.x.fromRed()},c.prototype.getY=function(){return this.y.fromRed()},c.prototype.mul=function(e){return e=new n(e,16),this.isInfinity()?this:this._hasDoubles(e)?this.curve._fixedNafMul(this,e):this.curve.endo?this.curve._endoWnafMulAdd([this],[e]):this.curve._wnafMul(this,e)},c.prototype.mulAdd=function(e,t,r){var i=[this,t],n=[e,r];return this.curve.endo?this.curve._endoWnafMulAdd(i,n):this.curve._wnafMulAdd(1,i,n,2)},c.prototype.jmulAdd=function(e,t,r){var i=[this,t],n=[e,r];return this.curve.endo?this.curve._endoWnafMulAdd(i,n,!0):this.curve._wnafMulAdd(1,i,n,2,!0)},c.prototype.eq=function(e){return this===e||this.inf===e.inf&&(this.inf||0===this.x.cmp(e.x)&&0===this.y.cmp(e.y))},c.prototype.neg=function(e){if(this.inf)return this;var t=this.curve.point(this.x,this.y.redNeg());if(e&&this.precomputed){var r=this.precomputed,i=function(e){return e.neg()};t.precomputed={naf:r.naf&&{wnd:r.naf.wnd,points:r.naf.points.map(i)},doubles:r.doubles&&{step:r.doubles.step,points:r.doubles.points.map(i)}}}return t},c.prototype.toJ=function(){return this.inf?this.curve.jpoint(null,null,null):this.curve.jpoint(this.x,this.y,this.curve.one)},f(h,o.BasePoint),s.prototype.jpoint=function(e,t,r){return new h(this,e,t,r)},h.prototype.toP=function(){if(this.isInfinity())return this.curve.point(null,null);var e=this.z.redInvm(),t=e.redSqr(),r=this.x.redMul(t),i=this.y.redMul(t).redMul(e);return this.curve.point(r,i)},h.prototype.neg=function(){return this.curve.jpoint(this.x,this.y.redNeg(),this.z)},h.prototype.add=function(e){if(this.isInfinity())return e;if(e.isInfinity())return this;var t=e.z.redSqr(),r=this.z.redSqr(),i=this.x.redMul(t),n=e.x.redMul(r),f=this.y.redMul(t.redMul(e.z)),o=e.y.redMul(r.redMul(this.z)),a=i.redSub(n),s=f.redSub(o);if(0===a.cmpn(0))return 0!==s.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var c=a.redSqr(),h=c.redMul(a),d=i.redMul(c),u=s.redSqr().redIAdd(h).redISub(d).redISub(d),l=s.redMul(d.redISub(u)).redISub(f.redMul(h)),p=this.z.redMul(e.z).redMul(a);return this.curve.jpoint(u,l,p)},h.prototype.mixedAdd=function(e){if(this.isInfinity())return e.toJ();if(e.isInfinity())return this;var t=this.z.redSqr(),r=this.x,i=e.x.redMul(t),n=this.y,f=e.y.redMul(t).redMul(this.z),o=r.redSub(i),a=n.redSub(f);if(0===o.cmpn(0))return 0!==a.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var s=o.redSqr(),c=s.redMul(o),h=r.redMul(s),d=a.redSqr().redIAdd(c).redISub(h).redISub(h),u=a.redMul(h.redISub(d)).redISub(n.redMul(c)),l=this.z.redMul(o);return this.curve.jpoint(d,u,l)},h.prototype.dblp=function(e){if(0===e)return this;if(this.isInfinity())return this;if(!e)return this.dbl();var t;if(this.curve.zeroA||this.curve.threeA){var r=this;for(t=0;t<e;t++)r=r.dbl();return r}var i=this.curve.a,n=this.curve.tinv,f=this.x,o=this.y,a=this.z,s=a.redSqr().redSqr(),c=o.redAdd(o);for(t=0;t<e;t++){var h=f.redSqr(),d=c.redSqr(),u=d.redSqr(),l=h.redAdd(h).redIAdd(h).redIAdd(i.redMul(s)),p=f.redMul(d),b=l.redSqr().redISub(p.redAdd(p)),y=p.redISub(b),g=l.redMul(y);g=g.redIAdd(g).redISub(u);var m=c.redMul(a);t+1<e&&(s=s.redMul(u)),f=b,a=m,c=g}return this.curve.jpoint(f,c.redMul(n),a)},h.prototype.dbl=function(){return this.isInfinity()?this:this.curve.zeroA?this._zeroDbl():this.curve.threeA?this._threeDbl():this._dbl()},h.prototype._zeroDbl=function(){var e,t,r;if(this.zOne){var i=this.x.redSqr(),n=this.y.redSqr(),f=n.redSqr(),o=this.x.redAdd(n).redSqr().redISub(i).redISub(f);o=o.redIAdd(o);var a=i.redAdd(i).redIAdd(i),s=a.redSqr().redISub(o).redISub(o),c=f.redIAdd(f);c=(c=c.redIAdd(c)).redIAdd(c),e=s,t=a.redMul(o.redISub(s)).redISub(c),r=this.y.redAdd(this.y)}else{var h=this.x.redSqr(),d=this.y.redSqr(),u=d.redSqr(),l=this.x.redAdd(d).redSqr().redISub(h).redISub(u);l=l.redIAdd(l);var p=h.redAdd(h).redIAdd(h),b=p.redSqr(),y=u.redIAdd(u);y=(y=y.redIAdd(y)).redIAdd(y),e=b.redISub(l).redISub(l),t=p.redMul(l.redISub(e)).redISub(y),r=(r=this.y.redMul(this.z)).redIAdd(r)}return this.curve.jpoint(e,t,r)},h.prototype._threeDbl=function(){var e,t,r;if(this.zOne){var i=this.x.redSqr(),n=this.y.redSqr(),f=n.redSqr(),o=this.x.redAdd(n).redSqr().redISub(i).redISub(f);o=o.redIAdd(o);var a=i.redAdd(i).redIAdd(i).redIAdd(this.curve.a),s=a.redSqr().redISub(o).redISub(o);e=s;var c=f.redIAdd(f);c=(c=c.redIAdd(c)).redIAdd(c),t=a.redMul(o.redISub(s)).redISub(c),r=this.y.redAdd(this.y)}else{var h=this.z.redSqr(),d=this.y.redSqr(),u=this.x.redMul(d),l=this.x.redSub(h).redMul(this.x.redAdd(h));l=l.redAdd(l).redIAdd(l);var p=u.redIAdd(u),b=(p=p.redIAdd(p)).redAdd(p);e=l.redSqr().redISub(b),r=this.y.redAdd(this.z).redSqr().redISub(d).redISub(h);var y=d.redSqr();y=(y=(y=y.redIAdd(y)).redIAdd(y)).redIAdd(y),t=l.redMul(p.redISub(e)).redISub(y)}return this.curve.jpoint(e,t,r)},h.prototype._dbl=function(){var e=this.curve.a,t=this.x,r=this.y,i=this.z,n=i.redSqr().redSqr(),f=t.redSqr(),o=r.redSqr(),a=f.redAdd(f).redIAdd(f).redIAdd(e.redMul(n)),s=t.redAdd(t),c=(s=s.redIAdd(s)).redMul(o),h=a.redSqr().redISub(c.redAdd(c)),d=c.redISub(h),u=o.redSqr();u=(u=(u=u.redIAdd(u)).redIAdd(u)).redIAdd(u);var l=a.redMul(d).redISub(u),p=r.redAdd(r).redMul(i);return this.curve.jpoint(h,l,p)},h.prototype.trpl=function(){if(!this.curve.zeroA)return this.dbl().add(this);var e=this.x.redSqr(),t=this.y.redSqr(),r=this.z.redSqr(),i=t.redSqr(),n=e.redAdd(e).redIAdd(e),f=n.redSqr(),o=this.x.redAdd(t).redSqr().redISub(e).redISub(i),a=(o=(o=(o=o.redIAdd(o)).redAdd(o).redIAdd(o)).redISub(f)).redSqr(),s=i.redIAdd(i);s=(s=(s=s.redIAdd(s)).redIAdd(s)).redIAdd(s);var c=n.redIAdd(o).redSqr().redISub(f).redISub(a).redISub(s),h=t.redMul(c);h=(h=h.redIAdd(h)).redIAdd(h);var d=this.x.redMul(a).redISub(h);d=(d=d.redIAdd(d)).redIAdd(d);var u=this.y.redMul(c.redMul(s.redISub(c)).redISub(o.redMul(a)));u=(u=(u=u.redIAdd(u)).redIAdd(u)).redIAdd(u);var l=this.z.redAdd(o).redSqr().redISub(r).redISub(a);return this.curve.jpoint(d,u,l)},h.prototype.mul=function(e,t){return e=new n(e,t),this.curve._wnafMul(this,e)},h.prototype.eq=function(e){if("affine"===e.type)return this.eq(e.toJ());if(this===e)return!0;var t=this.z.redSqr(),r=e.z.redSqr();if(0!==this.x.redMul(r).redISub(e.x.redMul(t)).cmpn(0))return!1;var i=t.redMul(this.z),n=r.redMul(e.z);return 0===this.y.redMul(n).redISub(e.y.redMul(i)).cmpn(0)},h.prototype.eqXToP=function(e){var t=this.z.redSqr(),r=e.toRed(this.curve.red).redMul(t);if(0===this.x.cmp(r))return!0;for(var i=e.clone(),n=this.curve.redN.redMul(t);;){if(i.iadd(this.curve.n),i.cmp(this.curve.p)>=0)return!1;if(r.redIAdd(n),0===this.x.cmp(r))return!0}},h.prototype.inspect=function(){return this.isInfinity()?"<EC JPoint Infinity>":"<EC JPoint x: "+this.x.toString(16,2)+" y: "+this.y.toString(16,2)+" z: "+this.z.toString(16,2)+">"},h.prototype.isInfinity=function(){return 0===this.z.cmpn(0)}},function(e,t,r){"use strict";var i=r(276),n=r(275),f=r(301),o=r(279);function a(e){f.call(this,"mont",e),this.a=new i(e.a,16).toRed(this.red),this.b=new i(e.b,16).toRed(this.red),this.i4=new i(4).toRed(this.red).redInvm(),this.two=new i(2).toRed(this.red),this.a24=this.i4.redMul(this.a.redAdd(this.two))}function s(e,t,r){f.BasePoint.call(this,e,"projective"),null===t&&null===r?(this.x=this.curve.one,this.z=this.curve.zero):(this.x=new i(t,16),this.z=new i(r,16),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)))}n(a,f),e.exports=a,a.prototype.validate=function(e){var t=e.normalize().x,r=t.redSqr(),i=r.redMul(t).redAdd(r.redMul(this.a)).redAdd(t);return 0===i.redSqrt().redSqr().cmp(i)},n(s,f.BasePoint),a.prototype.decodePoint=function(e,t){return this.point(o.toArray(e,t),1)},a.prototype.point=function(e,t){return new s(this,e,t)},a.prototype.pointFromJSON=function(e){return s.fromJSON(this,e)},s.prototype.precompute=function(){},s.prototype._encode=function(){return this.getX().toArray("be",this.curve.p.byteLength())},s.fromJSON=function(e,t){return new s(e,t[0],t[1]||e.one)},s.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" z: "+this.z.fromRed().toString(16,2)+">"},s.prototype.isInfinity=function(){return 0===this.z.cmpn(0)},s.prototype.dbl=function(){var e=this.x.redAdd(this.z).redSqr(),t=this.x.redSub(this.z).redSqr(),r=e.redSub(t),i=e.redMul(t),n=r.redMul(t.redAdd(this.curve.a24.redMul(r)));return this.curve.point(i,n)},s.prototype.add=function(){throw new Error("Not supported on Montgomery curve")},s.prototype.diffAdd=function(e,t){var r=this.x.redAdd(this.z),i=this.x.redSub(this.z),n=e.x.redAdd(e.z),f=e.x.redSub(e.z).redMul(r),o=n.redMul(i),a=t.z.redMul(f.redAdd(o).redSqr()),s=t.x.redMul(f.redISub(o).redSqr());return this.curve.point(a,s)},s.prototype.mul=function(e){for(var t=e.clone(),r=this,i=this.curve.point(null,null),n=[];0!==t.cmpn(0);t.iushrn(1))n.push(t.andln(1));for(var f=n.length-1;f>=0;f--)0===n[f]?(r=r.diffAdd(i,this),i=i.dbl()):(i=r.diffAdd(i,this),r=r.dbl());return i},s.prototype.mulAdd=function(){throw new Error("Not supported on Montgomery curve")},s.prototype.jumlAdd=function(){throw new Error("Not supported on Montgomery curve")},s.prototype.eq=function(e){return 0===this.getX().cmp(e.getX())},s.prototype.normalize=function(){return this.x=this.x.redMul(this.z.redInvm()),this.z=this.curve.one,this},s.prototype.getX=function(){return this.normalize(),this.x.fromRed()}},function(e,t,r){"use strict";var i=r(279),n=r(276),f=r(275),o=r(301),a=i.assert;function s(e){this.twisted=1!=(0|e.a),this.mOneA=this.twisted&&-1==(0|e.a),this.extended=this.mOneA,o.call(this,"edwards",e),this.a=new n(e.a,16).umod(this.red.m),this.a=this.a.toRed(this.red),this.c=new n(e.c,16).toRed(this.red),this.c2=this.c.redSqr(),this.d=new n(e.d,16).toRed(this.red),this.dd=this.d.redAdd(this.d),a(!this.twisted||0===this.c.fromRed().cmpn(1)),this.oneC=1==(0|e.c)}function c(e,t,r,i,f){o.BasePoint.call(this,e,"projective"),null===t&&null===r&&null===i?(this.x=this.curve.zero,this.y=this.curve.one,this.z=this.curve.one,this.t=this.curve.zero,this.zOne=!0):(this.x=new n(t,16),this.y=new n(r,16),this.z=i?new n(i,16):this.curve.one,this.t=f&&new n(f,16),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)),this.t&&!this.t.red&&(this.t=this.t.toRed(this.curve.red)),this.zOne=this.z===this.curve.one,this.curve.extended&&!this.t&&(this.t=this.x.redMul(this.y),this.zOne||(this.t=this.t.redMul(this.z.redInvm()))))}f(s,o),e.exports=s,s.prototype._mulA=function(e){return this.mOneA?e.redNeg():this.a.redMul(e)},s.prototype._mulC=function(e){return this.oneC?e:this.c.redMul(e)},s.prototype.jpoint=function(e,t,r,i){return this.point(e,t,r,i)},s.prototype.pointFromX=function(e,t){(e=new n(e,16)).red||(e=e.toRed(this.red));var r=e.redSqr(),i=this.c2.redSub(this.a.redMul(r)),f=this.one.redSub(this.c2.redMul(this.d).redMul(r)),o=i.redMul(f.redInvm()),a=o.redSqrt();if(0!==a.redSqr().redSub(o).cmp(this.zero))throw new Error("invalid point");var s=a.fromRed().isOdd();return(t&&!s||!t&&s)&&(a=a.redNeg()),this.point(e,a)},s.prototype.pointFromY=function(e,t){(e=new n(e,16)).red||(e=e.toRed(this.red));var r=e.redSqr(),i=r.redSub(this.c2),f=r.redMul(this.d).redMul(this.c2).redSub(this.a),o=i.redMul(f.redInvm());if(0===o.cmp(this.zero)){if(t)throw new Error("invalid point");return this.point(this.zero,e)}var a=o.redSqrt();if(0!==a.redSqr().redSub(o).cmp(this.zero))throw new Error("invalid point");return a.fromRed().isOdd()!==t&&(a=a.redNeg()),this.point(a,e)},s.prototype.validate=function(e){if(e.isInfinity())return!0;e.normalize();var t=e.x.redSqr(),r=e.y.redSqr(),i=t.redMul(this.a).redAdd(r),n=this.c2.redMul(this.one.redAdd(this.d.redMul(t).redMul(r)));return 0===i.cmp(n)},f(c,o.BasePoint),s.prototype.pointFromJSON=function(e){return c.fromJSON(this,e)},s.prototype.point=function(e,t,r,i){return new c(this,e,t,r,i)},c.fromJSON=function(e,t){return new c(e,t[0],t[1],t[2])},c.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" y: "+this.y.fromRed().toString(16,2)+" z: "+this.z.fromRed().toString(16,2)+">"},c.prototype.isInfinity=function(){return 0===this.x.cmpn(0)&&(0===this.y.cmp(this.z)||this.zOne&&0===this.y.cmp(this.curve.c))},c.prototype._extDbl=function(){var e=this.x.redSqr(),t=this.y.redSqr(),r=this.z.redSqr();r=r.redIAdd(r);var i=this.curve._mulA(e),n=this.x.redAdd(this.y).redSqr().redISub(e).redISub(t),f=i.redAdd(t),o=f.redSub(r),a=i.redSub(t),s=n.redMul(o),c=f.redMul(a),h=n.redMul(a),d=o.redMul(f);return this.curve.point(s,c,d,h)},c.prototype._projDbl=function(){var e,t,r,i,n,f,o=this.x.redAdd(this.y).redSqr(),a=this.x.redSqr(),s=this.y.redSqr();if(this.curve.twisted){var c=(i=this.curve._mulA(a)).redAdd(s);this.zOne?(e=o.redSub(a).redSub(s).redMul(c.redSub(this.curve.two)),t=c.redMul(i.redSub(s)),r=c.redSqr().redSub(c).redSub(c)):(n=this.z.redSqr(),f=c.redSub(n).redISub(n),e=o.redSub(a).redISub(s).redMul(f),t=c.redMul(i.redSub(s)),r=c.redMul(f))}else i=a.redAdd(s),n=this.curve._mulC(this.z).redSqr(),f=i.redSub(n).redSub(n),e=this.curve._mulC(o.redISub(i)).redMul(f),t=this.curve._mulC(i).redMul(a.redISub(s)),r=i.redMul(f);return this.curve.point(e,t,r)},c.prototype.dbl=function(){return this.isInfinity()?this:this.curve.extended?this._extDbl():this._projDbl()},c.prototype._extAdd=function(e){var t=this.y.redSub(this.x).redMul(e.y.redSub(e.x)),r=this.y.redAdd(this.x).redMul(e.y.redAdd(e.x)),i=this.t.redMul(this.curve.dd).redMul(e.t),n=this.z.redMul(e.z.redAdd(e.z)),f=r.redSub(t),o=n.redSub(i),a=n.redAdd(i),s=r.redAdd(t),c=f.redMul(o),h=a.redMul(s),d=f.redMul(s),u=o.redMul(a);return this.curve.point(c,h,u,d)},c.prototype._projAdd=function(e){var t,r,i=this.z.redMul(e.z),n=i.redSqr(),f=this.x.redMul(e.x),o=this.y.redMul(e.y),a=this.curve.d.redMul(f).redMul(o),s=n.redSub(a),c=n.redAdd(a),h=this.x.redAdd(this.y).redMul(e.x.redAdd(e.y)).redISub(f).redISub(o),d=i.redMul(s).redMul(h);return this.curve.twisted?(t=i.redMul(c).redMul(o.redSub(this.curve._mulA(f))),r=s.redMul(c)):(t=i.redMul(c).redMul(o.redSub(f)),r=this.curve._mulC(s).redMul(c)),this.curve.point(d,t,r)},c.prototype.add=function(e){return this.isInfinity()?e:e.isInfinity()?this:this.curve.extended?this._extAdd(e):this._projAdd(e)},c.prototype.mul=function(e){return this._hasDoubles(e)?this.curve._fixedNafMul(this,e):this.curve._wnafMul(this,e)},c.prototype.mulAdd=function(e,t,r){return this.curve._wnafMulAdd(1,[this,t],[e,r],2,!1)},c.prototype.jmulAdd=function(e,t,r){return this.curve._wnafMulAdd(1,[this,t],[e,r],2,!0)},c.prototype.normalize=function(){if(this.zOne)return this;var e=this.z.redInvm();return this.x=this.x.redMul(e),this.y=this.y.redMul(e),this.t&&(this.t=this.t.redMul(e)),this.z=this.curve.one,this.zOne=!0,this},c.prototype.neg=function(){return this.curve.point(this.x.redNeg(),this.y,this.z,this.t&&this.t.redNeg())},c.prototype.getX=function(){return this.normalize(),this.x.fromRed()},c.prototype.getY=function(){return this.normalize(),this.y.fromRed()},c.prototype.eq=function(e){return this===e||0===this.getX().cmp(e.getX())&&0===this.getY().cmp(e.getY())},c.prototype.eqXToP=function(e){var t=e.toRed(this.curve.red).redMul(this.z);if(0===this.x.cmp(t))return!0;for(var r=e.clone(),i=this.curve.redN.redMul(this.z);;){if(r.iadd(this.curve.n),r.cmp(this.curve.p)>=0)return!1;if(t.redIAdd(i),0===this.x.cmp(t))return!0}},c.prototype.toP=c.prototype.normalize,c.prototype.mixedAdd=c.prototype.add},function(e,t,r){"use strict";t.sha1=r(489),t.sha224=r(490),t.sha256=r(399),t.sha384=r(491),t.sha512=r(400)},function(e,t,r){"use strict";var i=r(280),n=r(296),f=r(398),o=i.rotl32,a=i.sum32,s=i.sum32_5,c=f.ft_1,h=n.BlockHash,d=[1518500249,1859775393,2400959708,3395469782];function u(){if(!(this instanceof u))return new u;h.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.W=new Array(80)}i.inherits(u,h),e.exports=u,u.blockSize=512,u.outSize=160,u.hmacStrength=80,u.padLength=64,u.prototype._update=function(e,t){for(var r=this.W,i=0;i<16;i++)r[i]=e[t+i];for(;i<r.length;i++)r[i]=o(r[i-3]^r[i-8]^r[i-14]^r[i-16],1);var n=this.h[0],f=this.h[1],h=this.h[2],u=this.h[3],l=this.h[4];for(i=0;i<r.length;i++){var p=~~(i/20),b=s(o(n,5),c(p,f,h,u),l,r[i],d[p]);l=u,u=h,h=o(f,30),f=n,n=b}this.h[0]=a(this.h[0],n),this.h[1]=a(this.h[1],f),this.h[2]=a(this.h[2],h),this.h[3]=a(this.h[3],u),this.h[4]=a(this.h[4],l)},u.prototype._digest=function(e){return"hex"===e?i.toHex32(this.h,"big"):i.split32(this.h,"big")}},function(e,t,r){"use strict";var i=r(280),n=r(399);function f(){if(!(this instanceof f))return new f;n.call(this),this.h=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]}i.inherits(f,n),e.exports=f,f.blockSize=512,f.outSize=224,f.hmacStrength=192,f.padLength=64,f.prototype._digest=function(e){return"hex"===e?i.toHex32(this.h.slice(0,7),"big"):i.split32(this.h.slice(0,7),"big")}},function(e,t,r){"use strict";var i=r(280),n=r(400);function f(){if(!(this instanceof f))return new f;n.call(this),this.h=[3418070365,3238371032,1654270250,914150663,2438529370,812702999,355462360,4144912697,1731405415,4290775857,2394180231,1750603025,3675008525,1694076839,1203062813,3204075428]}i.inherits(f,n),e.exports=f,f.blockSize=1024,f.outSize=384,f.hmacStrength=192,f.padLength=128,f.prototype._digest=function(e){return"hex"===e?i.toHex32(this.h.slice(0,12),"big"):i.split32(this.h.slice(0,12),"big")}},function(e,t,r){"use strict";var i=r(280),n=r(296),f=i.rotl32,o=i.sum32,a=i.sum32_3,s=i.sum32_4,c=n.BlockHash;function h(){if(!(this instanceof h))return new h;c.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.endian="little"}function d(e,t,r,i){return e<=15?t^r^i:e<=31?t&r|~t&i:e<=47?(t|~r)^i:e<=63?t&i|r&~i:t^(r|~i)}function u(e){return e<=15?0:e<=31?1518500249:e<=47?1859775393:e<=63?2400959708:2840853838}function l(e){return e<=15?1352829926:e<=31?1548603684:e<=47?1836072691:e<=63?2053994217:0}i.inherits(h,c),t.ripemd160=h,h.blockSize=512,h.outSize=160,h.hmacStrength=192,h.padLength=64,h.prototype._update=function(e,t){for(var r=this.h[0],i=this.h[1],n=this.h[2],c=this.h[3],h=this.h[4],m=r,v=i,_=n,w=c,S=h,M=0;M<80;M++){var E=o(f(s(r,d(M,i,n,c),e[p[M]+t],u(M)),y[M]),h);r=h,h=c,c=f(n,10),n=i,i=E,E=o(f(s(m,d(79-M,v,_,w),e[b[M]+t],l(M)),g[M]),S),m=S,S=w,w=f(_,10),_=v,v=E}E=a(this.h[1],n,w),this.h[1]=a(this.h[2],c,S),this.h[2]=a(this.h[3],h,m),this.h[3]=a(this.h[4],r,v),this.h[4]=a(this.h[0],i,_),this.h[0]=E},h.prototype._digest=function(e){return"hex"===e?i.toHex32(this.h,"little"):i.split32(this.h,"little")};var p=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],b=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],y=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],g=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]},function(e,t,r){"use strict";var i=r(280),n=r(278);function f(e,t,r){if(!(this instanceof f))return new f(e,t,r);this.Hash=e,this.blockSize=e.blockSize/8,this.outSize=e.outSize/8,this.inner=null,this.outer=null,this._init(i.toArray(t,r))}e.exports=f,f.prototype._init=function(e){e.length>this.blockSize&&(e=(new this.Hash).update(e).digest()),n(e.length<=this.blockSize);for(var t=e.length;t<this.blockSize;t++)e.push(0);for(t=0;t<e.length;t++)e[t]^=54;for(this.inner=(new this.Hash).update(e),t=0;t<e.length;t++)e[t]^=106;this.outer=(new this.Hash).update(e)},f.prototype.update=function(e,t){return this.inner.update(e,t),this},f.prototype.digest=function(e){return this.outer.update(this.inner.digest()),this.outer.digest(e)}},function(e,t){e.exports={doubles:{step:4,points:[["e60fce93b59e9ec53011aabc21c23e97b2a31369b87a5ae9c44ee89e2a6dec0a","f7e3507399e595929db99f34f57937101296891e44d23f0be1f32cce69616821"],["8282263212c609d9ea2a6e3e172de238d8c39cabd5ac1ca10646e23fd5f51508","11f8a8098557dfe45e8256e830b60ace62d613ac2f7b17bed31b6eaff6e26caf"],["175e159f728b865a72f99cc6c6fc846de0b93833fd2222ed73fce5b551e5b739","d3506e0d9e3c79eba4ef97a51ff71f5eacb5955add24345c6efa6ffee9fed695"],["363d90d447b00c9c99ceac05b6262ee053441c7e55552ffe526bad8f83ff4640","4e273adfc732221953b445397f3363145b9a89008199ecb62003c7f3bee9de9"],["8b4b5f165df3c2be8c6244b5b745638843e4a781a15bcd1b69f79a55dffdf80c","4aad0a6f68d308b4b3fbd7813ab0da04f9e336546162ee56b3eff0c65fd4fd36"],["723cbaa6e5db996d6bf771c00bd548c7b700dbffa6c0e77bcb6115925232fcda","96e867b5595cc498a921137488824d6e2660a0653779494801dc069d9eb39f5f"],["eebfa4d493bebf98ba5feec812c2d3b50947961237a919839a533eca0e7dd7fa","5d9a8ca3970ef0f269ee7edaf178089d9ae4cdc3a711f712ddfd4fdae1de8999"],["100f44da696e71672791d0a09b7bde459f1215a29b3c03bfefd7835b39a48db0","cdd9e13192a00b772ec8f3300c090666b7ff4a18ff5195ac0fbd5cd62bc65a09"],["e1031be262c7ed1b1dc9227a4a04c017a77f8d4464f3b3852c8acde6e534fd2d","9d7061928940405e6bb6a4176597535af292dd419e1ced79a44f18f29456a00d"],["feea6cae46d55b530ac2839f143bd7ec5cf8b266a41d6af52d5e688d9094696d","e57c6b6c97dce1bab06e4e12bf3ecd5c981c8957cc41442d3155debf18090088"],["da67a91d91049cdcb367be4be6ffca3cfeed657d808583de33fa978bc1ec6cb1","9bacaa35481642bc41f463f7ec9780e5dec7adc508f740a17e9ea8e27a68be1d"],["53904faa0b334cdda6e000935ef22151ec08d0f7bb11069f57545ccc1a37b7c0","5bc087d0bc80106d88c9eccac20d3c1c13999981e14434699dcb096b022771c8"],["8e7bcd0bd35983a7719cca7764ca906779b53a043a9b8bcaeff959f43ad86047","10b7770b2a3da4b3940310420ca9514579e88e2e47fd68b3ea10047e8460372a"],["385eed34c1cdff21e6d0818689b81bde71a7f4f18397e6690a841e1599c43862","283bebc3e8ea23f56701de19e9ebf4576b304eec2086dc8cc0458fe5542e5453"],["6f9d9b803ecf191637c73a4413dfa180fddf84a5947fbc9c606ed86c3fac3a7","7c80c68e603059ba69b8e2a30e45c4d47ea4dd2f5c281002d86890603a842160"],["3322d401243c4e2582a2147c104d6ecbf774d163db0f5e5313b7e0e742d0e6bd","56e70797e9664ef5bfb019bc4ddaf9b72805f63ea2873af624f3a2e96c28b2a0"],["85672c7d2de0b7da2bd1770d89665868741b3f9af7643397721d74d28134ab83","7c481b9b5b43b2eb6374049bfa62c2e5e77f17fcc5298f44c8e3094f790313a6"],["948bf809b1988a46b06c9f1919413b10f9226c60f668832ffd959af60c82a0a","53a562856dcb6646dc6b74c5d1c3418c6d4dff08c97cd2bed4cb7f88d8c8e589"],["6260ce7f461801c34f067ce0f02873a8f1b0e44dfc69752accecd819f38fd8e8","bc2da82b6fa5b571a7f09049776a1ef7ecd292238051c198c1a84e95b2b4ae17"],["e5037de0afc1d8d43d8348414bbf4103043ec8f575bfdc432953cc8d2037fa2d","4571534baa94d3b5f9f98d09fb990bddbd5f5b03ec481f10e0e5dc841d755bda"],["e06372b0f4a207adf5ea905e8f1771b4e7e8dbd1c6a6c5b725866a0ae4fce725","7a908974bce18cfe12a27bb2ad5a488cd7484a7787104870b27034f94eee31dd"],["213c7a715cd5d45358d0bbf9dc0ce02204b10bdde2a3f58540ad6908d0559754","4b6dad0b5ae462507013ad06245ba190bb4850f5f36a7eeddff2c27534b458f2"],["4e7c272a7af4b34e8dbb9352a5419a87e2838c70adc62cddf0cc3a3b08fbd53c","17749c766c9d0b18e16fd09f6def681b530b9614bff7dd33e0b3941817dcaae6"],["fea74e3dbe778b1b10f238ad61686aa5c76e3db2be43057632427e2840fb27b6","6e0568db9b0b13297cf674deccb6af93126b596b973f7b77701d3db7f23cb96f"],["76e64113f677cf0e10a2570d599968d31544e179b760432952c02a4417bdde39","c90ddf8dee4e95cf577066d70681f0d35e2a33d2b56d2032b4b1752d1901ac01"],["c738c56b03b2abe1e8281baa743f8f9a8f7cc643df26cbee3ab150242bcbb891","893fb578951ad2537f718f2eacbfbbbb82314eef7880cfe917e735d9699a84c3"],["d895626548b65b81e264c7637c972877d1d72e5f3a925014372e9f6588f6c14b","febfaa38f2bc7eae728ec60818c340eb03428d632bb067e179363ed75d7d991f"],["b8da94032a957518eb0f6433571e8761ceffc73693e84edd49150a564f676e03","2804dfa44805a1e4d7c99cc9762808b092cc584d95ff3b511488e4e74efdf6e7"],["e80fea14441fb33a7d8adab9475d7fab2019effb5156a792f1a11778e3c0df5d","eed1de7f638e00771e89768ca3ca94472d155e80af322ea9fcb4291b6ac9ec78"],["a301697bdfcd704313ba48e51d567543f2a182031efd6915ddc07bbcc4e16070","7370f91cfb67e4f5081809fa25d40f9b1735dbf7c0a11a130c0d1a041e177ea1"],["90ad85b389d6b936463f9d0512678de208cc330b11307fffab7ac63e3fb04ed4","e507a3620a38261affdcbd9427222b839aefabe1582894d991d4d48cb6ef150"],["8f68b9d2f63b5f339239c1ad981f162ee88c5678723ea3351b7b444c9ec4c0da","662a9f2dba063986de1d90c2b6be215dbbea2cfe95510bfdf23cbf79501fff82"],["e4f3fb0176af85d65ff99ff9198c36091f48e86503681e3e6686fd5053231e11","1e63633ad0ef4f1c1661a6d0ea02b7286cc7e74ec951d1c9822c38576feb73bc"],["8c00fa9b18ebf331eb961537a45a4266c7034f2f0d4e1d0716fb6eae20eae29e","efa47267fea521a1a9dc343a3736c974c2fadafa81e36c54e7d2a4c66702414b"],["e7a26ce69dd4829f3e10cec0a9e98ed3143d084f308b92c0997fddfc60cb3e41","2a758e300fa7984b471b006a1aafbb18d0a6b2c0420e83e20e8a9421cf2cfd51"],["b6459e0ee3662ec8d23540c223bcbdc571cbcb967d79424f3cf29eb3de6b80ef","67c876d06f3e06de1dadf16e5661db3c4b3ae6d48e35b2ff30bf0b61a71ba45"],["d68a80c8280bb840793234aa118f06231d6f1fc67e73c5a5deda0f5b496943e8","db8ba9fff4b586d00c4b1f9177b0e28b5b0e7b8f7845295a294c84266b133120"],["324aed7df65c804252dc0270907a30b09612aeb973449cea4095980fc28d3d5d","648a365774b61f2ff130c0c35aec1f4f19213b0c7e332843967224af96ab7c84"],["4df9c14919cde61f6d51dfdbe5fee5dceec4143ba8d1ca888e8bd373fd054c96","35ec51092d8728050974c23a1d85d4b5d506cdc288490192ebac06cad10d5d"],["9c3919a84a474870faed8a9c1cc66021523489054d7f0308cbfc99c8ac1f98cd","ddb84f0f4a4ddd57584f044bf260e641905326f76c64c8e6be7e5e03d4fc599d"],["6057170b1dd12fdf8de05f281d8e06bb91e1493a8b91d4cc5a21382120a959e5","9a1af0b26a6a4807add9a2daf71df262465152bc3ee24c65e899be932385a2a8"],["a576df8e23a08411421439a4518da31880cef0fba7d4df12b1a6973eecb94266","40a6bf20e76640b2c92b97afe58cd82c432e10a7f514d9f3ee8be11ae1b28ec8"],["7778a78c28dec3e30a05fe9629de8c38bb30d1f5cf9a3a208f763889be58ad71","34626d9ab5a5b22ff7098e12f2ff580087b38411ff24ac563b513fc1fd9f43ac"],["928955ee637a84463729fd30e7afd2ed5f96274e5ad7e5cb09eda9c06d903ac","c25621003d3f42a827b78a13093a95eeac3d26efa8a8d83fc5180e935bcd091f"],["85d0fef3ec6db109399064f3a0e3b2855645b4a907ad354527aae75163d82751","1f03648413a38c0be29d496e582cf5663e8751e96877331582c237a24eb1f962"],["ff2b0dce97eece97c1c9b6041798b85dfdfb6d8882da20308f5404824526087e","493d13fef524ba188af4c4dc54d07936c7b7ed6fb90e2ceb2c951e01f0c29907"],["827fbbe4b1e880ea9ed2b2e6301b212b57f1ee148cd6dd28780e5e2cf856e241","c60f9c923c727b0b71bef2c67d1d12687ff7a63186903166d605b68baec293ec"],["eaa649f21f51bdbae7be4ae34ce6e5217a58fdce7f47f9aa7f3b58fa2120e2b3","be3279ed5bbbb03ac69a80f89879aa5a01a6b965f13f7e59d47a5305ba5ad93d"],["e4a42d43c5cf169d9391df6decf42ee541b6d8f0c9a137401e23632dda34d24f","4d9f92e716d1c73526fc99ccfb8ad34ce886eedfa8d8e4f13a7f7131deba9414"],["1ec80fef360cbdd954160fadab352b6b92b53576a88fea4947173b9d4300bf19","aeefe93756b5340d2f3a4958a7abbf5e0146e77f6295a07b671cdc1cc107cefd"],["146a778c04670c2f91b00af4680dfa8bce3490717d58ba889ddb5928366642be","b318e0ec3354028add669827f9d4b2870aaa971d2f7e5ed1d0b297483d83efd0"],["fa50c0f61d22e5f07e3acebb1aa07b128d0012209a28b9776d76a8793180eef9","6b84c6922397eba9b72cd2872281a68a5e683293a57a213b38cd8d7d3f4f2811"],["da1d61d0ca721a11b1a5bf6b7d88e8421a288ab5d5bba5220e53d32b5f067ec2","8157f55a7c99306c79c0766161c91e2966a73899d279b48a655fba0f1ad836f1"],["a8e282ff0c9706907215ff98e8fd416615311de0446f1e062a73b0610d064e13","7f97355b8db81c09abfb7f3c5b2515888b679a3e50dd6bd6cef7c73111f4cc0c"],["174a53b9c9a285872d39e56e6913cab15d59b1fa512508c022f382de8319497c","ccc9dc37abfc9c1657b4155f2c47f9e6646b3a1d8cb9854383da13ac079afa73"],["959396981943785c3d3e57edf5018cdbe039e730e4918b3d884fdff09475b7ba","2e7e552888c331dd8ba0386a4b9cd6849c653f64c8709385e9b8abf87524f2fd"],["d2a63a50ae401e56d645a1153b109a8fcca0a43d561fba2dbb51340c9d82b151","e82d86fb6443fcb7565aee58b2948220a70f750af484ca52d4142174dcf89405"],["64587e2335471eb890ee7896d7cfdc866bacbdbd3839317b3436f9b45617e073","d99fcdd5bf6902e2ae96dd6447c299a185b90a39133aeab358299e5e9faf6589"],["8481bde0e4e4d885b3a546d3e549de042f0aa6cea250e7fd358d6c86dd45e458","38ee7b8cba5404dd84a25bf39cecb2ca900a79c42b262e556d64b1b59779057e"],["13464a57a78102aa62b6979ae817f4637ffcfed3c4b1ce30bcd6303f6caf666b","69be159004614580ef7e433453ccb0ca48f300a81d0942e13f495a907f6ecc27"],["bc4a9df5b713fe2e9aef430bcc1dc97a0cd9ccede2f28588cada3a0d2d83f366","d3a81ca6e785c06383937adf4b798caa6e8a9fbfa547b16d758d666581f33c1"],["8c28a97bf8298bc0d23d8c749452a32e694b65e30a9472a3954ab30fe5324caa","40a30463a3305193378fedf31f7cc0eb7ae784f0451cb9459e71dc73cbef9482"],["8ea9666139527a8c1dd94ce4f071fd23c8b350c5a4bb33748c4ba111faccae0","620efabbc8ee2782e24e7c0cfb95c5d735b783be9cf0f8e955af34a30e62b945"],["dd3625faef5ba06074669716bbd3788d89bdde815959968092f76cc4eb9a9787","7a188fa3520e30d461da2501045731ca941461982883395937f68d00c644a573"],["f710d79d9eb962297e4f6232b40e8f7feb2bc63814614d692c12de752408221e","ea98e67232d3b3295d3b535532115ccac8612c721851617526ae47a9c77bfc82"]]},naf:{wnd:7,points:[["f9308a019258c31049344f85f89d5229b531c845836f99b08601f113bce036f9","388f7b0f632de8140fe337e62a37f3566500a99934c2231b6cb9fd7584b8e672"],["2f8bde4d1a07209355b4a7250a5c5128e88b84bddc619ab7cba8d569b240efe4","d8ac222636e5e3d6d4dba9dda6c9c426f788271bab0d6840dca87d3aa6ac62d6"],["5cbdf0646e5db4eaa398f365f2ea7a0e3d419b7e0330e39ce92bddedcac4f9bc","6aebca40ba255960a3178d6d861a54dba813d0b813fde7b5a5082628087264da"],["acd484e2f0c7f65309ad178a9f559abde09796974c57e714c35f110dfc27ccbe","cc338921b0a7d9fd64380971763b61e9add888a4375f8e0f05cc262ac64f9c37"],["774ae7f858a9411e5ef4246b70c65aac5649980be5c17891bbec17895da008cb","d984a032eb6b5e190243dd56d7b7b365372db1e2dff9d6a8301d74c9c953c61b"],["f28773c2d975288bc7d1d205c3748651b075fbc6610e58cddeeddf8f19405aa8","ab0902e8d880a89758212eb65cdaf473a1a06da521fa91f29b5cb52db03ed81"],["d7924d4f7d43ea965a465ae3095ff41131e5946f3c85f79e44adbcf8e27e080e","581e2872a86c72a683842ec228cc6defea40af2bd896d3a5c504dc9ff6a26b58"],["defdea4cdb677750a420fee807eacf21eb9898ae79b9768766e4faa04a2d4a34","4211ab0694635168e997b0ead2a93daeced1f4a04a95c0f6cfb199f69e56eb77"],["2b4ea0a797a443d293ef5cff444f4979f06acfebd7e86d277475656138385b6c","85e89bc037945d93b343083b5a1c86131a01f60c50269763b570c854e5c09b7a"],["352bbf4a4cdd12564f93fa332ce333301d9ad40271f8107181340aef25be59d5","321eb4075348f534d59c18259dda3e1f4a1b3b2e71b1039c67bd3d8bcf81998c"],["2fa2104d6b38d11b0230010559879124e42ab8dfeff5ff29dc9cdadd4ecacc3f","2de1068295dd865b64569335bd5dd80181d70ecfc882648423ba76b532b7d67"],["9248279b09b4d68dab21a9b066edda83263c3d84e09572e269ca0cd7f5453714","73016f7bf234aade5d1aa71bdea2b1ff3fc0de2a887912ffe54a32ce97cb3402"],["daed4f2be3a8bf278e70132fb0beb7522f570e144bf615c07e996d443dee8729","a69dce4a7d6c98e8d4a1aca87ef8d7003f83c230f3afa726ab40e52290be1c55"],["c44d12c7065d812e8acf28d7cbb19f9011ecd9e9fdf281b0e6a3b5e87d22e7db","2119a460ce326cdc76c45926c982fdac0e106e861edf61c5a039063f0e0e6482"],["6a245bf6dc698504c89a20cfded60853152b695336c28063b61c65cbd269e6b4","e022cf42c2bd4a708b3f5126f16a24ad8b33ba48d0423b6efd5e6348100d8a82"],["1697ffa6fd9de627c077e3d2fe541084ce13300b0bec1146f95ae57f0d0bd6a5","b9c398f186806f5d27561506e4557433a2cf15009e498ae7adee9d63d01b2396"],["605bdb019981718b986d0f07e834cb0d9deb8360ffb7f61df982345ef27a7479","2972d2de4f8d20681a78d93ec96fe23c26bfae84fb14db43b01e1e9056b8c49"],["62d14dab4150bf497402fdc45a215e10dcb01c354959b10cfe31c7e9d87ff33d","80fc06bd8cc5b01098088a1950eed0db01aa132967ab472235f5642483b25eaf"],["80c60ad0040f27dade5b4b06c408e56b2c50e9f56b9b8b425e555c2f86308b6f","1c38303f1cc5c30f26e66bad7fe72f70a65eed4cbe7024eb1aa01f56430bd57a"],["7a9375ad6167ad54aa74c6348cc54d344cc5dc9487d847049d5eabb0fa03c8fb","d0e3fa9eca8726909559e0d79269046bdc59ea10c70ce2b02d499ec224dc7f7"],["d528ecd9b696b54c907a9ed045447a79bb408ec39b68df504bb51f459bc3ffc9","eecf41253136e5f99966f21881fd656ebc4345405c520dbc063465b521409933"],["49370a4b5f43412ea25f514e8ecdad05266115e4a7ecb1387231808f8b45963","758f3f41afd6ed428b3081b0512fd62a54c3f3afbb5b6764b653052a12949c9a"],["77f230936ee88cbbd73df930d64702ef881d811e0e1498e2f1c13eb1fc345d74","958ef42a7886b6400a08266e9ba1b37896c95330d97077cbbe8eb3c7671c60d6"],["f2dac991cc4ce4b9ea44887e5c7c0bce58c80074ab9d4dbaeb28531b7739f530","e0dedc9b3b2f8dad4da1f32dec2531df9eb5fbeb0598e4fd1a117dba703a3c37"],["463b3d9f662621fb1b4be8fbbe2520125a216cdfc9dae3debcba4850c690d45b","5ed430d78c296c3543114306dd8622d7c622e27c970a1de31cb377b01af7307e"],["f16f804244e46e2a09232d4aff3b59976b98fac14328a2d1a32496b49998f247","cedabd9b82203f7e13d206fcdf4e33d92a6c53c26e5cce26d6579962c4e31df6"],["caf754272dc84563b0352b7a14311af55d245315ace27c65369e15f7151d41d1","cb474660ef35f5f2a41b643fa5e460575f4fa9b7962232a5c32f908318a04476"],["2600ca4b282cb986f85d0f1709979d8b44a09c07cb86d7c124497bc86f082120","4119b88753c15bd6a693b03fcddbb45d5ac6be74ab5f0ef44b0be9475a7e4b40"],["7635ca72d7e8432c338ec53cd12220bc01c48685e24f7dc8c602a7746998e435","91b649609489d613d1d5e590f78e6d74ecfc061d57048bad9e76f302c5b9c61"],["754e3239f325570cdbbf4a87deee8a66b7f2b33479d468fbc1a50743bf56cc18","673fb86e5bda30fb3cd0ed304ea49a023ee33d0197a695d0c5d98093c536683"],["e3e6bd1071a1e96aff57859c82d570f0330800661d1c952f9fe2694691d9b9e8","59c9e0bba394e76f40c0aa58379a3cb6a5a2283993e90c4167002af4920e37f5"],["186b483d056a033826ae73d88f732985c4ccb1f32ba35f4b4cc47fdcf04aa6eb","3b952d32c67cf77e2e17446e204180ab21fb8090895138b4a4a797f86e80888b"],["df9d70a6b9876ce544c98561f4be4f725442e6d2b737d9c91a8321724ce0963f","55eb2dafd84d6ccd5f862b785dc39d4ab157222720ef9da217b8c45cf2ba2417"],["5edd5cc23c51e87a497ca815d5dce0f8ab52554f849ed8995de64c5f34ce7143","efae9c8dbc14130661e8cec030c89ad0c13c66c0d17a2905cdc706ab7399a868"],["290798c2b6476830da12fe02287e9e777aa3fba1c355b17a722d362f84614fba","e38da76dcd440621988d00bcf79af25d5b29c094db2a23146d003afd41943e7a"],["af3c423a95d9f5b3054754efa150ac39cd29552fe360257362dfdecef4053b45","f98a3fd831eb2b749a93b0e6f35cfb40c8cd5aa667a15581bc2feded498fd9c6"],["766dbb24d134e745cccaa28c99bf274906bb66b26dcf98df8d2fed50d884249a","744b1152eacbe5e38dcc887980da38b897584a65fa06cedd2c924f97cbac5996"],["59dbf46f8c94759ba21277c33784f41645f7b44f6c596a58ce92e666191abe3e","c534ad44175fbc300f4ea6ce648309a042ce739a7919798cd85e216c4a307f6e"],["f13ada95103c4537305e691e74e9a4a8dd647e711a95e73cb62dc6018cfd87b8","e13817b44ee14de663bf4bc808341f326949e21a6a75c2570778419bdaf5733d"],["7754b4fa0e8aced06d4167a2c59cca4cda1869c06ebadfb6488550015a88522c","30e93e864e669d82224b967c3020b8fa8d1e4e350b6cbcc537a48b57841163a2"],["948dcadf5990e048aa3874d46abef9d701858f95de8041d2a6828c99e2262519","e491a42537f6e597d5d28a3224b1bc25df9154efbd2ef1d2cbba2cae5347d57e"],["7962414450c76c1689c7b48f8202ec37fb224cf5ac0bfa1570328a8a3d7c77ab","100b610ec4ffb4760d5c1fc133ef6f6b12507a051f04ac5760afa5b29db83437"],["3514087834964b54b15b160644d915485a16977225b8847bb0dd085137ec47ca","ef0afbb2056205448e1652c48e8127fc6039e77c15c2378b7e7d15a0de293311"],["d3cc30ad6b483e4bc79ce2c9dd8bc54993e947eb8df787b442943d3f7b527eaf","8b378a22d827278d89c5e9be8f9508ae3c2ad46290358630afb34db04eede0a4"],["1624d84780732860ce1c78fcbfefe08b2b29823db913f6493975ba0ff4847610","68651cf9b6da903e0914448c6cd9d4ca896878f5282be4c8cc06e2a404078575"],["733ce80da955a8a26902c95633e62a985192474b5af207da6df7b4fd5fc61cd4","f5435a2bd2badf7d485a4d8b8db9fcce3e1ef8e0201e4578c54673bc1dc5ea1d"],["15d9441254945064cf1a1c33bbd3b49f8966c5092171e699ef258dfab81c045c","d56eb30b69463e7234f5137b73b84177434800bacebfc685fc37bbe9efe4070d"],["a1d0fcf2ec9de675b612136e5ce70d271c21417c9d2b8aaaac138599d0717940","edd77f50bcb5a3cab2e90737309667f2641462a54070f3d519212d39c197a629"],["e22fbe15c0af8ccc5780c0735f84dbe9a790badee8245c06c7ca37331cb36980","a855babad5cd60c88b430a69f53a1a7a38289154964799be43d06d77d31da06"],["311091dd9860e8e20ee13473c1155f5f69635e394704eaa74009452246cfa9b3","66db656f87d1f04fffd1f04788c06830871ec5a64feee685bd80f0b1286d8374"],["34c1fd04d301be89b31c0442d3e6ac24883928b45a9340781867d4232ec2dbdf","9414685e97b1b5954bd46f730174136d57f1ceeb487443dc5321857ba73abee"],["f219ea5d6b54701c1c14de5b557eb42a8d13f3abbcd08affcc2a5e6b049b8d63","4cb95957e83d40b0f73af4544cccf6b1f4b08d3c07b27fb8d8c2962a400766d1"],["d7b8740f74a8fbaab1f683db8f45de26543a5490bca627087236912469a0b448","fa77968128d9c92ee1010f337ad4717eff15db5ed3c049b3411e0315eaa4593b"],["32d31c222f8f6f0ef86f7c98d3a3335ead5bcd32abdd94289fe4d3091aa824bf","5f3032f5892156e39ccd3d7915b9e1da2e6dac9e6f26e961118d14b8462e1661"],["7461f371914ab32671045a155d9831ea8793d77cd59592c4340f86cbc18347b5","8ec0ba238b96bec0cbdddcae0aa442542eee1ff50c986ea6b39847b3cc092ff6"],["ee079adb1df1860074356a25aa38206a6d716b2c3e67453d287698bad7b2b2d6","8dc2412aafe3be5c4c5f37e0ecc5f9f6a446989af04c4e25ebaac479ec1c8c1e"],["16ec93e447ec83f0467b18302ee620f7e65de331874c9dc72bfd8616ba9da6b5","5e4631150e62fb40d0e8c2a7ca5804a39d58186a50e497139626778e25b0674d"],["eaa5f980c245f6f038978290afa70b6bd8855897f98b6aa485b96065d537bd99","f65f5d3e292c2e0819a528391c994624d784869d7e6ea67fb18041024edc07dc"],["78c9407544ac132692ee1910a02439958ae04877151342ea96c4b6b35a49f51","f3e0319169eb9b85d5404795539a5e68fa1fbd583c064d2462b675f194a3ddb4"],["494f4be219a1a77016dcd838431aea0001cdc8ae7a6fc688726578d9702857a5","42242a969283a5f339ba7f075e36ba2af925ce30d767ed6e55f4b031880d562c"],["a598a8030da6d86c6bc7f2f5144ea549d28211ea58faa70ebf4c1e665c1fe9b5","204b5d6f84822c307e4b4a7140737aec23fc63b65b35f86a10026dbd2d864e6b"],["c41916365abb2b5d09192f5f2dbeafec208f020f12570a184dbadc3e58595997","4f14351d0087efa49d245b328984989d5caf9450f34bfc0ed16e96b58fa9913"],["841d6063a586fa475a724604da03bc5b92a2e0d2e0a36acfe4c73a5514742881","73867f59c0659e81904f9a1c7543698e62562d6744c169ce7a36de01a8d6154"],["5e95bb399a6971d376026947f89bde2f282b33810928be4ded112ac4d70e20d5","39f23f366809085beebfc71181313775a99c9aed7d8ba38b161384c746012865"],["36e4641a53948fd476c39f8a99fd974e5ec07564b5315d8bf99471bca0ef2f66","d2424b1b1abe4eb8164227b085c9aa9456ea13493fd563e06fd51cf5694c78fc"],["336581ea7bfbbb290c191a2f507a41cf5643842170e914faeab27c2c579f726","ead12168595fe1be99252129b6e56b3391f7ab1410cd1e0ef3dcdcabd2fda224"],["8ab89816dadfd6b6a1f2634fcf00ec8403781025ed6890c4849742706bd43ede","6fdcef09f2f6d0a044e654aef624136f503d459c3e89845858a47a9129cdd24e"],["1e33f1a746c9c5778133344d9299fcaa20b0938e8acff2544bb40284b8c5fb94","60660257dd11b3aa9c8ed618d24edff2306d320f1d03010e33a7d2057f3b3b6"],["85b7c1dcb3cec1b7ee7f30ded79dd20a0ed1f4cc18cbcfcfa410361fd8f08f31","3d98a9cdd026dd43f39048f25a8847f4fcafad1895d7a633c6fed3c35e999511"],["29df9fbd8d9e46509275f4b125d6d45d7fbe9a3b878a7af872a2800661ac5f51","b4c4fe99c775a606e2d8862179139ffda61dc861c019e55cd2876eb2a27d84b"],["a0b1cae06b0a847a3fea6e671aaf8adfdfe58ca2f768105c8082b2e449fce252","ae434102edde0958ec4b19d917a6a28e6b72da1834aff0e650f049503a296cf2"],["4e8ceafb9b3e9a136dc7ff67e840295b499dfb3b2133e4ba113f2e4c0e121e5","cf2174118c8b6d7a4b48f6d534ce5c79422c086a63460502b827ce62a326683c"],["d24a44e047e19b6f5afb81c7ca2f69080a5076689a010919f42725c2b789a33b","6fb8d5591b466f8fc63db50f1c0f1c69013f996887b8244d2cdec417afea8fa3"],["ea01606a7a6c9cdd249fdfcfacb99584001edd28abbab77b5104e98e8e3b35d4","322af4908c7312b0cfbfe369f7a7b3cdb7d4494bc2823700cfd652188a3ea98d"],["af8addbf2b661c8a6c6328655eb96651252007d8c5ea31be4ad196de8ce2131f","6749e67c029b85f52a034eafd096836b2520818680e26ac8f3dfbcdb71749700"],["e3ae1974566ca06cc516d47e0fb165a674a3dabcfca15e722f0e3450f45889","2aeabe7e4531510116217f07bf4d07300de97e4874f81f533420a72eeb0bd6a4"],["591ee355313d99721cf6993ffed1e3e301993ff3ed258802075ea8ced397e246","b0ea558a113c30bea60fc4775460c7901ff0b053d25ca2bdeee98f1a4be5d196"],["11396d55fda54c49f19aa97318d8da61fa8584e47b084945077cf03255b52984","998c74a8cd45ac01289d5833a7beb4744ff536b01b257be4c5767bea93ea57a4"],["3c5d2a1ba39c5a1790000738c9e0c40b8dcdfd5468754b6405540157e017aa7a","b2284279995a34e2f9d4de7396fc18b80f9b8b9fdd270f6661f79ca4c81bd257"],["cc8704b8a60a0defa3a99a7299f2e9c3fbc395afb04ac078425ef8a1793cc030","bdd46039feed17881d1e0862db347f8cf395b74fc4bcdc4e940b74e3ac1f1b13"],["c533e4f7ea8555aacd9777ac5cad29b97dd4defccc53ee7ea204119b2889b197","6f0a256bc5efdf429a2fb6242f1a43a2d9b925bb4a4b3a26bb8e0f45eb596096"],["c14f8f2ccb27d6f109f6d08d03cc96a69ba8c34eec07bbcf566d48e33da6593","c359d6923bb398f7fd4473e16fe1c28475b740dd098075e6c0e8649113dc3a38"],["a6cbc3046bc6a450bac24789fa17115a4c9739ed75f8f21ce441f72e0b90e6ef","21ae7f4680e889bb130619e2c0f95a360ceb573c70603139862afd617fa9b9f"],["347d6d9a02c48927ebfb86c1359b1caf130a3c0267d11ce6344b39f99d43cc38","60ea7f61a353524d1c987f6ecec92f086d565ab687870cb12689ff1e31c74448"],["da6545d2181db8d983f7dcb375ef5866d47c67b1bf31c8cf855ef7437b72656a","49b96715ab6878a79e78f07ce5680c5d6673051b4935bd897fea824b77dc208a"],["c40747cc9d012cb1a13b8148309c6de7ec25d6945d657146b9d5994b8feb1111","5ca560753be2a12fc6de6caf2cb489565db936156b9514e1bb5e83037e0fa2d4"],["4e42c8ec82c99798ccf3a610be870e78338c7f713348bd34c8203ef4037f3502","7571d74ee5e0fb92a7a8b33a07783341a5492144cc54bcc40a94473693606437"],["3775ab7089bc6af823aba2e1af70b236d251cadb0c86743287522a1b3b0dedea","be52d107bcfa09d8bcb9736a828cfa7fac8db17bf7a76a2c42ad961409018cf7"],["cee31cbf7e34ec379d94fb814d3d775ad954595d1314ba8846959e3e82f74e26","8fd64a14c06b589c26b947ae2bcf6bfa0149ef0be14ed4d80f448a01c43b1c6d"],["b4f9eaea09b6917619f6ea6a4eb5464efddb58fd45b1ebefcdc1a01d08b47986","39e5c9925b5a54b07433a4f18c61726f8bb131c012ca542eb24a8ac07200682a"],["d4263dfc3d2df923a0179a48966d30ce84e2515afc3dccc1b77907792ebcc60e","62dfaf07a0f78feb30e30d6295853ce189e127760ad6cf7fae164e122a208d54"],["48457524820fa65a4f8d35eb6930857c0032acc0a4a2de422233eeda897612c4","25a748ab367979d98733c38a1fa1c2e7dc6cc07db2d60a9ae7a76aaa49bd0f77"],["dfeeef1881101f2cb11644f3a2afdfc2045e19919152923f367a1767c11cceda","ecfb7056cf1de042f9420bab396793c0c390bde74b4bbdff16a83ae09a9a7517"],["6d7ef6b17543f8373c573f44e1f389835d89bcbc6062ced36c82df83b8fae859","cd450ec335438986dfefa10c57fea9bcc521a0959b2d80bbf74b190dca712d10"],["e75605d59102a5a2684500d3b991f2e3f3c88b93225547035af25af66e04541f","f5c54754a8f71ee540b9b48728473e314f729ac5308b06938360990e2bfad125"],["eb98660f4c4dfaa06a2be453d5020bc99a0c2e60abe388457dd43fefb1ed620c","6cb9a8876d9cb8520609af3add26cd20a0a7cd8a9411131ce85f44100099223e"],["13e87b027d8514d35939f2e6892b19922154596941888336dc3563e3b8dba942","fef5a3c68059a6dec5d624114bf1e91aac2b9da568d6abeb2570d55646b8adf1"],["ee163026e9fd6fe017c38f06a5be6fc125424b371ce2708e7bf4491691e5764a","1acb250f255dd61c43d94ccc670d0f58f49ae3fa15b96623e5430da0ad6c62b2"],["b268f5ef9ad51e4d78de3a750c2dc89b1e626d43505867999932e5db33af3d80","5f310d4b3c99b9ebb19f77d41c1dee018cf0d34fd4191614003e945a1216e423"],["ff07f3118a9df035e9fad85eb6c7bfe42b02f01ca99ceea3bf7ffdba93c4750d","438136d603e858a3a5c440c38eccbaddc1d2942114e2eddd4740d098ced1f0d8"],["8d8b9855c7c052a34146fd20ffb658bea4b9f69e0d825ebec16e8c3ce2b526a1","cdb559eedc2d79f926baf44fb84ea4d44bcf50fee51d7ceb30e2e7f463036758"],["52db0b5384dfbf05bfa9d472d7ae26dfe4b851ceca91b1eba54263180da32b63","c3b997d050ee5d423ebaf66a6db9f57b3180c902875679de924b69d84a7b375"],["e62f9490d3d51da6395efd24e80919cc7d0f29c3f3fa48c6fff543becbd43352","6d89ad7ba4876b0b22c2ca280c682862f342c8591f1daf5170e07bfd9ccafa7d"],["7f30ea2476b399b4957509c88f77d0191afa2ff5cb7b14fd6d8e7d65aaab1193","ca5ef7d4b231c94c3b15389a5f6311e9daff7bb67b103e9880ef4bff637acaec"],["5098ff1e1d9f14fb46a210fada6c903fef0fb7b4a1dd1d9ac60a0361800b7a00","9731141d81fc8f8084d37c6e7542006b3ee1b40d60dfe5362a5b132fd17ddc0"],["32b78c7de9ee512a72895be6b9cbefa6e2f3c4ccce445c96b9f2c81e2778ad58","ee1849f513df71e32efc3896ee28260c73bb80547ae2275ba497237794c8753c"],["e2cb74fddc8e9fbcd076eef2a7c72b0ce37d50f08269dfc074b581550547a4f7","d3aa2ed71c9dd2247a62df062736eb0baddea9e36122d2be8641abcb005cc4a4"],["8438447566d4d7bedadc299496ab357426009a35f235cb141be0d99cd10ae3a8","c4e1020916980a4da5d01ac5e6ad330734ef0d7906631c4f2390426b2edd791f"],["4162d488b89402039b584c6fc6c308870587d9c46f660b878ab65c82c711d67e","67163e903236289f776f22c25fb8a3afc1732f2b84b4e95dbda47ae5a0852649"],["3fad3fa84caf0f34f0f89bfd2dcf54fc175d767aec3e50684f3ba4a4bf5f683d","cd1bc7cb6cc407bb2f0ca647c718a730cf71872e7d0d2a53fa20efcdfe61826"],["674f2600a3007a00568c1a7ce05d0816c1fb84bf1370798f1c69532faeb1a86b","299d21f9413f33b3edf43b257004580b70db57da0b182259e09eecc69e0d38a5"],["d32f4da54ade74abb81b815ad1fb3b263d82d6c692714bcff87d29bd5ee9f08f","f9429e738b8e53b968e99016c059707782e14f4535359d582fc416910b3eea87"],["30e4e670435385556e593657135845d36fbb6931f72b08cb1ed954f1e3ce3ff6","462f9bce619898638499350113bbc9b10a878d35da70740dc695a559eb88db7b"],["be2062003c51cc3004682904330e4dee7f3dcd10b01e580bf1971b04d4cad297","62188bc49d61e5428573d48a74e1c655b1c61090905682a0d5558ed72dccb9bc"],["93144423ace3451ed29e0fb9ac2af211cb6e84a601df5993c419859fff5df04a","7c10dfb164c3425f5c71a3f9d7992038f1065224f72bb9d1d902a6d13037b47c"],["b015f8044f5fcbdcf21ca26d6c34fb8197829205c7b7d2a7cb66418c157b112c","ab8c1e086d04e813744a655b2df8d5f83b3cdc6faa3088c1d3aea1454e3a1d5f"],["d5e9e1da649d97d89e4868117a465a3a4f8a18de57a140d36b3f2af341a21b52","4cb04437f391ed73111a13cc1d4dd0db1693465c2240480d8955e8592f27447a"],["d3ae41047dd7ca065dbf8ed77b992439983005cd72e16d6f996a5316d36966bb","bd1aeb21ad22ebb22a10f0303417c6d964f8cdd7df0aca614b10dc14d125ac46"],["463e2763d885f958fc66cdd22800f0a487197d0a82e377b49f80af87c897b065","bfefacdb0e5d0fd7df3a311a94de062b26b80c61fbc97508b79992671ef7ca7f"],["7985fdfd127c0567c6f53ec1bb63ec3158e597c40bfe747c83cddfc910641917","603c12daf3d9862ef2b25fe1de289aed24ed291e0ec6708703a5bd567f32ed03"],["74a1ad6b5f76e39db2dd249410eac7f99e74c59cb83d2d0ed5ff1543da7703e9","cc6157ef18c9c63cd6193d83631bbea0093e0968942e8c33d5737fd790e0db08"],["30682a50703375f602d416664ba19b7fc9bab42c72747463a71d0896b22f6da3","553e04f6b018b4fa6c8f39e7f311d3176290d0e0f19ca73f17714d9977a22ff8"],["9e2158f0d7c0d5f26c3791efefa79597654e7a2b2464f52b1ee6c1347769ef57","712fcdd1b9053f09003a3481fa7762e9ffd7c8ef35a38509e2fbf2629008373"],["176e26989a43c9cfeba4029c202538c28172e566e3c4fce7322857f3be327d66","ed8cc9d04b29eb877d270b4878dc43c19aefd31f4eee09ee7b47834c1fa4b1c3"],["75d46efea3771e6e68abb89a13ad747ecf1892393dfc4f1b7004788c50374da8","9852390a99507679fd0b86fd2b39a868d7efc22151346e1a3ca4726586a6bed8"],["809a20c67d64900ffb698c4c825f6d5f2310fb0451c869345b7319f645605721","9e994980d9917e22b76b061927fa04143d096ccc54963e6a5ebfa5f3f8e286c1"],["1b38903a43f7f114ed4500b4eac7083fdefece1cf29c63528d563446f972c180","4036edc931a60ae889353f77fd53de4a2708b26b6f5da72ad3394119daf408f9"]]}}},function(e,t,r){"use strict";var i=r(276),n=r(496),f=r(279),o=r(325),a=r(322),s=f.assert,c=r(497),h=r(498);function d(e){if(!(this instanceof d))return new d(e);"string"==typeof e&&(s(Object.prototype.hasOwnProperty.call(o,e),"Unknown curve "+e),e=o[e]),e instanceof o.PresetCurve&&(e={curve:e}),this.curve=e.curve.curve,this.n=this.curve.n,this.nh=this.n.ushrn(1),this.g=this.curve.g,this.g=e.curve.g,this.g.precompute(e.curve.n.bitLength()+1),this.hash=e.hash||e.curve.hash}e.exports=d,d.prototype.keyPair=function(e){return new c(this,e)},d.prototype.keyFromPrivate=function(e,t){return c.fromPrivate(this,e,t)},d.prototype.keyFromPublic=function(e,t){return c.fromPublic(this,e,t)},d.prototype.genKeyPair=function(e){e||(e={});for(var t=new n({hash:this.hash,pers:e.pers,persEnc:e.persEnc||"utf8",entropy:e.entropy||a(this.hash.hmacStrength),entropyEnc:e.entropy&&e.entropyEnc||"utf8",nonce:this.n.toArray()}),r=this.n.byteLength(),f=this.n.sub(new i(2));;){var o=new i(t.generate(r));if(!(o.cmp(f)>0))return o.iaddn(1),this.keyFromPrivate(o)}},d.prototype._truncateToN=function(e,t){var r=8*e.byteLength()-this.n.bitLength();return r>0&&(e=e.ushrn(r)),!t&&e.cmp(this.n)>=0?e.sub(this.n):e},d.prototype.sign=function(e,t,r,f){"object"==typeof r&&(f=r,r=null),f||(f={}),t=this.keyFromPrivate(t,r),e=this._truncateToN(new i(e,16));for(var o=this.n.byteLength(),a=t.getPrivate().toArray("be",o),s=e.toArray("be",o),c=new n({hash:this.hash,entropy:a,nonce:s,pers:f.pers,persEnc:f.persEnc||"utf8"}),d=this.n.sub(new i(1)),u=0;;u++){var l=f.k?f.k(u):new i(c.generate(this.n.byteLength()));if(!((l=this._truncateToN(l,!0)).cmpn(1)<=0||l.cmp(d)>=0)){var p=this.g.mul(l);if(!p.isInfinity()){var b=p.getX(),y=b.umod(this.n);if(0!==y.cmpn(0)){var g=l.invm(this.n).mul(y.mul(t.getPrivate()).iadd(e));if(0!==(g=g.umod(this.n)).cmpn(0)){var m=(p.getY().isOdd()?1:0)|(0!==b.cmp(y)?2:0);return f.canonical&&g.cmp(this.nh)>0&&(g=this.n.sub(g),m^=1),new h({r:y,s:g,recoveryParam:m})}}}}}},d.prototype.verify=function(e,t,r,n){e=this._truncateToN(new i(e,16)),r=this.keyFromPublic(r,n);var f=(t=new h(t,"hex")).r,o=t.s;if(f.cmpn(1)<0||f.cmp(this.n)>=0)return!1;if(o.cmpn(1)<0||o.cmp(this.n)>=0)return!1;var a,s=o.invm(this.n),c=s.mul(e).umod(this.n),d=s.mul(f).umod(this.n);return this.curve._maxwellTrick?!(a=this.g.jmulAdd(c,r.getPublic(),d)).isInfinity()&&a.eqXToP(f):!(a=this.g.mulAdd(c,r.getPublic(),d)).isInfinity()&&0===a.getX().umod(this.n).cmp(f)},d.prototype.recoverPubKey=function(e,t,r,n){s((3&r)===r,"The recovery param is more than two bits"),t=new h(t,n);var f=this.n,o=new i(e),a=t.r,c=t.s,d=1&r,u=r>>1;if(a.cmp(this.curve.p.umod(this.curve.n))>=0&&u)throw new Error("Unable to find sencond key candinate");a=u?this.curve.pointFromX(a.add(this.curve.n),d):this.curve.pointFromX(a,d);var l=t.r.invm(f),p=f.sub(o).mul(l).umod(f),b=c.mul(l).umod(f);return this.g.mulAdd(p,a,b)},d.prototype.getKeyRecoveryParam=function(e,t,r,i){if(null!==(t=new h(t,i)).recoveryParam)return t.recoveryParam;for(var n=0;n<4;n++){var f;try{f=this.recoverPubKey(e,t,n)}catch(e){continue}if(f.eq(r))return n}throw new Error("Unable to find valid recovery factor")}},function(e,t,r){"use strict";var i=r(326),n=r(396),f=r(278);function o(e){if(!(this instanceof o))return new o(e);this.hash=e.hash,this.predResist=!!e.predResist,this.outLen=this.hash.outSize,this.minEntropy=e.minEntropy||this.hash.hmacStrength,this._reseed=null,this.reseedInterval=null,this.K=null,this.V=null;var t=n.toArray(e.entropy,e.entropyEnc||"hex"),r=n.toArray(e.nonce,e.nonceEnc||"hex"),i=n.toArray(e.pers,e.persEnc||"hex");f(t.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._init(t,r,i)}e.exports=o,o.prototype._init=function(e,t,r){var i=e.concat(t).concat(r);this.K=new Array(this.outLen/8),this.V=new Array(this.outLen/8);for(var n=0;n<this.V.length;n++)this.K[n]=0,this.V[n]=1;this._update(i),this._reseed=1,this.reseedInterval=281474976710656},o.prototype._hmac=function(){return new i.hmac(this.hash,this.K)},o.prototype._update=function(e){var t=this._hmac().update(this.V).update([0]);e&&(t=t.update(e)),this.K=t.digest(),this.V=this._hmac().update(this.V).digest(),e&&(this.K=this._hmac().update(this.V).update([1]).update(e).digest(),this.V=this._hmac().update(this.V).digest())},o.prototype.reseed=function(e,t,r,i){"string"!=typeof t&&(i=r,r=t,t=null),e=n.toArray(e,t),r=n.toArray(r,i),f(e.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._update(e.concat(r||[])),this._reseed=1},o.prototype.generate=function(e,t,r,i){if(this._reseed>this.reseedInterval)throw new Error("Reseed is required");"string"!=typeof t&&(i=r,r=t,t=null),r&&(r=n.toArray(r,i||"hex"),this._update(r));for(var f=[];f.length<e;)this.V=this._hmac().update(this.V).digest(),f=f.concat(this.V);var o=f.slice(0,e);return this._update(r),this._reseed++,n.encode(o,t)}},function(e,t,r){"use strict";var i=r(276),n=r(279).assert;function f(e,t){this.ec=e,this.priv=null,this.pub=null,t.priv&&this._importPrivate(t.priv,t.privEnc),t.pub&&this._importPublic(t.pub,t.pubEnc)}e.exports=f,f.fromPublic=function(e,t,r){return t instanceof f?t:new f(e,{pub:t,pubEnc:r})},f.fromPrivate=function(e,t,r){return t instanceof f?t:new f(e,{priv:t,privEnc:r})},f.prototype.validate=function(){var e=this.getPublic();return e.isInfinity()?{result:!1,reason:"Invalid public key"}:e.validate()?e.mul(this.ec.curve.n).isInfinity()?{result:!0,reason:null}:{result:!1,reason:"Public key * N != O"}:{result:!1,reason:"Public key is not a point"}},f.prototype.getPublic=function(e,t){return"string"==typeof e&&(t=e,e=null),this.pub||(this.pub=this.ec.g.mul(this.priv)),t?this.pub.encode(t,e):this.pub},f.prototype.getPrivate=function(e){return"hex"===e?this.priv.toString(16,2):this.priv},f.prototype._importPrivate=function(e,t){this.priv=new i(e,t||16),this.priv=this.priv.umod(this.ec.curve.n)},f.prototype._importPublic=function(e,t){if(e.x||e.y)return"mont"===this.ec.curve.type?n(e.x,"Need x coordinate"):"short"!==this.ec.curve.type&&"edwards"!==this.ec.curve.type||n(e.x&&e.y,"Need both x and y coordinate"),void(this.pub=this.ec.curve.point(e.x,e.y));this.pub=this.ec.curve.decodePoint(e,t)},f.prototype.derive=function(e){return e.validate()||n(e.validate(),"public point not validated"),e.mul(this.priv).getX()},f.prototype.sign=function(e,t,r){return this.ec.sign(e,this,t,r)},f.prototype.verify=function(e,t){return this.ec.verify(e,t,this)},f.prototype.inspect=function(){return"<Key priv: "+(this.priv&&this.priv.toString(16,2))+" pub: "+(this.pub&&this.pub.inspect())+" >"}},function(e,t,r){"use strict";var i=r(276),n=r(279),f=n.assert;function o(e,t){if(e instanceof o)return e;this._importDER(e,t)||(f(e.r&&e.s,"Signature without r or s"),this.r=new i(e.r,16),this.s=new i(e.s,16),void 0===e.recoveryParam?this.recoveryParam=null:this.recoveryParam=e.recoveryParam)}function a(){this.place=0}function s(e,t){var r=e[t.place++];if(!(128&r))return r;var i=15&r;if(0===i||i>4)return!1;for(var n=0,f=0,o=t.place;f<i;f++,o++)n<<=8,n|=e[o],n>>>=0;return!(n<=127)&&(t.place=o,n)}function c(e){for(var t=0,r=e.length-1;!e[t]&&!(128&e[t+1])&&t<r;)t++;return 0===t?e:e.slice(t)}function h(e,t){if(t<128)e.push(t);else{var r=1+(Math.log(t)/Math.LN2>>>3);for(e.push(128|r);--r;)e.push(t>>>(r<<3)&255);e.push(t)}}e.exports=o,o.prototype._importDER=function(e,t){e=n.toArray(e,t);var r=new a;if(48!==e[r.place++])return!1;var f=s(e,r);if(!1===f)return!1;if(f+r.place!==e.length)return!1;if(2!==e[r.place++])return!1;var o=s(e,r);if(!1===o)return!1;var c=e.slice(r.place,o+r.place);if(r.place+=o,2!==e[r.place++])return!1;var h=s(e,r);if(!1===h)return!1;if(e.length!==h+r.place)return!1;var d=e.slice(r.place,h+r.place);if(0===c[0]){if(!(128&c[1]))return!1;c=c.slice(1)}if(0===d[0]){if(!(128&d[1]))return!1;d=d.slice(1)}return this.r=new i(c),this.s=new i(d),this.recoveryParam=null,!0},o.prototype.toDER=function(e){var t=this.r.toArray(),r=this.s.toArray();for(128&t[0]&&(t=[0].concat(t)),128&r[0]&&(r=[0].concat(r)),t=c(t),r=c(r);!(r[0]||128&r[1]);)r=r.slice(1);var i=[2];h(i,t.length),(i=i.concat(t)).push(2),h(i,r.length);var f=i.concat(r),o=[48];return h(o,f.length),o=o.concat(f),n.encode(o,e)}},function(e,t,r){"use strict";var i=r(326),n=r(325),f=r(279),o=f.assert,a=f.parseBytes,s=r(500),c=r(501);function h(e){if(o("ed25519"===e,"only tested with ed25519 so far"),!(this instanceof h))return new h(e);e=n[e].curve,this.curve=e,this.g=e.g,this.g.precompute(e.n.bitLength()+1),this.pointClass=e.point().constructor,this.encodingLength=Math.ceil(e.n.bitLength()/8),this.hash=i.sha512}e.exports=h,h.prototype.sign=function(e,t){e=a(e);var r=this.keyFromSecret(t),i=this.hashInt(r.messagePrefix(),e),n=this.g.mul(i),f=this.encodePoint(n),o=this.hashInt(f,r.pubBytes(),e).mul(r.priv()),s=i.add(o).umod(this.curve.n);return this.makeSignature({R:n,S:s,Rencoded:f})},h.prototype.verify=function(e,t,r){e=a(e),t=this.makeSignature(t);var i=this.keyFromPublic(r),n=this.hashInt(t.Rencoded(),i.pubBytes(),e),f=this.g.mul(t.S());return t.R().add(i.pub().mul(n)).eq(f)},h.prototype.hashInt=function(){for(var e=this.hash(),t=0;t<arguments.length;t++)e.update(arguments[t]);return f.intFromLE(e.digest()).umod(this.curve.n)},h.prototype.keyFromPublic=function(e){return s.fromPublic(this,e)},h.prototype.keyFromSecret=function(e){return s.fromSecret(this,e)},h.prototype.makeSignature=function(e){return e instanceof c?e:new c(this,e)},h.prototype.encodePoint=function(e){var t=e.getY().toArray("le",this.encodingLength);return t[this.encodingLength-1]|=e.getX().isOdd()?128:0,t},h.prototype.decodePoint=function(e){var t=(e=f.parseBytes(e)).length-1,r=e.slice(0,t).concat(-129&e[t]),i=0!=(128&e[t]),n=f.intFromLE(r);return this.curve.pointFromY(n,i)},h.prototype.encodeInt=function(e){return e.toArray("le",this.encodingLength)},h.prototype.decodeInt=function(e){return f.intFromLE(e)},h.prototype.isPoint=function(e){return e instanceof this.pointClass}},function(e,t,r){"use strict";var i=r(279),n=i.assert,f=i.parseBytes,o=i.cachedProperty;function a(e,t){this.eddsa=e,this._secret=f(t.secret),e.isPoint(t.pub)?this._pub=t.pub:this._pubBytes=f(t.pub)}a.fromPublic=function(e,t){return t instanceof a?t:new a(e,{pub:t})},a.fromSecret=function(e,t){return t instanceof a?t:new a(e,{secret:t})},a.prototype.secret=function(){return this._secret},o(a,"pubBytes",(function(){return this.eddsa.encodePoint(this.pub())})),o(a,"pub",(function(){return this._pubBytes?this.eddsa.decodePoint(this._pubBytes):this.eddsa.g.mul(this.priv())})),o(a,"privBytes",(function(){var e=this.eddsa,t=this.hash(),r=e.encodingLength-1,i=t.slice(0,e.encodingLength);return i[0]&=248,i[r]&=127,i[r]|=64,i})),o(a,"priv",(function(){return this.eddsa.decodeInt(this.privBytes())})),o(a,"hash",(function(){return this.eddsa.hash().update(this.secret()).digest()})),o(a,"messagePrefix",(function(){return this.hash().slice(this.eddsa.encodingLength)})),a.prototype.sign=function(e){return n(this._secret,"KeyPair can only verify"),this.eddsa.sign(e,this)},a.prototype.verify=function(e,t){return this.eddsa.verify(e,t,this)},a.prototype.getSecret=function(e){return n(this._secret,"KeyPair is public only"),i.encode(this.secret(),e)},a.prototype.getPublic=function(e){return i.encode(this.pubBytes(),e)},e.exports=a},function(e,t,r){"use strict";var i=r(276),n=r(279),f=n.assert,o=n.cachedProperty,a=n.parseBytes;function s(e,t){this.eddsa=e,"object"!=typeof t&&(t=a(t)),Array.isArray(t)&&(t={R:t.slice(0,e.encodingLength),S:t.slice(e.encodingLength)}),f(t.R&&t.S,"Signature without R or S"),e.isPoint(t.R)&&(this._R=t.R),t.S instanceof i&&(this._S=t.S),this._Rencoded=Array.isArray(t.R)?t.R:t.Rencoded,this._Sencoded=Array.isArray(t.S)?t.S:t.Sencoded}o(s,"S",(function(){return this.eddsa.decodeInt(this.Sencoded())})),o(s,"R",(function(){return this.eddsa.decodePoint(this.Rencoded())})),o(s,"Rencoded",(function(){return this.eddsa.encodePoint(this.R())})),o(s,"Sencoded",(function(){return this.eddsa.encodeInt(this.S())})),s.prototype.toBytes=function(){return this.Rencoded().concat(this.Sencoded())},s.prototype.toHex=function(){return n.encode(this.toBytes(),"hex").toUpperCase()},e.exports=s},function(e,t,r){"use strict";var i=r(401);t.certificate=r(508);var n=i.define("RSAPrivateKey",(function(){this.seq().obj(this.key("version").int(),this.key("modulus").int(),this.key("publicExponent").int(),this.key("privateExponent").int(),this.key("prime1").int(),this.key("prime2").int(),this.key("exponent1").int(),this.key("exponent2").int(),this.key("coefficient").int())}));t.RSAPrivateKey=n;var f=i.define("RSAPublicKey",(function(){this.seq().obj(this.key("modulus").int(),this.key("publicExponent").int())}));t.RSAPublicKey=f;var o=i.define("SubjectPublicKeyInfo",(function(){this.seq().obj(this.key("algorithm").use(a),this.key("subjectPublicKey").bitstr())}));t.PublicKey=o;var a=i.define("AlgorithmIdentifier",(function(){this.seq().obj(this.key("algorithm").objid(),this.key("none").null_().optional(),this.key("curve").objid().optional(),this.key("params").seq().obj(this.key("p").int(),this.key("q").int(),this.key("g").int()).optional())})),s=i.define("PrivateKeyInfo",(function(){this.seq().obj(this.key("version").int(),this.key("algorithm").use(a),this.key("subjectPrivateKey").octstr())}));t.PrivateKey=s;var c=i.define("EncryptedPrivateKeyInfo",(function(){this.seq().obj(this.key("algorithm").seq().obj(this.key("id").objid(),this.key("decrypt").seq().obj(this.key("kde").seq().obj(this.key("id").objid(),this.key("kdeparams").seq().obj(this.key("salt").octstr(),this.key("iters").int())),this.key("cipher").seq().obj(this.key("algo").objid(),this.key("iv").octstr()))),this.key("subjectPrivateKey").octstr())}));t.EncryptedPrivateKey=c;var h=i.define("DSAPrivateKey",(function(){this.seq().obj(this.key("version").int(),this.key("p").int(),this.key("q").int(),this.key("g").int(),this.key("pub_key").int(),this.key("priv_key").int())}));t.DSAPrivateKey=h,t.DSAparam=i.define("DSAparam",(function(){this.int()}));var d=i.define("ECPrivateKey",(function(){this.seq().obj(this.key("version").int(),this.key("privateKey").octstr(),this.key("parameters").optional().explicit(0).use(u),this.key("publicKey").optional().explicit(1).bitstr())}));t.ECPrivateKey=d;var u=i.define("ECParameters",(function(){this.choice({namedCurve:this.objid()})}));t.signature=i.define("signature",(function(){this.seq().obj(this.key("r").int(),this.key("s").int())}))},function(e,t,r){"use strict";const i=r(402),n=r(404),f=r(275);function o(e,t){this.name=e,this.body=t,this.decoders={},this.encoders={}}t.define=function(e,t){return new o(e,t)},o.prototype._createNamed=function(e){const t=this.name;function r(e){this._initNamed(e,t)}return f(r,e),r.prototype._initNamed=function(t,r){e.call(this,t,r)},new r(this)},o.prototype._getDecoder=function(e){return e=e||"der",this.decoders.hasOwnProperty(e)||(this.decoders[e]=this._createNamed(n[e])),this.decoders[e]},o.prototype.decode=function(e,t,r){return this._getDecoder(t).decode(e,r)},o.prototype._getEncoder=function(e){return e=e||"der",this.encoders.hasOwnProperty(e)||(this.encoders[e]=this._createNamed(i[e])),this.encoders[e]},o.prototype.encode=function(e,t,r){return this._getEncoder(t).encode(e,r)}},function(e,t,r){"use strict";const i=r(275),n=r(403);function f(e){n.call(this,e),this.enc="pem"}i(f,n),e.exports=f,f.prototype.encode=function(e,t){const r=n.prototype.encode.call(this,e).toString("base64"),i=["-----BEGIN "+t.label+"-----"];for(let e=0;e<r.length;e+=64)i.push(r.slice(e,e+64));return i.push("-----END "+t.label+"-----"),i.join("\n")}},function(e,t,r){"use strict";const i=r(275),n=r(327).Buffer,f=r(405);function o(e){f.call(this,e),this.enc="pem"}i(o,f),e.exports=o,o.prototype.decode=function(e,t){const r=e.toString().split(/[\r\n]+/g),i=t.label.toUpperCase(),o=/^-----(BEGIN|END) ([^-]+)-----$/;let a=-1,s=-1;for(let e=0;e<r.length;e++){const t=r[e].match(o);if(null!==t&&t[2]===i){if(-1!==a){if("END"!==t[1])break;s=e;break}if("BEGIN"!==t[1])break;a=e}}if(-1===a||-1===s)throw new Error("PEM section not found for: "+i);const c=r.slice(a+1,s).join("");c.replace(/[^a-z0-9+/=]+/gi,"");const h=n.from(c,"base64");return f.prototype.decode.call(this,h,t)}},function(e,t,r){"use strict";const i=t;i.Reporter=r(329).Reporter,i.DecoderBuffer=r(297).DecoderBuffer,i.EncoderBuffer=r(297).EncoderBuffer,i.Node=r(328)},function(e,t,r){"use strict";const i=t;i._reverse=function(e){const t={};return Object.keys(e).forEach((function(r){(0|r)==r&&(r|=0);const i=e[r];t[i]=r})),t},i.der=r(330)},function(e,t,r){"use strict";var i=r(401),n=i.define("Time",(function(){this.choice({utcTime:this.utctime(),generalTime:this.gentime()})})),f=i.define("AttributeTypeValue",(function(){this.seq().obj(this.key("type").objid(),this.key("value").any())})),o=i.define("AlgorithmIdentifier",(function(){this.seq().obj(this.key("algorithm").objid(),this.key("parameters").optional(),this.key("curve").objid().optional())})),a=i.define("SubjectPublicKeyInfo",(function(){this.seq().obj(this.key("algorithm").use(o),this.key("subjectPublicKey").bitstr())})),s=i.define("RelativeDistinguishedName",(function(){this.setof(f)})),c=i.define("RDNSequence",(function(){this.seqof(s)})),h=i.define("Name",(function(){this.choice({rdnSequence:this.use(c)})})),d=i.define("Validity",(function(){this.seq().obj(this.key("notBefore").use(n),this.key("notAfter").use(n))})),u=i.define("Extension",(function(){this.seq().obj(this.key("extnID").objid(),this.key("critical").bool().def(!1),this.key("extnValue").octstr())})),l=i.define("TBSCertificate",(function(){this.seq().obj(this.key("version").explicit(0).int().optional(),this.key("serialNumber").int(),this.key("signature").use(o),this.key("issuer").use(h),this.key("validity").use(d),this.key("subject").use(h),this.key("subjectPublicKeyInfo").use(a),this.key("issuerUniqueID").implicit(1).bitstr().optional(),this.key("subjectUniqueID").implicit(2).bitstr().optional(),this.key("extensions").explicit(3).seqof(u).optional())})),p=i.define("X509Certificate",(function(){this.seq().obj(this.key("tbsCertificate").use(l),this.key("signatureAlgorithm").use(o),this.key("signatureValue").bitstr())}));e.exports=p},function(e){e.exports=JSON.parse('{"2.16.840.1.101.3.4.1.1":"aes-128-ecb","2.16.840.1.101.3.4.1.2":"aes-128-cbc","2.16.840.1.101.3.4.1.3":"aes-128-ofb","2.16.840.1.101.3.4.1.4":"aes-128-cfb","2.16.840.1.101.3.4.1.21":"aes-192-ecb","2.16.840.1.101.3.4.1.22":"aes-192-cbc","2.16.840.1.101.3.4.1.23":"aes-192-ofb","2.16.840.1.101.3.4.1.24":"aes-192-cfb","2.16.840.1.101.3.4.1.41":"aes-256-ecb","2.16.840.1.101.3.4.1.42":"aes-256-cbc","2.16.840.1.101.3.4.1.43":"aes-256-ofb","2.16.840.1.101.3.4.1.44":"aes-256-cfb"}')},function(e,t,r){var i=/Proc-Type: 4,ENCRYPTED[\n\r]+DEK-Info: AES-((?:128)|(?:192)|(?:256))-CBC,([0-9A-H]+)[\n\r]+([0-9A-z\n\r+/=]+)[\n\r]+/m,n=/^-----BEGIN ((?:.*? KEY)|CERTIFICATE)-----/m,f=/^-----BEGIN ((?:.*? KEY)|CERTIFICATE)-----([0-9A-z\n\r+/=]+)-----END \1-----$/m,o=r(300),a=r(320),s=r(274).Buffer;e.exports=function(e,t){var r,c=e.toString(),h=c.match(i);if(h){var d="aes"+h[1],u=s.from(h[2],"hex"),l=s.from(h[3].replace(/[\r\n]/g,""),"base64"),p=o(t,u.slice(0,8),parseInt(h[1],10)).key,b=[],y=a.createDecipheriv(d,p,u);b.push(y.update(l)),b.push(y.final()),r=s.concat(b)}else{var g=c.match(f);r=s.from(g[2].replace(/[\r\n]/g,""),"base64")}return{tag:c.match(n)[1],data:r}}},function(e,t,r){"use strict";var i=r(274).Buffer,n=r(276),f=r(324).ec,o=r(302),a=r(406);function s(e,t){if(e.cmpn(0)<=0)throw new Error("invalid sig");if(e.cmp(t)>=0)throw new Error("invalid sig")}e.exports=function(e,t,r,c,h){var d=o(r);if("ec"===d.type){if("ecdsa"!==c&&"ecdsa/rsa"!==c)throw new Error("wrong public key type");return function(e,t,r){var i=a[r.data.algorithm.curve.join(".")];if(!i)throw new Error("unknown curve "+r.data.algorithm.curve.join("."));var n=new f(i),o=r.data.subjectPrivateKey.data;return n.verify(t,e,o)}(e,t,d)}if("dsa"===d.type){if("dsa"!==c)throw new Error("wrong public key type");return function(e,t,r){var i=r.data.p,f=r.data.q,a=r.data.g,c=r.data.pub_key,h=o.signature.decode(e,"der"),d=h.s,u=h.r;s(d,f),s(u,f);var l=n.mont(i),p=d.invm(f);return 0===a.toRed(l).redPow(new n(t).mul(p).mod(f)).fromRed().mul(c.toRed(l).redPow(u.mul(p).mod(f)).fromRed()).mod(i).mod(f).cmp(u)}(e,t,d)}if("rsa"!==c&&"ecdsa/rsa"!==c)throw new Error("wrong public key type");t=i.concat([h,t]);for(var u=d.modulus.byteLength(),l=[1],p=0;t.length+l.length+2<u;)l.push(255),p+=1;l.push(0);for(var b=-1;++b<t.length;)l.push(t[b]);l=i.from(l);var y=n.mont(d.modulus);e=(e=new n(e).toRed(y)).redPow(new n(d.publicExponent)),e=i.from(e.fromRed().toArray());var g=p<8?1:0;for(u=Math.min(e.length,l.length),e.length!==l.length&&(g=1),b=-1;++b<u;)g|=e[b]^l[b];return 0===g}},function(e,t,r){(function(t){var i=r(324),n=r(276);e.exports=function(e){return new o(e)};var f={secp256k1:{name:"secp256k1",byteLength:32},secp224r1:{name:"p224",byteLength:28},prime256v1:{name:"p256",byteLength:32},prime192v1:{name:"p192",byteLength:24},ed25519:{name:"ed25519",byteLength:32},secp384r1:{name:"p384",byteLength:48},secp521r1:{name:"p521",byteLength:66}};function o(e){this.curveType=f[e],this.curveType||(this.curveType={name:e}),this.curve=new i.ec(this.curveType.name),this.keys=void 0}function a(e,r,i){Array.isArray(e)||(e=e.toArray());var n=new t(e);if(i&&n.length<i){var f=new t(i-n.length);f.fill(0),n=t.concat([f,n])}return r?n.toString(r):n}f.p224=f.secp224r1,f.p256=f.secp256r1=f.prime256v1,f.p192=f.secp192r1=f.prime192v1,f.p384=f.secp384r1,f.p521=f.secp521r1,o.prototype.generateKeys=function(e,t){return this.keys=this.curve.genKeyPair(),this.getPublicKey(e,t)},o.prototype.computeSecret=function(e,r,i){return r=r||"utf8",t.isBuffer(e)||(e=new t(e,r)),a(this.curve.keyFromPublic(e).getPublic().mul(this.keys.getPrivate()).getX(),i,this.curveType.byteLength)},o.prototype.getPublicKey=function(e,t){var r=this.keys.getPublic("compressed"===t,!0);return"hybrid"===t&&(r[r.length-1]%2?r[0]=7:r[0]=6),a(r,e)},o.prototype.getPrivateKey=function(e){return a(this.keys.getPrivate(),e)},o.prototype.setPublicKey=function(e,r){return r=r||"utf8",t.isBuffer(e)||(e=new t(e,r)),this.keys._importPublic(e),this},o.prototype.setPrivateKey=function(e,r){r=r||"utf8",t.isBuffer(e)||(e=new t(e,r));var i=new n(e);return i=i.toString(16),this.keys=this.curve.genKeyPair(),this.keys._importPrivate(i),this}}).call(this,r(277).Buffer)},function(e,t,r){t.publicEncrypt=r(514),t.privateDecrypt=r(515),t.privateEncrypt=function(e,r){return t.publicEncrypt(e,r,!0)},t.publicDecrypt=function(e,r){return t.privateDecrypt(e,r,!0)}},function(e,t,r){var i=r(302),n=r(287),f=r(292),o=r(407),a=r(408),s=r(276),c=r(409),h=r(323),d=r(274).Buffer;e.exports=function(e,t,r){var u;u=e.padding?e.padding:r?1:4;var l,p=i(e);if(4===u)l=function(e,t){var r=e.modulus.byteLength(),i=t.length,c=f("sha1").update(d.alloc(0)).digest(),h=c.length,u=2*h;if(i>r-u-2)throw new Error("message too long");var l=d.alloc(r-i-u-2),p=r-h-1,b=n(h),y=a(d.concat([c,l,d.alloc(1,1),t],p),o(b,p)),g=a(b,o(y,h));return new s(d.concat([d.alloc(1),g,y],r))}(p,t);else if(1===u)l=function(e,t,r){var i,f=t.length,o=e.modulus.byteLength();if(f>o-11)throw new Error("message too long");i=r?d.alloc(o-f-3,255):function(e){var t,r=d.allocUnsafe(e),i=0,f=n(2*e),o=0;for(;i<e;)o===f.length&&(f=n(2*e),o=0),(t=f[o++])&&(r[i++]=t);return r}(o-f-3);return new s(d.concat([d.from([0,r?1:2]),i,d.alloc(1),t],o))}(p,t,r);else{if(3!==u)throw new Error("unknown padding");if((l=new s(t)).cmp(p.modulus)>=0)throw new Error("data too long for modulus")}return r?h(l,p):c(l,p)}},function(e,t,r){var i=r(302),n=r(407),f=r(408),o=r(276),a=r(323),s=r(292),c=r(409),h=r(274).Buffer;e.exports=function(e,t,r){var d;d=e.padding?e.padding:r?1:4;var u,l=i(e),p=l.modulus.byteLength();if(t.length>p||new o(t).cmp(l.modulus)>=0)throw new Error("decryption error");u=r?c(new o(t),l):a(t,l);var b=h.alloc(p-u.length);if(u=h.concat([b,u],p),4===d)return function(e,t){var r=e.modulus.byteLength(),i=s("sha1").update(h.alloc(0)).digest(),o=i.length;if(0!==t[0])throw new Error("decryption error");var a=t.slice(1,o+1),c=t.slice(o+1),d=f(a,n(c,o)),u=f(c,n(d,r-o-1));if(function(e,t){e=h.from(e),t=h.from(t);var r=0,i=e.length;e.length!==t.length&&(r++,i=Math.min(e.length,t.length));var n=-1;for(;++n<i;)r+=e[n]^t[n];return r}(i,u.slice(0,o)))throw new Error("decryption error");var l=o;for(;0===u[l];)l++;if(1!==u[l++])throw new Error("decryption error");return u.slice(l)}(l,u);if(1===d)return function(e,t,r){var i=t.slice(0,2),n=2,f=0;for(;0!==t[n++];)if(n>=t.length){f++;break}var o=t.slice(2,n-1);("0002"!==i.toString("hex")&&!r||"0001"!==i.toString("hex")&&r)&&f++;o.length<8&&f++;if(f)throw new Error("decryption error");return t.slice(n)}(0,u,r);if(3===d)return u;throw new Error("unknown padding")}},function(e,t,r){"use strict";(function(e,i){function n(){throw new Error("secure random number generation not supported by this browser\nuse chrome, FireFox or Internet Explorer 11")}var f=r(274),o=r(287),a=f.Buffer,s=f.kMaxLength,c=e.crypto||e.msCrypto,h=Math.pow(2,32)-1;function d(e,t){if("number"!=typeof e||e!=e)throw new TypeError("offset must be a number");if(e>h||e<0)throw new TypeError("offset must be a uint32");if(e>s||e>t)throw new RangeError("offset out of range")}function u(e,t,r){if("number"!=typeof e||e!=e)throw new TypeError("size must be a number");if(e>h||e<0)throw new TypeError("size must be a uint32");if(e+t>r||e>s)throw new RangeError("buffer too small")}function l(e,t,r,n){if(i.browser){var f=e.buffer,a=new Uint8Array(f,t,r);return c.getRandomValues(a),n?void i.nextTick((function(){n(null,e)})):e}if(!n)return o(r).copy(e,t),e;o(r,(function(r,i){if(r)return n(r);i.copy(e,t),n(null,e)}))}c&&c.getRandomValues||!i.browser?(t.randomFill=function(t,r,i,n){if(!(a.isBuffer(t)||t instanceof e.Uint8Array))throw new TypeError('"buf" argument must be a Buffer or Uint8Array');if("function"==typeof r)n=r,r=0,i=t.length;else if("function"==typeof i)n=i,i=t.length-r;else if("function"!=typeof n)throw new TypeError('"cb" argument must be a function');return d(r,t.length),u(i,r,t.length),l(t,r,i,n)},t.randomFillSync=function(t,r,i){void 0===r&&(r=0);if(!(a.isBuffer(t)||t instanceof e.Uint8Array))throw new TypeError('"buf" argument must be a Buffer or Uint8Array');d(r,t.length),void 0===i&&(i=t.length-r);return u(i,r,t.length),l(t,r,i)}):(t.randomFill=n,t.randomFillSync=n)}).call(this,r(16),r(109))}])]);