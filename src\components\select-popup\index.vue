<template>
  <div>
    <!-- 得到数据列表 -->
    <van-cell :value="selectedCount" is-link @click="openDataList()">
      <template #title>
        <span class="custom-title">{{ title }}</span>
      </template>
      <template #right-icon>
        <img
          style="width: 8px; height: 5px; margin-top: 9px; margin-left: 10px"
          src="./imgs/arrowdown.png"
          alt=""
        />
      </template>
    </van-cell>
    <van-popup v-model="showList" round position="bottom" :style="{ height: '60%' }">
      <div class="search-title-box">
        <form class="form" action="/">
          <van-search
            v-if="showSearch"
            show-action
            v-model="searchValue"
            placeholder="请输入检索关键词"
            @search="search"
          >
            <template #action>
              <div class="search" @click="search">搜索</div>
            </template>
          </van-search>
        </form>
      </div>
      <div class="popup-content">
        <div class="item-title">请选择{{ title }}</div>
        <div class="item-list">
          <van-checkbox-group
            v-model="tempCheckedDataList"
            ref="checkboxGroup"
            @change="changeSelect"
          >
            <div class="item-content" v-for="(item, index) in dataList" :key="index">
              <van-checkbox :name="item" :shape="isMulti ? 'square' : 'round'">{{
                item.title
              }}</van-checkbox>
            </div>
          </van-checkbox-group>
        </div>
        <div class="item-bottom">
          <div class="checkAll" v-if="isMulti">
            <van-checkbox v-model="allChecked" shape="square" @click="checkAllData"
              >全选</van-checkbox
            >
            <div class="line"></div>
            <div>共选择 {{ tempCheckedDataList.length }} 个{{ title }}</div>
          </div>
          <div
            class="button-t"
            :class="!isMulti ? 'single-button' : ''"
            style="background: rgb(231, 244, 253); color: #2d8cf0"
            @click="cancelSelection"
          >
            取消
          </div>
          <div
            class="button-t"
            :class="!isMulti ? 'single-button' : ''"
            style="background: linear-gradient(90deg, #67b6fb, #2d8cf0); color: #ffffff"
            @click="confirmSelection"
          >
            确定
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import _ from 'lodash'
import { isEqual } from '@gy-common/shared/utils'
export default {
  props: {
    value: {
      type: [String, Array],
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    showSearch: {
      type: Boolean,
      default: true,
    },
    options: {
      type: Array,
      default: () => {
        return []
      },
    },
    isMulti: {
      type: Boolean,
      default: true,
    },
    defaultValue: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      showList: false,
      searchValue: '',
      checkedDataList: [],
      allChecked: false,
      dataList: [],
      originDataList: [],
      lastCheckedDatas: [],
      tempCheckedDataList: [], // 新增临时变量
    }
  },
  watch: {
    options: {
      handler(newValue) {
        this.dataList = newValue
        this.originDataList = newValue
      },
      deep: true,
      immediate: true,
    },
    value: {
      handler(newValue) {
        if (isEqual(this.checkedDataList, this.value)) {
          return
        }
        this.checkedDataList = newValue
      },
      deep: true,
      immediate: true,
    },
    checkedDataList: {
      handler(newValue) {
        this.$emit('input', newValue)
      },
      deep: true,
    },
    defaultValue: {
      handler() {
        if (this.defaultValue && this.defaultValue.length > 0) {
          if (!this.isMulti) {
            this.checkedDataList = this.defaultValue.slice(0, 1)
            this.lastCheckedDatas = this.checkedDataList
          } else {
            this.checkedDataList = this.defaultValue
          }
          if (this.checkedDataList.length >= this.dataList.length) {
            this.allChecked = true
          } else {
            this.allChecked = false
          }
          this.$emit('input', this.checkedDataList)
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    selectedCount() {
      if (this.isMulti) {
        return `已选择` + this.checkedDataList.length + `个` + this.title
      } else {
        if (this.checkedDataList && this.checkedDataList.length > 0) {
          return this.checkedDataList[0].title || ''
        } else {
          return ''
        }
      }
    },
  },
  methods: {
    // 弹出选择框
    openDataList() {
      this.tempCheckedDataList = this.checkedDataList // 打开时拷贝当前值
      this.showList = !this.showList
    },
    changeSelect(values) {
      if (!this.isMulti) {
        if (values.length > 1) {
          // 两次选的不一样 取消原来的
          this.tempCheckedDataList = _.filter(
            values,
            (value) => !this.lastCheckedDatas.includes(value),
          )
        }
      }
      this.lastCheckedDatas = this.tempCheckedDataList
      if (this.isMulti) {
        this.toggleSelectAll()
      }
    },
    checkAllData() {
      this.$refs.checkboxGroup.toggleAll(this.allChecked)
    },
    // 确定按钮
    confirmSelection() {
      this.checkedDataList = this.tempCheckedDataList // 提交修改
      this.$emit('input', this.checkedDataList)
      this.showList = false
    },
    cancelSelection() {
      this.showList = false
    },
    // 筛选
    search() {
      let value = this.searchValue
      this.dataList = _.filter(this.originDataList, (data) => data['title'].includes(value))

      if (this.isMulti) {
        this.toggleSelectAll()
      }
    },
    // 全选的状态 选中的包含所有当前筛选出来的即为全选
    toggleSelectAll() {
      let showKeys = this.dataList.map((data) => data['id'])
      let datas = _.filter(this.tempCheckedDataList, (data) => showKeys.includes(data['id']))
      if (datas.length >= this.dataList.length) {
        this.allChecked = true
      } else {
        this.allChecked = false
      }
    },
  },
}
</script>

<style lang="less" scoped>
.custom-title {
  margin-left: 10px;
  &.required {
    margin-left: 0;
  }
  .star {
    color: red;
    margin-right: 5px;
  }
}
.search-title-box {
  display: flex;
  align-items: center;
  position: sticky;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  top: 0;
  .form {
    flex: 1;
    .van-search {
      padding: 4px 8px;
    }
    .search {
      font-weight: 400;
      font-size: 13px;
      color: #2d8cf0;
      padding-left: 3px;
      padding-right: 5px;
      line-height: 13px;
    }
  }
}
.popup-content {
  background-color: white;
  overflow: hidden;
  padding-bottom: 65px;
  .item-title {
    text-align: center;
    font-weight: 600;
    padding: 3px;
    width: 100%;
    font-size: 14px;
    box-sizing: border-box;
  }
  .item-list {
    height: calc(60vh - 75px - 25px - 70px);
    overflow-y: auto;
  }
  .item-content {
    width: 100%;
    height: 48px;
    background-color: white;
    padding-left: 20px;
    border-bottom: 1px solid ghostwhite;
    display: flex;
    align-items: center;
    box-sizing: border-box;
  }
  .item-bottom {
    display: flex;
    position: fixed;
    bottom: 0;
    width: 100vw;
    height: 64px;
    padding: 10px 0px 15px;
    background-color: #ffffff;
    box-sizing: border-box;
    .checkAll {
      width: 180px;
      display: flex;
      align-items: center;
      margin: 0 10px;
      .line {
        width: 1px;
        height: 13px;
        background: #084532;
        margin: 0px 8px;
      }
    }
    .button-t {
      margin: 0 10px;
      text-align: center;
      font-weight: 400;
      font-size: 15px;
      color: #ffffff;
      line-height: 15px;
      background: linear-gradient(90deg, #67b6fb, #2d8cf0);
      border-radius: 6px;
      flex: 1;
      padding: 11px 0;
    }
  }
}
</style>
