<template>
  <div ref="micro-app-container"></div>
</template>
<script>
import { loadMicroApp, initGlobalState } from 'qiankun'
import config from '@/config/config'
import { getToken } from '@indfnd/utils'
export default {
  props: {
    url: String,
    name: String,
  },
  data() {
    return {
      pagePath: '/' + config.routerBase,
    }
  },
  watch: {
    // eslint-disable-next-line no-unused-vars
    url: function (value) {
      this.loadDidaMicroApp()
    },
  },
  mounted() {
    this.loadDidaMicroApp()
  },
  methods: {
    loadDidaMicroApp() {
      if (this.url) {
        if (this.microAppRef) {
          this.microAppRef.update({
            url: config.didaHost + this.url,
            token: getToken(),
          })
        } else {
          let { $refs, url } = this
          const entry = config.didaHost + url
          this.microAppRef = loadMicroApp({
            name: this.name,
            entry,
            container: $refs['micro-app-container'],
            props: {
              url: entry,
              token: getToken(),
              pagePath: this.pagePath,
            },
          })
          this.qiankunActions = initGlobalState({
            monitor: '',
            type: '',
            data: {},
          })
          // eslint-disable-next-line no-unused-vars
          this.qiankunActions.onGlobalStateChange((state, prevState) => {
            if (state.monitor == 'main') {
              if (state.type == 'goBack') {
                this.routerBack()
              } else if (state.type == 'routerPush') {
                this.routerTo(state.data)
              } else {
                console.log('未识别的通信数据', state)
              }
            }
          })
        }
      }
    },
    routerTo(data) {
      this.$router.push({ path: data.url.replace(this.pagePath, '') })
    },
    routerBack() {
      this.$router.go(-1)
    },
  },

  destroyed() {
    this.microAppRef.mountPromise.then(() => this.microAppRef.unmount())
    this.qiankunActions.offGlobalStateChange()
  },
}
</script>
<style lang="less" scoped></style>
