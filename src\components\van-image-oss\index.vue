<template>
  <van-image v-bind="$props" :src="fullSrc" v-on="$listeners" @click="imagePreview" />
</template>

<script>
import { getFileURL } from '@/api/ics/common/index'
import { ImagePreview } from 'vant'

export default {
  name: 'van-image-oss',
  components: {},
  props: {
    src: {
      type: String,
    },
    fit: {
      type: String,
      default: 'fill',
    },
    alt: {
      type: String,
    },
    preview: {
      type: Boolean,
      default: true,
    },
    width: {
      type: [Number, String],
    },
    height: {
      type: [Number, String],
    },
    radius: {
      type: [Number, String],
      default: 0,
    },
    round: {
      type: Boolean,
      default: false,
    },
    lazyLoad: {
      type: Boolean,
      default: false,
    },
    showError: {
      type: Boolean,
      default: true,
    },
    showLoading: {
      type: Boolean,
      default: true,
    },
    errorIcon: {
      type: String,
      default: 'photo-fail',
    },
    loadingIcon: {
      type: String,
      default: 'photo',
    },
    iconPrefix: {
      type: String,
      default: 'van-icon',
    },
  },
  data() {
    return {}
  },
  computed: {
    fullSrc() {
      return getFileURL(this.src)
    },
  },
  watch: {},
  methods: {
    imagePreview() {
      if (this.preview) {
        ImagePreview({
          images: [this.fullSrc],
          startPosition: 0,
        })
      }
      this.$emit('click')
    },
  },
}
</script>
