(window.webpackJsonp=window.webpackJsonp||[]).push([[0],[]]);!function(n){function e(e){for(var r,a,s=e[0],c=e[1],l=e[2],p=0,d=[];p<s.length;p++)a=s[p],Object.prototype.hasOwnProperty.call(o,a)&&o[a]&&d.push(o[a][0]),o[a]=0;for(r in c)Object.prototype.hasOwnProperty.call(c,r)&&(n[r]=c[r]);for(u&&u(e);d.length;)d.shift()();return i.push.apply(i,l||[]),t()}function t(){for(var n,e=0;e<i.length;e++){for(var t=i[e],r=!0,s=1;s<t.length;s++){var c=t[s];0!==o[c]&&(r=!1)}r&&(i.splice(e--,1),n=a(a.s=t[0]))}return n}var r={},o={1:0},i=[];function a(e){if(r[e])return r[e].exports;var t=r[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,a),t.l=!0,t.exports}a.e=function(n){var e=[],t=o[n];if(0!==t)if(t)e.push(t[2]);else{var r=new Promise((function(e,r){t=o[n]=[e,r]}));e.push(t[2]=r);var i,s=document.createElement("script");s.charset="utf-8",s.timeout=120,a.nc&&s.setAttribute("nonce",a.nc),s.src=function(n){return a.p+"assets/js/"+({}[n]||n)+"."+{2:"0d1664b8",3:"0847e68c",4:"db7d37ec",5:"261c7dde",6:"5140a635",7:"fb647ce6",8:"4803d593",9:"879849e2",10:"badac427",11:"b2690f66",12:"4109fafc",13:"46d27f7c",14:"283347c4",15:"6448503c",16:"4a061cc5",17:"8aefe3f1",18:"f7541d8b",19:"19224b97",20:"37219256",21:"b252b4d0",22:"ecf9a606",23:"73c227e5",24:"0b377674",25:"14833ba2",26:"519ed874",27:"685fd56e",28:"a1ddc882",29:"9fbae7dc",30:"e1026440",31:"0d968515",32:"e23e1a40",33:"fd9da0a2"}[n]+".js"}(n);var c=new Error;i=function(e){s.onerror=s.onload=null,clearTimeout(l);var t=o[n];if(0!==t){if(t){var r=e&&("load"===e.type?"missing":e.type),i=e&&e.target&&e.target.src;c.message="Loading chunk "+n+" failed.\n("+r+": "+i+")",c.name="ChunkLoadError",c.type=r,c.request=i,t[1](c)}o[n]=void 0}};var l=setTimeout((function(){i({type:"timeout",target:s})}),12e4);s.onerror=s.onload=i,document.head.appendChild(s)}return Promise.all(e)},a.m=n,a.c=r,a.d=function(n,e,t){a.o(n,e)||Object.defineProperty(n,e,{enumerable:!0,get:t})},a.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},a.t=function(n,e){if(1&e&&(n=a(n)),8&e)return n;if(4&e&&"object"==typeof n&&n&&n.__esModule)return n;var t=Object.create(null);if(a.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:n}),2&e&&"string"!=typeof n)for(var r in n)a.d(t,r,function(e){return n[e]}.bind(null,r));return t},a.n=function(n){var e=n&&n.__esModule?function(){return n.default}:function(){return n};return a.d(e,"a",e),e},a.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},a.p="/ind-utils-doc/",a.oe=function(n){throw console.error(n),n};var s=window.webpackJsonp=window.webpackJsonp||[],c=s.push.bind(s);s.push=e,s=s.slice();for(var l=0;l<s.length;l++)e(s[l]);var u=c;i.push([121,0]),t()}([function(n,e,t){"use strict";var r=function(n){return n&&n.Math===Math&&n};n.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof global&&global)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},function(n,e,t){"use strict";var r="object"==typeof document&&document.all;n.exports=void 0===r&&void 0!==r?function(n){return"function"==typeof n||n===r}:function(n){return"function"==typeof n}},function(n,e,t){"use strict";var r=t(33),o=Function.prototype,i=o.call,a=r&&o.bind.bind(i,i);n.exports=r?a:function(n){return function(){return i.apply(n,arguments)}}},function(n,e,t){"use strict";n.exports=function(n){try{return!!n()}catch(n){return!0}}},function(n,e,t){"use strict";var r=t(3);n.exports=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},function(n,e){var t=Array.isArray;n.exports=t},function(n,e,t){var r=t(68),o="object"==typeof self&&self&&self.Object===Object&&self,i=r||o||Function("return this")();n.exports=i},function(n,e,t){"use strict";function r(n,e,t,r,o,i,a,s){var c,l="function"==typeof n?n.options:n;if(e&&(l.render=e,l.staticRenderFns=t,l._compiled=!0),r&&(l.functional=!0),i&&(l._scopeId="data-v-"+i),a?(c=function(n){(n=n||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(n=__VUE_SSR_CONTEXT__),o&&o.call(this,n),n&&n._registeredComponents&&n._registeredComponents.add(a)},l._ssrRegister=c):o&&(c=s?function(){o.call(this,(l.functional?this.parent:this).$root.$options.shadowRoot)}:o),c)if(l.functional){l._injectStyles=c;var u=l.render;l.render=function(n,e){return c.call(e),u(n,e)}}else{var p=l.beforeCreate;l.beforeCreate=p?[].concat(p,c):[c]}return{exports:n,options:l}}t.d(e,"a",(function(){return r}))},function(n,e,t){"use strict";var r=t(2),o=t(29),i=r({}.hasOwnProperty);n.exports=Object.hasOwn||function(n,e){return i(o(n),e)}},function(n,e,t){"use strict";var r=t(1);n.exports=function(n){return"object"==typeof n?null!==n:r(n)}},function(n,e,t){var r=t(163),o=t(166);n.exports=function(n,e){var t=o(n,e);return r(t)?t:void 0}},function(n,e,t){"use strict";t.d(e,"e",(function(){return r})),t.d(e,"b",(function(){return i})),t.d(e,"j",(function(){return a})),t.d(e,"g",(function(){return c})),t.d(e,"h",(function(){return l})),t.d(e,"i",(function(){return u})),t.d(e,"c",(function(){return p})),t.d(e,"f",(function(){return d})),t.d(e,"l",(function(){return f})),t.d(e,"m",(function(){return h})),t.d(e,"d",(function(){return g})),t.d(e,"k",(function(){return v})),t.d(e,"n",(function(){return y})),t.d(e,"a",(function(){return x}));t(26);const r=/#.*$/,o=/\.(md|html)$/,i=/\/$/,a=/^[a-z]+:/i;function s(n){return decodeURI(n).replace(r,"").replace(o,"")}function c(n){return a.test(n)}function l(n){return/^mailto:/.test(n)}function u(n){return/^tel:/.test(n)}function p(n){if(c(n))return n;if(!n)return"404";const e=n.match(r),t=e?e[0]:"",o=s(n);return i.test(o)?n:o+".html"+t}function d(n,e){const t=n.hash,o=function(n){const e=n&&n.match(r);if(e)return e[0]}(e);if(o&&t!==o)return!1;return s(n.path)===s(e)}function f(n,e,t){if(c(e))return{type:"external",path:e};t&&(e=function(n,e,t){const r=n.charAt(0);if("/"===r)return n;if("?"===r||"#"===r)return e+n;const o=e.split("/");t&&o[o.length-1]||o.pop();const i=n.replace(/^\//,"").split("/");for(let n=0;n<i.length;n++){const e=i[n];".."===e?o.pop():"."!==e&&o.push(e)}""!==o[0]&&o.unshift("");return o.join("/")}(e,t));const r=s(e);for(let e=0;e<n.length;e++)if(s(n[e].regularPath)===r)return Object.assign({},n[e],{type:"page",path:p(n[e].path)});return console.error(`[vuepress] No matching page found for sidebar item "${e}"`),{}}function h(n,e,t,r){const{pages:o,themeConfig:i}=t,a=r&&i.locales&&i.locales[r]||i;if("auto"===(n.frontmatter.sidebar||a.sidebar||i.sidebar))return m(n);const s=a.sidebar||i.sidebar;if(s){const{base:t,config:r}=function(n,e){if(Array.isArray(e))return{base:"/",config:e};for(const r in e)if(0===(t=n,/(\.html|\/)$/.test(t)?t:t+"/").indexOf(encodeURI(r)))return{base:r,config:e[r]};var t;return{}}(e,s);return"auto"===r?m(n):r?r.map(n=>function n(e,t,r,o=1){if("string"==typeof e)return f(t,e,r);if(Array.isArray(e))return Object.assign(f(t,e[0],r),{title:e[1]});{o>3&&console.error("[vuepress] detected a too deep nested sidebar group.");const i=e.children||[];return 0===i.length&&e.path?Object.assign(f(t,e.path,r),{title:e.title}):{type:"group",path:e.path,title:e.title,sidebarDepth:e.sidebarDepth,initialOpenGroupIndex:e.initialOpenGroupIndex,children:i.map(e=>n(e,t,r,o+1)),collapsable:!1!==e.collapsable}}}(n,o,t)):[]}return[]}function m(n){const e=g(n.headers||[]);return[{type:"group",collapsable:!1,title:n.title,path:null,children:e.map(e=>({type:"auto",title:e.title,basePath:n.path,path:n.path+"#"+e.slug,children:e.children||[]}))}]}function g(n){let e;return(n=n.map(n=>Object.assign({},n))).forEach(n=>{2===n.level?e=n:e&&(e.children||(e.children=[])).push(n)}),n.filter(n=>2===n.level)}function v(n){return Object.assign(n,{type:n.items&&n.items.length?"links":"link"})}function y(n){return Object.prototype.toString.call(n).match(/\[object (.*?)\]/)[1].toLowerCase()}function b(n){let e=n.frontmatter.date||n.lastUpdated||new Date,t=new Date(e);return"Invalid Date"==t&&e&&(t=new Date(e.replace(/-/g,"/"))),t.getTime()}function x(n,e){return b(e)-b(n)}},function(n,e){n.exports=function(n){return null!=n&&"object"==typeof n}},function(n,e,t){"use strict";var r=t(4),o=t(16),i=t(34);n.exports=r?function(n,e,t){return o.f(n,e,i(1,t))}:function(n,e,t){return n[e]=t,n}},function(n,e,t){var r=t(15),o=t(148),i=t(149),a=r?r.toStringTag:void 0;n.exports=function(n){return null==n?void 0===n?"[object Undefined]":"[object Null]":a&&a in Object(n)?o(n):i(n)}},function(n,e,t){var r=t(6).Symbol;n.exports=r},function(n,e,t){"use strict";var r=t(4),o=t(66),i=t(103),a=t(28),s=t(61),c=TypeError,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor;e.f=r?i?function(n,e,t){if(a(n),e=s(e),a(t),"function"==typeof n&&"prototype"===e&&"value"in t&&"writable"in t&&!t.writable){var r=u(n,e);r&&r.writable&&(n[e]=t.value,t={configurable:"configurable"in t?t.configurable:r.configurable,enumerable:"enumerable"in t?t.enumerable:r.enumerable,writable:!1})}return l(n,e,t)}:l:function(n,e,t){if(a(n),e=s(e),a(t),o)try{return l(n,e,t)}catch(n){}if("get"in t||"set"in t)throw new c("Accessors not supported");return"value"in t&&(n[e]=t.value),n}},function(n,e,t){"use strict";var r=t(2),o=r({}.toString),i=r("".slice);n.exports=function(n){return i(o(n),8,-1)}},function(n,e,t){var r=t(153),o=t(154),i=t(155),a=t(156),s=t(157);function c(n){var e=-1,t=null==n?0:n.length;for(this.clear();++e<t;){var r=n[e];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=s,n.exports=c},function(n,e,t){var r=t(70);n.exports=function(n,e){for(var t=n.length;t--;)if(r(n[t][0],e))return t;return-1}},function(n,e,t){var r=t(10)(Object,"create");n.exports=r},function(n,e,t){var r=t(175);n.exports=function(n,e){var t=n.__data__;return r(e)?t["string"==typeof e?"string":"hash"]:t.map}},function(n,e,t){var r=t(45);n.exports=function(n){if("string"==typeof n||r(n))return n;var e=n+"";return"0"==e&&1/n==-1/0?"-0":e}},function(n,e,t){var r,o;
/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT */void 0===(o="function"==typeof(r=function(){var n,e,t={version:"0.2.0"},r=t.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function o(n,e,t){return n<e?e:n>t?t:n}function i(n){return 100*(-1+n)}t.configure=function(n){var e,t;for(e in n)void 0!==(t=n[e])&&n.hasOwnProperty(e)&&(r[e]=t);return this},t.status=null,t.set=function(n){var e=t.isStarted();n=o(n,r.minimum,1),t.status=1===n?null:n;var c=t.render(!e),l=c.querySelector(r.barSelector),u=r.speed,p=r.easing;return c.offsetWidth,a((function(e){""===r.positionUsing&&(r.positionUsing=t.getPositioningCSS()),s(l,function(n,e,t){var o;return(o="translate3d"===r.positionUsing?{transform:"translate3d("+i(n)+"%,0,0)"}:"translate"===r.positionUsing?{transform:"translate("+i(n)+"%,0)"}:{"margin-left":i(n)+"%"}).transition="all "+e+"ms "+t,o}(n,u,p)),1===n?(s(c,{transition:"none",opacity:1}),c.offsetWidth,setTimeout((function(){s(c,{transition:"all "+u+"ms linear",opacity:0}),setTimeout((function(){t.remove(),e()}),u)}),u)):setTimeout(e,u)})),this},t.isStarted=function(){return"number"==typeof t.status},t.start=function(){t.status||t.set(0);var n=function(){setTimeout((function(){t.status&&(t.trickle(),n())}),r.trickleSpeed)};return r.trickle&&n(),this},t.done=function(n){return n||t.status?t.inc(.3+.5*Math.random()).set(1):this},t.inc=function(n){var e=t.status;return e?("number"!=typeof n&&(n=(1-e)*o(Math.random()*e,.1,.95)),e=o(e+n,0,.994),t.set(e)):t.start()},t.trickle=function(){return t.inc(Math.random()*r.trickleRate)},n=0,e=0,t.promise=function(r){return r&&"resolved"!==r.state()?(0===e&&t.start(),n++,e++,r.always((function(){0==--e?(n=0,t.done()):t.set((n-e)/n)})),this):this},t.render=function(n){if(t.isRendered())return document.getElementById("nprogress");l(document.documentElement,"nprogress-busy");var e=document.createElement("div");e.id="nprogress",e.innerHTML=r.template;var o,a=e.querySelector(r.barSelector),c=n?"-100":i(t.status||0),u=document.querySelector(r.parent);return s(a,{transition:"all 0 linear",transform:"translate3d("+c+"%,0,0)"}),r.showSpinner||(o=e.querySelector(r.spinnerSelector))&&d(o),u!=document.body&&l(u,"nprogress-custom-parent"),u.appendChild(e),e},t.remove=function(){u(document.documentElement,"nprogress-busy"),u(document.querySelector(r.parent),"nprogress-custom-parent");var n=document.getElementById("nprogress");n&&d(n)},t.isRendered=function(){return!!document.getElementById("nprogress")},t.getPositioningCSS=function(){var n=document.body.style,e="WebkitTransform"in n?"Webkit":"MozTransform"in n?"Moz":"msTransform"in n?"ms":"OTransform"in n?"O":"";return e+"Perspective"in n?"translate3d":e+"Transform"in n?"translate":"margin"};var a=function(){var n=[];function e(){var t=n.shift();t&&t(e)}return function(t){n.push(t),1==n.length&&e()}}(),s=function(){var n=["Webkit","O","Moz","ms"],e={};function t(t){return t=t.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,(function(n,e){return e.toUpperCase()})),e[t]||(e[t]=function(e){var t=document.body.style;if(e in t)return e;for(var r,o=n.length,i=e.charAt(0).toUpperCase()+e.slice(1);o--;)if((r=n[o]+i)in t)return r;return e}(t))}function r(n,e,r){e=t(e),n.style[e]=r}return function(n,e){var t,o,i=arguments;if(2==i.length)for(t in e)void 0!==(o=e[t])&&e.hasOwnProperty(t)&&r(n,t,o);else r(n,i[1],i[2])}}();function c(n,e){return("string"==typeof n?n:p(n)).indexOf(" "+e+" ")>=0}function l(n,e){var t=p(n),r=t+e;c(t,e)||(n.className=r.substring(1))}function u(n,e){var t,r=p(n);c(n,e)&&(t=r.replace(" "+e+" "," "),n.className=t.substring(1,t.length-1))}function p(n){return(" "+(n.className||"")+" ").replace(/\s+/gi," ")}function d(n){n&&n.parentNode&&n.parentNode.removeChild(n)}return t})?r.call(e,t,e,n):r)||(n.exports=o)},function(n,e,t){"use strict";var r=t(0),o=t(60).f,i=t(13),a=t(95),s=t(37),c=t(57),l=t(135);n.exports=function(n,e){var t,u,p,d,f,h=n.target,m=n.global,g=n.stat;if(t=m?r:g?r[h]||s(h,{}):(r[h]||{}).prototype)for(u in e){if(d=e[u],p=n.dontCallGetSet?(f=o(t,u))&&f.value:t[u],!l(m?u:h+(g?".":"#")+u,n.forced)&&void 0!==p){if(typeof d==typeof p)continue;c(d,p)}(n.sham||p&&p.sham)&&i(d,"sham",!0),a(t,u,d,n)}}},function(n,e,t){"use strict";var r=t(133);n.exports=function(n){return r(n.length)}},function(n,e,t){"use strict";var r=t(24),o=t(29),i=t(25),a=t(136),s=t(138);r({target:"Array",proto:!0,arity:1,forced:t(3)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(n){return n instanceof TypeError}}()},{push:function(n){var e=o(this),t=i(e),r=arguments.length;s(t+r);for(var c=0;c<r;c++)e[t]=arguments[c],t++;return a(e,t),t}})},function(n,e,t){"use strict";var r=t(0),o=t(65),i=t(8),a=t(56),s=t(64),c=t(63),l=r.Symbol,u=o("wks"),p=c?l.for||l:l&&l.withoutSetter||a;n.exports=function(n){return i(u,n)||(u[n]=s&&i(l,n)?l[n]:p("Symbol."+n)),u[n]}},function(n,e,t){"use strict";var r=t(9),o=String,i=TypeError;n.exports=function(n){if(r(n))return n;throw new i(o(n)+" is not an object")}},function(n,e,t){"use strict";var r=t(52),o=Object;n.exports=function(n){return o(r(n))}},function(n,e,t){"use strict";var r=t(33),o=Function.prototype.call;n.exports=r?o.bind(o):function(){return o.apply(o,arguments)}},function(n,e,t){"use strict";var r=t(0),o=t(1),i=function(n){return o(n)?n:void 0};n.exports=function(n,e){return arguments.length<2?i(r[n]):r[n]&&r[n][e]}},function(n,e,t){"use strict";var r=t(1),o=t(98),i=TypeError;n.exports=function(n){if(r(n))return n;throw new i(o(n)+" is not a function")}},function(n,e,t){"use strict";var r=t(3);n.exports=!r((function(){var n=function(){}.bind();return"function"!=typeof n||n.hasOwnProperty("prototype")}))},function(n,e,t){"use strict";n.exports=function(n,e){return{enumerable:!(1&n),configurable:!(2&n),writable:!(4&n),value:e}}},function(n,e,t){"use strict";var r=t(49),o=t(52);n.exports=function(n){return r(o(n))}},function(n,e,t){"use strict";var r=t(0),o=t(37),i=r["__core-js_shared__"]||o("__core-js_shared__",{});n.exports=i},function(n,e,t){"use strict";var r=t(0),o=Object.defineProperty;n.exports=function(n,e){try{o(r,n,{value:e,configurable:!0,writable:!0})}catch(t){r[n]=e}return e}},function(n,e,t){var r=t(147),o=t(12),i=Object.prototype,a=i.hasOwnProperty,s=i.propertyIsEnumerable,c=r(function(){return arguments}())?r:function(n){return o(n)&&a.call(n,"callee")&&!s.call(n,"callee")};n.exports=c},function(n,e,t){var r=t(10)(t(6),"Map");n.exports=r},function(n,e){n.exports=function(n){var e=typeof n;return null!=n&&("object"==e||"function"==e)}},function(n,e,t){var r=t(167),o=t(174),i=t(176),a=t(177),s=t(178);function c(n){var e=-1,t=null==n?0:n.length;for(this.clear();++e<t;){var r=n[e];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=s,n.exports=c},function(n,e){n.exports=function(n){var e=-1,t=Array(n.size);return n.forEach((function(n){t[++e]=n})),t}},function(n,e){n.exports=function(n){return"number"==typeof n&&n>-1&&n%1==0&&n<=9007199254740991}},function(n,e,t){var r=t(5),o=t(45),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;n.exports=function(n,e){if(r(n))return!1;var t=typeof n;return!("number"!=t&&"symbol"!=t&&"boolean"!=t&&null!=n&&!o(n))||(a.test(n)||!i.test(n)||null!=e&&n in Object(e))}},function(n,e,t){var r=t(14),o=t(12);n.exports=function(n){return"symbol"==typeof n||o(n)&&"[object Symbol]"==r(n)}},function(n,e){n.exports=function(n){return n}},function(n,e,t){"use strict";var r=t(132);n.exports=function(n){var e=+n;return e!=e||0===e?0:r(e)}},function(n,e,t){"use strict";var r=t(2);n.exports=r({}.isPrototypeOf)},function(n,e,t){"use strict";var r=t(2),o=t(3),i=t(17),a=Object,s=r("".split);n.exports=o((function(){return!a("z").propertyIsEnumerable(0)}))?function(n){return"String"===i(n)?s(n,""):a(n)}:a},function(n,e,t){"use strict";n.exports={}},function(n,e,t){"use strict";var r=t(139),o=t(28),i=t(140);n.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var n,e=!1,t={};try{(n=r(Object.prototype,"__proto__","set"))(t,[]),e=t instanceof Array}catch(n){}return function(t,r){return o(t),i(r),e?n(t,r):t.__proto__=r,t}}():void 0)},function(n,e,t){"use strict";var r=t(53),o=TypeError;n.exports=function(n){if(r(n))throw new o("Can't call method on "+n);return n}},function(n,e,t){"use strict";n.exports=function(n){return null==n}},function(n,e,t){"use strict";var r,o,i=t(0),a=t(123),s=i.process,c=i.Deno,l=s&&s.versions||c&&c.version,u=l&&l.v8;u&&(o=(r=u.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&a&&(!(r=a.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/))&&(o=+r[1]),n.exports=o},function(n,e,t){"use strict";n.exports=!1},function(n,e,t){"use strict";var r=t(2),o=0,i=Math.random(),a=r(1..toString);n.exports=function(n){return"Symbol("+(void 0===n?"":n)+")_"+a(++o+i,36)}},function(n,e,t){"use strict";var r=t(8),o=t(128),i=t(60),a=t(16);n.exports=function(n,e,t){for(var s=o(e),c=a.f,l=i.f,u=0;u<s.length;u++){var p=s[u];r(n,p)||t&&r(t,p)||c(n,p,l(e,p))}}},function(n,e){n.exports=function(n){return n.webpackPolyfill||(n.deprecate=function(){},n.paths=[],n.children||(n.children=[]),Object.defineProperty(n,"loaded",{enumerable:!0,get:function(){return n.l}}),Object.defineProperty(n,"id",{enumerable:!0,get:function(){return n.i}}),n.webpackPolyfill=1),n}},function(n,e){var t=/^\s+|\s+$/g,r=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,i=/^0o[0-7]+$/i,a=parseInt,s="object"==typeof global&&global&&global.Object===Object&&global,c="object"==typeof self&&self&&self.Object===Object&&self,l=s||c||Function("return this")(),u=Object.prototype.toString,p=Math.max,d=Math.min,f=function(){return l.Date.now()};function h(n){var e=typeof n;return!!n&&("object"==e||"function"==e)}function m(n){if("number"==typeof n)return n;if(function(n){return"symbol"==typeof n||function(n){return!!n&&"object"==typeof n}(n)&&"[object Symbol]"==u.call(n)}(n))return NaN;if(h(n)){var e="function"==typeof n.valueOf?n.valueOf():n;n=h(e)?e+"":e}if("string"!=typeof n)return 0===n?n:+n;n=n.replace(t,"");var s=o.test(n);return s||i.test(n)?a(n.slice(2),s?2:8):r.test(n)?NaN:+n}n.exports=function(n,e,t){var r,o,i,a,s,c,l=0,u=!1,g=!1,v=!0;if("function"!=typeof n)throw new TypeError("Expected a function");function y(e){var t=r,i=o;return r=o=void 0,l=e,a=n.apply(i,t)}function b(n){return l=n,s=setTimeout(_,e),u?y(n):a}function x(n){var t=n-c;return void 0===c||t>=e||t<0||g&&n-l>=i}function _(){var n=f();if(x(n))return w(n);s=setTimeout(_,function(n){var t=e-(n-c);return g?d(t,i-(n-l)):t}(n))}function w(n){return s=void 0,v&&r?y(n):(r=o=void 0,a)}function T(){var n=f(),t=x(n);if(r=arguments,o=this,c=n,t){if(void 0===s)return b(c);if(g)return s=setTimeout(_,e),y(c)}return void 0===s&&(s=setTimeout(_,e)),a}return e=m(e)||0,h(t)&&(u=!!t.leading,i=(g="maxWait"in t)?p(m(t.maxWait)||0,e):i,v="trailing"in t?!!t.trailing:v),T.cancel=function(){void 0!==s&&clearTimeout(s),l=0,r=c=o=s=void 0},T.flush=function(){return void 0===s?a:w(f())},T}},function(n,e,t){"use strict";var r=t(4),o=t(30),i=t(122),a=t(34),s=t(35),c=t(61),l=t(8),u=t(66),p=Object.getOwnPropertyDescriptor;e.f=r?p:function(n,e){if(n=s(n),e=c(e),u)try{return p(n,e)}catch(n){}if(l(n,e))return a(!o(i.f,n,e),n[e])}},function(n,e,t){"use strict";var r=t(108),o=t(62);n.exports=function(n){var e=r(n,"string");return o(e)?e:e+""}},function(n,e,t){"use strict";var r=t(31),o=t(1),i=t(48),a=t(63),s=Object;n.exports=a?function(n){return"symbol"==typeof n}:function(n){var e=r("Symbol");return o(e)&&i(e.prototype,s(n))}},function(n,e,t){"use strict";var r=t(64);n.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},function(n,e,t){"use strict";var r=t(54),o=t(3),i=t(0).String;n.exports=!!Object.getOwnPropertySymbols&&!o((function(){var n=Symbol("symbol detection");return!i(n)||!(Object(n)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},function(n,e,t){"use strict";var r=t(55),o=t(36);(n.exports=function(n,e){return o[n]||(o[n]=void 0!==e?e:{})})("versions",[]).push({version:"3.35.0",mode:r?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.35.0/LICENSE",source:"https://github.com/zloirock/core-js"})},function(n,e,t){"use strict";var r=t(4),o=t(3),i=t(102);n.exports=!r&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},function(n,e){n.exports=function(n,e){for(var t=-1,r=e.length,o=n.length;++t<r;)n[o+t]=e[t];return n}},function(n,e){var t="object"==typeof global&&global&&global.Object===Object&&global;n.exports=t},function(n,e,t){var r=t(18),o=t(158),i=t(159),a=t(160),s=t(161),c=t(162);function l(n){var e=this.__data__=new r(n);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=s,l.prototype.set=c,n.exports=l},function(n,e){n.exports=function(n,e){return n===e||n!=n&&e!=e}},function(n,e,t){var r=t(14),o=t(40);n.exports=function(n){if(!o(n))return!1;var e=r(n);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},function(n,e){var t=Function.prototype.toString;n.exports=function(n){if(null!=n){try{return t.call(n)}catch(n){}try{return n+""}catch(n){}}return""}},function(n,e,t){var r=t(179),o=t(12);n.exports=function n(e,t,i,a,s){return e===t||(null==e||null==t||!o(e)&&!o(t)?e!=e&&t!=t:r(e,t,i,a,n,s))}},function(n,e,t){var r=t(75),o=t(182),i=t(76);n.exports=function(n,e,t,a,s,c){var l=1&t,u=n.length,p=e.length;if(u!=p&&!(l&&p>u))return!1;var d=c.get(n),f=c.get(e);if(d&&f)return d==e&&f==n;var h=-1,m=!0,g=2&t?new r:void 0;for(c.set(n,e),c.set(e,n);++h<u;){var v=n[h],y=e[h];if(a)var b=l?a(y,v,h,e,n,c):a(v,y,h,n,e,c);if(void 0!==b){if(b)continue;m=!1;break}if(g){if(!o(e,(function(n,e){if(!i(g,e)&&(v===n||s(v,n,t,a,c)))return g.push(e)}))){m=!1;break}}else if(v!==y&&!s(v,y,t,a,c)){m=!1;break}}return c.delete(n),c.delete(e),m}},function(n,e,t){var r=t(41),o=t(180),i=t(181);function a(n){var e=-1,t=null==n?0:n.length;for(this.__data__=new r;++e<t;)this.add(n[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,n.exports=a},function(n,e){n.exports=function(n,e){return n.has(e)}},function(n,e,t){var r=t(192),o=t(198),i=t(81);n.exports=function(n){return i(n)?r(n):o(n)}},function(n,e,t){(function(n){var r=t(6),o=t(194),i=e&&!e.nodeType&&e,a=i&&"object"==typeof n&&n&&!n.nodeType&&n,s=a&&a.exports===i?r.Buffer:void 0,c=(s?s.isBuffer:void 0)||o;n.exports=c}).call(this,t(58)(n))},function(n,e){var t=/^(?:0|[1-9]\d*)$/;n.exports=function(n,e){var r=typeof n;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&t.test(n))&&n>-1&&n%1==0&&n<e}},function(n,e,t){var r=t(195),o=t(196),i=t(197),a=i&&i.isTypedArray,s=a?o(a):r;n.exports=s},function(n,e,t){var r=t(71),o=t(43);n.exports=function(n){return null!=n&&o(n.length)&&!r(n)}},function(n,e,t){var r=t(10)(t(6),"Set");n.exports=r},function(n,e,t){var r=t(40);n.exports=function(n){return n==n&&!r(n)}},function(n,e){n.exports=function(n,e){return function(t){return null!=t&&(t[n]===e&&(void 0!==e||n in Object(t)))}}},function(n,e,t){var r=t(86),o=t(22);n.exports=function(n,e){for(var t=0,i=(e=r(e,n)).length;null!=n&&t<i;)n=n[o(e[t++])];return t&&t==i?n:void 0}},function(n,e,t){var r=t(5),o=t(44),i=t(209),a=t(212);n.exports=function(n,e){return r(n)?n:o(n,e)?[n]:i(a(n))}},function(n,e,t){},function(n,e,t){},function(n,e,t){},function(n,e,t){},function(n,e,t){var r=t(145),o=t(150),i=t(221),a=t(229),s=t(238),c=t(120),l=i((function(n){var e=c(n);return s(e)&&(e=void 0),a(r(n,1,s,!0),o(e,2))}));n.exports=l},function(n,e,t){"use strict";
/*!
 * escape-html
 * Copyright(c) 2012-2013 TJ Holowaychuk
 * Copyright(c) 2015 Andreas Lubbe
 * Copyright(c) 2015 Tiancheng "Timothy" Gu
 * MIT Licensed
 */var r=/["'&<>]/;n.exports=function(n){var e,t=""+n,o=r.exec(t);if(!o)return t;var i="",a=0,s=0;for(a=o.index;a<t.length;a++){switch(t.charCodeAt(a)){case 34:e="&quot;";break;case 38:e="&amp;";break;case 39:e="&#39;";break;case 60:e="&lt;";break;case 62:e="&gt;";break;default:continue}s!==a&&(i+=t.substring(s,a)),s=a+1,i+=e}return s!==a?i+t.substring(s,a):i}},function(n,e,t){"use strict";t.r(e);var r={name:"CodeBlock",props:{title:{type:String,required:!0},active:{type:Boolean,default:!1}}},o=(t(241),t(7)),i=Object(o.a)(r,(function(){return(0,this._self._c)("div",{staticClass:"theme-code-block",class:{"theme-code-block__active":this.active}},[this._t("default")],2)}),[],!1,null,"4f1e9d0c",null);e.default=i.exports},function(n,e,t){"use strict";t.r(e);var r={name:"CodeGroup",data:()=>({codeTabs:[],activeCodeTabIndex:-1}),watch:{activeCodeTabIndex(n){this.codeTabs.forEach(n=>{n.elm.classList.remove("theme-code-block__active")}),this.codeTabs[n].elm.classList.add("theme-code-block__active")}},mounted(){this.codeTabs=(this.$slots.default||[]).filter(n=>Boolean(n.componentOptions)).map((n,e)=>(""===n.componentOptions.propsData.active&&(this.activeCodeTabIndex=e),{title:n.componentOptions.propsData.title,elm:n.elm})),-1===this.activeCodeTabIndex&&this.codeTabs.length>0&&(this.activeCodeTabIndex=0)},methods:{changeCodeTab(n){this.activeCodeTabIndex=n}}},o=(t(242),t(7)),i=Object(o.a)(r,(function(){var n=this,e=n._self._c;return e("div",{staticClass:"theme-code-group"},[e("div",{staticClass:"theme-code-group__nav"},[e("ul",{staticClass:"theme-code-group__ul"},n._l(n.codeTabs,(function(t,r){return e("li",{key:t.title,staticClass:"theme-code-group__li"},[e("button",{staticClass:"theme-code-group__nav-tab",class:{"theme-code-group__nav-tab-active":r===n.activeCodeTabIndex},on:{click:function(e){return n.changeCodeTab(r)}}},[n._v("\n            "+n._s(t.title)+"\n          ")])])})),0)]),n._v(" "),n._t("default"),n._v(" "),n.codeTabs.length<1?e("pre",{staticClass:"pre-blank"},[n._v("// Make sure to add code blocks to your code group")]):n._e()],2)}),[],!1,null,"2f5f1757",null);e.default=i.exports},function(n,e,t){"use strict";var r=t(1),o=t(16),i=t(104),a=t(37);n.exports=function(n,e,t,s){s||(s={});var c=s.enumerable,l=void 0!==s.name?s.name:e;if(r(t)&&i(t,l,s),s.global)c?n[e]=t:a(e,t);else{try{s.unsafe?n[e]&&(c=!0):delete n[e]}catch(n){}c?n[e]=t:o.f(n,e,{value:t,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return n}},function(n,e,t){"use strict";var r=t(100),o=String;n.exports=function(n){if("Symbol"===r(n))throw new TypeError("Cannot convert a Symbol value to a string");return o(n)}},function(n,e,t){"use strict";n.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(n,e,t){"use strict";var r=String;n.exports=function(n){try{return r(n)}catch(n){return"Object"}}},function(n,e,t){"use strict";var r=t(65),o=t(56),i=r("keys");n.exports=function(n){return i[n]||(i[n]=o(n))}},function(n,e,t){"use strict";var r=t(143),o=t(1),i=t(17),a=t(27)("toStringTag"),s=Object,c="Arguments"===i(function(){return arguments}());n.exports=r?i:function(n){var e,t,r;return void 0===n?"Undefined":null===n?"Null":"string"==typeof(t=function(n,e){try{return n[e]}catch(n){}}(e=s(n),a))?t:c?i(e):"Object"===(r=i(e))&&o(e.callee)?"Arguments":r}},function(n,e,t){"use strict";var r=t(32),o=t(53);n.exports=function(n,e){var t=n[e];return o(t)?void 0:r(t)}},function(n,e,t){"use strict";var r=t(0),o=t(9),i=r.document,a=o(i)&&o(i.createElement);n.exports=function(n){return a?i.createElement(n):{}}},function(n,e,t){"use strict";var r=t(4),o=t(3);n.exports=r&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},function(n,e,t){"use strict";var r=t(2),o=t(3),i=t(1),a=t(8),s=t(4),c=t(125).CONFIGURABLE,l=t(126),u=t(109),p=u.enforce,d=u.get,f=String,h=Object.defineProperty,m=r("".slice),g=r("".replace),v=r([].join),y=s&&!o((function(){return 8!==h((function(){}),"length",{value:8}).length})),b=String(String).split("String"),x=n.exports=function(n,e,t){"Symbol("===m(f(e),0,7)&&(e="["+g(f(e),/^Symbol\(([^)]*)\)/,"$1")+"]"),t&&t.getter&&(e="get "+e),t&&t.setter&&(e="set "+e),(!a(n,"name")||c&&n.name!==e)&&(s?h(n,"name",{value:e,configurable:!0}):n.name=e),y&&t&&a(t,"arity")&&n.length!==t.arity&&h(n,"length",{value:t.arity});try{t&&a(t,"constructor")&&t.constructor?s&&h(n,"prototype",{writable:!1}):n.prototype&&(n.prototype=void 0)}catch(n){}var r=p(n);return a(r,"source")||(r.source=v(b,"string"==typeof e?e:"")),n};Function.prototype.toString=x((function(){return i(this)&&d(this).source||l(this)}),"toString")},function(n,e,t){"use strict";var r=t(2),o=t(8),i=t(35),a=t(130).indexOf,s=t(50),c=r([].push);n.exports=function(n,e){var t,r=i(n),l=0,u=[];for(t in r)!o(s,t)&&o(r,t)&&c(u,t);for(;e.length>l;)o(r,t=e[l++])&&(~a(u,t)||c(u,t));return u}},function(n,e,t){"use strict";var r=t(96);n.exports=function(n,e){return void 0===n?arguments.length<2?"":e:r(n)}},function(n,e,t){"use strict";var r=t(24),o=t(110).left,i=t(111),a=t(54);r({target:"Array",proto:!0,forced:!t(112)&&a>79&&a<83||!i("reduce")},{reduce:function(n){var e=arguments.length;return o(this,n,e,e>1?arguments[1]:void 0)}})},function(n,e,t){"use strict";var r=t(30),o=t(9),i=t(62),a=t(101),s=t(124),c=t(27),l=TypeError,u=c("toPrimitive");n.exports=function(n,e){if(!o(n)||i(n))return n;var t,c=a(n,u);if(c){if(void 0===e&&(e="default"),t=r(c,n,e),!o(t)||i(t))return t;throw new l("Can't convert object to primitive value")}return void 0===e&&(e="number"),s(n,e)}},function(n,e,t){"use strict";var r,o,i,a=t(127),s=t(0),c=t(9),l=t(13),u=t(8),p=t(36),d=t(99),f=t(50),h=s.TypeError,m=s.WeakMap;if(a||p.state){var g=p.state||(p.state=new m);g.get=g.get,g.has=g.has,g.set=g.set,r=function(n,e){if(g.has(n))throw new h("Object already initialized");return e.facade=n,g.set(n,e),e},o=function(n){return g.get(n)||{}},i=function(n){return g.has(n)}}else{var v=d("state");f[v]=!0,r=function(n,e){if(u(n,v))throw new h("Object already initialized");return e.facade=n,l(n,v,e),e},o=function(n){return u(n,v)?n[v]:{}},i=function(n){return u(n,v)}}n.exports={set:r,get:o,has:i,enforce:function(n){return i(n)?o(n):r(n,{})},getterFor:function(n){return function(e){var t;if(!c(e)||(t=o(e)).type!==n)throw new h("Incompatible receiver, "+n+" required");return t}}}},function(n,e,t){"use strict";var r=t(32),o=t(29),i=t(49),a=t(25),s=TypeError,c=function(n){return function(e,t,c,l){var u=o(e),p=i(u),d=a(u);r(t);var f=n?d-1:0,h=n?-1:1;if(c<2)for(;;){if(f in p){l=p[f],f+=h;break}if(f+=h,n?f<0:d<=f)throw new s("Reduce of empty array with no initial value")}for(;n?f>=0:d>f;f+=h)f in p&&(l=t(l,p[f],f,u));return l}};n.exports={left:c(!1),right:c(!0)}},function(n,e,t){"use strict";var r=t(3);n.exports=function(n,e){var t=[][n];return!!t&&r((function(){t.call(null,e||function(){return 1},1)}))}},function(n,e,t){"use strict";var r=t(0),o=t(17);n.exports="process"===o(r.process)},function(n,e,t){"use strict";var r=t(24),o=t(0),i=t(114),a=t(115),s=o.WebAssembly,c=7!==new Error("e",{cause:7}).cause,l=function(n,e){var t={};t[n]=a(n,e,c),r({global:!0,constructor:!0,arity:1,forced:c},t)},u=function(n,e){if(s&&s[n]){var t={};t[n]=a("WebAssembly."+n,e,c),r({target:"WebAssembly",stat:!0,constructor:!0,arity:1,forced:c},t)}};l("Error",(function(n){return function(e){return i(n,this,arguments)}})),l("EvalError",(function(n){return function(e){return i(n,this,arguments)}})),l("RangeError",(function(n){return function(e){return i(n,this,arguments)}})),l("ReferenceError",(function(n){return function(e){return i(n,this,arguments)}})),l("SyntaxError",(function(n){return function(e){return i(n,this,arguments)}})),l("TypeError",(function(n){return function(e){return i(n,this,arguments)}})),l("URIError",(function(n){return function(e){return i(n,this,arguments)}})),u("CompileError",(function(n){return function(e){return i(n,this,arguments)}})),u("LinkError",(function(n){return function(e){return i(n,this,arguments)}})),u("RuntimeError",(function(n){return function(e){return i(n,this,arguments)}}))},function(n,e,t){"use strict";var r=t(33),o=Function.prototype,i=o.apply,a=o.call;n.exports="object"==typeof Reflect&&Reflect.apply||(r?a.bind(i):function(){return a.apply(i,arguments)})},function(n,e,t){"use strict";var r=t(31),o=t(8),i=t(13),a=t(48),s=t(51),c=t(57),l=t(142),u=t(116),p=t(106),d=t(117),f=t(118),h=t(4),m=t(55);n.exports=function(n,e,t,g){var v=g?2:1,y=n.split("."),b=y[y.length-1],x=r.apply(null,y);if(x){var _=x.prototype;if(!m&&o(_,"cause")&&delete _.cause,!t)return x;var w=r("Error"),T=e((function(n,e){var t=p(g?e:n,void 0),r=g?new x(n):new x;return void 0!==t&&i(r,"message",t),f(r,T,r.stack,2),this&&a(_,this)&&u(r,this,T),arguments.length>v&&d(r,arguments[v]),r}));if(T.prototype=_,"Error"!==b?s?s(T,w):c(T,w,{name:!0}):h&&"stackTraceLimit"in x&&(l(T,x,"stackTraceLimit"),l(T,x,"prepareStackTrace")),c(T,x),!m)try{_.name!==b&&i(_,"name",b),_.constructor=T}catch(n){}return T}}},function(n,e,t){"use strict";var r=t(1),o=t(9),i=t(51);n.exports=function(n,e,t){var a,s;return i&&r(a=e.constructor)&&a!==t&&o(s=a.prototype)&&s!==t.prototype&&i(n,s),n}},function(n,e,t){"use strict";var r=t(9),o=t(13);n.exports=function(n,e){r(e)&&"cause"in e&&o(n,"cause",e.cause)}},function(n,e,t){"use strict";var r=t(13),o=t(119),i=t(144),a=Error.captureStackTrace;n.exports=function(n,e,t,s){i&&(a?a(n,e):r(n,"stack",o(t,s)))}},function(n,e,t){"use strict";var r=t(2),o=Error,i=r("".replace),a=String(new o("zxcasd").stack),s=/\n\s*at [^:]*:[^\n]*/,c=s.test(a);n.exports=function(n,e){if(c&&"string"==typeof n&&!o.prepareStackTrace)for(;e--;)n=i(n,s,"");return n}},function(n,e){n.exports=function(n){var e=null==n?0:n.length;return e?n[e-1]:void 0}},function(n,e,t){n.exports=t(247)},function(n,e,t){"use strict";var r={}.propertyIsEnumerable,o=Object.getOwnPropertyDescriptor,i=o&&!r.call({1:2},1);e.f=i?function(n){var e=o(this,n);return!!e&&e.enumerable}:r},function(n,e,t){"use strict";n.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},function(n,e,t){"use strict";var r=t(30),o=t(1),i=t(9),a=TypeError;n.exports=function(n,e){var t,s;if("string"===e&&o(t=n.toString)&&!i(s=r(t,n)))return s;if(o(t=n.valueOf)&&!i(s=r(t,n)))return s;if("string"!==e&&o(t=n.toString)&&!i(s=r(t,n)))return s;throw new a("Can't convert object to primitive value")}},function(n,e,t){"use strict";var r=t(4),o=t(8),i=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,s=o(i,"name"),c=s&&"something"===function(){}.name,l=s&&(!r||r&&a(i,"name").configurable);n.exports={EXISTS:s,PROPER:c,CONFIGURABLE:l}},function(n,e,t){"use strict";var r=t(2),o=t(1),i=t(36),a=r(Function.toString);o(i.inspectSource)||(i.inspectSource=function(n){return a(n)}),n.exports=i.inspectSource},function(n,e,t){"use strict";var r=t(0),o=t(1),i=r.WeakMap;n.exports=o(i)&&/native code/.test(String(i))},function(n,e,t){"use strict";var r=t(31),o=t(2),i=t(129),a=t(134),s=t(28),c=o([].concat);n.exports=r("Reflect","ownKeys")||function(n){var e=i.f(s(n)),t=a.f;return t?c(e,t(n)):e}},function(n,e,t){"use strict";var r=t(105),o=t(97).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(n){return r(n,o)}},function(n,e,t){"use strict";var r=t(35),o=t(131),i=t(25),a=function(n){return function(e,t,a){var s,c=r(e),l=i(c),u=o(a,l);if(n&&t!=t){for(;l>u;)if((s=c[u++])!=s)return!0}else for(;l>u;u++)if((n||u in c)&&c[u]===t)return n||u||0;return!n&&-1}};n.exports={includes:a(!0),indexOf:a(!1)}},function(n,e,t){"use strict";var r=t(47),o=Math.max,i=Math.min;n.exports=function(n,e){var t=r(n);return t<0?o(t+e,0):i(t,e)}},function(n,e,t){"use strict";var r=Math.ceil,o=Math.floor;n.exports=Math.trunc||function(n){var e=+n;return(e>0?o:r)(e)}},function(n,e,t){"use strict";var r=t(47),o=Math.min;n.exports=function(n){return n>0?o(r(n),9007199254740991):0}},function(n,e,t){"use strict";e.f=Object.getOwnPropertySymbols},function(n,e,t){"use strict";var r=t(3),o=t(1),i=/#|\.prototype\./,a=function(n,e){var t=c[s(n)];return t===u||t!==l&&(o(e)?r(e):!!e)},s=a.normalize=function(n){return String(n).replace(i,".").toLowerCase()},c=a.data={},l=a.NATIVE="N",u=a.POLYFILL="P";n.exports=a},function(n,e,t){"use strict";var r=t(4),o=t(137),i=TypeError,a=Object.getOwnPropertyDescriptor,s=r&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(n){return n instanceof TypeError}}();n.exports=s?function(n,e){if(o(n)&&!a(n,"length").writable)throw new i("Cannot set read only .length");return n.length=e}:function(n,e){return n.length=e}},function(n,e,t){"use strict";var r=t(17);n.exports=Array.isArray||function(n){return"Array"===r(n)}},function(n,e,t){"use strict";var r=TypeError;n.exports=function(n){if(n>9007199254740991)throw r("Maximum allowed index exceeded");return n}},function(n,e,t){"use strict";var r=t(2),o=t(32);n.exports=function(n,e,t){try{return r(o(Object.getOwnPropertyDescriptor(n,e)[t]))}catch(n){}}},function(n,e,t){"use strict";var r=t(141),o=String,i=TypeError;n.exports=function(n){if(r(n))return n;throw new i("Can't set "+o(n)+" as a prototype")}},function(n,e,t){"use strict";var r=t(9);n.exports=function(n){return r(n)||null===n}},function(n,e,t){"use strict";var r=t(16).f;n.exports=function(n,e,t){t in n||r(n,t,{configurable:!0,get:function(){return e[t]},set:function(n){e[t]=n}})}},function(n,e,t){"use strict";var r={};r[t(27)("toStringTag")]="z",n.exports="[object z]"===String(r)},function(n,e,t){"use strict";var r=t(3),o=t(34);n.exports=!r((function(){var n=new Error("a");return!("stack"in n)||(Object.defineProperty(n,"stack",o(1,7)),7!==n.stack)}))},function(n,e,t){var r=t(67),o=t(146);n.exports=function n(e,t,i,a,s){var c=-1,l=e.length;for(i||(i=o),s||(s=[]);++c<l;){var u=e[c];t>0&&i(u)?t>1?n(u,t-1,i,a,s):r(s,u):a||(s[s.length]=u)}return s}},function(n,e,t){var r=t(15),o=t(38),i=t(5),a=r?r.isConcatSpreadable:void 0;n.exports=function(n){return i(n)||o(n)||!!(a&&n&&n[a])}},function(n,e,t){var r=t(14),o=t(12);n.exports=function(n){return o(n)&&"[object Arguments]"==r(n)}},function(n,e,t){var r=t(15),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,s=r?r.toStringTag:void 0;n.exports=function(n){var e=i.call(n,s),t=n[s];try{n[s]=void 0;var r=!0}catch(n){}var o=a.call(n);return r&&(e?n[s]=t:delete n[s]),o}},function(n,e){var t=Object.prototype.toString;n.exports=function(n){return t.call(n)}},function(n,e,t){var r=t(151),o=t(207),i=t(46),a=t(5),s=t(218);n.exports=function(n){return"function"==typeof n?n:null==n?i:"object"==typeof n?a(n)?o(n[0],n[1]):r(n):s(n)}},function(n,e,t){var r=t(152),o=t(206),i=t(84);n.exports=function(n){var e=o(n);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(t){return t===n||r(t,n,e)}}},function(n,e,t){var r=t(69),o=t(73);n.exports=function(n,e,t,i){var a=t.length,s=a,c=!i;if(null==n)return!s;for(n=Object(n);a--;){var l=t[a];if(c&&l[2]?l[1]!==n[l[0]]:!(l[0]in n))return!1}for(;++a<s;){var u=(l=t[a])[0],p=n[u],d=l[1];if(c&&l[2]){if(void 0===p&&!(u in n))return!1}else{var f=new r;if(i)var h=i(p,d,u,n,e,f);if(!(void 0===h?o(d,p,3,i,f):h))return!1}}return!0}},function(n,e){n.exports=function(){this.__data__=[],this.size=0}},function(n,e,t){var r=t(19),o=Array.prototype.splice;n.exports=function(n){var e=this.__data__,t=r(e,n);return!(t<0)&&(t==e.length-1?e.pop():o.call(e,t,1),--this.size,!0)}},function(n,e,t){var r=t(19);n.exports=function(n){var e=this.__data__,t=r(e,n);return t<0?void 0:e[t][1]}},function(n,e,t){var r=t(19);n.exports=function(n){return r(this.__data__,n)>-1}},function(n,e,t){var r=t(19);n.exports=function(n,e){var t=this.__data__,o=r(t,n);return o<0?(++this.size,t.push([n,e])):t[o][1]=e,this}},function(n,e,t){var r=t(18);n.exports=function(){this.__data__=new r,this.size=0}},function(n,e){n.exports=function(n){var e=this.__data__,t=e.delete(n);return this.size=e.size,t}},function(n,e){n.exports=function(n){return this.__data__.get(n)}},function(n,e){n.exports=function(n){return this.__data__.has(n)}},function(n,e,t){var r=t(18),o=t(39),i=t(41);n.exports=function(n,e){var t=this.__data__;if(t instanceof r){var a=t.__data__;if(!o||a.length<199)return a.push([n,e]),this.size=++t.size,this;t=this.__data__=new i(a)}return t.set(n,e),this.size=t.size,this}},function(n,e,t){var r=t(71),o=t(164),i=t(40),a=t(72),s=/^\[object .+?Constructor\]$/,c=Function.prototype,l=Object.prototype,u=c.toString,p=l.hasOwnProperty,d=RegExp("^"+u.call(p).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");n.exports=function(n){return!(!i(n)||o(n))&&(r(n)?d:s).test(a(n))}},function(n,e,t){var r,o=t(165),i=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";n.exports=function(n){return!!i&&i in n}},function(n,e,t){var r=t(6)["__core-js_shared__"];n.exports=r},function(n,e){n.exports=function(n,e){return null==n?void 0:n[e]}},function(n,e,t){var r=t(168),o=t(18),i=t(39);n.exports=function(){this.size=0,this.__data__={hash:new r,map:new(i||o),string:new r}}},function(n,e,t){var r=t(169),o=t(170),i=t(171),a=t(172),s=t(173);function c(n){var e=-1,t=null==n?0:n.length;for(this.clear();++e<t;){var r=n[e];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=s,n.exports=c},function(n,e,t){var r=t(20);n.exports=function(){this.__data__=r?r(null):{},this.size=0}},function(n,e){n.exports=function(n){var e=this.has(n)&&delete this.__data__[n];return this.size-=e?1:0,e}},function(n,e,t){var r=t(20),o=Object.prototype.hasOwnProperty;n.exports=function(n){var e=this.__data__;if(r){var t=e[n];return"__lodash_hash_undefined__"===t?void 0:t}return o.call(e,n)?e[n]:void 0}},function(n,e,t){var r=t(20),o=Object.prototype.hasOwnProperty;n.exports=function(n){var e=this.__data__;return r?void 0!==e[n]:o.call(e,n)}},function(n,e,t){var r=t(20);n.exports=function(n,e){var t=this.__data__;return this.size+=this.has(n)?0:1,t[n]=r&&void 0===e?"__lodash_hash_undefined__":e,this}},function(n,e,t){var r=t(21);n.exports=function(n){var e=r(this,n).delete(n);return this.size-=e?1:0,e}},function(n,e){n.exports=function(n){var e=typeof n;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==n:null===n}},function(n,e,t){var r=t(21);n.exports=function(n){return r(this,n).get(n)}},function(n,e,t){var r=t(21);n.exports=function(n){return r(this,n).has(n)}},function(n,e,t){var r=t(21);n.exports=function(n,e){var t=r(this,n),o=t.size;return t.set(n,e),this.size+=t.size==o?0:1,this}},function(n,e,t){var r=t(69),o=t(74),i=t(183),a=t(186),s=t(202),c=t(5),l=t(78),u=t(80),p="[object Object]",d=Object.prototype.hasOwnProperty;n.exports=function(n,e,t,f,h,m){var g=c(n),v=c(e),y=g?"[object Array]":s(n),b=v?"[object Array]":s(e),x=(y="[object Arguments]"==y?p:y)==p,_=(b="[object Arguments]"==b?p:b)==p,w=y==b;if(w&&l(n)){if(!l(e))return!1;g=!0,x=!1}if(w&&!x)return m||(m=new r),g||u(n)?o(n,e,t,f,h,m):i(n,e,y,t,f,h,m);if(!(1&t)){var T=x&&d.call(n,"__wrapped__"),E=_&&d.call(e,"__wrapped__");if(T||E){var k=T?n.value():n,C=E?e.value():e;return m||(m=new r),h(k,C,t,f,m)}}return!!w&&(m||(m=new r),a(n,e,t,f,h,m))}},function(n,e){n.exports=function(n){return this.__data__.set(n,"__lodash_hash_undefined__"),this}},function(n,e){n.exports=function(n){return this.__data__.has(n)}},function(n,e){n.exports=function(n,e){for(var t=-1,r=null==n?0:n.length;++t<r;)if(e(n[t],t,n))return!0;return!1}},function(n,e,t){var r=t(15),o=t(184),i=t(70),a=t(74),s=t(185),c=t(42),l=r?r.prototype:void 0,u=l?l.valueOf:void 0;n.exports=function(n,e,t,r,l,p,d){switch(t){case"[object DataView]":if(n.byteLength!=e.byteLength||n.byteOffset!=e.byteOffset)return!1;n=n.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(n.byteLength!=e.byteLength||!p(new o(n),new o(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+n,+e);case"[object Error]":return n.name==e.name&&n.message==e.message;case"[object RegExp]":case"[object String]":return n==e+"";case"[object Map]":var f=s;case"[object Set]":var h=1&r;if(f||(f=c),n.size!=e.size&&!h)return!1;var m=d.get(n);if(m)return m==e;r|=2,d.set(n,e);var g=a(f(n),f(e),r,l,p,d);return d.delete(n),g;case"[object Symbol]":if(u)return u.call(n)==u.call(e)}return!1}},function(n,e,t){var r=t(6).Uint8Array;n.exports=r},function(n,e){n.exports=function(n){var e=-1,t=Array(n.size);return n.forEach((function(n,r){t[++e]=[r,n]})),t}},function(n,e,t){var r=t(187),o=Object.prototype.hasOwnProperty;n.exports=function(n,e,t,i,a,s){var c=1&t,l=r(n),u=l.length;if(u!=r(e).length&&!c)return!1;for(var p=u;p--;){var d=l[p];if(!(c?d in e:o.call(e,d)))return!1}var f=s.get(n),h=s.get(e);if(f&&h)return f==e&&h==n;var m=!0;s.set(n,e),s.set(e,n);for(var g=c;++p<u;){var v=n[d=l[p]],y=e[d];if(i)var b=c?i(y,v,d,e,n,s):i(v,y,d,n,e,s);if(!(void 0===b?v===y||a(v,y,t,i,s):b)){m=!1;break}g||(g="constructor"==d)}if(m&&!g){var x=n.constructor,_=e.constructor;x==_||!("constructor"in n)||!("constructor"in e)||"function"==typeof x&&x instanceof x&&"function"==typeof _&&_ instanceof _||(m=!1)}return s.delete(n),s.delete(e),m}},function(n,e,t){var r=t(188),o=t(189),i=t(77);n.exports=function(n){return r(n,i,o)}},function(n,e,t){var r=t(67),o=t(5);n.exports=function(n,e,t){var i=e(n);return o(n)?i:r(i,t(n))}},function(n,e,t){var r=t(190),o=t(191),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,s=a?function(n){return null==n?[]:(n=Object(n),r(a(n),(function(e){return i.call(n,e)})))}:o;n.exports=s},function(n,e){n.exports=function(n,e){for(var t=-1,r=null==n?0:n.length,o=0,i=[];++t<r;){var a=n[t];e(a,t,n)&&(i[o++]=a)}return i}},function(n,e){n.exports=function(){return[]}},function(n,e,t){var r=t(193),o=t(38),i=t(5),a=t(78),s=t(79),c=t(80),l=Object.prototype.hasOwnProperty;n.exports=function(n,e){var t=i(n),u=!t&&o(n),p=!t&&!u&&a(n),d=!t&&!u&&!p&&c(n),f=t||u||p||d,h=f?r(n.length,String):[],m=h.length;for(var g in n)!e&&!l.call(n,g)||f&&("length"==g||p&&("offset"==g||"parent"==g)||d&&("buffer"==g||"byteLength"==g||"byteOffset"==g)||s(g,m))||h.push(g);return h}},function(n,e){n.exports=function(n,e){for(var t=-1,r=Array(n);++t<n;)r[t]=e(t);return r}},function(n,e){n.exports=function(){return!1}},function(n,e,t){var r=t(14),o=t(43),i=t(12),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,n.exports=function(n){return i(n)&&o(n.length)&&!!a[r(n)]}},function(n,e){n.exports=function(n){return function(e){return n(e)}}},function(n,e,t){(function(n){var r=t(68),o=e&&!e.nodeType&&e,i=o&&"object"==typeof n&&n&&!n.nodeType&&n,a=i&&i.exports===o&&r.process,s=function(){try{var n=i&&i.require&&i.require("util").types;return n||a&&a.binding&&a.binding("util")}catch(n){}}();n.exports=s}).call(this,t(58)(n))},function(n,e,t){var r=t(199),o=t(200),i=Object.prototype.hasOwnProperty;n.exports=function(n){if(!r(n))return o(n);var e=[];for(var t in Object(n))i.call(n,t)&&"constructor"!=t&&e.push(t);return e}},function(n,e){var t=Object.prototype;n.exports=function(n){var e=n&&n.constructor;return n===("function"==typeof e&&e.prototype||t)}},function(n,e,t){var r=t(201)(Object.keys,Object);n.exports=r},function(n,e){n.exports=function(n,e){return function(t){return n(e(t))}}},function(n,e,t){var r=t(203),o=t(39),i=t(204),a=t(82),s=t(205),c=t(14),l=t(72),u=l(r),p=l(o),d=l(i),f=l(a),h=l(s),m=c;(r&&"[object DataView]"!=m(new r(new ArrayBuffer(1)))||o&&"[object Map]"!=m(new o)||i&&"[object Promise]"!=m(i.resolve())||a&&"[object Set]"!=m(new a)||s&&"[object WeakMap]"!=m(new s))&&(m=function(n){var e=c(n),t="[object Object]"==e?n.constructor:void 0,r=t?l(t):"";if(r)switch(r){case u:return"[object DataView]";case p:return"[object Map]";case d:return"[object Promise]";case f:return"[object Set]";case h:return"[object WeakMap]"}return e}),n.exports=m},function(n,e,t){var r=t(10)(t(6),"DataView");n.exports=r},function(n,e,t){var r=t(10)(t(6),"Promise");n.exports=r},function(n,e,t){var r=t(10)(t(6),"WeakMap");n.exports=r},function(n,e,t){var r=t(83),o=t(77);n.exports=function(n){for(var e=o(n),t=e.length;t--;){var i=e[t],a=n[i];e[t]=[i,a,r(a)]}return e}},function(n,e,t){var r=t(73),o=t(208),i=t(215),a=t(44),s=t(83),c=t(84),l=t(22);n.exports=function(n,e){return a(n)&&s(e)?c(l(n),e):function(t){var a=o(t,n);return void 0===a&&a===e?i(t,n):r(e,a,3)}}},function(n,e,t){var r=t(85);n.exports=function(n,e,t){var o=null==n?void 0:r(n,e);return void 0===o?t:o}},function(n,e,t){var r=t(210),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=r((function(n){var e=[];return 46===n.charCodeAt(0)&&e.push(""),n.replace(o,(function(n,t,r,o){e.push(r?o.replace(i,"$1"):t||n)})),e}));n.exports=a},function(n,e,t){var r=t(211);n.exports=function(n){var e=r(n,(function(n){return 500===t.size&&t.clear(),n})),t=e.cache;return e}},function(n,e,t){var r=t(41);function o(n,e){if("function"!=typeof n||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var t=function(){var r=arguments,o=e?e.apply(this,r):r[0],i=t.cache;if(i.has(o))return i.get(o);var a=n.apply(this,r);return t.cache=i.set(o,a)||i,a};return t.cache=new(o.Cache||r),t}o.Cache=r,n.exports=o},function(n,e,t){var r=t(213);n.exports=function(n){return null==n?"":r(n)}},function(n,e,t){var r=t(15),o=t(214),i=t(5),a=t(45),s=r?r.prototype:void 0,c=s?s.toString:void 0;n.exports=function n(e){if("string"==typeof e)return e;if(i(e))return o(e,n)+"";if(a(e))return c?c.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},function(n,e){n.exports=function(n,e){for(var t=-1,r=null==n?0:n.length,o=Array(r);++t<r;)o[t]=e(n[t],t,n);return o}},function(n,e,t){var r=t(216),o=t(217);n.exports=function(n,e){return null!=n&&o(n,e,r)}},function(n,e){n.exports=function(n,e){return null!=n&&e in Object(n)}},function(n,e,t){var r=t(86),o=t(38),i=t(5),a=t(79),s=t(43),c=t(22);n.exports=function(n,e,t){for(var l=-1,u=(e=r(e,n)).length,p=!1;++l<u;){var d=c(e[l]);if(!(p=null!=n&&t(n,d)))break;n=n[d]}return p||++l!=u?p:!!(u=null==n?0:n.length)&&s(u)&&a(d,u)&&(i(n)||o(n))}},function(n,e,t){var r=t(219),o=t(220),i=t(44),a=t(22);n.exports=function(n){return i(n)?r(a(n)):o(n)}},function(n,e){n.exports=function(n){return function(e){return null==e?void 0:e[n]}}},function(n,e,t){var r=t(85);n.exports=function(n){return function(e){return r(e,n)}}},function(n,e,t){var r=t(46),o=t(222),i=t(224);n.exports=function(n,e){return i(o(n,e,r),n+"")}},function(n,e,t){var r=t(223),o=Math.max;n.exports=function(n,e,t){return e=o(void 0===e?n.length-1:e,0),function(){for(var i=arguments,a=-1,s=o(i.length-e,0),c=Array(s);++a<s;)c[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=t(c),r(n,this,l)}}},function(n,e){n.exports=function(n,e,t){switch(t.length){case 0:return n.call(e);case 1:return n.call(e,t[0]);case 2:return n.call(e,t[0],t[1]);case 3:return n.call(e,t[0],t[1],t[2])}return n.apply(e,t)}},function(n,e,t){var r=t(225),o=t(228)(r);n.exports=o},function(n,e,t){var r=t(226),o=t(227),i=t(46),a=o?function(n,e){return o(n,"toString",{configurable:!0,enumerable:!1,value:r(e),writable:!0})}:i;n.exports=a},function(n,e){n.exports=function(n){return function(){return n}}},function(n,e,t){var r=t(10),o=function(){try{var n=r(Object,"defineProperty");return n({},"",{}),n}catch(n){}}();n.exports=o},function(n,e){var t=Date.now;n.exports=function(n){var e=0,r=0;return function(){var o=t(),i=16-(o-r);if(r=o,i>0){if(++e>=800)return arguments[0]}else e=0;return n.apply(void 0,arguments)}}},function(n,e,t){var r=t(75),o=t(230),i=t(235),a=t(76),s=t(236),c=t(42);n.exports=function(n,e,t){var l=-1,u=o,p=n.length,d=!0,f=[],h=f;if(t)d=!1,u=i;else if(p>=200){var m=e?null:s(n);if(m)return c(m);d=!1,u=a,h=new r}else h=e?[]:f;n:for(;++l<p;){var g=n[l],v=e?e(g):g;if(g=t||0!==g?g:0,d&&v==v){for(var y=h.length;y--;)if(h[y]===v)continue n;e&&h.push(v),f.push(g)}else u(h,v,t)||(h!==f&&h.push(v),f.push(g))}return f}},function(n,e,t){var r=t(231);n.exports=function(n,e){return!!(null==n?0:n.length)&&r(n,e,0)>-1}},function(n,e,t){var r=t(232),o=t(233),i=t(234);n.exports=function(n,e,t){return e==e?i(n,e,t):r(n,o,t)}},function(n,e){n.exports=function(n,e,t,r){for(var o=n.length,i=t+(r?1:-1);r?i--:++i<o;)if(e(n[i],i,n))return i;return-1}},function(n,e){n.exports=function(n){return n!=n}},function(n,e){n.exports=function(n,e,t){for(var r=t-1,o=n.length;++r<o;)if(n[r]===e)return r;return-1}},function(n,e){n.exports=function(n,e,t){for(var r=-1,o=null==n?0:n.length;++r<o;)if(t(e,n[r]))return!0;return!1}},function(n,e,t){var r=t(82),o=t(237),i=t(42),a=r&&1/i(new r([,-0]))[1]==1/0?function(n){return new r(n)}:o;n.exports=a},function(n,e){n.exports=function(){}},function(n,e,t){var r=t(81),o=t(12);n.exports=function(n){return o(n)&&r(n)}},function(n,e,t){},function(n,e,t){},function(n,e,t){"use strict";t(87)},function(n,e,t){"use strict";t(88)},function(n,e,t){},function(n,e,t){},function(n,e,t){"use strict";t(89)},function(n,e,t){"use strict";t(90)},function(n,e,t){"use strict";t.r(e);
/*!
 * Vue.js v2.7.16
 * (c) 2014-2023 Evan You
 * Released under the MIT License.
 */
var r=Object.freeze({}),o=Array.isArray;function i(n){return null==n}function a(n){return null!=n}function s(n){return!0===n}function c(n){return"string"==typeof n||"number"==typeof n||"symbol"==typeof n||"boolean"==typeof n}function l(n){return"function"==typeof n}function u(n){return null!==n&&"object"==typeof n}var p=Object.prototype.toString;function d(n){return"[object Object]"===p.call(n)}function f(n){return"[object RegExp]"===p.call(n)}function h(n){var e=parseFloat(String(n));return e>=0&&Math.floor(e)===e&&isFinite(n)}function m(n){return a(n)&&"function"==typeof n.then&&"function"==typeof n.catch}function g(n){return null==n?"":Array.isArray(n)||d(n)&&n.toString===p?JSON.stringify(n,v,2):String(n)}function v(n,e){return e&&e.__v_isRef?e.value:e}function y(n){var e=parseFloat(n);return isNaN(e)?n:e}function b(n,e){for(var t=Object.create(null),r=n.split(","),o=0;o<r.length;o++)t[r[o]]=!0;return e?function(n){return t[n.toLowerCase()]}:function(n){return t[n]}}b("slot,component",!0);var x=b("key,ref,slot,slot-scope,is");function _(n,e){var t=n.length;if(t){if(e===n[t-1])return void(n.length=t-1);var r=n.indexOf(e);if(r>-1)return n.splice(r,1)}}var w=Object.prototype.hasOwnProperty;function T(n,e){return w.call(n,e)}function E(n){var e=Object.create(null);return function(t){return e[t]||(e[t]=n(t))}}var k=/-(\w)/g,C=E((function(n){return n.replace(k,(function(n,e){return e?e.toUpperCase():""}))})),S=E((function(n){return n.charAt(0).toUpperCase()+n.slice(1)})),O=/\B([A-Z])/g,j=E((function(n){return n.replace(O,"-$1").toLowerCase()}));var A=Function.prototype.bind?function(n,e){return n.bind(e)}:function(n,e){function t(t){var r=arguments.length;return r?r>1?n.apply(e,arguments):n.call(e,t):n.call(e)}return t._length=n.length,t};function I(n,e){e=e||0;for(var t=n.length-e,r=new Array(t);t--;)r[t]=n[t+e];return r}function $(n,e){for(var t in e)n[t]=e[t];return n}function P(n){for(var e={},t=0;t<n.length;t++)n[t]&&$(e,n[t]);return e}function z(n,e,t){}var M=function(n,e,t){return!1},D=function(n){return n};function L(n,e){if(n===e)return!0;var t=u(n),r=u(e);if(!t||!r)return!t&&!r&&String(n)===String(e);try{var o=Array.isArray(n),i=Array.isArray(e);if(o&&i)return n.length===e.length&&n.every((function(n,t){return L(n,e[t])}));if(n instanceof Date&&e instanceof Date)return n.getTime()===e.getTime();if(o||i)return!1;var a=Object.keys(n),s=Object.keys(e);return a.length===s.length&&a.every((function(t){return L(n[t],e[t])}))}catch(n){return!1}}function N(n,e){for(var t=0;t<n.length;t++)if(L(n[t],e))return t;return-1}function U(n){var e=!1;return function(){e||(e=!0,n.apply(this,arguments))}}function R(n,e){return n===e?0===n&&1/n!=1/e:n==n||e==e}var B=["component","directive","filter"],q=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch","renderTracked","renderTriggered"],F={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:M,isReservedAttr:M,isUnknownElement:M,getTagNamespace:z,parsePlatformTagName:D,mustUseProp:M,async:!0,_lifecycleHooks:q},H=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function V(n){var e=(n+"").charCodeAt(0);return 36===e||95===e}function Y(n,e,t,r){Object.defineProperty(n,e,{value:t,enumerable:!!r,writable:!0,configurable:!0})}var G=new RegExp("[^".concat(H.source,".$_\\d]"));var W="__proto__"in{},K="undefined"!=typeof window,Z=K&&window.navigator.userAgent.toLowerCase(),Q=Z&&/msie|trident/.test(Z),X=Z&&Z.indexOf("msie 9.0")>0,J=Z&&Z.indexOf("edge/")>0;Z&&Z.indexOf("android");var nn=Z&&/iphone|ipad|ipod|ios/.test(Z);Z&&/chrome\/\d+/.test(Z),Z&&/phantomjs/.test(Z);var en,tn=Z&&Z.match(/firefox\/(\d+)/),rn={}.watch,on=!1;if(K)try{var an={};Object.defineProperty(an,"passive",{get:function(){on=!0}}),window.addEventListener("test-passive",null,an)}catch(n){}var sn=function(){return void 0===en&&(en=!K&&"undefined"!=typeof global&&(global.process&&"server"===global.process.env.VUE_ENV)),en},cn=K&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function ln(n){return"function"==typeof n&&/native code/.test(n.toString())}var un,pn="undefined"!=typeof Symbol&&ln(Symbol)&&"undefined"!=typeof Reflect&&ln(Reflect.ownKeys);un="undefined"!=typeof Set&&ln(Set)?Set:function(){function n(){this.set=Object.create(null)}return n.prototype.has=function(n){return!0===this.set[n]},n.prototype.add=function(n){this.set[n]=!0},n.prototype.clear=function(){this.set=Object.create(null)},n}();var dn=null;function fn(n){void 0===n&&(n=null),n||dn&&dn._scope.off(),dn=n,n&&n._scope.on()}var hn=function(){function n(n,e,t,r,o,i,a,s){this.tag=n,this.data=e,this.children=t,this.text=r,this.elm=o,this.ns=void 0,this.context=i,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=e&&e.key,this.componentOptions=a,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1}return Object.defineProperty(n.prototype,"child",{get:function(){return this.componentInstance},enumerable:!1,configurable:!0}),n}(),mn=function(n){void 0===n&&(n="");var e=new hn;return e.text=n,e.isComment=!0,e};function gn(n){return new hn(void 0,void 0,void 0,String(n))}function vn(n){var e=new hn(n.tag,n.data,n.children&&n.children.slice(),n.text,n.elm,n.context,n.componentOptions,n.asyncFactory);return e.ns=n.ns,e.isStatic=n.isStatic,e.key=n.key,e.isComment=n.isComment,e.fnContext=n.fnContext,e.fnOptions=n.fnOptions,e.fnScopeId=n.fnScopeId,e.asyncMeta=n.asyncMeta,e.isCloned=!0,e}"function"==typeof SuppressedError&&SuppressedError;var yn=0,bn=[],xn=function(){function n(){this._pending=!1,this.id=yn++,this.subs=[]}return n.prototype.addSub=function(n){this.subs.push(n)},n.prototype.removeSub=function(n){this.subs[this.subs.indexOf(n)]=null,this._pending||(this._pending=!0,bn.push(this))},n.prototype.depend=function(e){n.target&&n.target.addDep(this)},n.prototype.notify=function(n){var e=this.subs.filter((function(n){return n}));for(var t=0,r=e.length;t<r;t++){0,e[t].update()}},n}();xn.target=null;var _n=[];function wn(n){_n.push(n),xn.target=n}function Tn(){_n.pop(),xn.target=_n[_n.length-1]}var En=Array.prototype,kn=Object.create(En);["push","pop","shift","unshift","splice","sort","reverse"].forEach((function(n){var e=En[n];Y(kn,n,(function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var o,i=e.apply(this,t),a=this.__ob__;switch(n){case"push":case"unshift":o=t;break;case"splice":o=t.slice(2)}return o&&a.observeArray(o),a.dep.notify(),i}))}));var Cn=Object.getOwnPropertyNames(kn),Sn={},On=!0;function jn(n){On=n}var An={notify:z,depend:z,addSub:z,removeSub:z},In=function(){function n(n,e,t){if(void 0===e&&(e=!1),void 0===t&&(t=!1),this.value=n,this.shallow=e,this.mock=t,this.dep=t?An:new xn,this.vmCount=0,Y(n,"__ob__",this),o(n)){if(!t)if(W)n.__proto__=kn;else for(var r=0,i=Cn.length;r<i;r++){Y(n,s=Cn[r],kn[s])}e||this.observeArray(n)}else{var a=Object.keys(n);for(r=0;r<a.length;r++){var s;Pn(n,s=a[r],Sn,void 0,e,t)}}}return n.prototype.observeArray=function(n){for(var e=0,t=n.length;e<t;e++)$n(n[e],!1,this.mock)},n}();function $n(n,e,t){return n&&T(n,"__ob__")&&n.__ob__ instanceof In?n.__ob__:!On||!t&&sn()||!o(n)&&!d(n)||!Object.isExtensible(n)||n.__v_skip||Rn(n)||n instanceof hn?void 0:new In(n,e,t)}function Pn(n,e,t,r,i,a,s){void 0===s&&(s=!1);var c=new xn,l=Object.getOwnPropertyDescriptor(n,e);if(!l||!1!==l.configurable){var u=l&&l.get,p=l&&l.set;u&&!p||t!==Sn&&2!==arguments.length||(t=n[e]);var d=i?t&&t.__ob__:$n(t,!1,a);return Object.defineProperty(n,e,{enumerable:!0,configurable:!0,get:function(){var e=u?u.call(n):t;return xn.target&&(c.depend(),d&&(d.dep.depend(),o(e)&&Dn(e))),Rn(e)&&!i?e.value:e},set:function(e){var r=u?u.call(n):t;if(R(r,e)){if(p)p.call(n,e);else{if(u)return;if(!i&&Rn(r)&&!Rn(e))return void(r.value=e);t=e}d=i?e&&e.__ob__:$n(e,!1,a),c.notify()}}}),c}}function zn(n,e,t){if(!Un(n)){var r=n.__ob__;return o(n)&&h(e)?(n.length=Math.max(n.length,e),n.splice(e,1,t),r&&!r.shallow&&r.mock&&$n(t,!1,!0),t):e in n&&!(e in Object.prototype)?(n[e]=t,t):n._isVue||r&&r.vmCount?t:r?(Pn(r.value,e,t,void 0,r.shallow,r.mock),r.dep.notify(),t):(n[e]=t,t)}}function Mn(n,e){if(o(n)&&h(e))n.splice(e,1);else{var t=n.__ob__;n._isVue||t&&t.vmCount||Un(n)||T(n,e)&&(delete n[e],t&&t.dep.notify())}}function Dn(n){for(var e=void 0,t=0,r=n.length;t<r;t++)(e=n[t])&&e.__ob__&&e.__ob__.dep.depend(),o(e)&&Dn(e)}function Ln(n){return Nn(n,!0),Y(n,"__v_isShallow",!0),n}function Nn(n,e){if(!Un(n)){$n(n,e,sn());0}}function Un(n){return!(!n||!n.__v_isReadonly)}function Rn(n){return!(!n||!0!==n.__v_isRef)}function Bn(n,e,t){Object.defineProperty(n,t,{enumerable:!0,configurable:!0,get:function(){var n=e[t];if(Rn(n))return n.value;var r=n&&n.__ob__;return r&&r.dep.depend(),n},set:function(n){var r=e[t];Rn(r)&&!Rn(n)?r.value=n:e[t]=n}})}"".concat("watcher"," callback"),"".concat("watcher"," getter"),"".concat("watcher"," cleanup");var qn;var Fn=function(){function n(n){void 0===n&&(n=!1),this.detached=n,this.active=!0,this.effects=[],this.cleanups=[],this.parent=qn,!n&&qn&&(this.index=(qn.scopes||(qn.scopes=[])).push(this)-1)}return n.prototype.run=function(n){if(this.active){var e=qn;try{return qn=this,n()}finally{qn=e}}else 0},n.prototype.on=function(){qn=this},n.prototype.off=function(){qn=this.parent},n.prototype.stop=function(n){if(this.active){var e=void 0,t=void 0;for(e=0,t=this.effects.length;e<t;e++)this.effects[e].teardown();for(e=0,t=this.cleanups.length;e<t;e++)this.cleanups[e]();if(this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].stop(!0);if(!this.detached&&this.parent&&!n){var r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this.active=!1}},n}();function Hn(n){var e=n._provided,t=n.$parent&&n.$parent._provided;return t===e?n._provided=Object.create(t):e}var Vn=E((function(n){var e="&"===n.charAt(0),t="~"===(n=e?n.slice(1):n).charAt(0),r="!"===(n=t?n.slice(1):n).charAt(0);return{name:n=r?n.slice(1):n,once:t,capture:r,passive:e}}));function Yn(n,e){function t(){var n=t.fns;if(!o(n))return Oe(n,null,arguments,e,"v-on handler");for(var r=n.slice(),i=0;i<r.length;i++)Oe(r[i],null,arguments,e,"v-on handler")}return t.fns=n,t}function Gn(n,e,t,r,o,a){var c,l,u,p;for(c in n)l=n[c],u=e[c],p=Vn(c),i(l)||(i(u)?(i(l.fns)&&(l=n[c]=Yn(l,a)),s(p.once)&&(l=n[c]=o(p.name,l,p.capture)),t(p.name,l,p.capture,p.passive,p.params)):l!==u&&(u.fns=l,n[c]=u));for(c in e)i(n[c])&&r((p=Vn(c)).name,e[c],p.capture)}function Wn(n,e,t){var r;n instanceof hn&&(n=n.data.hook||(n.data.hook={}));var o=n[e];function c(){t.apply(this,arguments),_(r.fns,c)}i(o)?r=Yn([c]):a(o.fns)&&s(o.merged)?(r=o).fns.push(c):r=Yn([o,c]),r.merged=!0,n[e]=r}function Kn(n,e,t,r,o){if(a(e)){if(T(e,t))return n[t]=e[t],o||delete e[t],!0;if(T(e,r))return n[t]=e[r],o||delete e[r],!0}return!1}function Zn(n){return c(n)?[gn(n)]:o(n)?function n(e,t){var r,l,u,p,d=[];for(r=0;r<e.length;r++)i(l=e[r])||"boolean"==typeof l||(u=d.length-1,p=d[u],o(l)?l.length>0&&(Qn((l=n(l,"".concat(t||"","_").concat(r)))[0])&&Qn(p)&&(d[u]=gn(p.text+l[0].text),l.shift()),d.push.apply(d,l)):c(l)?Qn(p)?d[u]=gn(p.text+l):""!==l&&d.push(gn(l)):Qn(l)&&Qn(p)?d[u]=gn(p.text+l.text):(s(e._isVList)&&a(l.tag)&&i(l.key)&&a(t)&&(l.key="__vlist".concat(t,"_").concat(r,"__")),d.push(l)));return d}(n):void 0}function Qn(n){return a(n)&&a(n.text)&&!1===n.isComment}function Xn(n,e){var t,r,i,s,c=null;if(o(n)||"string"==typeof n)for(c=new Array(n.length),t=0,r=n.length;t<r;t++)c[t]=e(n[t],t);else if("number"==typeof n)for(c=new Array(n),t=0;t<n;t++)c[t]=e(t+1,t);else if(u(n))if(pn&&n[Symbol.iterator]){c=[];for(var l=n[Symbol.iterator](),p=l.next();!p.done;)c.push(e(p.value,c.length)),p=l.next()}else for(i=Object.keys(n),c=new Array(i.length),t=0,r=i.length;t<r;t++)s=i[t],c[t]=e(n[s],s,t);return a(c)||(c=[]),c._isVList=!0,c}function Jn(n,e,t,r){var o,i=this.$scopedSlots[n];i?(t=t||{},r&&(t=$($({},r),t)),o=i(t)||(l(e)?e():e)):o=this.$slots[n]||(l(e)?e():e);var a=t&&t.slot;return a?this.$createElement("template",{slot:a},o):o}function ne(n){return It(this.$options,"filters",n,!0)||D}function ee(n,e){return o(n)?-1===n.indexOf(e):n!==e}function te(n,e,t,r,o){var i=F.keyCodes[e]||t;return o&&r&&!F.keyCodes[e]?ee(o,r):i?ee(i,n):r?j(r)!==e:void 0===n}function re(n,e,t,r,i){if(t)if(u(t)){o(t)&&(t=P(t));var a=void 0,s=function(o){if("class"===o||"style"===o||x(o))a=n;else{var s=n.attrs&&n.attrs.type;a=r||F.mustUseProp(e,s,o)?n.domProps||(n.domProps={}):n.attrs||(n.attrs={})}var c=C(o),l=j(o);c in a||l in a||(a[o]=t[o],i&&((n.on||(n.on={}))["update:".concat(o)]=function(n){t[o]=n}))};for(var c in t)s(c)}else;return n}function oe(n,e){var t=this._staticTrees||(this._staticTrees=[]),r=t[n];return r&&!e||ae(r=t[n]=this.$options.staticRenderFns[n].call(this._renderProxy,this._c,this),"__static__".concat(n),!1),r}function ie(n,e,t){return ae(n,"__once__".concat(e).concat(t?"_".concat(t):""),!0),n}function ae(n,e,t){if(o(n))for(var r=0;r<n.length;r++)n[r]&&"string"!=typeof n[r]&&se(n[r],"".concat(e,"_").concat(r),t);else se(n,e,t)}function se(n,e,t){n.isStatic=!0,n.key=e,n.isOnce=t}function ce(n,e){if(e)if(d(e)){var t=n.on=n.on?$({},n.on):{};for(var r in e){var o=t[r],i=e[r];t[r]=o?[].concat(o,i):i}}else;return n}function le(n,e,t,r){e=e||{$stable:!t};for(var i=0;i<n.length;i++){var a=n[i];o(a)?le(a,e,t):a&&(a.proxy&&(a.fn.proxy=!0),e[a.key]=a.fn)}return r&&(e.$key=r),e}function ue(n,e){for(var t=0;t<e.length;t+=2){var r=e[t];"string"==typeof r&&r&&(n[e[t]]=e[t+1])}return n}function pe(n,e){return"string"==typeof n?e+n:n}function de(n){n._o=ie,n._n=y,n._s=g,n._l=Xn,n._t=Jn,n._q=L,n._i=N,n._m=oe,n._f=ne,n._k=te,n._b=re,n._v=gn,n._e=mn,n._u=le,n._g=ce,n._d=ue,n._p=pe}function fe(n,e){if(!n||!n.length)return{};for(var t={},r=0,o=n.length;r<o;r++){var i=n[r],a=i.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs.slot,i.context!==e&&i.fnContext!==e||!a||null==a.slot)(t.default||(t.default=[])).push(i);else{var s=a.slot,c=t[s]||(t[s]=[]);"template"===i.tag?c.push.apply(c,i.children||[]):c.push(i)}}for(var l in t)t[l].every(he)&&delete t[l];return t}function he(n){return n.isComment&&!n.asyncFactory||" "===n.text}function me(n){return n.isComment&&n.asyncFactory}function ge(n,e,t,o){var i,a=Object.keys(t).length>0,s=e?!!e.$stable:!a,c=e&&e.$key;if(e){if(e._normalized)return e._normalized;if(s&&o&&o!==r&&c===o.$key&&!a&&!o.$hasNormal)return o;for(var l in i={},e)e[l]&&"$"!==l[0]&&(i[l]=ve(n,t,l,e[l]))}else i={};for(var u in t)u in i||(i[u]=ye(t,u));return e&&Object.isExtensible(e)&&(e._normalized=i),Y(i,"$stable",s),Y(i,"$key",c),Y(i,"$hasNormal",a),i}function ve(n,e,t,r){var i=function(){var e=dn;fn(n);var t=arguments.length?r.apply(null,arguments):r({}),i=(t=t&&"object"==typeof t&&!o(t)?[t]:Zn(t))&&t[0];return fn(e),t&&(!i||1===t.length&&i.isComment&&!me(i))?void 0:t};return r.proxy&&Object.defineProperty(e,t,{get:i,enumerable:!0,configurable:!0}),i}function ye(n,e){return function(){return n[e]}}function be(n){return{get attrs(){if(!n._attrsProxy){var e=n._attrsProxy={};Y(e,"_v_attr_proxy",!0),xe(e,n.$attrs,r,n,"$attrs")}return n._attrsProxy},get listeners(){n._listenersProxy||xe(n._listenersProxy={},n.$listeners,r,n,"$listeners");return n._listenersProxy},get slots(){return function(n){n._slotsProxy||we(n._slotsProxy={},n.$scopedSlots);return n._slotsProxy}(n)},emit:A(n.$emit,n),expose:function(e){e&&Object.keys(e).forEach((function(t){return Bn(n,e,t)}))}}}function xe(n,e,t,r,o){var i=!1;for(var a in e)a in n?e[a]!==t[a]&&(i=!0):(i=!0,_e(n,a,r,o));for(var a in n)a in e||(i=!0,delete n[a]);return i}function _e(n,e,t,r){Object.defineProperty(n,e,{enumerable:!0,configurable:!0,get:function(){return t[r][e]}})}function we(n,e){for(var t in e)n[t]=e[t];for(var t in n)t in e||delete n[t]}var Te=null;function Ee(n,e){return(n.__esModule||pn&&"Module"===n[Symbol.toStringTag])&&(n=n.default),u(n)?e.extend(n):n}function ke(n){if(o(n))for(var e=0;e<n.length;e++){var t=n[e];if(a(t)&&(a(t.componentOptions)||me(t)))return t}}function Ce(n,e,t,r,p,d){return(o(t)||c(t))&&(p=r,r=t,t=void 0),s(d)&&(p=2),function(n,e,t,r,c){if(a(t)&&a(t.__ob__))return mn();a(t)&&a(t.is)&&(e=t.is);if(!e)return mn();0;o(r)&&l(r[0])&&((t=t||{}).scopedSlots={default:r[0]},r.length=0);2===c?r=Zn(r):1===c&&(r=function(n){for(var e=0;e<n.length;e++)if(o(n[e]))return Array.prototype.concat.apply([],n);return n}(r));var p,d;if("string"==typeof e){var f=void 0;d=n.$vnode&&n.$vnode.ns||F.getTagNamespace(e),p=F.isReservedTag(e)?new hn(F.parsePlatformTagName(e),t,r,void 0,void 0,n):t&&t.pre||!a(f=It(n.$options,"components",e))?new hn(e,t,r,void 0,void 0,n):_t(f,t,n,r,e)}else p=_t(e,t,n,r);return o(p)?p:a(p)?(a(d)&&function n(e,t,r){e.ns=t,"foreignObject"===e.tag&&(t=void 0,r=!0);if(a(e.children))for(var o=0,c=e.children.length;o<c;o++){var l=e.children[o];a(l.tag)&&(i(l.ns)||s(r)&&"svg"!==l.tag)&&n(l,t,r)}}(p,d),a(t)&&function(n){u(n.style)&&Fe(n.style);u(n.class)&&Fe(n.class)}(t),p):mn()}(n,e,t,r,p)}function Se(n,e,t){wn();try{if(e)for(var r=e;r=r.$parent;){var o=r.$options.errorCaptured;if(o)for(var i=0;i<o.length;i++)try{if(!1===o[i].call(r,n,e,t))return}catch(n){je(n,r,"errorCaptured hook")}}je(n,e,t)}finally{Tn()}}function Oe(n,e,t,r,o){var i;try{(i=t?n.apply(e,t):n.call(e))&&!i._isVue&&m(i)&&!i._handled&&(i.catch((function(n){return Se(n,r,o+" (Promise/async)")})),i._handled=!0)}catch(n){Se(n,r,o)}return i}function je(n,e,t){if(F.errorHandler)try{return F.errorHandler.call(null,n,e,t)}catch(e){e!==n&&Ae(e,null,"config.errorHandler")}Ae(n,e,t)}function Ae(n,e,t){if(!K||"undefined"==typeof console)throw n;console.error(n)}var Ie,$e=!1,Pe=[],ze=!1;function Me(){ze=!1;var n=Pe.slice(0);Pe.length=0;for(var e=0;e<n.length;e++)n[e]()}if("undefined"!=typeof Promise&&ln(Promise)){var De=Promise.resolve();Ie=function(){De.then(Me),nn&&setTimeout(z)},$e=!0}else if(Q||"undefined"==typeof MutationObserver||!ln(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())Ie="undefined"!=typeof setImmediate&&ln(setImmediate)?function(){setImmediate(Me)}:function(){setTimeout(Me,0)};else{var Le=1,Ne=new MutationObserver(Me),Ue=document.createTextNode(String(Le));Ne.observe(Ue,{characterData:!0}),Ie=function(){Le=(Le+1)%2,Ue.data=String(Le)},$e=!0}function Re(n,e){var t;if(Pe.push((function(){if(n)try{n.call(e)}catch(n){Se(n,e,"nextTick")}else t&&t(e)})),ze||(ze=!0,Ie()),!n&&"undefined"!=typeof Promise)return new Promise((function(n){t=n}))}function Be(n){return function(e,t){if(void 0===t&&(t=dn),t)return function(n,e,t){var r=n.$options;r[e]=St(r[e],t)}(t,n,e)}}Be("beforeMount"),Be("mounted"),Be("beforeUpdate"),Be("updated"),Be("beforeDestroy"),Be("destroyed"),Be("activated"),Be("deactivated"),Be("serverPrefetch"),Be("renderTracked"),Be("renderTriggered"),Be("errorCaptured");var qe=new un;function Fe(n){return function n(e,t){var r,i,a=o(e);if(!a&&!u(e)||e.__v_skip||Object.isFrozen(e)||e instanceof hn)return;if(e.__ob__){var s=e.__ob__.dep.id;if(t.has(s))return;t.add(s)}if(a)for(r=e.length;r--;)n(e[r],t);else if(Rn(e))n(e.value,t);else for(i=Object.keys(e),r=i.length;r--;)n(e[i[r]],t)}(n,qe),qe.clear(),n}var He,Ve=0,Ye=function(){function n(n,e,t,r,o){var i,a;i=this,void 0===(a=qn&&!qn._vm?qn:n?n._scope:void 0)&&(a=qn),a&&a.active&&a.effects.push(i),(this.vm=n)&&o&&(n._watcher=this),r?(this.deep=!!r.deep,this.user=!!r.user,this.lazy=!!r.lazy,this.sync=!!r.sync,this.before=r.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=t,this.id=++Ve,this.active=!0,this.post=!1,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new un,this.newDepIds=new un,this.expression="",l(e)?this.getter=e:(this.getter=function(n){if(!G.test(n)){var e=n.split(".");return function(n){for(var t=0;t<e.length;t++){if(!n)return;n=n[e[t]]}return n}}}(e),this.getter||(this.getter=z)),this.value=this.lazy?void 0:this.get()}return n.prototype.get=function(){var n;wn(this);var e=this.vm;try{n=this.getter.call(e,e)}catch(n){if(!this.user)throw n;Se(n,e,'getter for watcher "'.concat(this.expression,'"'))}finally{this.deep&&Fe(n),Tn(),this.cleanupDeps()}return n},n.prototype.addDep=function(n){var e=n.id;this.newDepIds.has(e)||(this.newDepIds.add(e),this.newDeps.push(n),this.depIds.has(e)||n.addSub(this))},n.prototype.cleanupDeps=function(){for(var n=this.deps.length;n--;){var e=this.deps[n];this.newDepIds.has(e.id)||e.removeSub(this)}var t=this.depIds;this.depIds=this.newDepIds,this.newDepIds=t,this.newDepIds.clear(),t=this.deps,this.deps=this.newDeps,this.newDeps=t,this.newDeps.length=0},n.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():ft(this)},n.prototype.run=function(){if(this.active){var n=this.get();if(n!==this.value||u(n)||this.deep){var e=this.value;if(this.value=n,this.user){var t='callback for watcher "'.concat(this.expression,'"');Oe(this.cb,this.vm,[n,e],this.vm,t)}else this.cb.call(this.vm,n,e)}}},n.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},n.prototype.depend=function(){for(var n=this.deps.length;n--;)this.deps[n].depend()},n.prototype.teardown=function(){if(this.vm&&!this.vm._isBeingDestroyed&&_(this.vm._scope.effects,this),this.active){for(var n=this.deps.length;n--;)this.deps[n].removeSub(this);this.active=!1,this.onStop&&this.onStop()}},n}();function Ge(n,e){He.$on(n,e)}function We(n,e){He.$off(n,e)}function Ke(n,e){var t=He;return function r(){var o=e.apply(null,arguments);null!==o&&t.$off(n,r)}}function Ze(n,e,t){He=n,Gn(e,t||{},Ge,We,Ke,n),He=void 0}var Qe=null;function Xe(n){var e=Qe;return Qe=n,function(){Qe=e}}function Je(n){for(;n&&(n=n.$parent);)if(n._inactive)return!0;return!1}function nt(n,e){if(e){if(n._directInactive=!1,Je(n))return}else if(n._directInactive)return;if(n._inactive||null===n._inactive){n._inactive=!1;for(var t=0;t<n.$children.length;t++)nt(n.$children[t]);et(n,"activated")}}function et(n,e,t,r){void 0===r&&(r=!0),wn();var o=dn,i=qn;r&&fn(n);var a=n.$options[e],s="".concat(e," hook");if(a)for(var c=0,l=a.length;c<l;c++)Oe(a[c],n,t||null,n,s);n._hasHookEvent&&n.$emit("hook:"+e),r&&(fn(o),i&&i.on()),Tn()}var tt=[],rt=[],ot={},it=!1,at=!1,st=0;var ct=0,lt=Date.now;if(K&&!Q){var ut=window.performance;ut&&"function"==typeof ut.now&&lt()>document.createEvent("Event").timeStamp&&(lt=function(){return ut.now()})}var pt=function(n,e){if(n.post){if(!e.post)return 1}else if(e.post)return-1;return n.id-e.id};function dt(){var n,e;for(ct=lt(),at=!0,tt.sort(pt),st=0;st<tt.length;st++)(n=tt[st]).before&&n.before(),e=n.id,ot[e]=null,n.run();var t=rt.slice(),r=tt.slice();st=tt.length=rt.length=0,ot={},it=at=!1,function(n){for(var e=0;e<n.length;e++)n[e]._inactive=!0,nt(n[e],!0)}(t),function(n){var e=n.length;for(;e--;){var t=n[e],r=t.vm;r&&r._watcher===t&&r._isMounted&&!r._isDestroyed&&et(r,"updated")}}(r),function(){for(var n=0;n<bn.length;n++){var e=bn[n];e.subs=e.subs.filter((function(n){return n})),e._pending=!1}bn.length=0}(),cn&&F.devtools&&cn.emit("flush")}function ft(n){var e=n.id;if(null==ot[e]&&(n!==xn.target||!n.noRecurse)){if(ot[e]=!0,at){for(var t=tt.length-1;t>st&&tt[t].id>n.id;)t--;tt.splice(t+1,0,n)}else tt.push(n);it||(it=!0,Re(dt))}}function ht(n,e){if(n){for(var t=Object.create(null),r=pn?Reflect.ownKeys(n):Object.keys(n),o=0;o<r.length;o++){var i=r[o];if("__ob__"!==i){var a=n[i].from;if(a in e._provided)t[i]=e._provided[a];else if("default"in n[i]){var s=n[i].default;t[i]=l(s)?s.call(e):s}else 0}}return t}}function mt(n,e,t,i,a){var c,l=this,u=a.options;T(i,"_uid")?(c=Object.create(i))._original=i:(c=i,i=i._original);var p=s(u._compiled),d=!p;this.data=n,this.props=e,this.children=t,this.parent=i,this.listeners=n.on||r,this.injections=ht(u.inject,i),this.slots=function(){return l.$slots||ge(i,n.scopedSlots,l.$slots=fe(t,i)),l.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return ge(i,n.scopedSlots,this.slots())}}),p&&(this.$options=u,this.$slots=this.slots(),this.$scopedSlots=ge(i,n.scopedSlots,this.$slots)),u._scopeId?this._c=function(n,e,t,r){var a=Ce(c,n,e,t,r,d);return a&&!o(a)&&(a.fnScopeId=u._scopeId,a.fnContext=i),a}:this._c=function(n,e,t,r){return Ce(c,n,e,t,r,d)}}function gt(n,e,t,r,o){var i=vn(n);return i.fnContext=t,i.fnOptions=r,e.slot&&((i.data||(i.data={})).slot=e.slot),i}function vt(n,e){for(var t in e)n[C(t)]=e[t]}function yt(n){return n.name||n.__name||n._componentTag}de(mt.prototype);var bt={init:function(n,e){if(n.componentInstance&&!n.componentInstance._isDestroyed&&n.data.keepAlive){var t=n;bt.prepatch(t,t)}else{(n.componentInstance=function(n,e){var t={_isComponent:!0,_parentVnode:n,parent:e},r=n.data.inlineTemplate;a(r)&&(t.render=r.render,t.staticRenderFns=r.staticRenderFns);return new n.componentOptions.Ctor(t)}(n,Qe)).$mount(e?n.elm:void 0,e)}},prepatch:function(n,e){var t=e.componentOptions;!function(n,e,t,o,i){var a=o.data.scopedSlots,s=n.$scopedSlots,c=!!(a&&!a.$stable||s!==r&&!s.$stable||a&&n.$scopedSlots.$key!==a.$key||!a&&n.$scopedSlots.$key),l=!!(i||n.$options._renderChildren||c),u=n.$vnode;n.$options._parentVnode=o,n.$vnode=o,n._vnode&&(n._vnode.parent=o),n.$options._renderChildren=i;var p=o.data.attrs||r;n._attrsProxy&&xe(n._attrsProxy,p,u.data&&u.data.attrs||r,n,"$attrs")&&(l=!0),n.$attrs=p,t=t||r;var d=n.$options._parentListeners;if(n._listenersProxy&&xe(n._listenersProxy,t,d||r,n,"$listeners"),n.$listeners=n.$options._parentListeners=t,Ze(n,t,d),e&&n.$options.props){jn(!1);for(var f=n._props,h=n.$options._propKeys||[],m=0;m<h.length;m++){var g=h[m],v=n.$options.props;f[g]=$t(g,v,e,n)}jn(!0),n.$options.propsData=e}l&&(n.$slots=fe(i,o.context),n.$forceUpdate())}(e.componentInstance=n.componentInstance,t.propsData,t.listeners,e,t.children)},insert:function(n){var e,t=n.context,r=n.componentInstance;r._isMounted||(r._isMounted=!0,et(r,"mounted")),n.data.keepAlive&&(t._isMounted?((e=r)._inactive=!1,rt.push(e)):nt(r,!0))},destroy:function(n){var e=n.componentInstance;e._isDestroyed||(n.data.keepAlive?function n(e,t){if(!(t&&(e._directInactive=!0,Je(e))||e._inactive)){e._inactive=!0;for(var r=0;r<e.$children.length;r++)n(e.$children[r]);et(e,"deactivated")}}(e,!0):e.$destroy())}},xt=Object.keys(bt);function _t(n,e,t,c,l){if(!i(n)){var p=t.$options._base;if(u(n)&&(n=p.extend(n)),"function"==typeof n){var d;if(i(n.cid)&&void 0===(n=function(n,e){if(s(n.error)&&a(n.errorComp))return n.errorComp;if(a(n.resolved))return n.resolved;var t=Te;if(t&&a(n.owners)&&-1===n.owners.indexOf(t)&&n.owners.push(t),s(n.loading)&&a(n.loadingComp))return n.loadingComp;if(t&&!a(n.owners)){var r=n.owners=[t],o=!0,c=null,l=null;t.$on("hook:destroyed",(function(){return _(r,t)}));var p=function(n){for(var e=0,t=r.length;e<t;e++)r[e].$forceUpdate();n&&(r.length=0,null!==c&&(clearTimeout(c),c=null),null!==l&&(clearTimeout(l),l=null))},d=U((function(t){n.resolved=Ee(t,e),o?r.length=0:p(!0)})),f=U((function(e){a(n.errorComp)&&(n.error=!0,p(!0))})),h=n(d,f);return u(h)&&(m(h)?i(n.resolved)&&h.then(d,f):m(h.component)&&(h.component.then(d,f),a(h.error)&&(n.errorComp=Ee(h.error,e)),a(h.loading)&&(n.loadingComp=Ee(h.loading,e),0===h.delay?n.loading=!0:c=setTimeout((function(){c=null,i(n.resolved)&&i(n.error)&&(n.loading=!0,p(!1))}),h.delay||200)),a(h.timeout)&&(l=setTimeout((function(){l=null,i(n.resolved)&&f(null)}),h.timeout)))),o=!1,n.loading?n.loadingComp:n.resolved}}(d=n,p)))return function(n,e,t,r,o){var i=mn();return i.asyncFactory=n,i.asyncMeta={data:e,context:t,children:r,tag:o},i}(d,e,t,c,l);e=e||{},Yt(n),a(e.model)&&function(n,e){var t=n.model&&n.model.prop||"value",r=n.model&&n.model.event||"input";(e.attrs||(e.attrs={}))[t]=e.model.value;var i=e.on||(e.on={}),s=i[r],c=e.model.callback;a(s)?(o(s)?-1===s.indexOf(c):s!==c)&&(i[r]=[c].concat(s)):i[r]=c}(n.options,e);var f=function(n,e,t){var r=e.options.props;if(!i(r)){var o={},s=n.attrs,c=n.props;if(a(s)||a(c))for(var l in r){var u=j(l);Kn(o,c,l,u,!0)||Kn(o,s,l,u,!1)}return o}}(e,n);if(s(n.options.functional))return function(n,e,t,i,s){var c=n.options,l={},u=c.props;if(a(u))for(var p in u)l[p]=$t(p,u,e||r);else a(t.attrs)&&vt(l,t.attrs),a(t.props)&&vt(l,t.props);var d=new mt(t,l,s,i,n),f=c.render.call(null,d._c,d);if(f instanceof hn)return gt(f,t,d.parent,c,d);if(o(f)){for(var h=Zn(f)||[],m=new Array(h.length),g=0;g<h.length;g++)m[g]=gt(h[g],t,d.parent,c,d);return m}}(n,f,e,t,c);var h=e.on;if(e.on=e.nativeOn,s(n.options.abstract)){var g=e.slot;e={},g&&(e.slot=g)}!function(n){for(var e=n.hook||(n.hook={}),t=0;t<xt.length;t++){var r=xt[t],o=e[r],i=bt[r];o===i||o&&o._merged||(e[r]=o?wt(i,o):i)}}(e);var v=yt(n.options)||l;return new hn("vue-component-".concat(n.cid).concat(v?"-".concat(v):""),e,void 0,void 0,void 0,t,{Ctor:n,propsData:f,listeners:h,tag:l,children:c},d)}}}function wt(n,e){var t=function(t,r){n(t,r),e(t,r)};return t._merged=!0,t}var Tt=z,Et=F.optionMergeStrategies;function kt(n,e,t){if(void 0===t&&(t=!0),!e)return n;for(var r,o,i,a=pn?Reflect.ownKeys(e):Object.keys(e),s=0;s<a.length;s++)"__ob__"!==(r=a[s])&&(o=n[r],i=e[r],t&&T(n,r)?o!==i&&d(o)&&d(i)&&kt(o,i):zn(n,r,i));return n}function Ct(n,e,t){return t?function(){var r=l(e)?e.call(t,t):e,o=l(n)?n.call(t,t):n;return r?kt(r,o):o}:e?n?function(){return kt(l(e)?e.call(this,this):e,l(n)?n.call(this,this):n)}:e:n}function St(n,e){var t=e?n?n.concat(e):o(e)?e:[e]:n;return t?function(n){for(var e=[],t=0;t<n.length;t++)-1===e.indexOf(n[t])&&e.push(n[t]);return e}(t):t}function Ot(n,e,t,r){var o=Object.create(n||null);return e?$(o,e):o}Et.data=function(n,e,t){return t?Ct(n,e,t):e&&"function"!=typeof e?n:Ct(n,e)},q.forEach((function(n){Et[n]=St})),B.forEach((function(n){Et[n+"s"]=Ot})),Et.watch=function(n,e,t,r){if(n===rn&&(n=void 0),e===rn&&(e=void 0),!e)return Object.create(n||null);if(!n)return e;var i={};for(var a in $(i,n),e){var s=i[a],c=e[a];s&&!o(s)&&(s=[s]),i[a]=s?s.concat(c):o(c)?c:[c]}return i},Et.props=Et.methods=Et.inject=Et.computed=function(n,e,t,r){if(!n)return e;var o=Object.create(null);return $(o,n),e&&$(o,e),o},Et.provide=function(n,e){return n?function(){var t=Object.create(null);return kt(t,l(n)?n.call(this):n),e&&kt(t,l(e)?e.call(this):e,!1),t}:e};var jt=function(n,e){return void 0===e?n:e};function At(n,e,t){if(l(e)&&(e=e.options),function(n,e){var t=n.props;if(t){var r,i,a={};if(o(t))for(r=t.length;r--;)"string"==typeof(i=t[r])&&(a[C(i)]={type:null});else if(d(t))for(var s in t)i=t[s],a[C(s)]=d(i)?i:{type:i};else 0;n.props=a}}(e),function(n,e){var t=n.inject;if(t){var r=n.inject={};if(o(t))for(var i=0;i<t.length;i++)r[t[i]]={from:t[i]};else if(d(t))for(var a in t){var s=t[a];r[a]=d(s)?$({from:a},s):{from:s}}else 0}}(e),function(n){var e=n.directives;if(e)for(var t in e){var r=e[t];l(r)&&(e[t]={bind:r,update:r})}}(e),!e._base&&(e.extends&&(n=At(n,e.extends,t)),e.mixins))for(var r=0,i=e.mixins.length;r<i;r++)n=At(n,e.mixins[r],t);var a,s={};for(a in n)c(a);for(a in e)T(n,a)||c(a);function c(r){var o=Et[r]||jt;s[r]=o(n[r],e[r],t,r)}return s}function It(n,e,t,r){if("string"==typeof t){var o=n[e];if(T(o,t))return o[t];var i=C(t);if(T(o,i))return o[i];var a=S(i);return T(o,a)?o[a]:o[t]||o[i]||o[a]}}function $t(n,e,t,r){var o=e[n],i=!T(t,n),a=t[n],s=Dt(Boolean,o.type);if(s>-1)if(i&&!T(o,"default"))a=!1;else if(""===a||a===j(n)){var c=Dt(String,o.type);(c<0||s<c)&&(a=!0)}if(void 0===a){a=function(n,e,t){if(!T(e,"default"))return;var r=e.default;0;if(n&&n.$options.propsData&&void 0===n.$options.propsData[t]&&void 0!==n._props[t])return n._props[t];return l(r)&&"Function"!==zt(e.type)?r.call(n):r}(r,o,n);var u=On;jn(!0),$n(a),jn(u)}return a}var Pt=/^\s*function (\w+)/;function zt(n){var e=n&&n.toString().match(Pt);return e?e[1]:""}function Mt(n,e){return zt(n)===zt(e)}function Dt(n,e){if(!o(e))return Mt(e,n)?0:-1;for(var t=0,r=e.length;t<r;t++)if(Mt(e[t],n))return t;return-1}var Lt={enumerable:!0,configurable:!0,get:z,set:z};function Nt(n,e,t){Lt.get=function(){return this[e][t]},Lt.set=function(n){this[e][t]=n},Object.defineProperty(n,t,Lt)}function Ut(n){var e=n.$options;if(e.props&&function(n,e){var t=n.$options.propsData||{},r=n._props=Ln({}),o=n.$options._propKeys=[];n.$parent&&jn(!1);var i=function(i){o.push(i);var a=$t(i,e,t,n);Pn(r,i,a,void 0,!0),i in n||Nt(n,"_props",i)};for(var a in e)i(a);jn(!0)}(n,e.props),function(n){var e=n.$options,t=e.setup;if(t){var r=n._setupContext=be(n);fn(n),wn();var o=Oe(t,null,[n._props||Ln({}),r],n,"setup");if(Tn(),fn(),l(o))e.render=o;else if(u(o))if(n._setupState=o,o.__sfc){var i=n._setupProxy={};for(var a in o)"__sfc"!==a&&Bn(i,o,a)}else for(var a in o)V(a)||Bn(n,o,a);else 0}}(n),e.methods&&function(n,e){n.$options.props;for(var t in e)n[t]="function"!=typeof e[t]?z:A(e[t],n)}(n,e.methods),e.data)!function(n){var e=n.$options.data;d(e=n._data=l(e)?function(n,e){wn();try{return n.call(e,e)}catch(n){return Se(n,e,"data()"),{}}finally{Tn()}}(e,n):e||{})||(e={});var t=Object.keys(e),r=n.$options.props,o=(n.$options.methods,t.length);for(;o--;){var i=t[o];0,r&&T(r,i)||V(i)||Nt(n,"_data",i)}var a=$n(e);a&&a.vmCount++}(n);else{var t=$n(n._data={});t&&t.vmCount++}e.computed&&function(n,e){var t=n._computedWatchers=Object.create(null),r=sn();for(var o in e){var i=e[o],a=l(i)?i:i.get;0,r||(t[o]=new Ye(n,a||z,z,Rt)),o in n||Bt(n,o,i)}}(n,e.computed),e.watch&&e.watch!==rn&&function(n,e){for(var t in e){var r=e[t];if(o(r))for(var i=0;i<r.length;i++)Ht(n,t,r[i]);else Ht(n,t,r)}}(n,e.watch)}var Rt={lazy:!0};function Bt(n,e,t){var r=!sn();l(t)?(Lt.get=r?qt(e):Ft(t),Lt.set=z):(Lt.get=t.get?r&&!1!==t.cache?qt(e):Ft(t.get):z,Lt.set=t.set||z),Object.defineProperty(n,e,Lt)}function qt(n){return function(){var e=this._computedWatchers&&this._computedWatchers[n];if(e)return e.dirty&&e.evaluate(),xn.target&&e.depend(),e.value}}function Ft(n){return function(){return n.call(this,this)}}function Ht(n,e,t,r){return d(t)&&(r=t,t=t.handler),"string"==typeof t&&(t=n[t]),n.$watch(e,t,r)}var Vt=0;function Yt(n){var e=n.options;if(n.super){var t=Yt(n.super);if(t!==n.superOptions){n.superOptions=t;var r=function(n){var e,t=n.options,r=n.sealedOptions;for(var o in t)t[o]!==r[o]&&(e||(e={}),e[o]=t[o]);return e}(n);r&&$(n.extendOptions,r),(e=n.options=At(t,n.extendOptions)).name&&(e.components[e.name]=n)}}return e}function Gt(n){this._init(n)}function Wt(n){n.cid=0;var e=1;n.extend=function(n){n=n||{};var t=this,r=t.cid,o=n._Ctor||(n._Ctor={});if(o[r])return o[r];var i=yt(n)||yt(t.options);var a=function(n){this._init(n)};return(a.prototype=Object.create(t.prototype)).constructor=a,a.cid=e++,a.options=At(t.options,n),a.super=t,a.options.props&&function(n){var e=n.options.props;for(var t in e)Nt(n.prototype,"_props",t)}(a),a.options.computed&&function(n){var e=n.options.computed;for(var t in e)Bt(n.prototype,t,e[t])}(a),a.extend=t.extend,a.mixin=t.mixin,a.use=t.use,B.forEach((function(n){a[n]=t[n]})),i&&(a.options.components[i]=a),a.superOptions=t.options,a.extendOptions=n,a.sealedOptions=$({},a.options),o[r]=a,a}}function Kt(n){return n&&(yt(n.Ctor.options)||n.tag)}function Zt(n,e){return o(n)?n.indexOf(e)>-1:"string"==typeof n?n.split(",").indexOf(e)>-1:!!f(n)&&n.test(e)}function Qt(n,e){var t=n.cache,r=n.keys,o=n._vnode,i=n.$vnode;for(var a in t){var s=t[a];if(s){var c=s.name;c&&!e(c)&&Xt(t,a,r,o)}}i.componentOptions.children=void 0}function Xt(n,e,t,r){var o=n[e];!o||r&&o.tag===r.tag||o.componentInstance.$destroy(),n[e]=null,_(t,e)}Gt.prototype._init=function(n){var e=this;e._uid=Vt++,e._isVue=!0,e.__v_skip=!0,e._scope=new Fn(!0),e._scope.parent=void 0,e._scope._vm=!0,n&&n._isComponent?function(n,e){var t=n.$options=Object.create(n.constructor.options),r=e._parentVnode;t.parent=e.parent,t._parentVnode=r;var o=r.componentOptions;t.propsData=o.propsData,t._parentListeners=o.listeners,t._renderChildren=o.children,t._componentTag=o.tag,e.render&&(t.render=e.render,t.staticRenderFns=e.staticRenderFns)}(e,n):e.$options=At(Yt(e.constructor),n||{},e),e._renderProxy=e,e._self=e,function(n){var e=n.$options,t=e.parent;if(t&&!e.abstract){for(;t.$options.abstract&&t.$parent;)t=t.$parent;t.$children.push(n)}n.$parent=t,n.$root=t?t.$root:n,n.$children=[],n.$refs={},n._provided=t?t._provided:Object.create(null),n._watcher=null,n._inactive=null,n._directInactive=!1,n._isMounted=!1,n._isDestroyed=!1,n._isBeingDestroyed=!1}(e),function(n){n._events=Object.create(null),n._hasHookEvent=!1;var e=n.$options._parentListeners;e&&Ze(n,e)}(e),function(n){n._vnode=null,n._staticTrees=null;var e=n.$options,t=n.$vnode=e._parentVnode,o=t&&t.context;n.$slots=fe(e._renderChildren,o),n.$scopedSlots=t?ge(n.$parent,t.data.scopedSlots,n.$slots):r,n._c=function(e,t,r,o){return Ce(n,e,t,r,o,!1)},n.$createElement=function(e,t,r,o){return Ce(n,e,t,r,o,!0)};var i=t&&t.data;Pn(n,"$attrs",i&&i.attrs||r,null,!0),Pn(n,"$listeners",e._parentListeners||r,null,!0)}(e),et(e,"beforeCreate",void 0,!1),function(n){var e=ht(n.$options.inject,n);e&&(jn(!1),Object.keys(e).forEach((function(t){Pn(n,t,e[t])})),jn(!0))}(e),Ut(e),function(n){var e=n.$options.provide;if(e){var t=l(e)?e.call(n):e;if(!u(t))return;for(var r=Hn(n),o=pn?Reflect.ownKeys(t):Object.keys(t),i=0;i<o.length;i++){var a=o[i];Object.defineProperty(r,a,Object.getOwnPropertyDescriptor(t,a))}}}(e),et(e,"created"),e.$options.el&&e.$mount(e.$options.el)},function(n){var e={get:function(){return this._data}},t={get:function(){return this._props}};Object.defineProperty(n.prototype,"$data",e),Object.defineProperty(n.prototype,"$props",t),n.prototype.$set=zn,n.prototype.$delete=Mn,n.prototype.$watch=function(n,e,t){if(d(e))return Ht(this,n,e,t);(t=t||{}).user=!0;var r=new Ye(this,n,e,t);if(t.immediate){var o='callback for immediate watcher "'.concat(r.expression,'"');wn(),Oe(e,this,[r.value],this,o),Tn()}return function(){r.teardown()}}}(Gt),function(n){var e=/^hook:/;n.prototype.$on=function(n,t){var r=this;if(o(n))for(var i=0,a=n.length;i<a;i++)r.$on(n[i],t);else(r._events[n]||(r._events[n]=[])).push(t),e.test(n)&&(r._hasHookEvent=!0);return r},n.prototype.$once=function(n,e){var t=this;function r(){t.$off(n,r),e.apply(t,arguments)}return r.fn=e,t.$on(n,r),t},n.prototype.$off=function(n,e){var t=this;if(!arguments.length)return t._events=Object.create(null),t;if(o(n)){for(var r=0,i=n.length;r<i;r++)t.$off(n[r],e);return t}var a,s=t._events[n];if(!s)return t;if(!e)return t._events[n]=null,t;for(var c=s.length;c--;)if((a=s[c])===e||a.fn===e){s.splice(c,1);break}return t},n.prototype.$emit=function(n){var e=this,t=e._events[n];if(t){t=t.length>1?I(t):t;for(var r=I(arguments,1),o='event handler for "'.concat(n,'"'),i=0,a=t.length;i<a;i++)Oe(t[i],e,r,e,o)}return e}}(Gt),function(n){n.prototype._update=function(n,e){var t=this,r=t.$el,o=t._vnode,i=Xe(t);t._vnode=n,t.$el=o?t.__patch__(o,n):t.__patch__(t.$el,n,e,!1),i(),r&&(r.__vue__=null),t.$el&&(t.$el.__vue__=t);for(var a=t;a&&a.$vnode&&a.$parent&&a.$vnode===a.$parent._vnode;)a.$parent.$el=a.$el,a=a.$parent},n.prototype.$forceUpdate=function(){this._watcher&&this._watcher.update()},n.prototype.$destroy=function(){var n=this;if(!n._isBeingDestroyed){et(n,"beforeDestroy"),n._isBeingDestroyed=!0;var e=n.$parent;!e||e._isBeingDestroyed||n.$options.abstract||_(e.$children,n),n._scope.stop(),n._data.__ob__&&n._data.__ob__.vmCount--,n._isDestroyed=!0,n.__patch__(n._vnode,null),et(n,"destroyed"),n.$off(),n.$el&&(n.$el.__vue__=null),n.$vnode&&(n.$vnode.parent=null)}}}(Gt),function(n){de(n.prototype),n.prototype.$nextTick=function(n){return Re(n,this)},n.prototype._render=function(){var n=this,e=n.$options,t=e.render,r=e._parentVnode;r&&n._isMounted&&(n.$scopedSlots=ge(n.$parent,r.data.scopedSlots,n.$slots,n.$scopedSlots),n._slotsProxy&&we(n._slotsProxy,n.$scopedSlots)),n.$vnode=r;var i,a=dn,s=Te;try{fn(n),Te=n,i=t.call(n._renderProxy,n.$createElement)}catch(e){Se(e,n,"render"),i=n._vnode}finally{Te=s,fn(a)}return o(i)&&1===i.length&&(i=i[0]),i instanceof hn||(i=mn()),i.parent=r,i}}(Gt);var Jt=[String,RegExp,Array],nr={KeepAlive:{name:"keep-alive",abstract:!0,props:{include:Jt,exclude:Jt,max:[String,Number]},methods:{cacheVNode:function(){var n=this.cache,e=this.keys,t=this.vnodeToCache,r=this.keyToCache;if(t){var o=t.tag,i=t.componentInstance,a=t.componentOptions;n[r]={name:Kt(a),tag:o,componentInstance:i},e.push(r),this.max&&e.length>parseInt(this.max)&&Xt(n,e[0],e,this._vnode),this.vnodeToCache=null}}},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var n in this.cache)Xt(this.cache,n,this.keys)},mounted:function(){var n=this;this.cacheVNode(),this.$watch("include",(function(e){Qt(n,(function(n){return Zt(e,n)}))})),this.$watch("exclude",(function(e){Qt(n,(function(n){return!Zt(e,n)}))}))},updated:function(){this.cacheVNode()},render:function(){var n=this.$slots.default,e=ke(n),t=e&&e.componentOptions;if(t){var r=Kt(t),o=this.include,i=this.exclude;if(o&&(!r||!Zt(o,r))||i&&r&&Zt(i,r))return e;var a=this.cache,s=this.keys,c=null==e.key?t.Ctor.cid+(t.tag?"::".concat(t.tag):""):e.key;a[c]?(e.componentInstance=a[c].componentInstance,_(s,c),s.push(c)):(this.vnodeToCache=e,this.keyToCache=c),e.data.keepAlive=!0}return e||n&&n[0]}}};!function(n){var e={get:function(){return F}};Object.defineProperty(n,"config",e),n.util={warn:Tt,extend:$,mergeOptions:At,defineReactive:Pn},n.set=zn,n.delete=Mn,n.nextTick=Re,n.observable=function(n){return $n(n),n},n.options=Object.create(null),B.forEach((function(e){n.options[e+"s"]=Object.create(null)})),n.options._base=n,$(n.options.components,nr),function(n){n.use=function(n){var e=this._installedPlugins||(this._installedPlugins=[]);if(e.indexOf(n)>-1)return this;var t=I(arguments,1);return t.unshift(this),l(n.install)?n.install.apply(n,t):l(n)&&n.apply(null,t),e.push(n),this}}(n),function(n){n.mixin=function(n){return this.options=At(this.options,n),this}}(n),Wt(n),function(n){B.forEach((function(e){n[e]=function(n,t){return t?("component"===e&&d(t)&&(t.name=t.name||n,t=this.options._base.extend(t)),"directive"===e&&l(t)&&(t={bind:t,update:t}),this.options[e+"s"][n]=t,t):this.options[e+"s"][n]}}))}(n)}(Gt),Object.defineProperty(Gt.prototype,"$isServer",{get:sn}),Object.defineProperty(Gt.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(Gt,"FunctionalRenderContext",{value:mt}),Gt.version="2.7.16";var er=b("style,class"),tr=b("input,textarea,option,select,progress"),rr=b("contenteditable,draggable,spellcheck"),or=b("events,caret,typing,plaintext-only"),ir=b("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,truespeed,typemustmatch,visible"),ar="http://www.w3.org/1999/xlink",sr=function(n){return":"===n.charAt(5)&&"xlink"===n.slice(0,5)},cr=function(n){return sr(n)?n.slice(6,n.length):""},lr=function(n){return null==n||!1===n};function ur(n){for(var e=n.data,t=n,r=n;a(r.componentInstance);)(r=r.componentInstance._vnode)&&r.data&&(e=pr(r.data,e));for(;a(t=t.parent);)t&&t.data&&(e=pr(e,t.data));return function(n,e){if(a(n)||a(e))return dr(n,fr(e));return""}(e.staticClass,e.class)}function pr(n,e){return{staticClass:dr(n.staticClass,e.staticClass),class:a(n.class)?[n.class,e.class]:e.class}}function dr(n,e){return n?e?n+" "+e:n:e||""}function fr(n){return Array.isArray(n)?function(n){for(var e,t="",r=0,o=n.length;r<o;r++)a(e=fr(n[r]))&&""!==e&&(t&&(t+=" "),t+=e);return t}(n):u(n)?function(n){var e="";for(var t in n)n[t]&&(e&&(e+=" "),e+=t);return e}(n):"string"==typeof n?n:""}var hr={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},mr=b("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"),gr=b("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignobject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),vr=function(n){return mr(n)||gr(n)};var yr=Object.create(null);var br=b("text,number,password,search,email,tel,url");var xr=Object.freeze({__proto__:null,createElement:function(n,e){var t=document.createElement(n);return"select"!==n||e.data&&e.data.attrs&&void 0!==e.data.attrs.multiple&&t.setAttribute("multiple","multiple"),t},createElementNS:function(n,e){return document.createElementNS(hr[n],e)},createTextNode:function(n){return document.createTextNode(n)},createComment:function(n){return document.createComment(n)},insertBefore:function(n,e,t){n.insertBefore(e,t)},removeChild:function(n,e){n.removeChild(e)},appendChild:function(n,e){n.appendChild(e)},parentNode:function(n){return n.parentNode},nextSibling:function(n){return n.nextSibling},tagName:function(n){return n.tagName},setTextContent:function(n,e){n.textContent=e},setStyleScope:function(n,e){n.setAttribute(e,"")}}),_r={create:function(n,e){wr(e)},update:function(n,e){n.data.ref!==e.data.ref&&(wr(n,!0),wr(e))},destroy:function(n){wr(n,!0)}};function wr(n,e){var t=n.data.ref;if(a(t)){var r=n.context,i=n.componentInstance||n.elm,s=e?null:i,c=e?void 0:i;if(l(t))Oe(t,r,[s],r,"template ref function");else{var u=n.data.refInFor,p="string"==typeof t||"number"==typeof t,d=Rn(t),f=r.$refs;if(p||d)if(u){var h=p?f[t]:t.value;e?o(h)&&_(h,i):o(h)?h.includes(i)||h.push(i):p?(f[t]=[i],Tr(r,t,f[t])):t.value=[i]}else if(p){if(e&&f[t]!==i)return;f[t]=c,Tr(r,t,s)}else if(d){if(e&&t.value!==i)return;t.value=s}else 0}}}function Tr(n,e,t){var r=n._setupState;r&&T(r,e)&&(Rn(r[e])?r[e].value=t:r[e]=t)}var Er=new hn("",{},[]),kr=["create","activate","update","remove","destroy"];function Cr(n,e){return n.key===e.key&&n.asyncFactory===e.asyncFactory&&(n.tag===e.tag&&n.isComment===e.isComment&&a(n.data)===a(e.data)&&function(n,e){if("input"!==n.tag)return!0;var t,r=a(t=n.data)&&a(t=t.attrs)&&t.type,o=a(t=e.data)&&a(t=t.attrs)&&t.type;return r===o||br(r)&&br(o)}(n,e)||s(n.isAsyncPlaceholder)&&i(e.asyncFactory.error))}function Sr(n,e,t){var r,o,i={};for(r=e;r<=t;++r)a(o=n[r].key)&&(i[o]=r);return i}var Or={create:jr,update:jr,destroy:function(n){jr(n,Er)}};function jr(n,e){(n.data.directives||e.data.directives)&&function(n,e){var t,r,o,i=n===Er,a=e===Er,s=Ir(n.data.directives,n.context),c=Ir(e.data.directives,e.context),l=[],u=[];for(t in c)r=s[t],o=c[t],r?(o.oldValue=r.value,o.oldArg=r.arg,Pr(o,"update",e,n),o.def&&o.def.componentUpdated&&u.push(o)):(Pr(o,"bind",e,n),o.def&&o.def.inserted&&l.push(o));if(l.length){var p=function(){for(var t=0;t<l.length;t++)Pr(l[t],"inserted",e,n)};i?Wn(e,"insert",p):p()}u.length&&Wn(e,"postpatch",(function(){for(var t=0;t<u.length;t++)Pr(u[t],"componentUpdated",e,n)}));if(!i)for(t in s)c[t]||Pr(s[t],"unbind",n,n,a)}(n,e)}var Ar=Object.create(null);function Ir(n,e){var t,r,o=Object.create(null);if(!n)return o;for(t=0;t<n.length;t++){if((r=n[t]).modifiers||(r.modifiers=Ar),o[$r(r)]=r,e._setupState&&e._setupState.__sfc){var i=r.def||It(e,"_setupState","v-"+r.name);r.def="function"==typeof i?{bind:i,update:i}:i}r.def=r.def||It(e.$options,"directives",r.name)}return o}function $r(n){return n.rawName||"".concat(n.name,".").concat(Object.keys(n.modifiers||{}).join("."))}function Pr(n,e,t,r,o){var i=n.def&&n.def[e];if(i)try{i(t.elm,n,t,r,o)}catch(r){Se(r,t.context,"directive ".concat(n.name," ").concat(e," hook"))}}var zr=[_r,Or];function Mr(n,e){var t=e.componentOptions;if(!(a(t)&&!1===t.Ctor.options.inheritAttrs||i(n.data.attrs)&&i(e.data.attrs))){var r,o,c=e.elm,l=n.data.attrs||{},u=e.data.attrs||{};for(r in(a(u.__ob__)||s(u._v_attr_proxy))&&(u=e.data.attrs=$({},u)),u)o=u[r],l[r]!==o&&Dr(c,r,o,e.data.pre);for(r in(Q||J)&&u.value!==l.value&&Dr(c,"value",u.value),l)i(u[r])&&(sr(r)?c.removeAttributeNS(ar,cr(r)):rr(r)||c.removeAttribute(r))}}function Dr(n,e,t,r){r||n.tagName.indexOf("-")>-1?Lr(n,e,t):ir(e)?lr(t)?n.removeAttribute(e):(t="allowfullscreen"===e&&"EMBED"===n.tagName?"true":e,n.setAttribute(e,t)):rr(e)?n.setAttribute(e,function(n,e){return lr(e)||"false"===e?"false":"contenteditable"===n&&or(e)?e:"true"}(e,t)):sr(e)?lr(t)?n.removeAttributeNS(ar,cr(e)):n.setAttributeNS(ar,e,t):Lr(n,e,t)}function Lr(n,e,t){if(lr(t))n.removeAttribute(e);else{if(Q&&!X&&"TEXTAREA"===n.tagName&&"placeholder"===e&&""!==t&&!n.__ieph){var r=function(e){e.stopImmediatePropagation(),n.removeEventListener("input",r)};n.addEventListener("input",r),n.__ieph=!0}n.setAttribute(e,t)}}var Nr={create:Mr,update:Mr};function Ur(n,e){var t=e.elm,r=e.data,o=n.data;if(!(i(r.staticClass)&&i(r.class)&&(i(o)||i(o.staticClass)&&i(o.class)))){var s=ur(e),c=t._transitionClasses;a(c)&&(s=dr(s,fr(c))),s!==t._prevClass&&(t.setAttribute("class",s),t._prevClass=s)}}var Rr,Br={create:Ur,update:Ur};function qr(n,e,t){var r=Rr;return function o(){var i=e.apply(null,arguments);null!==i&&Vr(n,o,t,r)}}var Fr=$e&&!(tn&&Number(tn[1])<=53);function Hr(n,e,t,r){if(Fr){var o=ct,i=e;e=i._wrapper=function(n){if(n.target===n.currentTarget||n.timeStamp>=o||n.timeStamp<=0||n.target.ownerDocument!==document)return i.apply(this,arguments)}}Rr.addEventListener(n,e,on?{capture:t,passive:r}:t)}function Vr(n,e,t,r){(r||Rr).removeEventListener(n,e._wrapper||e,t)}function Yr(n,e){if(!i(n.data.on)||!i(e.data.on)){var t=e.data.on||{},r=n.data.on||{};Rr=e.elm||n.elm,function(n){if(a(n.__r)){var e=Q?"change":"input";n[e]=[].concat(n.__r,n[e]||[]),delete n.__r}a(n.__c)&&(n.change=[].concat(n.__c,n.change||[]),delete n.__c)}(t),Gn(t,r,Hr,Vr,qr,e.context),Rr=void 0}}var Gr,Wr={create:Yr,update:Yr,destroy:function(n){return Yr(n,Er)}};function Kr(n,e){if(!i(n.data.domProps)||!i(e.data.domProps)){var t,r,o=e.elm,c=n.data.domProps||{},l=e.data.domProps||{};for(t in(a(l.__ob__)||s(l._v_attr_proxy))&&(l=e.data.domProps=$({},l)),c)t in l||(o[t]="");for(t in l){if(r=l[t],"textContent"===t||"innerHTML"===t){if(e.children&&(e.children.length=0),r===c[t])continue;1===o.childNodes.length&&o.removeChild(o.childNodes[0])}if("value"===t&&"PROGRESS"!==o.tagName){o._value=r;var u=i(r)?"":String(r);Zr(o,u)&&(o.value=u)}else if("innerHTML"===t&&gr(o.tagName)&&i(o.innerHTML)){(Gr=Gr||document.createElement("div")).innerHTML="<svg>".concat(r,"</svg>");for(var p=Gr.firstChild;o.firstChild;)o.removeChild(o.firstChild);for(;p.firstChild;)o.appendChild(p.firstChild)}else if(r!==c[t])try{o[t]=r}catch(n){}}}}function Zr(n,e){return!n.composing&&("OPTION"===n.tagName||function(n,e){var t=!0;try{t=document.activeElement!==n}catch(n){}return t&&n.value!==e}(n,e)||function(n,e){var t=n.value,r=n._vModifiers;if(a(r)){if(r.number)return y(t)!==y(e);if(r.trim)return t.trim()!==e.trim()}return t!==e}(n,e))}var Qr={create:Kr,update:Kr},Xr=E((function(n){var e={},t=/:(.+)/;return n.split(/;(?![^(]*\))/g).forEach((function(n){if(n){var r=n.split(t);r.length>1&&(e[r[0].trim()]=r[1].trim())}})),e}));function Jr(n){var e=no(n.style);return n.staticStyle?$(n.staticStyle,e):e}function no(n){return Array.isArray(n)?P(n):"string"==typeof n?Xr(n):n}var eo,to=/^--/,ro=/\s*!important$/,oo=function(n,e,t){if(to.test(e))n.style.setProperty(e,t);else if(ro.test(t))n.style.setProperty(j(e),t.replace(ro,""),"important");else{var r=ao(e);if(Array.isArray(t))for(var o=0,i=t.length;o<i;o++)n.style[r]=t[o];else n.style[r]=t}},io=["Webkit","Moz","ms"],ao=E((function(n){if(eo=eo||document.createElement("div").style,"filter"!==(n=C(n))&&n in eo)return n;for(var e=n.charAt(0).toUpperCase()+n.slice(1),t=0;t<io.length;t++){var r=io[t]+e;if(r in eo)return r}}));function so(n,e){var t=e.data,r=n.data;if(!(i(t.staticStyle)&&i(t.style)&&i(r.staticStyle)&&i(r.style))){var o,s,c=e.elm,l=r.staticStyle,u=r.normalizedStyle||r.style||{},p=l||u,d=no(e.data.style)||{};e.data.normalizedStyle=a(d.__ob__)?$({},d):d;var f=function(n,e){var t,r={};if(e)for(var o=n;o.componentInstance;)(o=o.componentInstance._vnode)&&o.data&&(t=Jr(o.data))&&$(r,t);(t=Jr(n.data))&&$(r,t);for(var i=n;i=i.parent;)i.data&&(t=Jr(i.data))&&$(r,t);return r}(e,!0);for(s in p)i(f[s])&&oo(c,s,"");for(s in f)o=f[s],oo(c,s,null==o?"":o)}}var co={create:so,update:so},lo=/\s+/;function uo(n,e){if(e&&(e=e.trim()))if(n.classList)e.indexOf(" ")>-1?e.split(lo).forEach((function(e){return n.classList.add(e)})):n.classList.add(e);else{var t=" ".concat(n.getAttribute("class")||""," ");t.indexOf(" "+e+" ")<0&&n.setAttribute("class",(t+e).trim())}}function po(n,e){if(e&&(e=e.trim()))if(n.classList)e.indexOf(" ")>-1?e.split(lo).forEach((function(e){return n.classList.remove(e)})):n.classList.remove(e),n.classList.length||n.removeAttribute("class");else{for(var t=" ".concat(n.getAttribute("class")||""," "),r=" "+e+" ";t.indexOf(r)>=0;)t=t.replace(r," ");(t=t.trim())?n.setAttribute("class",t):n.removeAttribute("class")}}function fo(n){if(n){if("object"==typeof n){var e={};return!1!==n.css&&$(e,ho(n.name||"v")),$(e,n),e}return"string"==typeof n?ho(n):void 0}}var ho=E((function(n){return{enterClass:"".concat(n,"-enter"),enterToClass:"".concat(n,"-enter-to"),enterActiveClass:"".concat(n,"-enter-active"),leaveClass:"".concat(n,"-leave"),leaveToClass:"".concat(n,"-leave-to"),leaveActiveClass:"".concat(n,"-leave-active")}})),mo=K&&!X,go="transition",vo="transitionend",yo="animation",bo="animationend";mo&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(go="WebkitTransition",vo="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationend&&(yo="WebkitAnimation",bo="webkitAnimationEnd"));var xo=K?window.requestAnimationFrame?window.requestAnimationFrame.bind(window):setTimeout:function(n){return n()};function _o(n){xo((function(){xo(n)}))}function wo(n,e){var t=n._transitionClasses||(n._transitionClasses=[]);t.indexOf(e)<0&&(t.push(e),uo(n,e))}function To(n,e){n._transitionClasses&&_(n._transitionClasses,e),po(n,e)}function Eo(n,e,t){var r=Co(n,e),o=r.type,i=r.timeout,a=r.propCount;if(!o)return t();var s="transition"===o?vo:bo,c=0,l=function(){n.removeEventListener(s,u),t()},u=function(e){e.target===n&&++c>=a&&l()};setTimeout((function(){c<a&&l()}),i+1),n.addEventListener(s,u)}var ko=/\b(transform|all)(,|$)/;function Co(n,e){var t,r=window.getComputedStyle(n),o=(r[go+"Delay"]||"").split(", "),i=(r[go+"Duration"]||"").split(", "),a=So(o,i),s=(r[yo+"Delay"]||"").split(", "),c=(r[yo+"Duration"]||"").split(", "),l=So(s,c),u=0,p=0;return"transition"===e?a>0&&(t="transition",u=a,p=i.length):"animation"===e?l>0&&(t="animation",u=l,p=c.length):p=(t=(u=Math.max(a,l))>0?a>l?"transition":"animation":null)?"transition"===t?i.length:c.length:0,{type:t,timeout:u,propCount:p,hasTransform:"transition"===t&&ko.test(r[go+"Property"])}}function So(n,e){for(;n.length<e.length;)n=n.concat(n);return Math.max.apply(null,e.map((function(e,t){return Oo(e)+Oo(n[t])})))}function Oo(n){return 1e3*Number(n.slice(0,-1).replace(",","."))}function jo(n,e){var t=n.elm;a(t._leaveCb)&&(t._leaveCb.cancelled=!0,t._leaveCb());var r=fo(n.data.transition);if(!i(r)&&!a(t._enterCb)&&1===t.nodeType){for(var o=r.css,s=r.type,c=r.enterClass,p=r.enterToClass,d=r.enterActiveClass,f=r.appearClass,h=r.appearToClass,m=r.appearActiveClass,g=r.beforeEnter,v=r.enter,b=r.afterEnter,x=r.enterCancelled,_=r.beforeAppear,w=r.appear,T=r.afterAppear,E=r.appearCancelled,k=r.duration,C=Qe,S=Qe.$vnode;S&&S.parent;)C=S.context,S=S.parent;var O=!C._isMounted||!n.isRootInsert;if(!O||w||""===w){var j=O&&f?f:c,A=O&&m?m:d,I=O&&h?h:p,$=O&&_||g,P=O&&l(w)?w:v,z=O&&T||b,M=O&&E||x,D=y(u(k)?k.enter:k);0;var L=!1!==o&&!X,N=$o(P),R=t._enterCb=U((function(){L&&(To(t,I),To(t,A)),R.cancelled?(L&&To(t,j),M&&M(t)):z&&z(t),t._enterCb=null}));n.data.show||Wn(n,"insert",(function(){var e=t.parentNode,r=e&&e._pending&&e._pending[n.key];r&&r.tag===n.tag&&r.elm._leaveCb&&r.elm._leaveCb(),P&&P(t,R)})),$&&$(t),L&&(wo(t,j),wo(t,A),_o((function(){To(t,j),R.cancelled||(wo(t,I),N||(Io(D)?setTimeout(R,D):Eo(t,s,R)))}))),n.data.show&&(e&&e(),P&&P(t,R)),L||N||R()}}}function Ao(n,e){var t=n.elm;a(t._enterCb)&&(t._enterCb.cancelled=!0,t._enterCb());var r=fo(n.data.transition);if(i(r)||1!==t.nodeType)return e();if(!a(t._leaveCb)){var o=r.css,s=r.type,c=r.leaveClass,l=r.leaveToClass,p=r.leaveActiveClass,d=r.beforeLeave,f=r.leave,h=r.afterLeave,m=r.leaveCancelled,g=r.delayLeave,v=r.duration,b=!1!==o&&!X,x=$o(f),_=y(u(v)?v.leave:v);0;var w=t._leaveCb=U((function(){t.parentNode&&t.parentNode._pending&&(t.parentNode._pending[n.key]=null),b&&(To(t,l),To(t,p)),w.cancelled?(b&&To(t,c),m&&m(t)):(e(),h&&h(t)),t._leaveCb=null}));g?g(T):T()}function T(){w.cancelled||(!n.data.show&&t.parentNode&&((t.parentNode._pending||(t.parentNode._pending={}))[n.key]=n),d&&d(t),b&&(wo(t,c),wo(t,p),_o((function(){To(t,c),w.cancelled||(wo(t,l),x||(Io(_)?setTimeout(w,_):Eo(t,s,w)))}))),f&&f(t,w),b||x||w())}}function Io(n){return"number"==typeof n&&!isNaN(n)}function $o(n){if(i(n))return!1;var e=n.fns;return a(e)?$o(Array.isArray(e)?e[0]:e):(n._length||n.length)>1}function Po(n,e){!0!==e.data.show&&jo(e)}var zo=function(n){var e,t,r={},l=n.modules,u=n.nodeOps;for(e=0;e<kr.length;++e)for(r[kr[e]]=[],t=0;t<l.length;++t)a(l[t][kr[e]])&&r[kr[e]].push(l[t][kr[e]]);function p(n){var e=u.parentNode(n);a(e)&&u.removeChild(e,n)}function d(n,e,t,o,i,c,l){if(a(n.elm)&&a(c)&&(n=c[l]=vn(n)),n.isRootInsert=!i,!function(n,e,t,o){var i=n.data;if(a(i)){var c=a(n.componentInstance)&&i.keepAlive;if(a(i=i.hook)&&a(i=i.init)&&i(n,!1),a(n.componentInstance))return f(n,e),h(t,n.elm,o),s(c)&&function(n,e,t,o){var i,s=n;for(;s.componentInstance;)if(s=s.componentInstance._vnode,a(i=s.data)&&a(i=i.transition)){for(i=0;i<r.activate.length;++i)r.activate[i](Er,s);e.push(s);break}h(t,n.elm,o)}(n,e,t,o),!0}}(n,e,t,o)){var p=n.data,d=n.children,g=n.tag;a(g)?(n.elm=n.ns?u.createElementNS(n.ns,g):u.createElement(g,n),y(n),m(n,d,e),a(p)&&v(n,e),h(t,n.elm,o)):s(n.isComment)?(n.elm=u.createComment(n.text),h(t,n.elm,o)):(n.elm=u.createTextNode(n.text),h(t,n.elm,o))}}function f(n,e){a(n.data.pendingInsert)&&(e.push.apply(e,n.data.pendingInsert),n.data.pendingInsert=null),n.elm=n.componentInstance.$el,g(n)?(v(n,e),y(n)):(wr(n),e.push(n))}function h(n,e,t){a(n)&&(a(t)?u.parentNode(t)===n&&u.insertBefore(n,e,t):u.appendChild(n,e))}function m(n,e,t){if(o(e)){0;for(var r=0;r<e.length;++r)d(e[r],t,n.elm,null,!0,e,r)}else c(n.text)&&u.appendChild(n.elm,u.createTextNode(String(n.text)))}function g(n){for(;n.componentInstance;)n=n.componentInstance._vnode;return a(n.tag)}function v(n,t){for(var o=0;o<r.create.length;++o)r.create[o](Er,n);a(e=n.data.hook)&&(a(e.create)&&e.create(Er,n),a(e.insert)&&t.push(n))}function y(n){var e;if(a(e=n.fnScopeId))u.setStyleScope(n.elm,e);else for(var t=n;t;)a(e=t.context)&&a(e=e.$options._scopeId)&&u.setStyleScope(n.elm,e),t=t.parent;a(e=Qe)&&e!==n.context&&e!==n.fnContext&&a(e=e.$options._scopeId)&&u.setStyleScope(n.elm,e)}function x(n,e,t,r,o,i){for(;r<=o;++r)d(t[r],i,n,e,!1,t,r)}function _(n){var e,t,o=n.data;if(a(o))for(a(e=o.hook)&&a(e=e.destroy)&&e(n),e=0;e<r.destroy.length;++e)r.destroy[e](n);if(a(e=n.children))for(t=0;t<n.children.length;++t)_(n.children[t])}function w(n,e,t){for(;e<=t;++e){var r=n[e];a(r)&&(a(r.tag)?(T(r),_(r)):p(r.elm))}}function T(n,e){if(a(e)||a(n.data)){var t,o=r.remove.length+1;for(a(e)?e.listeners+=o:e=function(n,e){function t(){0==--t.listeners&&p(n)}return t.listeners=e,t}(n.elm,o),a(t=n.componentInstance)&&a(t=t._vnode)&&a(t.data)&&T(t,e),t=0;t<r.remove.length;++t)r.remove[t](n,e);a(t=n.data.hook)&&a(t=t.remove)?t(n,e):e()}else p(n.elm)}function E(n,e,t,r){for(var o=t;o<r;o++){var i=e[o];if(a(i)&&Cr(n,i))return o}}function k(n,e,t,o,c,l){if(n!==e){a(e.elm)&&a(o)&&(e=o[c]=vn(e));var p=e.elm=n.elm;if(s(n.isAsyncPlaceholder))a(e.asyncFactory.resolved)?O(n.elm,e,t):e.isAsyncPlaceholder=!0;else if(s(e.isStatic)&&s(n.isStatic)&&e.key===n.key&&(s(e.isCloned)||s(e.isOnce)))e.componentInstance=n.componentInstance;else{var f,h=e.data;a(h)&&a(f=h.hook)&&a(f=f.prepatch)&&f(n,e);var m=n.children,v=e.children;if(a(h)&&g(e)){for(f=0;f<r.update.length;++f)r.update[f](n,e);a(f=h.hook)&&a(f=f.update)&&f(n,e)}i(e.text)?a(m)&&a(v)?m!==v&&function(n,e,t,r,o){var s,c,l,p=0,f=0,h=e.length-1,m=e[0],g=e[h],v=t.length-1,y=t[0],b=t[v],_=!o;for(0;p<=h&&f<=v;)i(m)?m=e[++p]:i(g)?g=e[--h]:Cr(m,y)?(k(m,y,r,t,f),m=e[++p],y=t[++f]):Cr(g,b)?(k(g,b,r,t,v),g=e[--h],b=t[--v]):Cr(m,b)?(k(m,b,r,t,v),_&&u.insertBefore(n,m.elm,u.nextSibling(g.elm)),m=e[++p],b=t[--v]):Cr(g,y)?(k(g,y,r,t,f),_&&u.insertBefore(n,g.elm,m.elm),g=e[--h],y=t[++f]):(i(s)&&(s=Sr(e,p,h)),i(c=a(y.key)?s[y.key]:E(y,e,p,h))?d(y,r,n,m.elm,!1,t,f):Cr(l=e[c],y)?(k(l,y,r,t,f),e[c]=void 0,_&&u.insertBefore(n,l.elm,m.elm)):d(y,r,n,m.elm,!1,t,f),y=t[++f]);p>h?x(n,i(t[v+1])?null:t[v+1].elm,t,f,v,r):f>v&&w(e,p,h)}(p,m,v,t,l):a(v)?(a(n.text)&&u.setTextContent(p,""),x(p,null,v,0,v.length-1,t)):a(m)?w(m,0,m.length-1):a(n.text)&&u.setTextContent(p,""):n.text!==e.text&&u.setTextContent(p,e.text),a(h)&&a(f=h.hook)&&a(f=f.postpatch)&&f(n,e)}}}function C(n,e,t){if(s(t)&&a(n.parent))n.parent.data.pendingInsert=e;else for(var r=0;r<e.length;++r)e[r].data.hook.insert(e[r])}var S=b("attrs,class,staticClass,staticStyle,key");function O(n,e,t,r){var o,i=e.tag,c=e.data,l=e.children;if(r=r||c&&c.pre,e.elm=n,s(e.isComment)&&a(e.asyncFactory))return e.isAsyncPlaceholder=!0,!0;if(a(c)&&(a(o=c.hook)&&a(o=o.init)&&o(e,!0),a(o=e.componentInstance)))return f(e,t),!0;if(a(i)){if(a(l))if(n.hasChildNodes())if(a(o=c)&&a(o=o.domProps)&&a(o=o.innerHTML)){if(o!==n.innerHTML)return!1}else{for(var u=!0,p=n.firstChild,d=0;d<l.length;d++){if(!p||!O(p,l[d],t,r)){u=!1;break}p=p.nextSibling}if(!u||p)return!1}else m(e,l,t);if(a(c)){var h=!1;for(var g in c)if(!S(g)){h=!0,v(e,t);break}!h&&c.class&&Fe(c.class)}}else n.data!==e.text&&(n.data=e.text);return!0}return function(n,e,t,o){if(!i(e)){var c,l=!1,p=[];if(i(n))l=!0,d(e,p);else{var f=a(n.nodeType);if(!f&&Cr(n,e))k(n,e,p,null,null,o);else{if(f){if(1===n.nodeType&&n.hasAttribute("data-server-rendered")&&(n.removeAttribute("data-server-rendered"),t=!0),s(t)&&O(n,e,p))return C(e,p,!0),n;c=n,n=new hn(u.tagName(c).toLowerCase(),{},[],void 0,c)}var h=n.elm,m=u.parentNode(h);if(d(e,p,h._leaveCb?null:m,u.nextSibling(h)),a(e.parent))for(var v=e.parent,y=g(e);v;){for(var b=0;b<r.destroy.length;++b)r.destroy[b](v);if(v.elm=e.elm,y){for(var x=0;x<r.create.length;++x)r.create[x](Er,v);var T=v.data.hook.insert;if(T.merged)for(var E=T.fns.slice(1),S=0;S<E.length;S++)E[S]()}else wr(v);v=v.parent}a(m)?w([n],0,0):a(n.tag)&&_(n)}}return C(e,p,l),e.elm}a(n)&&_(n)}}({nodeOps:xr,modules:[Nr,Br,Wr,Qr,co,K?{create:Po,activate:Po,remove:function(n,e){!0!==n.data.show?Ao(n,e):e()}}:{}].concat(zr)});X&&document.addEventListener("selectionchange",(function(){var n=document.activeElement;n&&n.vmodel&&qo(n,"input")}));var Mo={inserted:function(n,e,t,r){"select"===t.tag?(r.elm&&!r.elm._vOptions?Wn(t,"postpatch",(function(){Mo.componentUpdated(n,e,t)})):Do(n,e,t.context),n._vOptions=[].map.call(n.options,Uo)):("textarea"===t.tag||br(n.type))&&(n._vModifiers=e.modifiers,e.modifiers.lazy||(n.addEventListener("compositionstart",Ro),n.addEventListener("compositionend",Bo),n.addEventListener("change",Bo),X&&(n.vmodel=!0)))},componentUpdated:function(n,e,t){if("select"===t.tag){Do(n,e,t.context);var r=n._vOptions,o=n._vOptions=[].map.call(n.options,Uo);if(o.some((function(n,e){return!L(n,r[e])})))(n.multiple?e.value.some((function(n){return No(n,o)})):e.value!==e.oldValue&&No(e.value,o))&&qo(n,"change")}}};function Do(n,e,t){Lo(n,e,t),(Q||J)&&setTimeout((function(){Lo(n,e,t)}),0)}function Lo(n,e,t){var r=e.value,o=n.multiple;if(!o||Array.isArray(r)){for(var i,a,s=0,c=n.options.length;s<c;s++)if(a=n.options[s],o)i=N(r,Uo(a))>-1,a.selected!==i&&(a.selected=i);else if(L(Uo(a),r))return void(n.selectedIndex!==s&&(n.selectedIndex=s));o||(n.selectedIndex=-1)}}function No(n,e){return e.every((function(e){return!L(e,n)}))}function Uo(n){return"_value"in n?n._value:n.value}function Ro(n){n.target.composing=!0}function Bo(n){n.target.composing&&(n.target.composing=!1,qo(n.target,"input"))}function qo(n,e){var t=document.createEvent("HTMLEvents");t.initEvent(e,!0,!0),n.dispatchEvent(t)}function Fo(n){return!n.componentInstance||n.data&&n.data.transition?n:Fo(n.componentInstance._vnode)}var Ho={model:Mo,show:{bind:function(n,e,t){var r=e.value,o=(t=Fo(t)).data&&t.data.transition,i=n.__vOriginalDisplay="none"===n.style.display?"":n.style.display;r&&o?(t.data.show=!0,jo(t,(function(){n.style.display=i}))):n.style.display=r?i:"none"},update:function(n,e,t){var r=e.value;!r!=!e.oldValue&&((t=Fo(t)).data&&t.data.transition?(t.data.show=!0,r?jo(t,(function(){n.style.display=n.__vOriginalDisplay})):Ao(t,(function(){n.style.display="none"}))):n.style.display=r?n.__vOriginalDisplay:"none")},unbind:function(n,e,t,r,o){o||(n.style.display=n.__vOriginalDisplay)}}},Vo={name:String,appear:Boolean,css:Boolean,mode:String,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Number,String,Object]};function Yo(n){var e=n&&n.componentOptions;return e&&e.Ctor.options.abstract?Yo(ke(e.children)):n}function Go(n){var e={},t=n.$options;for(var r in t.propsData)e[r]=n[r];var o=t._parentListeners;for(var r in o)e[C(r)]=o[r];return e}function Wo(n,e){if(/\d-keep-alive$/.test(e.tag))return n("keep-alive",{props:e.componentOptions.propsData})}var Ko=function(n){return n.tag||me(n)},Zo=function(n){return"show"===n.name},Qo={name:"transition",props:Vo,abstract:!0,render:function(n){var e=this,t=this.$slots.default;if(t&&(t=t.filter(Ko)).length){0;var r=this.mode;0;var o=t[0];if(function(n){for(;n=n.parent;)if(n.data.transition)return!0}(this.$vnode))return o;var i=Yo(o);if(!i)return o;if(this._leaving)return Wo(n,o);var a="__transition-".concat(this._uid,"-");i.key=null==i.key?i.isComment?a+"comment":a+i.tag:c(i.key)?0===String(i.key).indexOf(a)?i.key:a+i.key:i.key;var s=(i.data||(i.data={})).transition=Go(this),l=this._vnode,u=Yo(l);if(i.data.directives&&i.data.directives.some(Zo)&&(i.data.show=!0),u&&u.data&&!function(n,e){return e.key===n.key&&e.tag===n.tag}(i,u)&&!me(u)&&(!u.componentInstance||!u.componentInstance._vnode.isComment)){var p=u.data.transition=$({},s);if("out-in"===r)return this._leaving=!0,Wn(p,"afterLeave",(function(){e._leaving=!1,e.$forceUpdate()})),Wo(n,o);if("in-out"===r){if(me(i))return l;var d,f=function(){d()};Wn(s,"afterEnter",f),Wn(s,"enterCancelled",f),Wn(p,"delayLeave",(function(n){d=n}))}}return o}}},Xo=$({tag:String,moveClass:String},Vo);function Jo(n){n.elm._moveCb&&n.elm._moveCb(),n.elm._enterCb&&n.elm._enterCb()}function ni(n){n.data.newPos=n.elm.getBoundingClientRect()}function ei(n){var e=n.data.pos,t=n.data.newPos,r=e.left-t.left,o=e.top-t.top;if(r||o){n.data.moved=!0;var i=n.elm.style;i.transform=i.WebkitTransform="translate(".concat(r,"px,").concat(o,"px)"),i.transitionDuration="0s"}}delete Xo.mode;var ti={Transition:Qo,TransitionGroup:{props:Xo,beforeMount:function(){var n=this,e=this._update;this._update=function(t,r){var o=Xe(n);n.__patch__(n._vnode,n.kept,!1,!0),n._vnode=n.kept,o(),e.call(n,t,r)}},render:function(n){for(var e=this.tag||this.$vnode.data.tag||"span",t=Object.create(null),r=this.prevChildren=this.children,o=this.$slots.default||[],i=this.children=[],a=Go(this),s=0;s<o.length;s++){if((u=o[s]).tag)if(null!=u.key&&0!==String(u.key).indexOf("__vlist"))i.push(u),t[u.key]=u,(u.data||(u.data={})).transition=a;else;}if(r){var c=[],l=[];for(s=0;s<r.length;s++){var u;(u=r[s]).data.transition=a,u.data.pos=u.elm.getBoundingClientRect(),t[u.key]?c.push(u):l.push(u)}this.kept=n(e,null,c),this.removed=l}return n(e,null,i)},updated:function(){var n=this.prevChildren,e=this.moveClass||(this.name||"v")+"-move";n.length&&this.hasMove(n[0].elm,e)&&(n.forEach(Jo),n.forEach(ni),n.forEach(ei),this._reflow=document.body.offsetHeight,n.forEach((function(n){if(n.data.moved){var t=n.elm,r=t.style;wo(t,e),r.transform=r.WebkitTransform=r.transitionDuration="",t.addEventListener(vo,t._moveCb=function n(r){r&&r.target!==t||r&&!/transform$/.test(r.propertyName)||(t.removeEventListener(vo,n),t._moveCb=null,To(t,e))})}})))},methods:{hasMove:function(n,e){if(!mo)return!1;if(this._hasMove)return this._hasMove;var t=n.cloneNode();n._transitionClasses&&n._transitionClasses.forEach((function(n){po(t,n)})),uo(t,e),t.style.display="none",this.$el.appendChild(t);var r=Co(t);return this.$el.removeChild(t),this._hasMove=r.hasTransform}}}};function ri(n,e){for(var t in e)n[t]=e[t];return n}Gt.config.mustUseProp=function(n,e,t){return"value"===t&&tr(n)&&"button"!==e||"selected"===t&&"option"===n||"checked"===t&&"input"===n||"muted"===t&&"video"===n},Gt.config.isReservedTag=vr,Gt.config.isReservedAttr=er,Gt.config.getTagNamespace=function(n){return gr(n)?"svg":"math"===n?"math":void 0},Gt.config.isUnknownElement=function(n){if(!K)return!0;if(vr(n))return!1;if(n=n.toLowerCase(),null!=yr[n])return yr[n];var e=document.createElement(n);return n.indexOf("-")>-1?yr[n]=e.constructor===window.HTMLUnknownElement||e.constructor===window.HTMLElement:yr[n]=/HTMLUnknownElement/.test(e.toString())},$(Gt.options.directives,Ho),$(Gt.options.components,ti),Gt.prototype.__patch__=K?zo:z,Gt.prototype.$mount=function(n,e){return function(n,e,t){var r;n.$el=e,n.$options.render||(n.$options.render=mn),et(n,"beforeMount"),r=function(){n._update(n._render(),t)},new Ye(n,r,z,{before:function(){n._isMounted&&!n._isDestroyed&&et(n,"beforeUpdate")}},!0),t=!1;var o=n._preWatchers;if(o)for(var i=0;i<o.length;i++)o[i].run();return null==n.$vnode&&(n._isMounted=!0,et(n,"mounted")),n}(this,n=n&&K?function(n){if("string"==typeof n){var e=document.querySelector(n);return e||document.createElement("div")}return n}(n):void 0,e)},K&&setTimeout((function(){F.devtools&&cn&&cn.emit("init",Gt)}),0);var oi=/[!'()*]/g,ii=function(n){return"%"+n.charCodeAt(0).toString(16)},ai=/%2C/g,si=function(n){return encodeURIComponent(n).replace(oi,ii).replace(ai,",")};function ci(n){try{return decodeURIComponent(n)}catch(n){0}return n}var li=function(n){return null==n||"object"==typeof n?n:String(n)};function ui(n){var e={};return(n=n.trim().replace(/^(\?|#|&)/,""))?(n.split("&").forEach((function(n){var t=n.replace(/\+/g," ").split("="),r=ci(t.shift()),o=t.length>0?ci(t.join("=")):null;void 0===e[r]?e[r]=o:Array.isArray(e[r])?e[r].push(o):e[r]=[e[r],o]})),e):e}function pi(n){var e=n?Object.keys(n).map((function(e){var t=n[e];if(void 0===t)return"";if(null===t)return si(e);if(Array.isArray(t)){var r=[];return t.forEach((function(n){void 0!==n&&(null===n?r.push(si(e)):r.push(si(e)+"="+si(n)))})),r.join("&")}return si(e)+"="+si(t)})).filter((function(n){return n.length>0})).join("&"):null;return e?"?"+e:""}var di=/\/?$/;function fi(n,e,t,r){var o=r&&r.options.stringifyQuery,i=e.query||{};try{i=hi(i)}catch(n){}var a={name:e.name||n&&n.name,meta:n&&n.meta||{},path:e.path||"/",hash:e.hash||"",query:i,params:e.params||{},fullPath:vi(e,o),matched:n?gi(n):[]};return t&&(a.redirectedFrom=vi(t,o)),Object.freeze(a)}function hi(n){if(Array.isArray(n))return n.map(hi);if(n&&"object"==typeof n){var e={};for(var t in n)e[t]=hi(n[t]);return e}return n}var mi=fi(null,{path:"/"});function gi(n){for(var e=[];n;)e.unshift(n),n=n.parent;return e}function vi(n,e){var t=n.path,r=n.query;void 0===r&&(r={});var o=n.hash;return void 0===o&&(o=""),(t||"/")+(e||pi)(r)+o}function yi(n,e,t){return e===mi?n===e:!!e&&(n.path&&e.path?n.path.replace(di,"")===e.path.replace(di,"")&&(t||n.hash===e.hash&&bi(n.query,e.query)):!(!n.name||!e.name)&&(n.name===e.name&&(t||n.hash===e.hash&&bi(n.query,e.query)&&bi(n.params,e.params))))}function bi(n,e){if(void 0===n&&(n={}),void 0===e&&(e={}),!n||!e)return n===e;var t=Object.keys(n).sort(),r=Object.keys(e).sort();return t.length===r.length&&t.every((function(t,o){var i=n[t];if(r[o]!==t)return!1;var a=e[t];return null==i||null==a?i===a:"object"==typeof i&&"object"==typeof a?bi(i,a):String(i)===String(a)}))}function xi(n){for(var e=0;e<n.matched.length;e++){var t=n.matched[e];for(var r in t.instances){var o=t.instances[r],i=t.enteredCbs[r];if(o&&i){delete t.enteredCbs[r];for(var a=0;a<i.length;a++)o._isBeingDestroyed||i[a](o)}}}}var _i={name:"RouterView",functional:!0,props:{name:{type:String,default:"default"}},render:function(n,e){var t=e.props,r=e.children,o=e.parent,i=e.data;i.routerView=!0;for(var a=o.$createElement,s=t.name,c=o.$route,l=o._routerViewCache||(o._routerViewCache={}),u=0,p=!1;o&&o._routerRoot!==o;){var d=o.$vnode?o.$vnode.data:{};d.routerView&&u++,d.keepAlive&&o._directInactive&&o._inactive&&(p=!0),o=o.$parent}if(i.routerViewDepth=u,p){var f=l[s],h=f&&f.component;return h?(f.configProps&&wi(h,i,f.route,f.configProps),a(h,i,r)):a()}var m=c.matched[u],g=m&&m.components[s];if(!m||!g)return l[s]=null,a();l[s]={component:g},i.registerRouteInstance=function(n,e){var t=m.instances[s];(e&&t!==n||!e&&t===n)&&(m.instances[s]=e)},(i.hook||(i.hook={})).prepatch=function(n,e){m.instances[s]=e.componentInstance},i.hook.init=function(n){n.data.keepAlive&&n.componentInstance&&n.componentInstance!==m.instances[s]&&(m.instances[s]=n.componentInstance),xi(c)};var v=m.props&&m.props[s];return v&&(ri(l[s],{route:c,configProps:v}),wi(g,i,c,v)),a(g,i,r)}};function wi(n,e,t,r){var o=e.props=function(n,e){switch(typeof e){case"undefined":return;case"object":return e;case"function":return e(n);case"boolean":return e?n.params:void 0;default:0}}(t,r);if(o){o=e.props=ri({},o);var i=e.attrs=e.attrs||{};for(var a in o)n.props&&a in n.props||(i[a]=o[a],delete o[a])}}function Ti(n,e,t){var r=n.charAt(0);if("/"===r)return n;if("?"===r||"#"===r)return e+n;var o=e.split("/");t&&o[o.length-1]||o.pop();for(var i=n.replace(/^\//,"").split("/"),a=0;a<i.length;a++){var s=i[a];".."===s?o.pop():"."!==s&&o.push(s)}return""!==o[0]&&o.unshift(""),o.join("/")}function Ei(n){return n.replace(/\/(?:\s*\/)+/g,"/")}var ki=Array.isArray||function(n){return"[object Array]"==Object.prototype.toString.call(n)},Ci=Ri,Si=$i,Oi=function(n,e){return zi($i(n,e),e)},ji=zi,Ai=Ui,Ii=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function $i(n,e){for(var t,r=[],o=0,i=0,a="",s=e&&e.delimiter||"/";null!=(t=Ii.exec(n));){var c=t[0],l=t[1],u=t.index;if(a+=n.slice(i,u),i=u+c.length,l)a+=l[1];else{var p=n[i],d=t[2],f=t[3],h=t[4],m=t[5],g=t[6],v=t[7];a&&(r.push(a),a="");var y=null!=d&&null!=p&&p!==d,b="+"===g||"*"===g,x="?"===g||"*"===g,_=t[2]||s,w=h||m;r.push({name:f||o++,prefix:d||"",delimiter:_,optional:x,repeat:b,partial:y,asterisk:!!v,pattern:w?Di(w):v?".*":"[^"+Mi(_)+"]+?"})}}return i<n.length&&(a+=n.substr(i)),a&&r.push(a),r}function Pi(n){return encodeURI(n).replace(/[\/?#]/g,(function(n){return"%"+n.charCodeAt(0).toString(16).toUpperCase()}))}function zi(n,e){for(var t=new Array(n.length),r=0;r<n.length;r++)"object"==typeof n[r]&&(t[r]=new RegExp("^(?:"+n[r].pattern+")$",Ni(e)));return function(e,r){for(var o="",i=e||{},a=(r||{}).pretty?Pi:encodeURIComponent,s=0;s<n.length;s++){var c=n[s];if("string"!=typeof c){var l,u=i[c.name];if(null==u){if(c.optional){c.partial&&(o+=c.prefix);continue}throw new TypeError('Expected "'+c.name+'" to be defined')}if(ki(u)){if(!c.repeat)throw new TypeError('Expected "'+c.name+'" to not repeat, but received `'+JSON.stringify(u)+"`");if(0===u.length){if(c.optional)continue;throw new TypeError('Expected "'+c.name+'" to not be empty')}for(var p=0;p<u.length;p++){if(l=a(u[p]),!t[s].test(l))throw new TypeError('Expected all "'+c.name+'" to match "'+c.pattern+'", but received `'+JSON.stringify(l)+"`");o+=(0===p?c.prefix:c.delimiter)+l}}else{if(l=c.asterisk?encodeURI(u).replace(/[?#]/g,(function(n){return"%"+n.charCodeAt(0).toString(16).toUpperCase()})):a(u),!t[s].test(l))throw new TypeError('Expected "'+c.name+'" to match "'+c.pattern+'", but received "'+l+'"');o+=c.prefix+l}}else o+=c}return o}}function Mi(n){return n.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function Di(n){return n.replace(/([=!:$\/()])/g,"\\$1")}function Li(n,e){return n.keys=e,n}function Ni(n){return n&&n.sensitive?"":"i"}function Ui(n,e,t){ki(e)||(t=e||t,e=[]);for(var r=(t=t||{}).strict,o=!1!==t.end,i="",a=0;a<n.length;a++){var s=n[a];if("string"==typeof s)i+=Mi(s);else{var c=Mi(s.prefix),l="(?:"+s.pattern+")";e.push(s),s.repeat&&(l+="(?:"+c+l+")*"),i+=l=s.optional?s.partial?c+"("+l+")?":"(?:"+c+"("+l+"))?":c+"("+l+")"}}var u=Mi(t.delimiter||"/"),p=i.slice(-u.length)===u;return r||(i=(p?i.slice(0,-u.length):i)+"(?:"+u+"(?=$))?"),i+=o?"$":r&&p?"":"(?="+u+"|$)",Li(new RegExp("^"+i,Ni(t)),e)}function Ri(n,e,t){return ki(e)||(t=e||t,e=[]),t=t||{},n instanceof RegExp?function(n,e){var t=n.source.match(/\((?!\?)/g);if(t)for(var r=0;r<t.length;r++)e.push({name:r,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return Li(n,e)}(n,e):ki(n)?function(n,e,t){for(var r=[],o=0;o<n.length;o++)r.push(Ri(n[o],e,t).source);return Li(new RegExp("(?:"+r.join("|")+")",Ni(t)),e)}(n,e,t):function(n,e,t){return Ui($i(n,t),e,t)}(n,e,t)}Ci.parse=Si,Ci.compile=Oi,Ci.tokensToFunction=ji,Ci.tokensToRegExp=Ai;var Bi=Object.create(null);function qi(n,e,t){e=e||{};try{var r=Bi[n]||(Bi[n]=Ci.compile(n));return"string"==typeof e.pathMatch&&(e[0]=e.pathMatch),r(e,{pretty:!0})}catch(n){return""}finally{delete e[0]}}function Fi(n,e,t,r){var o="string"==typeof n?{path:n}:n;if(o._normalized)return o;if(o.name){var i=(o=ri({},n)).params;return i&&"object"==typeof i&&(o.params=ri({},i)),o}if(!o.path&&o.params&&e){(o=ri({},o))._normalized=!0;var a=ri(ri({},e.params),o.params);if(e.name)o.name=e.name,o.params=a;else if(e.matched.length){var s=e.matched[e.matched.length-1].path;o.path=qi(s,a,e.path)}else 0;return o}var c=function(n){var e="",t="",r=n.indexOf("#");r>=0&&(e=n.slice(r),n=n.slice(0,r));var o=n.indexOf("?");return o>=0&&(t=n.slice(o+1),n=n.slice(0,o)),{path:n,query:t,hash:e}}(o.path||""),l=e&&e.path||"/",u=c.path?Ti(c.path,l,t||o.append):l,p=function(n,e,t){void 0===e&&(e={});var r,o=t||ui;try{r=o(n||"")}catch(n){r={}}for(var i in e){var a=e[i];r[i]=Array.isArray(a)?a.map(li):li(a)}return r}(c.query,o.query,r&&r.options.parseQuery),d=o.hash||c.hash;return d&&"#"!==d.charAt(0)&&(d="#"+d),{_normalized:!0,path:u,query:p,hash:d}}var Hi,Vi=function(){},Yi={name:"RouterLink",props:{to:{type:[String,Object],required:!0},tag:{type:String,default:"a"},custom:Boolean,exact:Boolean,exactPath:Boolean,append:Boolean,replace:Boolean,activeClass:String,exactActiveClass:String,ariaCurrentValue:{type:String,default:"page"},event:{type:[String,Array],default:"click"}},render:function(n){var e=this,t=this.$router,r=this.$route,o=t.resolve(this.to,r,this.append),i=o.location,a=o.route,s=o.href,c={},l=t.options.linkActiveClass,u=t.options.linkExactActiveClass,p=null==l?"router-link-active":l,d=null==u?"router-link-exact-active":u,f=null==this.activeClass?p:this.activeClass,h=null==this.exactActiveClass?d:this.exactActiveClass,m=a.redirectedFrom?fi(null,Fi(a.redirectedFrom),null,t):a;c[h]=yi(r,m,this.exactPath),c[f]=this.exact||this.exactPath?c[h]:function(n,e){return 0===n.path.replace(di,"/").indexOf(e.path.replace(di,"/"))&&(!e.hash||n.hash===e.hash)&&function(n,e){for(var t in e)if(!(t in n))return!1;return!0}(n.query,e.query)}(r,m);var g=c[h]?this.ariaCurrentValue:null,v=function(n){Gi(n)&&(e.replace?t.replace(i,Vi):t.push(i,Vi))},y={click:Gi};Array.isArray(this.event)?this.event.forEach((function(n){y[n]=v})):y[this.event]=v;var b={class:c},x=!this.$scopedSlots.$hasNormal&&this.$scopedSlots.default&&this.$scopedSlots.default({href:s,route:a,navigate:v,isActive:c[f],isExactActive:c[h]});if(x){if(1===x.length)return x[0];if(x.length>1||!x.length)return 0===x.length?n():n("span",{},x)}if("a"===this.tag)b.on=y,b.attrs={href:s,"aria-current":g};else{var _=function n(e){var t;if(e)for(var r=0;r<e.length;r++){if("a"===(t=e[r]).tag)return t;if(t.children&&(t=n(t.children)))return t}}(this.$slots.default);if(_){_.isStatic=!1;var w=_.data=ri({},_.data);for(var T in w.on=w.on||{},w.on){var E=w.on[T];T in y&&(w.on[T]=Array.isArray(E)?E:[E])}for(var k in y)k in w.on?w.on[k].push(y[k]):w.on[k]=v;var C=_.data.attrs=ri({},_.data.attrs);C.href=s,C["aria-current"]=g}else b.on=y}return n(this.tag,b,this.$slots.default)}};function Gi(n){if(!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey||n.defaultPrevented||void 0!==n.button&&0!==n.button)){if(n.currentTarget&&n.currentTarget.getAttribute){var e=n.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return n.preventDefault&&n.preventDefault(),!0}}var Wi="undefined"!=typeof window;function Ki(n,e,t,r,o){var i=e||[],a=t||Object.create(null),s=r||Object.create(null);n.forEach((function(n){!function n(e,t,r,o,i,a){var s=o.path,c=o.name;0;var l=o.pathToRegexpOptions||{},u=function(n,e,t){t||(n=n.replace(/\/$/,""));if("/"===n[0])return n;if(null==e)return n;return Ei(e.path+"/"+n)}(s,i,l.strict);"boolean"==typeof o.caseSensitive&&(l.sensitive=o.caseSensitive);var p={path:u,regex:Zi(u,l),components:o.components||{default:o.component},alias:o.alias?"string"==typeof o.alias?[o.alias]:o.alias:[],instances:{},enteredCbs:{},name:c,parent:i,matchAs:a,redirect:o.redirect,beforeEnter:o.beforeEnter,meta:o.meta||{},props:null==o.props?{}:o.components?o.props:{default:o.props}};o.children&&o.children.forEach((function(o){var i=a?Ei(a+"/"+o.path):void 0;n(e,t,r,o,p,i)}));t[p.path]||(e.push(p.path),t[p.path]=p);if(void 0!==o.alias)for(var d=Array.isArray(o.alias)?o.alias:[o.alias],f=0;f<d.length;++f){0;var h={path:d[f],children:o.children};n(e,t,r,h,i,p.path||"/")}c&&(r[c]||(r[c]=p))}(i,a,s,n,o)}));for(var c=0,l=i.length;c<l;c++)"*"===i[c]&&(i.push(i.splice(c,1)[0]),l--,c--);return{pathList:i,pathMap:a,nameMap:s}}function Zi(n,e){return Ci(n,[],e)}function Qi(n,e){var t=Ki(n),r=t.pathList,o=t.pathMap,i=t.nameMap;function a(n,t,a){var s=Fi(n,t,!1,e),l=s.name;if(l){var u=i[l];if(!u)return c(null,s);var p=u.regex.keys.filter((function(n){return!n.optional})).map((function(n){return n.name}));if("object"!=typeof s.params&&(s.params={}),t&&"object"==typeof t.params)for(var d in t.params)!(d in s.params)&&p.indexOf(d)>-1&&(s.params[d]=t.params[d]);return s.path=qi(u.path,s.params),c(u,s,a)}if(s.path){s.params={};for(var f=0;f<r.length;f++){var h=r[f],m=o[h];if(Xi(m.regex,s.path,s.params))return c(m,s,a)}}return c(null,s)}function s(n,t){var r=n.redirect,o="function"==typeof r?r(fi(n,t,null,e)):r;if("string"==typeof o&&(o={path:o}),!o||"object"!=typeof o)return c(null,t);var s=o,l=s.name,u=s.path,p=t.query,d=t.hash,f=t.params;if(p=s.hasOwnProperty("query")?s.query:p,d=s.hasOwnProperty("hash")?s.hash:d,f=s.hasOwnProperty("params")?s.params:f,l){i[l];return a({_normalized:!0,name:l,query:p,hash:d,params:f},void 0,t)}if(u){var h=function(n,e){return Ti(n,e.parent?e.parent.path:"/",!0)}(u,n);return a({_normalized:!0,path:qi(h,f),query:p,hash:d},void 0,t)}return c(null,t)}function c(n,t,r){return n&&n.redirect?s(n,r||t):n&&n.matchAs?function(n,e,t){var r=a({_normalized:!0,path:qi(t,e.params)});if(r){var o=r.matched,i=o[o.length-1];return e.params=r.params,c(i,e)}return c(null,e)}(0,t,n.matchAs):fi(n,t,r,e)}return{match:a,addRoute:function(n,e){var t="object"!=typeof n?i[n]:void 0;Ki([e||n],r,o,i,t),t&&t.alias.length&&Ki(t.alias.map((function(n){return{path:n,children:[e]}})),r,o,i,t)},getRoutes:function(){return r.map((function(n){return o[n]}))},addRoutes:function(n){Ki(n,r,o,i)}}}function Xi(n,e,t){var r=e.match(n);if(!r)return!1;if(!t)return!0;for(var o=1,i=r.length;o<i;++o){var a=n.keys[o-1];a&&(t[a.name||"pathMatch"]="string"==typeof r[o]?ci(r[o]):r[o])}return!0}var Ji=Wi&&window.performance&&window.performance.now?window.performance:Date;function na(){return Ji.now().toFixed(3)}var ea=na();function ta(){return ea}function ra(n){return ea=n}var oa=Object.create(null);function ia(){"scrollRestoration"in window.history&&(window.history.scrollRestoration="manual");var n=window.location.protocol+"//"+window.location.host,e=window.location.href.replace(n,""),t=ri({},window.history.state);return t.key=ta(),window.history.replaceState(t,"",e),window.addEventListener("popstate",ca),function(){window.removeEventListener("popstate",ca)}}function aa(n,e,t,r){if(n.app){var o=n.options.scrollBehavior;o&&n.app.$nextTick((function(){var i=function(){var n=ta();if(n)return oa[n]}(),a=o.call(n,e,t,r?i:null);a&&("function"==typeof a.then?a.then((function(n){fa(n,i)})).catch((function(n){0})):fa(a,i))}))}}function sa(){var n=ta();n&&(oa[n]={x:window.pageXOffset,y:window.pageYOffset})}function ca(n){sa(),n.state&&n.state.key&&ra(n.state.key)}function la(n){return pa(n.x)||pa(n.y)}function ua(n){return{x:pa(n.x)?n.x:window.pageXOffset,y:pa(n.y)?n.y:window.pageYOffset}}function pa(n){return"number"==typeof n}var da=/^#\d/;function fa(n,e){var t,r="object"==typeof n;if(r&&"string"==typeof n.selector){var o=da.test(n.selector)?document.getElementById(n.selector.slice(1)):document.querySelector(n.selector);if(o){var i=n.offset&&"object"==typeof n.offset?n.offset:{};e=function(n,e){var t=document.documentElement.getBoundingClientRect(),r=n.getBoundingClientRect();return{x:r.left-t.left-e.x,y:r.top-t.top-e.y}}(o,i={x:pa((t=i).x)?t.x:0,y:pa(t.y)?t.y:0})}else la(n)&&(e=ua(n))}else r&&la(n)&&(e=ua(n));e&&("scrollBehavior"in document.documentElement.style?window.scrollTo({left:e.x,top:e.y,behavior:n.behavior}):window.scrollTo(e.x,e.y))}var ha,ma=Wi&&((-1===(ha=window.navigator.userAgent).indexOf("Android 2.")&&-1===ha.indexOf("Android 4.0")||-1===ha.indexOf("Mobile Safari")||-1!==ha.indexOf("Chrome")||-1!==ha.indexOf("Windows Phone"))&&window.history&&"function"==typeof window.history.pushState);function ga(n,e){sa();var t=window.history;try{if(e){var r=ri({},t.state);r.key=ta(),t.replaceState(r,"",n)}else t.pushState({key:ra(na())},"",n)}catch(t){window.location[e?"replace":"assign"](n)}}function va(n){ga(n,!0)}var ya={redirected:2,aborted:4,cancelled:8,duplicated:16};function ba(n,e){return _a(n,e,ya.redirected,'Redirected when going from "'+n.fullPath+'" to "'+function(n){if("string"==typeof n)return n;if("path"in n)return n.path;var e={};return wa.forEach((function(t){t in n&&(e[t]=n[t])})),JSON.stringify(e,null,2)}(e)+'" via a navigation guard.')}function xa(n,e){return _a(n,e,ya.cancelled,'Navigation cancelled from "'+n.fullPath+'" to "'+e.fullPath+'" with a new navigation.')}function _a(n,e,t,r){var o=new Error(r);return o._isRouter=!0,o.from=n,o.to=e,o.type=t,o}var wa=["params","query","hash"];function Ta(n){return Object.prototype.toString.call(n).indexOf("Error")>-1}function Ea(n,e){return Ta(n)&&n._isRouter&&(null==e||n.type===e)}function ka(n,e,t){var r=function(o){o>=n.length?t():n[o]?e(n[o],(function(){r(o+1)})):r(o+1)};r(0)}function Ca(n){return function(e,t,r){var o=!1,i=0,a=null;Sa(n,(function(n,e,t,s){if("function"==typeof n&&void 0===n.cid){o=!0,i++;var c,l=Aa((function(e){var o;((o=e).__esModule||ja&&"Module"===o[Symbol.toStringTag])&&(e=e.default),n.resolved="function"==typeof e?e:Hi.extend(e),t.components[s]=e,--i<=0&&r()})),u=Aa((function(n){var e="Failed to resolve async component "+s+": "+n;a||(a=Ta(n)?n:new Error(e),r(a))}));try{c=n(l,u)}catch(n){u(n)}if(c)if("function"==typeof c.then)c.then(l,u);else{var p=c.component;p&&"function"==typeof p.then&&p.then(l,u)}}})),o||r()}}function Sa(n,e){return Oa(n.map((function(n){return Object.keys(n.components).map((function(t){return e(n.components[t],n.instances[t],n,t)}))})))}function Oa(n){return Array.prototype.concat.apply([],n)}var ja="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;function Aa(n){var e=!1;return function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];if(!e)return e=!0,n.apply(this,t)}}var Ia=function(n,e){this.router=n,this.base=function(n){if(!n)if(Wi){var e=document.querySelector("base");n=(n=e&&e.getAttribute("href")||"/").replace(/^https?:\/\/[^\/]+/,"")}else n="/";"/"!==n.charAt(0)&&(n="/"+n);return n.replace(/\/$/,"")}(e),this.current=mi,this.pending=null,this.ready=!1,this.readyCbs=[],this.readyErrorCbs=[],this.errorCbs=[],this.listeners=[]};function $a(n,e,t,r){var o=Sa(n,(function(n,r,o,i){var a=function(n,e){"function"!=typeof n&&(n=Hi.extend(n));return n.options[e]}(n,e);if(a)return Array.isArray(a)?a.map((function(n){return t(n,r,o,i)})):t(a,r,o,i)}));return Oa(r?o.reverse():o)}function Pa(n,e){if(e)return function(){return n.apply(e,arguments)}}Ia.prototype.listen=function(n){this.cb=n},Ia.prototype.onReady=function(n,e){this.ready?n():(this.readyCbs.push(n),e&&this.readyErrorCbs.push(e))},Ia.prototype.onError=function(n){this.errorCbs.push(n)},Ia.prototype.transitionTo=function(n,e,t){var r,o=this;try{r=this.router.match(n,this.current)}catch(n){throw this.errorCbs.forEach((function(e){e(n)})),n}var i=this.current;this.confirmTransition(r,(function(){o.updateRoute(r),e&&e(r),o.ensureURL(),o.router.afterHooks.forEach((function(n){n&&n(r,i)})),o.ready||(o.ready=!0,o.readyCbs.forEach((function(n){n(r)})))}),(function(n){t&&t(n),n&&!o.ready&&(Ea(n,ya.redirected)&&i===mi||(o.ready=!0,o.readyErrorCbs.forEach((function(e){e(n)}))))}))},Ia.prototype.confirmTransition=function(n,e,t){var r=this,o=this.current;this.pending=n;var i,a,s=function(n){!Ea(n)&&Ta(n)&&(r.errorCbs.length?r.errorCbs.forEach((function(e){e(n)})):console.error(n)),t&&t(n)},c=n.matched.length-1,l=o.matched.length-1;if(yi(n,o)&&c===l&&n.matched[c]===o.matched[l])return this.ensureURL(),n.hash&&aa(this.router,o,n,!1),s(((a=_a(i=o,n,ya.duplicated,'Avoided redundant navigation to current location: "'+i.fullPath+'".')).name="NavigationDuplicated",a));var u=function(n,e){var t,r=Math.max(n.length,e.length);for(t=0;t<r&&n[t]===e[t];t++);return{updated:e.slice(0,t),activated:e.slice(t),deactivated:n.slice(t)}}(this.current.matched,n.matched),p=u.updated,d=u.deactivated,f=u.activated,h=[].concat(function(n){return $a(n,"beforeRouteLeave",Pa,!0)}(d),this.router.beforeHooks,function(n){return $a(n,"beforeRouteUpdate",Pa)}(p),f.map((function(n){return n.beforeEnter})),Ca(f)),m=function(e,t){if(r.pending!==n)return s(xa(o,n));try{e(n,o,(function(e){!1===e?(r.ensureURL(!0),s(function(n,e){return _a(n,e,ya.aborted,'Navigation aborted from "'+n.fullPath+'" to "'+e.fullPath+'" via a navigation guard.')}(o,n))):Ta(e)?(r.ensureURL(!0),s(e)):"string"==typeof e||"object"==typeof e&&("string"==typeof e.path||"string"==typeof e.name)?(s(ba(o,n)),"object"==typeof e&&e.replace?r.replace(e):r.push(e)):t(e)}))}catch(n){s(n)}};ka(h,m,(function(){ka(function(n){return $a(n,"beforeRouteEnter",(function(n,e,t,r){return function(n,e,t){return function(r,o,i){return n(r,o,(function(n){"function"==typeof n&&(e.enteredCbs[t]||(e.enteredCbs[t]=[]),e.enteredCbs[t].push(n)),i(n)}))}}(n,t,r)}))}(f).concat(r.router.resolveHooks),m,(function(){if(r.pending!==n)return s(xa(o,n));r.pending=null,e(n),r.router.app&&r.router.app.$nextTick((function(){xi(n)}))}))}))},Ia.prototype.updateRoute=function(n){this.current=n,this.cb&&this.cb(n)},Ia.prototype.setupListeners=function(){},Ia.prototype.teardown=function(){this.listeners.forEach((function(n){n()})),this.listeners=[],this.current=mi,this.pending=null};var za=function(n){function e(e,t){n.call(this,e,t),this._startLocation=Ma(this.base)}return n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e,e.prototype.setupListeners=function(){var n=this;if(!(this.listeners.length>0)){var e=this.router,t=e.options.scrollBehavior,r=ma&&t;r&&this.listeners.push(ia());var o=function(){var t=n.current,o=Ma(n.base);n.current===mi&&o===n._startLocation||n.transitionTo(o,(function(n){r&&aa(e,n,t,!0)}))};window.addEventListener("popstate",o),this.listeners.push((function(){window.removeEventListener("popstate",o)}))}},e.prototype.go=function(n){window.history.go(n)},e.prototype.push=function(n,e,t){var r=this,o=this.current;this.transitionTo(n,(function(n){ga(Ei(r.base+n.fullPath)),aa(r.router,n,o,!1),e&&e(n)}),t)},e.prototype.replace=function(n,e,t){var r=this,o=this.current;this.transitionTo(n,(function(n){va(Ei(r.base+n.fullPath)),aa(r.router,n,o,!1),e&&e(n)}),t)},e.prototype.ensureURL=function(n){if(Ma(this.base)!==this.current.fullPath){var e=Ei(this.base+this.current.fullPath);n?ga(e):va(e)}},e.prototype.getCurrentLocation=function(){return Ma(this.base)},e}(Ia);function Ma(n){var e=window.location.pathname,t=e.toLowerCase(),r=n.toLowerCase();return!n||t!==r&&0!==t.indexOf(Ei(r+"/"))||(e=e.slice(n.length)),(e||"/")+window.location.search+window.location.hash}var Da=function(n){function e(e,t,r){n.call(this,e,t),r&&function(n){var e=Ma(n);if(!/^\/#/.test(e))return window.location.replace(Ei(n+"/#"+e)),!0}(this.base)||La()}return n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e,e.prototype.setupListeners=function(){var n=this;if(!(this.listeners.length>0)){var e=this.router.options.scrollBehavior,t=ma&&e;t&&this.listeners.push(ia());var r=function(){var e=n.current;La()&&n.transitionTo(Na(),(function(r){t&&aa(n.router,r,e,!0),ma||Ba(r.fullPath)}))},o=ma?"popstate":"hashchange";window.addEventListener(o,r),this.listeners.push((function(){window.removeEventListener(o,r)}))}},e.prototype.push=function(n,e,t){var r=this,o=this.current;this.transitionTo(n,(function(n){Ra(n.fullPath),aa(r.router,n,o,!1),e&&e(n)}),t)},e.prototype.replace=function(n,e,t){var r=this,o=this.current;this.transitionTo(n,(function(n){Ba(n.fullPath),aa(r.router,n,o,!1),e&&e(n)}),t)},e.prototype.go=function(n){window.history.go(n)},e.prototype.ensureURL=function(n){var e=this.current.fullPath;Na()!==e&&(n?Ra(e):Ba(e))},e.prototype.getCurrentLocation=function(){return Na()},e}(Ia);function La(){var n=Na();return"/"===n.charAt(0)||(Ba("/"+n),!1)}function Na(){var n=window.location.href,e=n.indexOf("#");return e<0?"":n=n.slice(e+1)}function Ua(n){var e=window.location.href,t=e.indexOf("#");return(t>=0?e.slice(0,t):e)+"#"+n}function Ra(n){ma?ga(Ua(n)):window.location.hash=n}function Ba(n){ma?va(Ua(n)):window.location.replace(Ua(n))}var qa=function(n){function e(e,t){n.call(this,e,t),this.stack=[],this.index=-1}return n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e,e.prototype.push=function(n,e,t){var r=this;this.transitionTo(n,(function(n){r.stack=r.stack.slice(0,r.index+1).concat(n),r.index++,e&&e(n)}),t)},e.prototype.replace=function(n,e,t){var r=this;this.transitionTo(n,(function(n){r.stack=r.stack.slice(0,r.index).concat(n),e&&e(n)}),t)},e.prototype.go=function(n){var e=this,t=this.index+n;if(!(t<0||t>=this.stack.length)){var r=this.stack[t];this.confirmTransition(r,(function(){var n=e.current;e.index=t,e.updateRoute(r),e.router.afterHooks.forEach((function(e){e&&e(r,n)}))}),(function(n){Ea(n,ya.duplicated)&&(e.index=t)}))}},e.prototype.getCurrentLocation=function(){var n=this.stack[this.stack.length-1];return n?n.fullPath:"/"},e.prototype.ensureURL=function(){},e}(Ia),Fa=function(n){void 0===n&&(n={}),this.app=null,this.apps=[],this.options=n,this.beforeHooks=[],this.resolveHooks=[],this.afterHooks=[],this.matcher=Qi(n.routes||[],this);var e=n.mode||"hash";switch(this.fallback="history"===e&&!ma&&!1!==n.fallback,this.fallback&&(e="hash"),Wi||(e="abstract"),this.mode=e,e){case"history":this.history=new za(this,n.base);break;case"hash":this.history=new Da(this,n.base,this.fallback);break;case"abstract":this.history=new qa(this,n.base);break;default:0}},Ha={currentRoute:{configurable:!0}};Fa.prototype.match=function(n,e,t){return this.matcher.match(n,e,t)},Ha.currentRoute.get=function(){return this.history&&this.history.current},Fa.prototype.init=function(n){var e=this;if(this.apps.push(n),n.$once("hook:destroyed",(function(){var t=e.apps.indexOf(n);t>-1&&e.apps.splice(t,1),e.app===n&&(e.app=e.apps[0]||null),e.app||e.history.teardown()})),!this.app){this.app=n;var t=this.history;if(t instanceof za||t instanceof Da){var r=function(n){t.setupListeners(),function(n){var r=t.current,o=e.options.scrollBehavior;ma&&o&&"fullPath"in n&&aa(e,n,r,!1)}(n)};t.transitionTo(t.getCurrentLocation(),r,r)}t.listen((function(n){e.apps.forEach((function(e){e._route=n}))}))}},Fa.prototype.beforeEach=function(n){return Ya(this.beforeHooks,n)},Fa.prototype.beforeResolve=function(n){return Ya(this.resolveHooks,n)},Fa.prototype.afterEach=function(n){return Ya(this.afterHooks,n)},Fa.prototype.onReady=function(n,e){this.history.onReady(n,e)},Fa.prototype.onError=function(n){this.history.onError(n)},Fa.prototype.push=function(n,e,t){var r=this;if(!e&&!t&&"undefined"!=typeof Promise)return new Promise((function(e,t){r.history.push(n,e,t)}));this.history.push(n,e,t)},Fa.prototype.replace=function(n,e,t){var r=this;if(!e&&!t&&"undefined"!=typeof Promise)return new Promise((function(e,t){r.history.replace(n,e,t)}));this.history.replace(n,e,t)},Fa.prototype.go=function(n){this.history.go(n)},Fa.prototype.back=function(){this.go(-1)},Fa.prototype.forward=function(){this.go(1)},Fa.prototype.getMatchedComponents=function(n){var e=n?n.matched?n:this.resolve(n).route:this.currentRoute;return e?[].concat.apply([],e.matched.map((function(n){return Object.keys(n.components).map((function(e){return n.components[e]}))}))):[]},Fa.prototype.resolve=function(n,e,t){var r=Fi(n,e=e||this.history.current,t,this),o=this.match(r,e),i=o.redirectedFrom||o.fullPath;return{location:r,route:o,href:function(n,e,t){var r="hash"===t?"#"+e:e;return n?Ei(n+"/"+r):r}(this.history.base,i,this.mode),normalizedTo:r,resolved:o}},Fa.prototype.getRoutes=function(){return this.matcher.getRoutes()},Fa.prototype.addRoute=function(n,e){this.matcher.addRoute(n,e),this.history.current!==mi&&this.history.transitionTo(this.history.getCurrentLocation())},Fa.prototype.addRoutes=function(n){this.matcher.addRoutes(n),this.history.current!==mi&&this.history.transitionTo(this.history.getCurrentLocation())},Object.defineProperties(Fa.prototype,Ha);var Va=Fa;function Ya(n,e){return n.push(e),function(){var t=n.indexOf(e);t>-1&&n.splice(t,1)}}Fa.install=function n(e){if(!n.installed||Hi!==e){n.installed=!0,Hi=e;var t=function(n){return void 0!==n},r=function(n,e){var r=n.$options._parentVnode;t(r)&&t(r=r.data)&&t(r=r.registerRouteInstance)&&r(n,e)};e.mixin({beforeCreate:function(){t(this.$options.router)?(this._routerRoot=this,this._router=this.$options.router,this._router.init(this),e.util.defineReactive(this,"_route",this._router.history.current)):this._routerRoot=this.$parent&&this.$parent._routerRoot||this,r(this,this)},destroyed:function(){r(this)}}),Object.defineProperty(e.prototype,"$router",{get:function(){return this._routerRoot._router}}),Object.defineProperty(e.prototype,"$route",{get:function(){return this._routerRoot._route}}),e.component("RouterView",_i),e.component("RouterLink",Yi);var o=e.config.optionMergeStrategies;o.beforeRouteEnter=o.beforeRouteLeave=o.beforeRouteUpdate=o.created}},Fa.version="3.6.5",Fa.isNavigationFailure=Ea,Fa.NavigationFailureType=ya,Fa.START_LOCATION=mi,Wi&&window.Vue&&window.Vue.use(Fa);t(107);t(26),t(113);var Ga={NotFound:()=>Promise.all([t.e(0),t.e(7)]).then(t.bind(null,375)),Layout:()=>Promise.all([t.e(0),t.e(2)]).then(t.bind(null,373))},Wa={"v-1ab3550e":()=>t.e(9).then(t.bind(null,378)),"v-0d90132a":()=>t.e(11).then(t.bind(null,379)),"v-0411c3fc":()=>t.e(12).then(t.bind(null,380)),"v-f1e45dec":()=>t.e(10).then(t.bind(null,381)),"v-dd18d2ac":()=>t.e(13).then(t.bind(null,382)),"v-943203dc":()=>t.e(16).then(t.bind(null,383)),"v-3c7c1402":()=>t.e(14).then(t.bind(null,384)),"v-1eb4dd3d":()=>t.e(17).then(t.bind(null,385)),"v-7f1df8b5":()=>t.e(15).then(t.bind(null,386)),"v-edb78c74":()=>t.e(18).then(t.bind(null,387)),"v-b40f85f4":()=>t.e(20).then(t.bind(null,388)),"v-7e0ed6b0":()=>t.e(19).then(t.bind(null,389)),"v-4d3d5367":()=>t.e(21).then(t.bind(null,390)),"v-79192e6c":()=>t.e(22).then(t.bind(null,391)),"v-687254ec":()=>t.e(23).then(t.bind(null,392)),"v-62a65d27":()=>t.e(24).then(t.bind(null,393)),"v-44c7acca":()=>t.e(25).then(t.bind(null,394)),"v-769530ce":()=>t.e(26).then(t.bind(null,395)),"v-694ce7b5":()=>t.e(27).then(t.bind(null,396)),"v-ee6a7e4c":()=>t.e(29).then(t.bind(null,397)),"v-5d5908ce":()=>t.e(30).then(t.bind(null,398)),"v-285c42a3":()=>t.e(31).then(t.bind(null,399)),"v-0fdd426f":()=>t.e(28).then(t.bind(null,400)),"v-44f6d96e":()=>t.e(32).then(t.bind(null,401)),"v-5f63254c":()=>t.e(33).then(t.bind(null,402))};function Ka(n){const e=Object.create(null);return function(t){return e[t]||(e[t]=n(t))}}const Za=/-(\w)/g,Qa=Ka(n=>n.replace(Za,(n,e)=>e?e.toUpperCase():"")),Xa=/\B([A-Z])/g,Ja=Ka(n=>n.replace(Xa,"-$1").toLowerCase()),ns=Ka(n=>n.charAt(0).toUpperCase()+n.slice(1));function es(n,e){if(!e)return;if(n(e))return n(e);return e.includes("-")?n(ns(Qa(e))):n(ns(e))||n(Ja(e))}const ts=Object.assign({},Ga,Wa),rs=n=>ts[n],os=n=>Wa[n],is=n=>Ga[n],as=n=>Gt.component(n);function ss(n){return es(os,n)}function cs(n){return es(is,n)}function ls(n){return es(rs,n)}function us(n){return es(as,n)}function ps(...n){return Promise.all(n.filter(n=>n).map(async n=>{if(!us(n)&&ls(n)){const e=await ls(n)();Gt.component(n,e.default)}}))}function ds(n,e){"undefined"!=typeof window&&window.__VUEPRESS__&&(window.__VUEPRESS__[n]=e)}var fs=t(91),hs=t.n(fs),ms=t(92),gs=t.n(ms),vs={created(){if(this.siteMeta=this.$site.headTags.filter(([n])=>"meta"===n).map(([n,e])=>e),this.$ssrContext){const e=this.getMergedMetaTags();this.$ssrContext.title=this.$title,this.$ssrContext.lang=this.$lang,this.$ssrContext.pageMeta=(n=e)?n.map(n=>{let e="<meta";return Object.keys(n).forEach(t=>{e+=` ${t}="${gs()(n[t])}"`}),e+">"}).join("\n    "):"",this.$ssrContext.canonicalLink=bs(this.$canonicalUrl)}var n},mounted(){this.currentMetaTags=[...document.querySelectorAll("meta")],this.updateMeta(),this.updateCanonicalLink()},methods:{updateMeta(){document.title=this.$title,document.documentElement.lang=this.$lang;const n=this.getMergedMetaTags();this.currentMetaTags=xs(n,this.currentMetaTags)},getMergedMetaTags(){const n=this.$page.frontmatter.meta||[];return hs()([{name:"description",content:this.$description}],n,this.siteMeta,_s)},updateCanonicalLink(){ys(),this.$canonicalUrl&&document.head.insertAdjacentHTML("beforeend",bs(this.$canonicalUrl))}},watch:{$page(){this.updateMeta(),this.updateCanonicalLink()}},beforeDestroy(){xs(null,this.currentMetaTags),ys()}};function ys(){const n=document.querySelector("link[rel='canonical']");n&&n.remove()}function bs(n=""){return n?`<link href="${n}" rel="canonical" />`:""}function xs(n,e){if(e&&[...e].filter(n=>n.parentNode===document.head).forEach(n=>document.head.removeChild(n)),n)return n.map(n=>{const e=document.createElement("meta");return Object.keys(n).forEach(t=>{e.setAttribute(t,n[t])}),document.head.appendChild(e),e})}function _s(n){for(const e of["name","property","itemprop"])if(n.hasOwnProperty(e))return n[e]+e;return JSON.stringify(n)}var ws=t(59),Ts={mounted(){window.addEventListener("scroll",this.onScroll)},methods:{onScroll:t.n(ws)()((function(){this.setActiveHash()}),300),setActiveHash(){const n=[].slice.call(document.querySelectorAll(".sidebar-link")),e=[].slice.call(document.querySelectorAll(".header-anchor")).filter(e=>n.some(n=>n.hash===e.hash)),t=Math.max(window.pageYOffset,document.documentElement.scrollTop,document.body.scrollTop),r=Math.max(document.documentElement.scrollHeight,document.body.scrollHeight),o=window.innerHeight+t;for(let n=0;n<e.length;n++){const i=e[n],a=e[n+1],s=0===n&&0===t||t>=i.parentElement.offsetTop+10&&(!a||t<a.parentElement.offsetTop-10),c=decodeURIComponent(this.$route.hash);if(s&&c!==decodeURIComponent(i.hash)){const t=i;if(o===r)for(let t=n+1;t<e.length;t++)if(c===decodeURIComponent(e[t].hash))return;return this.$vuepress.$set("disableScrollBehavior",!0),void this.$router.replace(decodeURIComponent(t.hash),()=>{this.$nextTick(()=>{this.$vuepress.$set("disableScrollBehavior",!1)})})}}}},beforeDestroy(){window.removeEventListener("scroll",this.onScroll)}},Es=t(23),ks=t.n(Es),Cs={mounted(){ks.a.configure({showSpinner:!1}),this.$router.beforeEach((n,e,t)=>{n.path===e.path||Gt.component(n.name)||ks.a.start(),t()}),this.$router.afterEach(()=>{ks.a.done(),this.isSidebarOpen=!1})}};t(239),t(240);class Ss{constructor(){this.containerEl=document.getElementById("message-container"),this.containerEl||(this.containerEl=document.createElement("div"),this.containerEl.id="message-container",document.body.appendChild(this.containerEl))}show({text:n="",duration:e=3e3}){let t=document.createElement("div");t.className="message move-in",t.innerHTML=`\n      <i style="fill: #06a35a;font-size: 14px;display:inline-flex;align-items: center;">\n        <svg style="fill: #06a35a;font-size: 14px;" t="1572421810237" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2323" width="16" height="16"><path d="M822.811993 824.617989c-83.075838 81.99224-188.546032 124.613757-316.049383 127.86455-122.085362-3.250794-223.943563-45.87231-305.935802-127.86455s-124.613757-184.21164-127.86455-305.935802c3.250794-127.503351 45.87231-232.973545 127.86455-316.049383 81.99224-83.075838 184.21164-126.058554 305.935802-129.309347 127.503351 3.250794 232.973545 46.23351 316.049383 129.309347 83.075838 83.075838 126.058554 188.546032 129.309347 316.049383C949.231746 640.406349 905.887831 742.62575 822.811993 824.617989zM432.716755 684.111464c3.973192 3.973192 8.307584 5.779189 13.364374 6.140388 5.05679 0.361199 9.752381-1.444797 13.364374-5.417989l292.571429-287.514638c3.973192-3.973192 5.779189-8.307584 5.779189-13.364374 0-5.05679-1.805996-9.752381-5.779189-13.364374l1.805996 1.805996c-3.973192-3.973192-8.668783-5.779189-14.086772-6.140388-5.417989-0.361199-10.47478 1.444797-14.809171 5.417989l-264.397884 220.33157c-3.973192 3.250794-8.668783 4.695591-14.447972 4.695591-5.779189 0-10.835979-1.444797-15.53157-3.973192l-94.273016-72.962257c-4.334392-3.250794-9.391182-4.334392-14.447972-3.973192s-9.391182 3.250794-12.641975 7.585185l-2.889594 3.973192c-3.250794 4.334392-4.334392 9.391182-3.973192 14.809171 0.722399 5.417989 2.528395 10.11358 5.779189 14.086772L432.716755 684.111464z" p-id="2324"></path></svg>\n      </i>\n      <div class="text">${n}</div>\n    `,this.containerEl.appendChild(t),e>0&&setTimeout(()=>{this.close(t)},e)}close(n){n.className=n.className.replace("move-in",""),n.className+="move-out",n.addEventListener("animationend",()=>{n.remove()})}}var Os={mounted(){!!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||this.updateCopy()},updated(){!!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||this.updateCopy()},methods:{updateCopy(){setTimeout(()=>{(['div[class*="language-"]']instanceof Array||Array.isArray(['div[class*="language-"]']))&&['div[class*="language-"]'].forEach(n=>{document.querySelectorAll(n).forEach(this.generateCopyButton)})},1e3)},generateCopyButton(n){if(n.classList.contains("codecopy-enabled"))return;const e=document.createElement("i");e.className="code-copy",e.innerHTML='<svg  style="color:#aaa;font-size:14px" t="1572422231464" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3201" width="14" height="14"><path d="M866.461538 39.384615H354.461538c-43.323077 0-78.769231 35.446154-78.76923 78.769231v39.384616h472.615384c43.323077 0 78.769231 35.446154 78.769231 78.76923v551.384616h39.384615c43.323077 0 78.769231-35.446154 78.769231-78.769231V118.153846c0-43.323077-35.446154-78.769231-78.769231-78.769231z m-118.153846 275.692308c0-43.323077-35.446154-78.769231-78.76923-78.769231H157.538462c-43.323077 0-78.769231 35.446154-78.769231 78.769231v590.769231c0 43.323077 35.446154 78.769231 78.769231 78.769231h512c43.323077 0 78.769231-35.446154 78.76923-78.769231V315.076923z m-354.461538 137.846154c0 11.815385-7.876923 19.692308-19.692308 19.692308h-157.538461c-11.815385 0-19.692308-7.876923-19.692308-19.692308v-39.384615c0-11.815385 7.876923-19.692308 19.692308-19.692308h157.538461c11.815385 0 19.692308 7.876923 19.692308 19.692308v39.384615z m157.538461 315.076923c0 11.815385-7.876923 19.692308-19.692307 19.692308H216.615385c-11.815385 0-19.692308-7.876923-19.692308-19.692308v-39.384615c0-11.815385 7.876923-19.692308 19.692308-19.692308h315.076923c11.815385 0 19.692308 7.876923 19.692307 19.692308v39.384615z m78.769231-157.538462c0 11.815385-7.876923 19.692308-19.692308 19.692308H216.615385c-11.815385 0-19.692308-7.876923-19.692308-19.692308v-39.384615c0-11.815385 7.876923-19.692308 19.692308-19.692308h393.846153c11.815385 0 19.692308 7.876923 19.692308 19.692308v39.384615z" p-id="3202"></path></svg>',e.title="Copy to clipboard",e.addEventListener("click",()=>{this.copyToClipboard(n.innerText)}),n.appendChild(e),n.classList.add("codecopy-enabled")},copyToClipboard(n){const e=document.createElement("textarea");e.value=n,e.setAttribute("readonly",""),e.style.position="absolute",e.style.left="-9999px",document.body.appendChild(e);const t=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);e.select(),document.execCommand("copy");(new Ss).show({text:"复制成功",duration:1e3}),document.body.removeChild(e),t&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(t))}}},js="auto",As="zoom-in",Is="zoom-out",$s="grab",Ps="move";function zs(n,e,t){var r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o={passive:!1};r?n.addEventListener(e,t,o):n.removeEventListener(e,t,o)}function Ms(n,e){if(n){var t=new Image;t.onload=function(){e&&e(t)},t.src=n}}function Ds(n){return n.dataset.original?n.dataset.original:"A"===n.parentNode.tagName?n.parentNode.getAttribute("href"):null}function Ls(n,e,t){!function(n){var e=Ns,t=Us;if(n.transition){var r=n.transition;delete n.transition,n[e]=r}if(n.transform){var o=n.transform;delete n.transform,n[t]=o}}(e);var r=n.style,o={};for(var i in e)t&&(o[i]=r[i]||""),r[i]=e[i];return o}var Ns="transition",Us="transform",Rs="transform",Bs="transitionend";var qs=function(){},Fs={enableGrab:!0,preloadImage:!1,closeOnWindowResize:!0,transitionDuration:.4,transitionTimingFunction:"cubic-bezier(0.4, 0, 0, 1)",bgColor:"rgb(255, 255, 255)",bgOpacity:1,scaleBase:1,scaleExtra:.5,scrollThreshold:40,zIndex:998,customSize:null,onOpen:qs,onClose:qs,onGrab:qs,onMove:qs,onRelease:qs,onBeforeOpen:qs,onBeforeClose:qs,onBeforeGrab:qs,onBeforeRelease:qs,onImageLoading:qs,onImageLoaded:qs},Hs={init:function(n){var e,t;e=this,t=n,Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach((function(n){e[n]=e[n].bind(t)}))},click:function(n){if(n.preventDefault(),Ys(n))return window.open(this.target.srcOriginal||n.currentTarget.src,"_blank");this.shown?this.released?this.close():this.release():this.open(n.currentTarget)},scroll:function(){var n=document.documentElement||document.body.parentNode||document.body,e=window.pageXOffset||n.scrollLeft,t=window.pageYOffset||n.scrollTop;null===this.lastScrollPosition&&(this.lastScrollPosition={x:e,y:t});var r=this.lastScrollPosition.x-e,o=this.lastScrollPosition.y-t,i=this.options.scrollThreshold;(Math.abs(o)>=i||Math.abs(r)>=i)&&(this.lastScrollPosition=null,this.close())},keydown:function(n){(function(n){return"Escape"===(n.key||n.code)||27===n.keyCode})(n)&&(this.released?this.close():this.release(this.close))},mousedown:function(n){if(Vs(n)&&!Ys(n)){n.preventDefault();var e=n.clientX,t=n.clientY;this.pressTimer=setTimeout(function(){this.grab(e,t)}.bind(this),200)}},mousemove:function(n){this.released||this.move(n.clientX,n.clientY)},mouseup:function(n){Vs(n)&&!Ys(n)&&(clearTimeout(this.pressTimer),this.released?this.close():this.release())},touchstart:function(n){n.preventDefault();var e=n.touches[0],t=e.clientX,r=e.clientY;this.pressTimer=setTimeout(function(){this.grab(t,r)}.bind(this),200)},touchmove:function(n){if(!this.released){var e=n.touches[0],t=e.clientX,r=e.clientY;this.move(t,r)}},touchend:function(n){(function(n){n.targetTouches.length})(n)||(clearTimeout(this.pressTimer),this.released?this.close():this.release())},clickOverlay:function(){this.close()},resizeWindow:function(){this.close()}};function Vs(n){return 0===n.button}function Ys(n){return n.metaKey||n.ctrlKey}var Gs={init:function(n){this.el=document.createElement("div"),this.instance=n,this.parent=document.body,Ls(this.el,{position:"fixed",top:0,left:0,right:0,bottom:0,opacity:0}),this.updateStyle(n.options),zs(this.el,"click",n.handler.clickOverlay.bind(n))},updateStyle:function(n){Ls(this.el,{zIndex:n.zIndex,backgroundColor:n.bgColor,transition:"opacity\n        "+n.transitionDuration+"s\n        "+n.transitionTimingFunction})},insert:function(){this.parent.appendChild(this.el)},remove:function(){this.parent.removeChild(this.el)},fadeIn:function(){this.el.offsetWidth,this.el.style.opacity=this.instance.options.bgOpacity},fadeOut:function(){this.el.style.opacity=0}},Ws="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Ks=function(){function n(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,r.key,r)}}return function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}}(),Zs=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Qs={init:function(n,e){this.el=n,this.instance=e,this.srcThumbnail=this.el.getAttribute("src"),this.srcset=this.el.getAttribute("srcset"),this.srcOriginal=Ds(this.el),this.rect=this.el.getBoundingClientRect(),this.translate=null,this.scale=null,this.styleOpen=null,this.styleClose=null},zoomIn:function(){var n=this.instance.options,e=n.zIndex,t=n.enableGrab,r=n.transitionDuration,o=n.transitionTimingFunction;this.translate=this.calculateTranslate(),this.scale=this.calculateScale(),this.styleOpen={position:"relative",zIndex:e+1,cursor:t?$s:Is,transition:Rs+"\n        "+r+"s\n        "+o,transform:"translate3d("+this.translate.x+"px, "+this.translate.y+"px, 0px)\n        scale("+this.scale.x+","+this.scale.y+")",height:this.rect.height+"px",width:this.rect.width+"px"},this.el.offsetWidth,this.styleClose=Ls(this.el,this.styleOpen,!0)},zoomOut:function(){this.el.offsetWidth,Ls(this.el,{transform:"none"})},grab:function(n,e,t){var r=Xs(),o=r.x-n,i=r.y-e;Ls(this.el,{cursor:Ps,transform:"translate3d(\n        "+(this.translate.x+o)+"px, "+(this.translate.y+i)+"px, 0px)\n        scale("+(this.scale.x+t)+","+(this.scale.y+t)+")"})},move:function(n,e,t){var r=Xs(),o=r.x-n,i=r.y-e;Ls(this.el,{transition:Rs,transform:"translate3d(\n        "+(this.translate.x+o)+"px, "+(this.translate.y+i)+"px, 0px)\n        scale("+(this.scale.x+t)+","+(this.scale.y+t)+")"})},restoreCloseStyle:function(){Ls(this.el,this.styleClose)},restoreOpenStyle:function(){Ls(this.el,this.styleOpen)},upgradeSource:function(){if(this.srcOriginal){var n=this.el.parentNode;this.srcset&&this.el.removeAttribute("srcset");var e=this.el.cloneNode(!1);e.setAttribute("src",this.srcOriginal),e.style.position="fixed",e.style.visibility="hidden",n.appendChild(e),setTimeout(function(){this.el.setAttribute("src",this.srcOriginal),n.removeChild(e)}.bind(this),50)}},downgradeSource:function(){this.srcOriginal&&(this.srcset&&this.el.setAttribute("srcset",this.srcset),this.el.setAttribute("src",this.srcThumbnail))},calculateTranslate:function(){var n=Xs(),e=this.rect.left+this.rect.width/2,t=this.rect.top+this.rect.height/2;return{x:n.x-e,y:n.y-t}},calculateScale:function(){var n=this.el.dataset,e=n.zoomingHeight,t=n.zoomingWidth,r=this.instance.options,o=r.customSize,i=r.scaleBase;if(!o&&e&&t)return{x:t/this.rect.width,y:e/this.rect.height};if(o&&"object"===(void 0===o?"undefined":Ws(o)))return{x:o.width/this.rect.width,y:o.height/this.rect.height};var a=this.rect.width/2,s=this.rect.height/2,c=Xs(),l={x:c.x-a,y:c.y-s},u=l.x/a,p=l.y/s,d=i+Math.min(u,p);if(o&&"string"==typeof o){var f=t||this.el.naturalWidth,h=e||this.el.naturalHeight,m=parseFloat(o)*f/(100*this.rect.width),g=parseFloat(o)*h/(100*this.rect.height);if(d>m||d>g)return{x:m,y:g}}return{x:d,y:d}}};function Xs(){var n=document.documentElement;return{x:Math.min(n.clientWidth,window.innerWidth)/2,y:Math.min(n.clientHeight,window.innerHeight)/2}}function Js(n,e,t){["mousedown","mousemove","mouseup","touchstart","touchmove","touchend"].forEach((function(r){zs(n,r,e[r],t)}))}var nc=function(){function n(e){!function(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),this.target=Object.create(Qs),this.overlay=Object.create(Gs),this.handler=Object.create(Hs),this.body=document.body,this.shown=!1,this.lock=!1,this.released=!0,this.lastScrollPosition=null,this.pressTimer=null,this.options=Zs({},Fs,e),this.overlay.init(this),this.handler.init(this)}return Ks(n,[{key:"listen",value:function(n){if("string"==typeof n)for(var e=document.querySelectorAll(n),t=e.length;t--;)this.listen(e[t]);else"IMG"===n.tagName&&(n.style.cursor=As,zs(n,"click",this.handler.click),this.options.preloadImage&&Ms(Ds(n)));return this}},{key:"config",value:function(n){return n?(Zs(this.options,n),this.overlay.updateStyle(this.options),this):this.options}},{key:"open",value:function(n){var e=this,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options.onOpen;if(!this.shown&&!this.lock){var r="string"==typeof n?document.querySelector(n):n;if("IMG"===r.tagName){if(this.options.onBeforeOpen(r),this.target.init(r,this),!this.options.preloadImage){var o=this.target.srcOriginal;null!=o&&(this.options.onImageLoading(r),Ms(o,this.options.onImageLoaded))}this.shown=!0,this.lock=!0,this.target.zoomIn(),this.overlay.insert(),this.overlay.fadeIn(),zs(document,"scroll",this.handler.scroll),zs(document,"keydown",this.handler.keydown),this.options.closeOnWindowResize&&zs(window,"resize",this.handler.resizeWindow);var i=function n(){zs(r,Bs,n,!1),e.lock=!1,e.target.upgradeSource(),e.options.enableGrab&&Js(document,e.handler,!0),t(r)};return zs(r,Bs,i),this}}}},{key:"close",value:function(){var n=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.onClose;if(this.shown&&!this.lock){var t=this.target.el;this.options.onBeforeClose(t),this.lock=!0,this.body.style.cursor=js,this.overlay.fadeOut(),this.target.zoomOut(),zs(document,"scroll",this.handler.scroll,!1),zs(document,"keydown",this.handler.keydown,!1),this.options.closeOnWindowResize&&zs(window,"resize",this.handler.resizeWindow,!1);var r=function r(){zs(t,Bs,r,!1),n.shown=!1,n.lock=!1,n.target.downgradeSource(),n.options.enableGrab&&Js(document,n.handler,!1),n.target.restoreCloseStyle(),n.overlay.remove(),e(t)};return zs(t,Bs,r),this}}},{key:"grab",value:function(n,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.options.scaleExtra,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.options.onGrab;if(this.shown&&!this.lock){var o=this.target.el;this.options.onBeforeGrab(o),this.released=!1,this.target.grab(n,e,t);var i=function n(){zs(o,Bs,n,!1),r(o)};return zs(o,Bs,i),this}}},{key:"move",value:function(n,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.options.scaleExtra,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.options.onMove;if(this.shown&&!this.lock){this.released=!1,this.body.style.cursor=Ps,this.target.move(n,e,t);var o=this.target.el,i=function n(){zs(o,Bs,n,!1),r(o)};return zs(o,Bs,i),this}}},{key:"release",value:function(){var n=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.onRelease;if(this.shown&&!this.lock){var t=this.target.el;this.options.onBeforeRelease(t),this.lock=!0,this.body.style.cursor=js,this.target.restoreOpenStyle();var r=function r(){zs(t,Bs,r,!1),n.lock=!1,n.released=!0,e(t)};return zs(t,Bs,r),this}}}]),n}();const ec=JSON.parse('{"bgColor":"rgba(0,0,0,0.6)"}'),tc=Number("500");class rc{constructor(){this.instance=new nc(ec)}update(n=".theme-vdoing-content img:not(.no-zoom)"){"undefined"!=typeof window&&this.instance.listen(n)}updateDelay(n=".theme-vdoing-content img:not(.no-zoom)",e=tc){setTimeout(()=>this.update(n),e)}}var oc=[vs,Ts,Cs,Os,{watch:{"$page.path"(){void 0!==this.$vuepress.zooming&&this.$vuepress.zooming.updateDelay()}},mounted(){this.$vuepress.zooming=new rc,this.$vuepress.zooming.updateDelay()}}],ic={name:"GlobalLayout",computed:{layout(){const n=this.getLayout();return ds("layout",n),Gt.component(n)}},methods:{getLayout(){if(this.$page.path){const n=this.$page.frontmatter.layout;return n&&(this.$vuepress.getLayoutAsyncComponent(n)||this.$vuepress.getVueComponent(n))?n:"Layout"}return"NotFound"}}},ac=t(7),sc=Object(ac.a)(ic,(function(){return(0,this._self._c)(this.layout,{tag:"component"})}),[],!1,null,null,null).exports;!function(n,e,t){switch(e){case"components":n[e]||(n[e]={}),Object.assign(n[e],t);break;case"mixins":n[e]||(n[e]=[]),n[e].push(...t);break;default:throw new Error("Unknown option name.")}}(sc,"mixins",oc);const cc=[{name:"v-1ab3550e",path:"/pages/index/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-1ab3550e").then(t)}},{path:"/pages/index/index.html",redirect:"/pages/index/"},{path:"/00.指南/00.概述.html",redirect:"/pages/index/"},{name:"v-0d90132a",path:"/pages/request/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-0d90132a").then(t)}},{path:"/pages/request/index.html",redirect:"/pages/request/"},{path:"/00.指南/01.工具库/02.Request.html",redirect:"/pages/request/"},{name:"v-0411c3fc",path:"/pages/cache/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-0411c3fc").then(t)}},{path:"/pages/cache/index.html",redirect:"/pages/cache/"},{path:"/00.指南/01.工具库/03.缓存.html",redirect:"/pages/cache/"},{name:"v-f1e45dec",path:"/pages/token/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-f1e45dec").then(t)}},{path:"/pages/token/index.html",redirect:"/pages/token/"},{path:"/00.指南/01.工具库/01.Token.html",redirect:"/pages/token/"},{name:"v-dd18d2ac",path:"/pages/dcc8f2/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-dd18d2ac").then(t)}},{path:"/pages/dcc8f2/index.html",redirect:"/pages/dcc8f2/"},{path:"/00.指南/01.工具库/04.SM3.html",redirect:"/pages/dcc8f2/"},{name:"v-943203dc",path:"/pages/is-type/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-943203dc").then(t)}},{path:"/pages/is-type/index.html",redirect:"/pages/is-type/"},{path:"/00.指南/01.工具库/07.类型判断.html",redirect:"/pages/is-type/"},{name:"v-3c7c1402",path:"/pages/date/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-3c7c1402").then(t)}},{path:"/pages/date/index.html",redirect:"/pages/date/"},{path:"/00.指南/01.工具库/05.日期.html",redirect:"/pages/date/"},{name:"v-1eb4dd3d",path:"/pages/number/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-1eb4dd3d").then(t)}},{path:"/pages/number/index.html",redirect:"/pages/number/"},{path:"/00.指南/01.工具库/08.数字.html",redirect:"/pages/number/"},{name:"v-7f1df8b5",path:"/pages/enum/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-7f1df8b5").then(t)}},{path:"/pages/enum/index.html",redirect:"/pages/enum/"},{path:"/00.指南/01.工具库/06.枚举.html",redirect:"/pages/enum/"},{name:"v-edb78c74",path:"/pages/b93470/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-edb78c74").then(t)}},{path:"/pages/b93470/index.html",redirect:"/pages/b93470/"},{path:"/00.指南/01.工具库/09.半年.html",redirect:"/pages/b93470/"},{name:"v-b40f85f4",path:"/pages/57e307/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-b40f85f4").then(t)}},{path:"/pages/57e307/index.html",redirect:"/pages/57e307/"},{path:"/00.指南/01.工具库/11.UUID.html",redirect:"/pages/57e307/"},{name:"v-7e0ed6b0",path:"/pages/quarter/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-7e0ed6b0").then(t)}},{path:"/pages/quarter/index.html",redirect:"/pages/quarter/"},{path:"/00.指南/01.工具库/10.季度.html",redirect:"/pages/quarter/"},{name:"v-4d3d5367",path:"/pages/mime-type/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-4d3d5367").then(t)}},{path:"/pages/mime-type/index.html",redirect:"/pages/mime-type/"},{path:"/00.指南/01.工具库/12.MIME types.html",redirect:"/pages/mime-type/"},{name:"v-79192e6c",path:"/pages/event/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-79192e6c").then(t)}},{path:"/pages/event/index.html",redirect:"/pages/event/"},{path:"/00.指南/01.工具库/13.Event.html",redirect:"/pages/event/"},{name:"v-687254ec",path:"/pages/validate/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-687254ec").then(t)}},{path:"/pages/validate/index.html",redirect:"/pages/validate/"},{path:"/00.指南/01.工具库/14.校验.html",redirect:"/pages/validate/"},{name:"v-62a65d27",path:"/pages/table/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-62a65d27").then(t)}},{path:"/pages/table/index.html",redirect:"/pages/table/"},{path:"/00.指南/01.工具库/15.表格.html",redirect:"/pages/table/"},{name:"v-44c7acca",path:"/pages/excel/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-44c7acca").then(t)}},{path:"/pages/excel/index.html",redirect:"/pages/excel/"},{path:"/00.指南/01.工具库/16.Excel.html",redirect:"/pages/excel/"},{name:"v-769530ce",path:"/pages/31f744/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-769530ce").then(t)}},{path:"/pages/31f744/index.html",redirect:"/pages/31f744/"},{path:"/00.指南/01.工具库/17.blob.html",redirect:"/pages/31f744/"},{name:"v-694ce7b5",path:"/pages/7296bf/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-694ce7b5").then(t)}},{path:"/pages/7296bf/index.html",redirect:"/pages/7296bf/"},{path:"/00.指南/02.接口/01.平台接口.html",redirect:"/pages/7296bf/"},{name:"v-ee6a7e4c",path:"/pages/config/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-ee6a7e4c").then(t)}},{path:"/pages/config/index.html",redirect:"/pages/config/"},{path:"/00.指南/03.config/01.config.html",redirect:"/pages/config/"},{name:"v-5d5908ce",path:"/pages/plugin-sfc-demo/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-5d5908ce").then(t)}},{path:"/pages/plugin-sfc-demo/index.html",redirect:"/pages/plugin-sfc-demo/"},{path:"/98.插件测试/01.sfc-demo.html",redirect:"/pages/plugin-sfc-demo/"},{name:"v-285c42a3",path:"/pages/release/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-285c42a3").then(t)}},{path:"/pages/release/index.html",redirect:"/pages/release/"},{path:"/99.更新日志/01.release.html",redirect:"/pages/release/"},{name:"v-0fdd426f",path:"/pages/d7f021/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-0fdd426f").then(t)}},{path:"/pages/d7f021/index.html",redirect:"/pages/d7f021/"},{path:"/00.指南/02.接口/02.业务接口.html",redirect:"/pages/d7f021/"},{name:"v-44f6d96e",path:"/blog/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-44f6d96e").then(t)}},{path:"/blog/index.html",redirect:"/blog/"},{path:"/@pages/archivesPage.html",redirect:"/blog/"},{name:"v-5f63254c",path:"/",component:sc,beforeEnter:(n,e,t)=>{ps("Layout","v-5f63254c").then(t)}},{path:"/index.html",redirect:"/"},{path:"*",component:sc}],lc={title:"@indfnd/utils",description:"工业营销一体化前端工具库",base:"/ind-utils-doc/",headTags:[["link",{rel:"icon",href:"/ind-utils-doc/img/favicon.ico"}],["meta",{name:"theme-color",content:"#11a8cd"}]],pages:[{title:"概述",frontmatter:{title:"概述",date:"2024-01-04T14:14:56.000Z",permalink:"/pages/index/"},regularPath:"/00.%E6%8C%87%E5%8D%97/00.%E6%A6%82%E8%BF%B0.html",relativePath:"00.指南/00.概述.md",key:"v-1ab3550e",path:"/pages/index/",headers:[{level:2,title:"背景",slug:"背景",normalizedTitle:"背景",charIndex:2},{level:2,title:"提取到工具库的原则",slug:"提取到工具库的原则",normalizedTitle:"提取到工具库的原则",charIndex:211},{level:2,title:"工程结构",slug:"工程结构",normalizedTitle:"工程结构",charIndex:275}],headersStr:"背景 提取到工具库的原则 工程结构",content:'# 背景\n\n 1. 经过之前多个项目的惨痛教训，随着业务的增加，前端工程越来越臃肿，打包越来越慢。由此前端拆分成了刚需，随之而来的就是将各前端业务应用共用的工具方法提取出来，保证各应用的统一。\n 2. 在历史的项目中，由于缺少统一的工具库，起相同作用的方法在多处出现，不便于后续的维护，且逻辑较复杂的，很容易重复的出现一样的bug。\n\n无论是从业务角度、质量角度、产品化角度来看，都需要创建属于工业的前端工具库。\n\n\n# 提取到工具库的原则\n\n 1. 多个应用都需要用到\n 2. 纯 js 或 ts 实现，不依赖于框架（Vue2、Vue3）\n\n\n# 工程结构\n\nutils                                  \n├─ src                                 \n│  ├─ api                              \n│  │  └─ ...                           \n│  ├─ config                           \n│  │  ├─ base.config.ts                \n│  │  ├─ dev.config.ts                 \n│  │  ├─ index.ts                      \n│  │  └─ prod.config.ts                \n│  ├─ utils                            \n│  │  └─ ...                           \n│  └─ index.ts                         \n├─ CHANGELOG.md                        \n└─ global.d.ts                         \n\n\n * api 提供登录、枚举等平台相关接口定义，以及公司树、卷烟树等公共业务接口。\n * config 提供一些公共配置\n * utils 提供如日期、表格、导出excel等的公共方法\n * CHANGELOG.md 为 standard-version 根据 git 的 commit-msg 自动生成的版本更新日志\n * global.d.ts 为该工程对全局变量的一些扩展定义，用于其它应用配置 jsconfig.json 或 tsconfig.json 时增加，便于使用时有提示\n   \n   "types": ["@indfnd/utils/global"],\n   ',normalizedContent:'# 背景\n\n 1. 经过之前多个项目的惨痛教训，随着业务的增加，前端工程越来越臃肿，打包越来越慢。由此前端拆分成了刚需，随之而来的就是将各前端业务应用共用的工具方法提取出来，保证各应用的统一。\n 2. 在历史的项目中，由于缺少统一的工具库，起相同作用的方法在多处出现，不便于后续的维护，且逻辑较复杂的，很容易重复的出现一样的bug。\n\n无论是从业务角度、质量角度、产品化角度来看，都需要创建属于工业的前端工具库。\n\n\n# 提取到工具库的原则\n\n 1. 多个应用都需要用到\n 2. 纯 js 或 ts 实现，不依赖于框架（vue2、vue3）\n\n\n# 工程结构\n\nutils                                  \n├─ src                                 \n│  ├─ api                              \n│  │  └─ ...                           \n│  ├─ config                           \n│  │  ├─ base.config.ts                \n│  │  ├─ dev.config.ts                 \n│  │  ├─ index.ts                      \n│  │  └─ prod.config.ts                \n│  ├─ utils                            \n│  │  └─ ...                           \n│  └─ index.ts                         \n├─ changelog.md                        \n└─ global.d.ts                         \n\n\n * api 提供登录、枚举等平台相关接口定义，以及公司树、卷烟树等公共业务接口。\n * config 提供一些公共配置\n * utils 提供如日期、表格、导出excel等的公共方法\n * changelog.md 为 standard-version 根据 git 的 commit-msg 自动生成的版本更新日志\n * global.d.ts 为该工程对全局变量的一些扩展定义，用于其它应用配置 jsconfig.json 或 tsconfig.json 时增加，便于使用时有提示\n   \n   "types": ["@indfnd/utils/global"],\n   ',charsets:{cjk:!0},lastUpdated:"2024-01-24 18:07:31",lastUpdatedTimestamp:1706090851e3},{title:"Request",frontmatter:{title:"Request",date:"2024-01-06T09:29:31.000Z",permalink:"/pages/request"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/02.Request.html",relativePath:"00.指南/01.工具库/02.Request.md",key:"v-0d90132a",path:"/pages/request/",headers:[{level:2,title:"介绍",slug:"介绍",normalizedTitle:"介绍",charIndex:2},{level:3,title:"get请求",slug:"get请求",normalizedTitle:"get请求",charIndex:146},{level:3,title:"post请求 - application/json",slug:"post请求-application-json",normalizedTitle:"post请求 - application/json",charIndex:537},{level:3,title:"post请求 - application/x-www-form-urlencoded",slug:"post请求-application-x-www-form-urlencoded",normalizedTitle:"post请求 - application/x-www-form-urlencoded",charIndex:1019},{level:3,title:"AxiosRequestConfig",slug:"axiosrequestconfig",normalizedTitle:"axiosrequestconfig",charIndex:185},{level:2,title:"请求拦截器",slug:"请求拦截器",normalizedTitle:"请求拦截器",charIndex:3806},{level:2,title:"响应拦截器",slug:"响应拦截器",normalizedTitle:"响应拦截器",charIndex:4147},{level:2,title:"getUrlParams",slug:"geturlparams",normalizedTitle:"geturlparams",charIndex:4620},{level:2,title:"CONTENT_TYPE",slug:"content-type",normalizedTitle:"content_type",charIndex:4770}],headersStr:"介绍 get请求 post请求 - application/json post请求 - application/x-www-form-urlencoded AxiosRequestConfig 请求拦截器 响应拦截器 getUrlParams CONTENT_TYPE",content:"# 介绍\n\n请求工具使用 axios，其具有强大的处理各种请求的能力，且支持配置拦截器，在发送请求前和收到响应后可以进行一定预处理操作。\n\naxios 支持 GET 、POST 、PUT 、DELETE 等方法的请求，我们最常用的是 GET 和 POST ，以下只介绍这两种的用法。\n\n\n# get请求\n\naxios.get(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<any>>\n\n# 样例\n\nimport { axios } from '@indfnd/utils'\nimport config from '@/config'\n\nconst CONTEXT = config.ismAmServerContext\n\n// params 为参数对象，后端使用 @RequestParam 接收\n// 例: { isAgreeUnit: '1', ... }\nexport function listComTreeApi(params) {\n  return axios.get(`${CONTEXT}/tree/com/listComTree`, { params })\n}\n\n\n\n# post请求 - application/json\n\naxios.post(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<any>>\n\npost 默认的 Content-Type 为 application/json ，后端需要从请求体里获取参数，SpringBoot 使用 @RequestBody 接收到一个实体对象或Map对象。\n\n# 样例\n\nimport { axios } from '@indfnd/utils'\nimport config from '@/config'\n\nconst CONTEXT = `${config.ismAmServerContext}/manage/com`\n\n// data = { comCode: '111', ... }\nexport function createComApi(data) {\n  return axios.post(`${CONTEXT}/create`, data)\n}\n\n\n\n# post请求 - application/x-www-form-urlencoded\n\naxios.formPost(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<any>>\n\n有一些场景需要使用 form表单 的方式发送请求（如登录的接口→_→），使用 axios.post 时需要指定请求头并处理数据格式，此场景比较明确，所以将其封装成 axios.formPost ，使用方法和 axios.post 保持一致。\n\n后端获取参数的方式和 GET 一样，使用 @RequestParam 接收。\n\n一些碎碎念\n\n虽然封装了这个方法，但是不建议使用，在一定要用的场景才用，要不然前后端对接时还得对接取数方式。\n\n# 样例\n\nimport { axios } from '@indfnd/utils'\nimport config from '@/config'\n\nconst CONTEXT = `${config.ismAmServerContext}/manage/com`\n\n// data = { comCode: '111', ... }\nexport function createComApi(data) {\n  return axios.post(`${CONTEXT}/create`, data)\n}\n\n\n\n# AxiosRequestConfig\n\naxios 发送请求时的一些配置，支持指定 headers 等，以下只列出部分，详细请查看官方文档\n\n{\n  // `url` 是用于请求的服务器 URL\n  url: '/user',\n\n  // `method` 是创建请求时使用的方法\n  method: 'get', // 默认值\n\n  // `baseURL` 将自动加在 `url` 前面，除非 `url` 是一个绝对 URL。\n  // 它可以通过设置一个 `baseURL` 便于为 axios 实例的方法传递相对 URL\n  baseURL: 'https://some-domain.com/api/',\n\n  // 自定义请求头\n  headers: {'X-Requested-With': 'XMLHttpRequest'},\n\n  // `params` 是与请求一起发送的 URL 参数，会拼接到 URL 后\n  // 必须是一个简单对象或 URLSearchParams 对象\n  params: {\n    ID: 12345\n  },\n\n  // `data` 是作为请求体被发送的数据\n  // 仅适用 'PUT', 'POST', 'DELETE 和 'PATCH' 请求方法\n  // 在没有设置 `transformRequest` 时，则必须是以下类型之一:\n  // - string, plain object, ArrayBuffer, ArrayBufferView, URLSearchParams\n  // - 浏览器专属: FormData, File, Blob\n  // - Node 专属: Stream, Buffer\n  data: {\n    firstName: 'Fred'\n  },\n  \n  // `timeout` 指定请求超时的毫秒数。\n  // 如果请求时间超过 `timeout` 的值，则请求会被中断\n  timeout: 1000, // 默认值是 `0` (永不超时)\n\n  // `withCredentials` 表示跨域请求时是否需要使用凭证\n  withCredentials: false, // default\n\n  // `adapter` 允许自定义处理请求，这使测试更加容易。\n  // 返回一个 promise 并提供一个有效的响应 （参见 lib/adapters/README.md）。\n  adapter: function (config) {\n    /* ... */\n  },\n\n  // `auth` HTTP Basic Auth\n  auth: {\n    username: 'janedoe',\n    password: 's00pers3cret'\n  },\n\n  // `responseType` 表示浏览器将要响应的数据类型\n  // 选项包括: 'arraybuffer', 'document', 'json', 'text', 'stream'\n  // 浏览器专属：'blob'\n  responseType: 'json', // 默认值\n\n  // `responseEncoding` 表示用于解码响应的编码 (Node.js 专属)\n  // 注意：忽略 `responseType` 的值为 'stream'，或者是客户端请求\n  // Note: Ignored for `responseType` of 'stream' or client-side requests\n  responseEncoding: 'utf8', // 默认值\n\n  // `onUploadProgress` 允许为上传处理进度事件\n  // 浏览器专属\n  onUploadProgress: function (progressEvent) {\n    // 处理原生进度事件\n  },\n\n  // `onDownloadProgress` 允许为下载处理进度事件\n  // 浏览器专属\n  onDownloadProgress: function (progressEvent) {\n    // 处理原生进度事件\n  },\n\n  // `validateStatus` 定义了对于给定的 HTTP状态码是 resolve 还是 reject promise。\n  // 如果 `validateStatus` 返回 `true` (或者设置为 `null` 或 `undefined`)，\n  // 则promise 将会 resolved，否则是 rejected。\n  validateStatus: function (status) {\n    return status >= 200 && status < 300; // 默认值\n  },\n\n  // see https://axios-http.com/zh/docs/cancellation\n  cancelToken: new CancelToken(function (cancel) {}),\n}\n\n\n\n# 请求拦截器\n\naxios.interceptors.request.use((config: AxiosRequestConfig) => {}, (error) => {})\n\n拦截器接收两个回调函数，第一个是正常走到拦截逻辑的回调，第二个是中途报错的回调。\n正常的回调会将此次请求的 config 传递过来，可以在回调函数中对其进行处理。\n\n我们在请求拦截器中做了以下事情：\n\n 1. 获取 token ，添加到 headers 里。\n 2. 若 url 参数或 sessionStorage 里有 sso_token 和 sso_id ，将其一起添加到参数里。\n 3. 如果是form表单提交，将 data 进行序列化，转换成 URLSearchParams 格式。\n\n\n# 响应拦截器\n\naxios.interceptors.response.use((response: AxiosResponse) => {}, (error) => {})\n\n响应拦截器同样也接收两个回调函数，第一个回调会将响应传过来。\n\n我们在响应拦截器中做了以下事情：\n\n 1. 如果响应的 Content-Type 不是 application/json ，直接将响应数据返回。\n 2. 如果响应数据是 string 类型，将其进行反序列化。\n 3. 判断响应数据的 code ，若不是成功的 code (1)，返回 Promise.reject(data) ，发送请求的地方会接收到异常。\n    同时：\n    * 没有会话 (10106) 会调用 window.sessionNotValidHandler()\n    * 其它错误会调用 window.apiErrorHandler(data.message || data.msg || config.errorMessage)\n\n * 成功的响应，会返回 data 。\n\n\n# getUrlParams\n\n提供将当前 url 的参数 ( location.search ) 反序列化为对象的方法，使用方法：\n\nimport { getUrlParams } from '@indfnd/utils'\n\nconst urlParams = getUrlParams()\n\n\n\n# CONTENT_TYPE\n\n由于需要修改 headers 里的 Content-Type ，为了便于使用，维护了一个枚举：\n\nexport const CONTENT_TYPE = {\n  form: 'application/x-www-form-urlencoded',\n  'multi-form': 'multipart/form-data',\n  body: 'application/json',\n  os: 'application/octet-stream',\n  json: 'application/json',\n}\n\n\n以及获取 Content-Type 和 向对象里添加 Content-Type 的方法：\n\nconst contentTypeKey = 'Content-Type'\n\nexport function getContentType(config: any) {\n  return config?.[contentTypeKey] || config?.['content-type']\n}\n\nexport function setContentType(headers: any, type: string) {\n  headers[contentTypeKey] = type\n}\n\n\n# 样例\n\nimport { CONTENT_TYPE, setContentType } from '@indfnd/utils'\n\nconst contentType = CONTENT_TYPE.form\nconst headers = {}\nsetContentType(headers, contentType)\n",normalizedContent:"# 介绍\n\n请求工具使用 axios，其具有强大的处理各种请求的能力，且支持配置拦截器，在发送请求前和收到响应后可以进行一定预处理操作。\n\naxios 支持 get 、post 、put 、delete 等方法的请求，我们最常用的是 get 和 post ，以下只介绍这两种的用法。\n\n\n# get请求\n\naxios.get(url: string, config?: axiosrequestconfig): promise<axiosresponse<any>>\n\n# 样例\n\nimport { axios } from '@indfnd/utils'\nimport config from '@/config'\n\nconst context = config.ismamservercontext\n\n// params 为参数对象，后端使用 @requestparam 接收\n// 例: { isagreeunit: '1', ... }\nexport function listcomtreeapi(params) {\n  return axios.get(`${context}/tree/com/listcomtree`, { params })\n}\n\n\n\n# post请求 - application/json\n\naxios.post(url: string, data?: any, config?: axiosrequestconfig): promise<axiosresponse<any>>\n\npost 默认的 content-type 为 application/json ，后端需要从请求体里获取参数，springboot 使用 @requestbody 接收到一个实体对象或map对象。\n\n# 样例\n\nimport { axios } from '@indfnd/utils'\nimport config from '@/config'\n\nconst context = `${config.ismamservercontext}/manage/com`\n\n// data = { comcode: '111', ... }\nexport function createcomapi(data) {\n  return axios.post(`${context}/create`, data)\n}\n\n\n\n# post请求 - application/x-www-form-urlencoded\n\naxios.formpost(url: string, data?: any, config?: axiosrequestconfig): promise<axiosresponse<any>>\n\n有一些场景需要使用 form表单 的方式发送请求（如登录的接口→_→），使用 axios.post 时需要指定请求头并处理数据格式，此场景比较明确，所以将其封装成 axios.formpost ，使用方法和 axios.post 保持一致。\n\n后端获取参数的方式和 get 一样，使用 @requestparam 接收。\n\n一些碎碎念\n\n虽然封装了这个方法，但是不建议使用，在一定要用的场景才用，要不然前后端对接时还得对接取数方式。\n\n# 样例\n\nimport { axios } from '@indfnd/utils'\nimport config from '@/config'\n\nconst context = `${config.ismamservercontext}/manage/com`\n\n// data = { comcode: '111', ... }\nexport function createcomapi(data) {\n  return axios.post(`${context}/create`, data)\n}\n\n\n\n# axiosrequestconfig\n\naxios 发送请求时的一些配置，支持指定 headers 等，以下只列出部分，详细请查看官方文档\n\n{\n  // `url` 是用于请求的服务器 url\n  url: '/user',\n\n  // `method` 是创建请求时使用的方法\n  method: 'get', // 默认值\n\n  // `baseurl` 将自动加在 `url` 前面，除非 `url` 是一个绝对 url。\n  // 它可以通过设置一个 `baseurl` 便于为 axios 实例的方法传递相对 url\n  baseurl: 'https://some-domain.com/api/',\n\n  // 自定义请求头\n  headers: {'x-requested-with': 'xmlhttprequest'},\n\n  // `params` 是与请求一起发送的 url 参数，会拼接到 url 后\n  // 必须是一个简单对象或 urlsearchparams 对象\n  params: {\n    id: 12345\n  },\n\n  // `data` 是作为请求体被发送的数据\n  // 仅适用 'put', 'post', 'delete 和 'patch' 请求方法\n  // 在没有设置 `transformrequest` 时，则必须是以下类型之一:\n  // - string, plain object, arraybuffer, arraybufferview, urlsearchparams\n  // - 浏览器专属: formdata, file, blob\n  // - node 专属: stream, buffer\n  data: {\n    firstname: 'fred'\n  },\n  \n  // `timeout` 指定请求超时的毫秒数。\n  // 如果请求时间超过 `timeout` 的值，则请求会被中断\n  timeout: 1000, // 默认值是 `0` (永不超时)\n\n  // `withcredentials` 表示跨域请求时是否需要使用凭证\n  withcredentials: false, // default\n\n  // `adapter` 允许自定义处理请求，这使测试更加容易。\n  // 返回一个 promise 并提供一个有效的响应 （参见 lib/adapters/readme.md）。\n  adapter: function (config) {\n    /* ... */\n  },\n\n  // `auth` http basic auth\n  auth: {\n    username: 'janedoe',\n    password: 's00pers3cret'\n  },\n\n  // `responsetype` 表示浏览器将要响应的数据类型\n  // 选项包括: 'arraybuffer', 'document', 'json', 'text', 'stream'\n  // 浏览器专属：'blob'\n  responsetype: 'json', // 默认值\n\n  // `responseencoding` 表示用于解码响应的编码 (node.js 专属)\n  // 注意：忽略 `responsetype` 的值为 'stream'，或者是客户端请求\n  // note: ignored for `responsetype` of 'stream' or client-side requests\n  responseencoding: 'utf8', // 默认值\n\n  // `onuploadprogress` 允许为上传处理进度事件\n  // 浏览器专属\n  onuploadprogress: function (progressevent) {\n    // 处理原生进度事件\n  },\n\n  // `ondownloadprogress` 允许为下载处理进度事件\n  // 浏览器专属\n  ondownloadprogress: function (progressevent) {\n    // 处理原生进度事件\n  },\n\n  // `validatestatus` 定义了对于给定的 http状态码是 resolve 还是 reject promise。\n  // 如果 `validatestatus` 返回 `true` (或者设置为 `null` 或 `undefined`)，\n  // 则promise 将会 resolved，否则是 rejected。\n  validatestatus: function (status) {\n    return status >= 200 && status < 300; // 默认值\n  },\n\n  // see https://axios-http.com/zh/docs/cancellation\n  canceltoken: new canceltoken(function (cancel) {}),\n}\n\n\n\n# 请求拦截器\n\naxios.interceptors.request.use((config: axiosrequestconfig) => {}, (error) => {})\n\n拦截器接收两个回调函数，第一个是正常走到拦截逻辑的回调，第二个是中途报错的回调。\n正常的回调会将此次请求的 config 传递过来，可以在回调函数中对其进行处理。\n\n我们在请求拦截器中做了以下事情：\n\n 1. 获取 token ，添加到 headers 里。\n 2. 若 url 参数或 sessionstorage 里有 sso_token 和 sso_id ，将其一起添加到参数里。\n 3. 如果是form表单提交，将 data 进行序列化，转换成 urlsearchparams 格式。\n\n\n# 响应拦截器\n\naxios.interceptors.response.use((response: axiosresponse) => {}, (error) => {})\n\n响应拦截器同样也接收两个回调函数，第一个回调会将响应传过来。\n\n我们在响应拦截器中做了以下事情：\n\n 1. 如果响应的 content-type 不是 application/json ，直接将响应数据返回。\n 2. 如果响应数据是 string 类型，将其进行反序列化。\n 3. 判断响应数据的 code ，若不是成功的 code (1)，返回 promise.reject(data) ，发送请求的地方会接收到异常。\n    同时：\n    * 没有会话 (10106) 会调用 window.sessionnotvalidhandler()\n    * 其它错误会调用 window.apierrorhandler(data.message || data.msg || config.errormessage)\n\n * 成功的响应，会返回 data 。\n\n\n# geturlparams\n\n提供将当前 url 的参数 ( location.search ) 反序列化为对象的方法，使用方法：\n\nimport { geturlparams } from '@indfnd/utils'\n\nconst urlparams = geturlparams()\n\n\n\n# content_type\n\n由于需要修改 headers 里的 content-type ，为了便于使用，维护了一个枚举：\n\nexport const content_type = {\n  form: 'application/x-www-form-urlencoded',\n  'multi-form': 'multipart/form-data',\n  body: 'application/json',\n  os: 'application/octet-stream',\n  json: 'application/json',\n}\n\n\n以及获取 content-type 和 向对象里添加 content-type 的方法：\n\nconst contenttypekey = 'content-type'\n\nexport function getcontenttype(config: any) {\n  return config?.[contenttypekey] || config?.['content-type']\n}\n\nexport function setcontenttype(headers: any, type: string) {\n  headers[contenttypekey] = type\n}\n\n\n# 样例\n\nimport { content_type, setcontenttype } from '@indfnd/utils'\n\nconst contenttype = content_type.form\nconst headers = {}\nsetcontenttype(headers, contenttype)\n",charsets:{cjk:!0},lastUpdated:"2024-03-15 15:20:00",lastUpdatedTimestamp:17104872e5},{title:"缓存",frontmatter:{title:"缓存",date:"2024-01-06T09:30:30.000Z",permalink:"/pages/cache"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/03.%E7%BC%93%E5%AD%98.html",relativePath:"00.指南/01.工具库/03.缓存.md",key:"v-0411c3fc",path:"/pages/cache/",headers:[{level:2,title:"sessionStorage",slug:"sessionstorage",normalizedTitle:"sessionstorage",charIndex:2},{level:3,title:"getSessionStorage",slug:"getsessionstorage",normalizedTitle:"getsessionstorage",charIndex:94},{level:3,title:"setSessionStorage",slug:"setsessionstorage",normalizedTitle:"setsessionstorage",charIndex:203},{level:3,title:"removeSessionStorage",slug:"removesessionstorage",normalizedTitle:"removesessionstorage",charIndex:321},{level:3,title:"clearSessionStorage",slug:"clearsessionstorage",normalizedTitle:"clearsessionstorage",charIndex:396},{level:2,title:"localStorage",slug:"localstorage",normalizedTitle:"localstorage",charIndex:483},{level:3,title:"getLocalStorage",slug:"getlocalstorage",normalizedTitle:"getlocalstorage",charIndex:573},{level:3,title:"setLocalStorage",slug:"setlocalstorage",normalizedTitle:"setlocalstorage",charIndex:678},{level:3,title:"removeLocalStorage",slug:"removelocalstorage",normalizedTitle:"removelocalstorage",charIndex:792},{level:2,title:"应用缓存",slug:"应用缓存",normalizedTitle:"应用缓存",charIndex:863},{level:3,title:"permissionCache",slug:"permissioncache",normalizedTitle:"permissioncache",charIndex:937},{level:3,title:"userCache",slug:"usercache",normalizedTitle:"usercache",charIndex:1091}],headersStr:"sessionStorage getSessionStorage setSessionStorage removeSessionStorage clearSessionStorage localStorage getLocalStorage setLocalStorage removeLocalStorage 应用缓存 permissionCache userCache",content:"# sessionStorage\n\n原生的 sessionStorage Api要求的是序列化存储，对象、数组等使用时都要先进行序列化和反序列化，所以进行了二次封装统一进行处理。\n\n\n# getSessionStorage\n\ngetSessionStorage(key: string): Object | string\n\n此方法会将缓存值反序列化后返回，无需再使用 JSON.parse() 。\n\n\n# setSessionStorage\n\nsetSessionStorage(key: string, value: any)\n\n此方法会将要缓存的内容序列化后存储，在使用此方法前无需使用 JSON.stringify() 处理。\n\n\n# removeSessionStorage\n\nremoveSessionStorage(key: string)\n\n清除指定 key 的缓存。\n\n\n# clearSessionStorage\n\nclearSessionStorage()\n\n清除 sessionStorage 里的所有缓存，退出登录和会话失效时调用。\n\n\n# localStorage\n\n原生的 sessionStorage Api要求的是序列化存储，对象、数组等使用时都要先进行序列化和反序列化，所以进行了二次封装统一进行处理。\n\n\n# getLocalStorage\n\ngetLocalStorage(key: string): Object | string\n\n此方法会将缓存值反序列化后返回，无需再使用 JSON.parse() 。\n\n\n# setLocalStorage\n\nsetLocalStorage(key: string, value: any)\n\n此方法会将要缓存的内容序列化后存储，在使用此方法前无需使用 JSON.stringify() 处理。\n\n\n# removeLocalStorage\n\nremoveLocalStorage(key: string)\n\n清除指定 key 的缓存。\n\n\n# 应用缓存\n\n一些本项目前端应用用到的缓存，因其比较重要，且后续 key 可能修改，所以再封一层对应的 get、set、remove 方法。\n\n\n# permissionCache\n\n功能资源缓存，目前缓存 key 用的是 ibp-sc-permission\n\nimport { getPermissionCache, setPermissionCache, clearPermissionCache } from '@indfnd/utils'\n\n\n\n# userCache\n\n当前登录用户信息缓存，目前缓存 key 用的是 userInfo\n\nimport { getUserInfoCache, setUserInfoCache, clearUserInfoCache } from '@indfnd/utils'\n",normalizedContent:"# sessionstorage\n\n原生的 sessionstorage api要求的是序列化存储，对象、数组等使用时都要先进行序列化和反序列化，所以进行了二次封装统一进行处理。\n\n\n# getsessionstorage\n\ngetsessionstorage(key: string): object | string\n\n此方法会将缓存值反序列化后返回，无需再使用 json.parse() 。\n\n\n# setsessionstorage\n\nsetsessionstorage(key: string, value: any)\n\n此方法会将要缓存的内容序列化后存储，在使用此方法前无需使用 json.stringify() 处理。\n\n\n# removesessionstorage\n\nremovesessionstorage(key: string)\n\n清除指定 key 的缓存。\n\n\n# clearsessionstorage\n\nclearsessionstorage()\n\n清除 sessionstorage 里的所有缓存，退出登录和会话失效时调用。\n\n\n# localstorage\n\n原生的 sessionstorage api要求的是序列化存储，对象、数组等使用时都要先进行序列化和反序列化，所以进行了二次封装统一进行处理。\n\n\n# getlocalstorage\n\ngetlocalstorage(key: string): object | string\n\n此方法会将缓存值反序列化后返回，无需再使用 json.parse() 。\n\n\n# setlocalstorage\n\nsetlocalstorage(key: string, value: any)\n\n此方法会将要缓存的内容序列化后存储，在使用此方法前无需使用 json.stringify() 处理。\n\n\n# removelocalstorage\n\nremovelocalstorage(key: string)\n\n清除指定 key 的缓存。\n\n\n# 应用缓存\n\n一些本项目前端应用用到的缓存，因其比较重要，且后续 key 可能修改，所以再封一层对应的 get、set、remove 方法。\n\n\n# permissioncache\n\n功能资源缓存，目前缓存 key 用的是 ibp-sc-permission\n\nimport { getpermissioncache, setpermissioncache, clearpermissioncache } from '@indfnd/utils'\n\n\n\n# usercache\n\n当前登录用户信息缓存，目前缓存 key 用的是 userinfo\n\nimport { getuserinfocache, setuserinfocache, clearuserinfocache } from '@indfnd/utils'\n",charsets:{cjk:!0},lastUpdated:"2024-04-30 14:53:41",lastUpdatedTimestamp:1714460021e3},{title:"Token",frontmatter:{title:"Token",date:"2024-01-06T09:27:53.000Z",permalink:"/pages/token"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/01.Token.html",relativePath:"00.指南/01.工具库/01.Token.md",key:"v-f1e45dec",path:"/pages/token/",headers:[{level:2,title:"介绍",slug:"介绍",normalizedTitle:"介绍",charIndex:2},{level:2,title:"setToken",slug:"settoken",normalizedTitle:"settoken",charIndex:254},{level:2,title:"getToken",slug:"gettoken",normalizedTitle:"gettoken",charIndex:264}],headersStr:"介绍 setToken getToken",content:"# 介绍\n\n * 系统通过请求头中携带的 token 进行认证登录、登录有效时长控制。\n * 登录接口会将 token 返回。\n * 前端接收 token 后将其存储在 localStorage 中，之所以不存储在 sessionStorage 中，是为了新打开标签页时无需再次登录。\n * ibp 应用存储在 sessionStorage 中，为了兼容，先从 sessionStorage 中获取 ibp 的 token，若取不到，再从 localStorage 里取。\n\n使用方法：\n\nimport { setToken, getToken } from '@indfnd/utils'\n\n\n\n# setToken\n\n登录成功后调用，将 token 存储在 localStorage 。\n\n\n# getToken\n\n 1. 从 sessionStorage 获取 ibp token，若有，则返回。\n 2. 从 localStorage 获取 token ，若有，则返回。\n 3. 都没有，返回 false 。",normalizedContent:"# 介绍\n\n * 系统通过请求头中携带的 token 进行认证登录、登录有效时长控制。\n * 登录接口会将 token 返回。\n * 前端接收 token 后将其存储在 localstorage 中，之所以不存储在 sessionstorage 中，是为了新打开标签页时无需再次登录。\n * ibp 应用存储在 sessionstorage 中，为了兼容，先从 sessionstorage 中获取 ibp 的 token，若取不到，再从 localstorage 里取。\n\n使用方法：\n\nimport { settoken, gettoken } from '@indfnd/utils'\n\n\n\n# settoken\n\n登录成功后调用，将 token 存储在 localstorage 。\n\n\n# gettoken\n\n 1. 从 sessionstorage 获取 ibp token，若有，则返回。\n 2. 从 localstorage 获取 token ，若有，则返回。\n 3. 都没有，返回 false 。",charsets:{cjk:!0},lastUpdated:"2024-01-24 18:07:31",lastUpdatedTimestamp:1706090851e3},{title:"SM3",frontmatter:{title:"SM3",date:"2024-01-06T09:14:17.000Z",permalink:"/pages/dcc8f2/"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/04.SM3.html",relativePath:"00.指南/01.工具库/04.SM3.md",key:"v-dd18d2ac",path:"/pages/dcc8f2/",headers:[{level:2,title:"encrypt",slug:"encrypt",normalizedTitle:"encrypt",charIndex:240},{level:2,title:"genSalt",slug:"gensalt",normalizedTitle:"gensalt",charIndex:352},{level:2,title:"encryptForLogin",slug:"encryptforlogin",normalizedTitle:"encryptforlogin",charIndex:383}],headersStr:"encrypt genSalt encryptForLogin",content:"SM3密码杂凑算法是中国国家密码管理局2010年公布的中国商用密码杂凑算法标准。该算法于2012年发布为密码行业标准(GM/T 0004-2012)，2016年发布为国家密码杂凑算法标准(GB/T 32905-2016)。\n\nSM3适用于商用密码应用中的数字签名和验证，是在SHA-256基础上改进实现的一种算法，其安全性和SHA-256相当。\n\n目前各项目均要求登录密码等敏感数据需要进行SM3散列后存储，其中密码不能通过明文传输，所以前端需要有SM3散列的公共方法。\n\n\n# encrypt\n\nSM3散列方法，使用方法：\n\nimport { cryptor } from '@indfnd/utils'\n\nconst encodeStr = cryptor.encrypt('1111')\n\n\n\n# genSalt\n\n加盐函数，盐值是写死的，登录需要。\n\n\n# encryptForLogin\n\n专为登录密码封装的散列函数，因其需要加盐后加密，且需二次散列，使用方法：\n\nimport { cryptor } from '@indfnd/utils'\n\nconst password = '111'\nconst validCode = 'XDMM' // 页面上输入的验证码\n\nconst pwd = cryptor.encryptForLogin(\n  cryptor.encryptForLogin(password, 'sm3', true) + validCode,\n  'sm3',\n  false,\n)\n",normalizedContent:"sm3密码杂凑算法是中国国家密码管理局2010年公布的中国商用密码杂凑算法标准。该算法于2012年发布为密码行业标准(gm/t 0004-2012)，2016年发布为国家密码杂凑算法标准(gb/t 32905-2016)。\n\nsm3适用于商用密码应用中的数字签名和验证，是在sha-256基础上改进实现的一种算法，其安全性和sha-256相当。\n\n目前各项目均要求登录密码等敏感数据需要进行sm3散列后存储，其中密码不能通过明文传输，所以前端需要有sm3散列的公共方法。\n\n\n# encrypt\n\nsm3散列方法，使用方法：\n\nimport { cryptor } from '@indfnd/utils'\n\nconst encodestr = cryptor.encrypt('1111')\n\n\n\n# gensalt\n\n加盐函数，盐值是写死的，登录需要。\n\n\n# encryptforlogin\n\n专为登录密码封装的散列函数，因其需要加盐后加密，且需二次散列，使用方法：\n\nimport { cryptor } from '@indfnd/utils'\n\nconst password = '111'\nconst validcode = 'xdmm' // 页面上输入的验证码\n\nconst pwd = cryptor.encryptforlogin(\n  cryptor.encryptforlogin(password, 'sm3', true) + validcode,\n  'sm3',\n  false,\n)\n",charsets:{cjk:!0},lastUpdated:"2024-03-03 19:30:39",lastUpdatedTimestamp:1709465439e3},{title:"类型判断",frontmatter:{title:"类型判断",date:"2024-01-06T09:31:05.000Z",permalink:"/pages/is-type"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/07.%E7%B1%BB%E5%9E%8B%E5%88%A4%E6%96%AD.html",relativePath:"00.指南/01.工具库/07.类型判断.md",key:"v-943203dc",path:"/pages/is-type/",headers:[{level:2,title:"isNull",slug:"isnull",normalizedTitle:"isnull",charIndex:179},{level:2,title:"isUndefined",slug:"isundefined",normalizedTitle:"isundefined",charIndex:301},{level:2,title:"isNil",slug:"isnil",normalizedTitle:"isnil",charIndex:453},{level:2,title:"getType",slug:"gettype",normalizedTitle:"gettype",charIndex:605},{level:2,title:"isType",slug:"istype",normalizedTitle:"istype",charIndex:809},{level:2,title:"isPrototype",slug:"isprototype",normalizedTitle:"isprototype",charIndex:985},{level:2,title:"isBoolean",slug:"isboolean",normalizedTitle:"isboolean",charIndex:1050},{level:2,title:"isNumber",slug:"isnumber",normalizedTitle:"isnumber",charIndex:1106},{level:2,title:"isString",slug:"isstring",normalizedTitle:"isstring",charIndex:1159},{level:2,title:"isArray",slug:"isarray",normalizedTitle:"isarray",charIndex:940},{level:2,title:"isArrayLike",slug:"isarraylike",normalizedTitle:"isarraylike",charIndex:1264},{level:2,title:"isObject",slug:"isobject",normalizedTitle:"isobject",charIndex:1368},{level:2,title:"isObjectLike",slug:"isobjectlike",normalizedTitle:"isobjectlike",charIndex:1437},{level:2,title:"isPlainObject",slug:"isplainobject",normalizedTitle:"isplainobject",charIndex:1515},{level:2,title:"isFunction",slug:"isfunction",normalizedTitle:"isfunction",charIndex:1585},{level:2,title:"isPromise",slug:"ispromise",normalizedTitle:"ispromise",charIndex:1642},{level:2,title:"isDate",slug:"isdate",normalizedTitle:"isdate",charIndex:1718},{level:2,title:"isArguments",slug:"isarguments",normalizedTitle:"isarguments",charIndex:1769},{level:2,title:"isRegExp",slug:"isregexp",normalizedTitle:"isregexp",charIndex:1834},{level:2,title:"isError",slug:"iserror",normalizedTitle:"iserror",charIndex:1890},{level:2,title:"isElement",slug:"iselement",normalizedTitle:"iselement",charIndex:1943},{level:2,title:"isEmpty",slug:"isempty",normalizedTitle:"isempty",charIndex:2004},{level:2,title:"isEqual",slug:"isequal",normalizedTitle:"isequal",charIndex:2248},{level:2,title:"isEven",slug:"iseven",normalizedTitle:"iseven",charIndex:2343},{level:2,title:"isOdd",slug:"isodd",normalizedTitle:"isodd",charIndex:2363},{level:2,title:"isFinite",slug:"isfinite",normalizedTitle:"isfinite",charIndex:2382},{level:2,title:"isInteger",slug:"isinteger",normalizedTitle:"isinteger",charIndex:2407},{level:2,title:"isDecimal",slug:"isdecimal",normalizedTitle:"isdecimal",charIndex:2430},{level:2,title:"isNegative",slug:"isnegative",normalizedTitle:"isnegative",charIndex:2454},{level:2,title:"isPositive",slug:"ispositive",normalizedTitle:"ispositive",charIndex:2478},{level:2,title:"isNumberEqual",slug:"isnumberequal",normalizedTitle:"isnumberequal",charIndex:2502},{level:2,title:"isEqualWith",slug:"isequalwith",normalizedTitle:"isequalwith",charIndex:2551}],headersStr:"isNull isUndefined isNil getType isType isPrototype isBoolean isNumber isString isArray isArrayLike isObject isObjectLike isPlainObject isFunction isPromise isDate isArguments isRegExp isError isElement isEmpty isEqual isEven isOdd isFinite isInteger isDecimal isNegative isPositive isNumberEqual isEqualWith",content:"由于以下等多种场景，避免大家瞎写导致bug，提供一些判断类型的方法。\n\n * 复杂对象，如日期对象、数组等不能使用 typeof\n * 判断 val !== null && val !== undefined 不能直接使用 !val\n * 将 val === null || val === undefined 与 val === 0 的情形区分开\n\n\n# isNull\n\nisNull(value: any): boolean\n\n判断是否为 null\n\nexport const isNull = (value: any): value is null => value === null\n\n\n\n# isUndefined\n\nisUndefined(value: any): boolean\n\n判断是否为 undefined\n\nexport const isUndefined = (value: any): value is undefined => value === undefined\n\n\n\n# isNil\n\nisNil(value: any): boolean\n\n判断是否为 null 或 undefined\n\nexport const isNil = (value: any): value is null => isNull(value) || isUndefined(value)\n\n\n\n# getType\n\ngetType(value: any): string\n\n获取对象的实际类型，如 Array 的实际类型是 [object Array] ，此方法会返回 object 后的类型。\n\n# 样例\n\nimport { getType } from '@indfnd/utils'\n\nconst list = []\nconst type = getType(list) // Array\n\n\n\n# isType\n\nisType(value: any, type: string): boolean\n\n判断是否是指定的类型\n\n# 样例\n\nimport { isType } from '@indfnd/utils'\n\nconst list = []\nconst isArray = isType(list, 'Array') // true\n\n\n\n# isPrototype\n\nisPrototype(value: any): boolean\n\n判断是否是有原型属性的对象\n\n\n# isBoolean\n\nisBoolean(value: any): boolean\n\n判断是否是布尔值\n\n\n# isNumber\n\nisNumber(value: any): boolean\n\n判断是否是数字\n\n\n# isString\n\nisString(value: any): boolean\n\n判断是否是字符串\n\n\n# isArray\n\nisArray(value: any): boolean\n\n判断是否是数组\n\n\n# isArrayLike\n\nisArrayLike(value: any): boolean\n\n判断是否是类数组对象，如 document.body.children ，abc (有 length )\n\n\n# isObject\n\nisObject(value: any): boolean\n\n判断是否是对象，广义的，包括 Function\n\n\n# isObjectLike\n\nisObjectLike(value: any): boolean\n\n判断是否是对象，广义的，不包括 Function\n\n\n# isPlainObject\n\nisPlainObject(value: any): boolean\n\n判断是否是朴素对象，即 {}\n\n\n# isFunction\n\nisFunction(value: any): boolean\n\n判断是否是函数\n\n\n# isPromise\n\nisPromise(value: any): boolean\n\n判断是否是 Promise 函数 (有 then 方法)\n\n\n# isDate\n\nisDate(value: any): boolean\n\n判断是否是日期对象\n\n\n# isArguments\n\nisArguments(value: any): boolean\n\n判断是否是函数的参数类数组\n\n\n# isRegExp\n\nisRegExp(value: any): boolean\n\n判断是否是正则表达式\n\n\n# isError\n\nisError(value: any): boolean\n\n判断是否是异常对象\n\n\n# isElement\n\nisElement(value: any): boolean\n\n判断是否是 Element\n\n\n# isEmpty\n\nisEmpty(value: any): boolean\n\n判断是否为空\n\nisEmpty(null)      // true\nisEmpty()          // true\nisEmpty(true)      // true\nisEmpty(1)         // true\nisEmpty([1, 2, 3]) // false\nisEmpty('abc')     // false\nisEmpty({ a: 1 })  // false\n\n\n\n# isEqual\n\nisEqual(value: any, other: any): boolean\n\n判断两个值是否相等，支持数组和对象，会按顺序判断每一个值，都相等返回 true\n\n\n# isEven\n\n判断是否是偶数\n\n\n# isOdd\n\n判断是否是奇数\n\n\n# isFinite\n\n判断是否是有限的数字\n\n\n# isInteger\n\n判断是否是整数\n\n\n# isDecimal\n\n判断是否是非整数\n\n\n# isNegative\n\n判断是否是负数\n\n\n# isPositive\n\n判断是否是正数\n\n\n# isNumberEqual\n\n判断两个数是否相等，二者之差小于 0.00001 视为相等\n\n\n# isEqualWith\n\n<T> isEqualWith(value<T>, other: T, fn: (v1: T, v2: T) => boolean): boolean\n\n根据传入的 fn 判断两个值是否相等",normalizedContent:"由于以下等多种场景，避免大家瞎写导致bug，提供一些判断类型的方法。\n\n * 复杂对象，如日期对象、数组等不能使用 typeof\n * 判断 val !== null && val !== undefined 不能直接使用 !val\n * 将 val === null || val === undefined 与 val === 0 的情形区分开\n\n\n# isnull\n\nisnull(value: any): boolean\n\n判断是否为 null\n\nexport const isnull = (value: any): value is null => value === null\n\n\n\n# isundefined\n\nisundefined(value: any): boolean\n\n判断是否为 undefined\n\nexport const isundefined = (value: any): value is undefined => value === undefined\n\n\n\n# isnil\n\nisnil(value: any): boolean\n\n判断是否为 null 或 undefined\n\nexport const isnil = (value: any): value is null => isnull(value) || isundefined(value)\n\n\n\n# gettype\n\ngettype(value: any): string\n\n获取对象的实际类型，如 array 的实际类型是 [object array] ，此方法会返回 object 后的类型。\n\n# 样例\n\nimport { gettype } from '@indfnd/utils'\n\nconst list = []\nconst type = gettype(list) // array\n\n\n\n# istype\n\nistype(value: any, type: string): boolean\n\n判断是否是指定的类型\n\n# 样例\n\nimport { istype } from '@indfnd/utils'\n\nconst list = []\nconst isarray = istype(list, 'array') // true\n\n\n\n# isprototype\n\nisprototype(value: any): boolean\n\n判断是否是有原型属性的对象\n\n\n# isboolean\n\nisboolean(value: any): boolean\n\n判断是否是布尔值\n\n\n# isnumber\n\nisnumber(value: any): boolean\n\n判断是否是数字\n\n\n# isstring\n\nisstring(value: any): boolean\n\n判断是否是字符串\n\n\n# isarray\n\nisarray(value: any): boolean\n\n判断是否是数组\n\n\n# isarraylike\n\nisarraylike(value: any): boolean\n\n判断是否是类数组对象，如 document.body.children ，abc (有 length )\n\n\n# isobject\n\nisobject(value: any): boolean\n\n判断是否是对象，广义的，包括 function\n\n\n# isobjectlike\n\nisobjectlike(value: any): boolean\n\n判断是否是对象，广义的，不包括 function\n\n\n# isplainobject\n\nisplainobject(value: any): boolean\n\n判断是否是朴素对象，即 {}\n\n\n# isfunction\n\nisfunction(value: any): boolean\n\n判断是否是函数\n\n\n# ispromise\n\nispromise(value: any): boolean\n\n判断是否是 promise 函数 (有 then 方法)\n\n\n# isdate\n\nisdate(value: any): boolean\n\n判断是否是日期对象\n\n\n# isarguments\n\nisarguments(value: any): boolean\n\n判断是否是函数的参数类数组\n\n\n# isregexp\n\nisregexp(value: any): boolean\n\n判断是否是正则表达式\n\n\n# iserror\n\niserror(value: any): boolean\n\n判断是否是异常对象\n\n\n# iselement\n\niselement(value: any): boolean\n\n判断是否是 element\n\n\n# isempty\n\nisempty(value: any): boolean\n\n判断是否为空\n\nisempty(null)      // true\nisempty()          // true\nisempty(true)      // true\nisempty(1)         // true\nisempty([1, 2, 3]) // false\nisempty('abc')     // false\nisempty({ a: 1 })  // false\n\n\n\n# isequal\n\nisequal(value: any, other: any): boolean\n\n判断两个值是否相等，支持数组和对象，会按顺序判断每一个值，都相等返回 true\n\n\n# iseven\n\n判断是否是偶数\n\n\n# isodd\n\n判断是否是奇数\n\n\n# isfinite\n\n判断是否是有限的数字\n\n\n# isinteger\n\n判断是否是整数\n\n\n# isdecimal\n\n判断是否是非整数\n\n\n# isnegative\n\n判断是否是负数\n\n\n# ispositive\n\n判断是否是正数\n\n\n# isnumberequal\n\n判断两个数是否相等，二者之差小于 0.00001 视为相等\n\n\n# isequalwith\n\n<t> isequalwith(value<t>, other: t, fn: (v1: t, v2: t) => boolean): boolean\n\n根据传入的 fn 判断两个值是否相等",charsets:{cjk:!0},lastUpdated:"2024-01-24 18:07:31",lastUpdatedTimestamp:1706090851e3},{title:"日期",frontmatter:{title:"日期",date:"2024-01-06T09:35:00.000Z",permalink:"/pages/date"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/05.%E6%97%A5%E6%9C%9F.html",relativePath:"00.指南/01.工具库/05.日期.md",key:"v-3c7c1402",path:"/pages/date/",headers:[{level:2,title:"str2Date",slug:"str2date",normalizedTitle:"str2date",charIndex:16},{level:2,title:"formatDate",slug:"formatdate",normalizedTitle:"formatdate",charIndex:174},{level:2,title:"formatDateChinese",slug:"formatdatechinese",normalizedTitle:"formatdatechinese",charIndex:509}],headersStr:"str2Date formatDate formatDateChinese",content:"提供一些日期的处理方法\n\n\n# str2Date\n\nstr2Date(value: string): Date\n\n将符合 ['YYYYMMDDHHmmss', 'YYYYMMDDHHmm', 'YYYYMMDD', 'YYYYMM', 'YYYY'] 其中任一格式的字符串转换为日期对象返回，若不符合，会返回 Invalid Date 。\n\n\n# formatDate\n\nformatDate(value: string): string\n\n根据 value 的长度，将我们常用在数据库中存储的没有分隔符日期字符串转换为展示时带分隔符的格式。\n\n * 为 null 、undefined 、false 、'' 、0 时，返回 '-'\n * 长度为14时，返回 YYYY-MM-DD HH:mm:ss 格式。\n * 长度为12时，返回 YYYY-MM-DD HH:mm 格式。\n * 长度为8时，返回 YYYY-MM-DD 格式。\n * 长度为6时，返回 YYYY-MM 格式。\n * 长度为4时，返回 YY-MM 格式，如 2401 会转换为 24-01 表示为2024年01月，所以年处理请不要使用此方法。\n\n\n# formatDateChinese\n\nformatDateChinese(value: string): string\n\n根据 value 的长度，将我们常用在数据库中存储的没有分隔符日期字符串进行中文的日期格式化。\n\n * 为 null 、undefined 、false 、'' 、0 时，返回 '-'\n * 长度为14时，返回 YYYY年MM月DD日 HH:mm:ss 格式。\n * 长度为12时，返回 YYYY年MM月DD日 HH:mm 格式。\n * 长度为8时，返回 YYYY年MM月DD日 格式。\n * 长度为6时，返回 YYYY年MM月 格式。\n * 长度为4时，返回 YY年MM月 格式，如 2401 会转换为 24年01月 表示为2024年01月，所以年处理请不要使用此方法。",normalizedContent:"提供一些日期的处理方法\n\n\n# str2date\n\nstr2date(value: string): date\n\n将符合 ['yyyymmddhhmmss', 'yyyymmddhhmm', 'yyyymmdd', 'yyyymm', 'yyyy'] 其中任一格式的字符串转换为日期对象返回，若不符合，会返回 invalid date 。\n\n\n# formatdate\n\nformatdate(value: string): string\n\n根据 value 的长度，将我们常用在数据库中存储的没有分隔符日期字符串转换为展示时带分隔符的格式。\n\n * 为 null 、undefined 、false 、'' 、0 时，返回 '-'\n * 长度为14时，返回 yyyy-mm-dd hh:mm:ss 格式。\n * 长度为12时，返回 yyyy-mm-dd hh:mm 格式。\n * 长度为8时，返回 yyyy-mm-dd 格式。\n * 长度为6时，返回 yyyy-mm 格式。\n * 长度为4时，返回 yy-mm 格式，如 2401 会转换为 24-01 表示为2024年01月，所以年处理请不要使用此方法。\n\n\n# formatdatechinese\n\nformatdatechinese(value: string): string\n\n根据 value 的长度，将我们常用在数据库中存储的没有分隔符日期字符串进行中文的日期格式化。\n\n * 为 null 、undefined 、false 、'' 、0 时，返回 '-'\n * 长度为14时，返回 yyyy年mm月dd日 hh:mm:ss 格式。\n * 长度为12时，返回 yyyy年mm月dd日 hh:mm 格式。\n * 长度为8时，返回 yyyy年mm月dd日 格式。\n * 长度为6时，返回 yyyy年mm月 格式。\n * 长度为4时，返回 yy年mm月 格式，如 2401 会转换为 24年01月 表示为2024年01月，所以年处理请不要使用此方法。",charsets:{cjk:!0},lastUpdated:"2024-01-08 10:05:24",lastUpdatedTimestamp:1704679524e3},{title:"数字",frontmatter:{title:"数字",date:"2024-01-06T09:16:47.000Z",permalink:"/pages/number"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/08.%E6%95%B0%E5%AD%97.html",relativePath:"00.指南/01.工具库/08.数字.md",key:"v-1eb4dd3d",path:"/pages/number/",headers:[{level:2,title:"toThousands",slug:"tothousands",normalizedTitle:"tothousands",charIndex:18},{level:2,title:"toChies",slug:"tochies",normalizedTitle:"tochies",charIndex:83},{level:2,title:"numToDX",slug:"numtodx",normalizedTitle:"numtodx",charIndex:175},{level:2,title:"numToChineseNumerals",slug:"numtochinesenumerals",normalizedTitle:"numtochinesenumerals",charIndex:284},{level:2,title:"formatDecimal",slug:"formatdecimal",normalizedTitle:"formatdecimal",charIndex:416},{level:2,title:"round",slug:"round",normalizedTitle:"round",charIndex:560},{level:2,title:"toFixed",slug:"tofixed",normalizedTitle:"tofixed",charIndex:664}],headersStr:"toThousands toChies numToDX numToChineseNumerals formatDecimal round toFixed",content:"提供一些数字相关的处理方法\n\n\n# toThousands\n\ntoThousands(numStr: string): string\n\n将数字转为千分位展示\n\n\n# toChies\n\ntoChies(numStr: string): string\n\n将数字转为大写汉字，后面会添加 元整 两字\n\n零壹贰叁肆伍陆柒捌玖\n仟佰拾亿仟佰拾万仟佰拾\n\n\n# numToDX\n\nnumToDX(numStr: string): string\n\n将带有小数的数字转为部分大写汉字，部分非大写汉字 搞不懂什么时候用这个\n\n零壹贰叁肆伍陆柒捌玖\n千百拾亿千百拾万千百拾元角分\n\n\n# numToChineseNumerals\n\n怎么又来一个转汉字的，这个不是大写汉字，到底该用哪个 都是拷的四川的，和我没有关系 你看，没有统一的工具库就是这样，一样的东西写了好几个，还都不一样\n\n零一二三四五六七八九\n十百千\n\n这是什么东西，有这么用的吗\n\n\n# formatDecimal\n\nformatDecimal(num: number | string, decimal: number): string\n\n截取数字的小数位数，decimal 为要截取几位，没有这么多会补0\n\n提示\n\n不会四舍五入的哦，是截取，是截取，是截取！！！\n\n\n# round\n\nround(number: number, precision: number): number\n\n保留指定位数的小数，对最后一位进行四舍五入，precision 为保留几位，默认为2\n\n\n# toFixed\n\ntoFixed(num: number | string, precision: number): string\n\n保留指定位数的小数，对最后一位进行四舍五入，并转换为字符串，precision 为保留几位，默认为2",normalizedContent:"提供一些数字相关的处理方法\n\n\n# tothousands\n\ntothousands(numstr: string): string\n\n将数字转为千分位展示\n\n\n# tochies\n\ntochies(numstr: string): string\n\n将数字转为大写汉字，后面会添加 元整 两字\n\n零壹贰叁肆伍陆柒捌玖\n仟佰拾亿仟佰拾万仟佰拾\n\n\n# numtodx\n\nnumtodx(numstr: string): string\n\n将带有小数的数字转为部分大写汉字，部分非大写汉字 搞不懂什么时候用这个\n\n零壹贰叁肆伍陆柒捌玖\n千百拾亿千百拾万千百拾元角分\n\n\n# numtochinesenumerals\n\n怎么又来一个转汉字的，这个不是大写汉字，到底该用哪个 都是拷的四川的，和我没有关系 你看，没有统一的工具库就是这样，一样的东西写了好几个，还都不一样\n\n零一二三四五六七八九\n十百千\n\n这是什么东西，有这么用的吗\n\n\n# formatdecimal\n\nformatdecimal(num: number | string, decimal: number): string\n\n截取数字的小数位数，decimal 为要截取几位，没有这么多会补0\n\n提示\n\n不会四舍五入的哦，是截取，是截取，是截取！！！\n\n\n# round\n\nround(number: number, precision: number): number\n\n保留指定位数的小数，对最后一位进行四舍五入，precision 为保留几位，默认为2\n\n\n# tofixed\n\ntofixed(num: number | string, precision: number): string\n\n保留指定位数的小数，对最后一位进行四舍五入，并转换为字符串，precision 为保留几位，默认为2",charsets:{cjk:!0},lastUpdated:"2024-01-08 10:05:24",lastUpdatedTimestamp:1704679524e3},{title:"枚举",frontmatter:{title:"枚举",date:"2024-01-06T09:32:16.000Z",permalink:"/pages/enum"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/06.%E6%9E%9A%E4%B8%BE.html",relativePath:"00.指南/01.工具库/06.枚举.md",key:"v-7f1df8b5",path:"/pages/enum/",headers:[{level:2,title:"是否的枚举",slug:"是否的枚举",normalizedTitle:"是否的枚举",charIndex:31},{level:2,title:"中台相关枚举",slug:"中台相关枚举",normalizedTitle:"中台相关枚举",charIndex:415},{level:2,title:"renderEnumList",slug:"renderenumlist",normalizedTitle:"renderenumlist",charIndex:2280},{level:2,title:"renderEnumData",slug:"renderenumdata",normalizedTitle:"renderenumdata",charIndex:2651},{level:2,title:"renderColumnEnums",slug:"rendercolumnenums",normalizedTitle:"rendercolumnenums",charIndex:3006},{level:2,title:"renderFieldEnums",slug:"renderfieldenums",normalizedTitle:"renderfieldenums",charIndex:4536}],headersStr:"是否的枚举 中台相关枚举 renderEnumList renderEnumData renderColumnEnums renderFieldEnums",content:"提供一些前端公用的枚举，和后端数据转换为枚举的方法。\n\n\n# 是否的枚举\n\n因项目刚启动时，前后端还未正式对接，所以前端定义了是否的枚举，后来X1里也有人定义了是否的枚举，key 为 IND_Y_N ，在本项目中两个都可以用。\n\n/** 是否枚举的key */\nexport const IS_OR_NOT_ENUM_KEY = 'IS_ENUM'\n/** 是否枚举的map形式 */\nexport const IS_OR_NOT_ENUM = { '1': '是', '0': '否' }\n/** 是否枚举的list形式 */\nexport const IS_OR_NOT_ENUM_LIST = [\n  { K: '1', V: '是' },\n  { K: '0', V: '否' },\n]\n\n\n一些碎碎念\n\n本人看到后端起名为 IND_Y_N 后，一度想把 IS_OR_NOT_ENUM 改为 Y_N_ENUM 。\n\n\n# 中台相关枚举\n\n定义了一些中台管理单元、组织类型、组织级别等的枚举，各业务代码不要写死，万一以后要大改呢→_→\n\nexport const UC_ENUM = {\n  MANAGE_UNIT_ID: '00000000000000000000000020370001', // 管理单元Id\n  ORG_CODE_ADMIN: 'default',                          // 行政树类型\n  ORG_CODE_MKT: '08',                                 // 营销业务树类型\n  ORG_CODE_LOG: '08',                                 // 物流业务树类型，之前说要分开建的，现在和营销一个树\n  ORG_CODE_ACTIVITY: '08',                            // 宣促业务树类型，之前说要分开建的，现在和营销一个树\n  ADMIN_LEVEL_IND: '01',                              // 行政树组织级别 - 工业公司\n  ADMIN_LEVEL_CENTER: '02',                           // 行政树组织级别 - 营销中心\n  ADMIN_LEVEL_FAC: '03',                              // 行政树组织级别 - 卷烟厂\n  ADMIN_LEVEL_DIST: '04',                             // 行政树组织级别 - 区域\n  ADMIN_LEVEL_COMMON_DEPT: '08',                      // 行政树组织级别 - 普通部门\n  ADMIN_LEVEL_COMMON_POST: '80',                      // 行政树组织级别 - 普通岗位\n  LEVEL_IND: '01',                                    // 业务树组织级别 - 工业公司\n  LEVEL_CENTER: '02',                                 // 业务树组织级别 - 营销中心\n  LEVEL_FAC: '02',                                    // 业务树组织级别 - 卷烟厂\n  LEVEL_DIST: '04',                                   // 业务树组织级别 - 普通卷烟营销的区域\n  LEVEL_CIGAR_DIST: '05',                             // 业务树组织级别 - 雪茄片区\n  LEVEL_COMMON_DEPT: '03',                            // 业务树组织级别 - 普通部门\n  LEVEL_COMMON_POST: '80',                            // 业务树组织级别 - 普通岗位\n  LEVEL_DIST_MANAGE_POST: '81',                       // 业务树组织级别 - 区域经理岗位\n  LEVEL_CUST_MANAGE_POST: '82',                       // 业务树组织级别 - 客户经理岗位\n  LEVEL_CIGAR_DIST_MANAGE_POST: '83',                 // 业务树组织级别 - 雪茄片区经理岗位\n  LEVEL_CIGAR_CUST_MANAGE_POST: '84',                 // 业务树组织级别 - 雪茄客户经理岗位\n  LEVEL_INTERNAL_POST: '85',                          // 业务树组织级别 - 内勤岗位\n}\n\n\n# 样例\n\nimport { UC_ENUM } from '@indfnd/utils'\n\nconst datePermitType = UC_ENUM.LEVEL_DIST_MANAGE_POST // 81\n\n\n\n# renderEnumList\n\nrenderEnumList(list: Array<any>, kProp: string, vProp: string)\n\n将列表数据转换为枚举的列表格式，即 [{ K: 'key', V: 'value' }] ，kProp 为列表中 key 对应的属性名，vProp 为 value 对应的属性名，使用方法：\n\nimport { renderEnumList } from '@indfnd/utils'\n\nconst list = [{ brandId: '0916', brandName: '泰山' }]\nconst enumList = renderEnumList(list, 'brandId', 'brandName') // [{ K: '0916', V: '泰山' }]\n\n\n\n# renderEnumData\n\nrenderEnumData(list: Array<any>, kProp: string, vProp: string)\n\n将列表数据转换为枚举的对象格式，即 { 'key': 'value' } ，kProp 为列表中 key 对应的属性名，vProp 为 value 对应的属性名，使用方法：\n\nimport { renderEnumData } from '@indfnd/utils'\n\nconst list = [{ brandId: '0916', brandName: '泰山' }]\nconst enumData = renderEnumData(list, 'brandId', 'brandName') // { '0916': '泰山' }\n\n\n\n# renderColumnEnums\n\nrenderColumnEnums(columns: Array<any>, enumRelation: Object): Array<any>\n\n由于表格组件不方便获取枚举数据（时机不好确定），需要各业务先获取枚举数据后，重新组织列定义，有多种方案：\n\n * 列定义写在 computed 中，获取到数据列定义会自动更新，本人觉得这样用起来很怪。\n * 先不组织列定义，获取到数据后再组织，这样在获取到数据前表格是没有列的，看起来也很怪。\n * 先定义列定义，获取到数据后重新组织所有列，本人觉得这样太麻烦了。\n\n所以封装了本方法，本方法不能响应式修改定义，需要重新赋值。第一个参数是列定义，第二个参数是列的 key 和其枚举的对应关系，使用方法：\n\nimport {\n  IS_OR_NOT_ENUM_KEY,\n  IS_OR_NOT_ENUM,\n  IS_OR_NOT_ENUM_LIST,\n  renderEnumList,\n  renderColumnEnums\n} from '@/utils'\n\nlet columns = [\n  {\n    key: IS_OR_NOT_ENUM_KEY,\n    title: '是否',\n    type: 'enum', // 需注意，type 必须是 enum 或 select\n    width: 50,\n  },\n  {\n    key: 'itemCode',\n    title: '卷烟',\n    type: 'select', // 需注意，type 必须是 enum 或 select\n    width: 50,\n  },\n]\n\nconst itemList = [{ itemCode: '111', itemName: '卷烟1' }, { itemCode: '112', itemName: '卷烟2' }]\n\ncolumns = renderColumnEnums(columns, {\n  // 属性名和列的 key 保持一致\n  // enum 时 IS_OR_NOT_ENUM 也可以，列表格式和对象格式都可以\n  // select 只支持列表格式或返回列表格式的函数\n  IS_OR_NOT_ENUM_KEY: IS_OR_NOT_ENUM_LIST,\n  itemCode: renderEnumList(itemList, 'itemCode', 'itemName') // 非枚举数据可以先转为枚举格式\n})\n\n// 处理之后的 columns = [\n//   {\n//     key: IS_OR_NOT_ENUM_KEY,\n//     title: '是否',\n//     type: 'enum',\n//     width: 50,\n//     cellRendererParams: { enumList: IS_OR_NOT_ENUM_LIST } // 如果是对象，这里会是 enumData\n//   },\n//   {\n//     key: 'itemCode',\n//     title: '卷烟',\n//     type: 'select',\n//     width: 50,\n//     cellRendererParams: {\n//       datas: [{ itemCode: '111', itemName: '卷烟1' }, { itemCode: '112', itemName: '卷烟2' }]\n//     }\n//   },\n// ]\n\n\n\n# renderFieldEnums\n\nrenderFieldEnums(fieldList: Array<any>, enumRelation: Object): Array<any>\n\n表单虽然支持传枚举的key，但对于非枚举的数据仍然要自己组织，基于上方表格相同的原因，也封装方法进行统一处理。同理，第一个参数为表单项的定义，第二个参数为表单项 formKey 和枚举的对应关系。使用方法：\n\nimport { renderEnumList, renderFieldEnums } from '@/utils'\n\nlet fieldList = [\n  {\n    title: '发货点',\n    type: 'select',\n    formKey: 'conWhseId',\n    props: { multiple: true, filterable: true },\n  },\n]\n\nconst conWhseList = [{ conWhseId: '111', conWhseName: '仓库1' }]\n\nfieldList = renderFieldEnums(fieldList, {\n  // 属性名和列的 formKey 保持一致\n  conWhseId: renderEnumList(conWhseList, 'conWhseId', 'conWhseName') // 先转为枚举格式，同样支持枚举对象格式\n})\n\n// 处理之后的 fieldList = [\n//   {\n//     title: '发货点',\n//     type: 'select',\n//     formKey: 'conWhseId',\n//     props: { multiple: true, filterable: true },\n//     enumList: [{ conWhseId: '111', conWhseName: '仓库1' }] // 如果是对象，这里会是 enumData\n//   },\n// ]\n",normalizedContent:"提供一些前端公用的枚举，和后端数据转换为枚举的方法。\n\n\n# 是否的枚举\n\n因项目刚启动时，前后端还未正式对接，所以前端定义了是否的枚举，后来x1里也有人定义了是否的枚举，key 为 ind_y_n ，在本项目中两个都可以用。\n\n/** 是否枚举的key */\nexport const is_or_not_enum_key = 'is_enum'\n/** 是否枚举的map形式 */\nexport const is_or_not_enum = { '1': '是', '0': '否' }\n/** 是否枚举的list形式 */\nexport const is_or_not_enum_list = [\n  { k: '1', v: '是' },\n  { k: '0', v: '否' },\n]\n\n\n一些碎碎念\n\n本人看到后端起名为 ind_y_n 后，一度想把 is_or_not_enum 改为 y_n_enum 。\n\n\n# 中台相关枚举\n\n定义了一些中台管理单元、组织类型、组织级别等的枚举，各业务代码不要写死，万一以后要大改呢→_→\n\nexport const uc_enum = {\n  manage_unit_id: '00000000000000000000000020370001', // 管理单元id\n  org_code_admin: 'default',                          // 行政树类型\n  org_code_mkt: '08',                                 // 营销业务树类型\n  org_code_log: '08',                                 // 物流业务树类型，之前说要分开建的，现在和营销一个树\n  org_code_activity: '08',                            // 宣促业务树类型，之前说要分开建的，现在和营销一个树\n  admin_level_ind: '01',                              // 行政树组织级别 - 工业公司\n  admin_level_center: '02',                           // 行政树组织级别 - 营销中心\n  admin_level_fac: '03',                              // 行政树组织级别 - 卷烟厂\n  admin_level_dist: '04',                             // 行政树组织级别 - 区域\n  admin_level_common_dept: '08',                      // 行政树组织级别 - 普通部门\n  admin_level_common_post: '80',                      // 行政树组织级别 - 普通岗位\n  level_ind: '01',                                    // 业务树组织级别 - 工业公司\n  level_center: '02',                                 // 业务树组织级别 - 营销中心\n  level_fac: '02',                                    // 业务树组织级别 - 卷烟厂\n  level_dist: '04',                                   // 业务树组织级别 - 普通卷烟营销的区域\n  level_cigar_dist: '05',                             // 业务树组织级别 - 雪茄片区\n  level_common_dept: '03',                            // 业务树组织级别 - 普通部门\n  level_common_post: '80',                            // 业务树组织级别 - 普通岗位\n  level_dist_manage_post: '81',                       // 业务树组织级别 - 区域经理岗位\n  level_cust_manage_post: '82',                       // 业务树组织级别 - 客户经理岗位\n  level_cigar_dist_manage_post: '83',                 // 业务树组织级别 - 雪茄片区经理岗位\n  level_cigar_cust_manage_post: '84',                 // 业务树组织级别 - 雪茄客户经理岗位\n  level_internal_post: '85',                          // 业务树组织级别 - 内勤岗位\n}\n\n\n# 样例\n\nimport { uc_enum } from '@indfnd/utils'\n\nconst datepermittype = uc_enum.level_dist_manage_post // 81\n\n\n\n# renderenumlist\n\nrenderenumlist(list: array<any>, kprop: string, vprop: string)\n\n将列表数据转换为枚举的列表格式，即 [{ k: 'key', v: 'value' }] ，kprop 为列表中 key 对应的属性名，vprop 为 value 对应的属性名，使用方法：\n\nimport { renderenumlist } from '@indfnd/utils'\n\nconst list = [{ brandid: '0916', brandname: '泰山' }]\nconst enumlist = renderenumlist(list, 'brandid', 'brandname') // [{ k: '0916', v: '泰山' }]\n\n\n\n# renderenumdata\n\nrenderenumdata(list: array<any>, kprop: string, vprop: string)\n\n将列表数据转换为枚举的对象格式，即 { 'key': 'value' } ，kprop 为列表中 key 对应的属性名，vprop 为 value 对应的属性名，使用方法：\n\nimport { renderenumdata } from '@indfnd/utils'\n\nconst list = [{ brandid: '0916', brandname: '泰山' }]\nconst enumdata = renderenumdata(list, 'brandid', 'brandname') // { '0916': '泰山' }\n\n\n\n# rendercolumnenums\n\nrendercolumnenums(columns: array<any>, enumrelation: object): array<any>\n\n由于表格组件不方便获取枚举数据（时机不好确定），需要各业务先获取枚举数据后，重新组织列定义，有多种方案：\n\n * 列定义写在 computed 中，获取到数据列定义会自动更新，本人觉得这样用起来很怪。\n * 先不组织列定义，获取到数据后再组织，这样在获取到数据前表格是没有列的，看起来也很怪。\n * 先定义列定义，获取到数据后重新组织所有列，本人觉得这样太麻烦了。\n\n所以封装了本方法，本方法不能响应式修改定义，需要重新赋值。第一个参数是列定义，第二个参数是列的 key 和其枚举的对应关系，使用方法：\n\nimport {\n  is_or_not_enum_key,\n  is_or_not_enum,\n  is_or_not_enum_list,\n  renderenumlist,\n  rendercolumnenums\n} from '@/utils'\n\nlet columns = [\n  {\n    key: is_or_not_enum_key,\n    title: '是否',\n    type: 'enum', // 需注意，type 必须是 enum 或 select\n    width: 50,\n  },\n  {\n    key: 'itemcode',\n    title: '卷烟',\n    type: 'select', // 需注意，type 必须是 enum 或 select\n    width: 50,\n  },\n]\n\nconst itemlist = [{ itemcode: '111', itemname: '卷烟1' }, { itemcode: '112', itemname: '卷烟2' }]\n\ncolumns = rendercolumnenums(columns, {\n  // 属性名和列的 key 保持一致\n  // enum 时 is_or_not_enum 也可以，列表格式和对象格式都可以\n  // select 只支持列表格式或返回列表格式的函数\n  is_or_not_enum_key: is_or_not_enum_list,\n  itemcode: renderenumlist(itemlist, 'itemcode', 'itemname') // 非枚举数据可以先转为枚举格式\n})\n\n// 处理之后的 columns = [\n//   {\n//     key: is_or_not_enum_key,\n//     title: '是否',\n//     type: 'enum',\n//     width: 50,\n//     cellrendererparams: { enumlist: is_or_not_enum_list } // 如果是对象，这里会是 enumdata\n//   },\n//   {\n//     key: 'itemcode',\n//     title: '卷烟',\n//     type: 'select',\n//     width: 50,\n//     cellrendererparams: {\n//       datas: [{ itemcode: '111', itemname: '卷烟1' }, { itemcode: '112', itemname: '卷烟2' }]\n//     }\n//   },\n// ]\n\n\n\n# renderfieldenums\n\nrenderfieldenums(fieldlist: array<any>, enumrelation: object): array<any>\n\n表单虽然支持传枚举的key，但对于非枚举的数据仍然要自己组织，基于上方表格相同的原因，也封装方法进行统一处理。同理，第一个参数为表单项的定义，第二个参数为表单项 formkey 和枚举的对应关系。使用方法：\n\nimport { renderenumlist, renderfieldenums } from '@/utils'\n\nlet fieldlist = [\n  {\n    title: '发货点',\n    type: 'select',\n    formkey: 'conwhseid',\n    props: { multiple: true, filterable: true },\n  },\n]\n\nconst conwhselist = [{ conwhseid: '111', conwhsename: '仓库1' }]\n\nfieldlist = renderfieldenums(fieldlist, {\n  // 属性名和列的 formkey 保持一致\n  conwhseid: renderenumlist(conwhselist, 'conwhseid', 'conwhsename') // 先转为枚举格式，同样支持枚举对象格式\n})\n\n// 处理之后的 fieldlist = [\n//   {\n//     title: '发货点',\n//     type: 'select',\n//     formkey: 'conwhseid',\n//     props: { multiple: true, filterable: true },\n//     enumlist: [{ conwhseid: '111', conwhsename: '仓库1' }] // 如果是对象，这里会是 enumdata\n//   },\n// ]\n",charsets:{cjk:!0},lastUpdated:"2024-01-24 18:07:31",lastUpdatedTimestamp:1706090851e3},{title:"半年",frontmatter:{title:"半年",date:"2024-01-24T08:12:35.000Z",permalink:"/pages/b93470/"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/09.%E5%8D%8A%E5%B9%B4.html",relativePath:"00.指南/01.工具库/09.半年.md",key:"v-edb78c74",path:"/pages/b93470/",headers:[{level:2,title:"getHalfYear",slug:"gethalfyear",normalizedTitle:"gethalfyear",charIndex:16},{level:2,title:"getHalfYearNum",slug:"gethalfyearnum",normalizedTitle:"gethalfyearnum",charIndex:100},{level:2,title:"formatHalfYear",slug:"formathalfyear",normalizedTitle:"formathalfyear",charIndex:196},{level:2,title:"getHalfYearBeginMonth",slug:"gethalfyearbeginmonth",normalizedTitle:"gethalfyearbeginmonth",charIndex:290},{level:2,title:"getHalfYearEndMonth",slug:"gethalfyearendmonth",normalizedTitle:"gethalfyearendmonth",charIndex:396}],headersStr:"getHalfYear getHalfYearNum formatHalfYear getHalfYearBeginMonth getHalfYearEndMonth",content:"提供半年相关的处理方法\n\n\n# getHalfYear\n\ngetHalfYear(date: string): string\n\n获取日期或月份的半年编码，如 202401 返回 2024H1\n\n\n# getHalfYearNum\n\ngetHalfYearNum(month: number | string): number\n\n获取月份所在上下半年的数字，如 202401 返回 1\n\n\n# formatHalfYear\n\nformatHalfYear(quarter: string): string\n\n根据半年编码获取半年名称，如 2024H1 返回 2024上半年\n\n\n# getHalfYearBeginMonth\n\ngetHalfYearBeginMonth(quarter: string): string\n\n获取半年所在的开始月份，如 2024H1 返回 202401\n\n\n# getHalfYearEndMonth\n\ngetHalfYearEndMonth(quarter: string): string\n\n获取半年所在的结束月份，如 2024H1 返回 202406",normalizedContent:"提供半年相关的处理方法\n\n\n# gethalfyear\n\ngethalfyear(date: string): string\n\n获取日期或月份的半年编码，如 202401 返回 2024h1\n\n\n# gethalfyearnum\n\ngethalfyearnum(month: number | string): number\n\n获取月份所在上下半年的数字，如 202401 返回 1\n\n\n# formathalfyear\n\nformathalfyear(quarter: string): string\n\n根据半年编码获取半年名称，如 2024h1 返回 2024上半年\n\n\n# gethalfyearbeginmonth\n\ngethalfyearbeginmonth(quarter: string): string\n\n获取半年所在的开始月份，如 2024h1 返回 202401\n\n\n# gethalfyearendmonth\n\ngethalfyearendmonth(quarter: string): string\n\n获取半年所在的结束月份，如 2024h1 返回 202406",charsets:{cjk:!0},lastUpdated:"2024-01-24 08:25:00",lastUpdatedTimestamp:17060559e5},{title:"UUID",frontmatter:{title:"UUID",date:"2024-01-06T20:39:24.000Z",permalink:"/pages/57e307/"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/11.UUID.html",relativePath:"00.指南/01.工具库/11.UUID.md",key:"v-b40f85f4",path:"/pages/57e307/",headers:[{level:2,title:"uuid",slug:"uuid",normalizedTitle:"uuid",charIndex:18},{level:2,title:"guid",slug:"guid",normalizedTitle:"guid",charIndex:211}],headersStr:"uuid guid",content:"提供生成 UUID 的方法\n\n\n# uuid\n\n生成字母表为 0123456789abcdef 的uuid，支持指定长度，格式如 f72b14877cf3a35a249922ff4e3dde49\n\n# 样例\n\nimport { uuid } from '@indfnd/utils'\n\nconst id = uuid() // 默认长度为32\nconst shortId = uuid(16) // 长度为16\n\n\n\n# guid\n\n目前生成的是32位带 - 的 UUID，如 45e19ad8-2a27-4146-a277-f952797ac35a ，生成登录验证码使用",normalizedContent:"提供生成 uuid 的方法\n\n\n# uuid\n\n生成字母表为 0123456789abcdef 的uuid，支持指定长度，格式如 f72b14877cf3a35a249922ff4e3dde49\n\n# 样例\n\nimport { uuid } from '@indfnd/utils'\n\nconst id = uuid() // 默认长度为32\nconst shortid = uuid(16) // 长度为16\n\n\n\n# guid\n\n目前生成的是32位带 - 的 uuid，如 45e19ad8-2a27-4146-a277-f952797ac35a ，生成登录验证码使用",charsets:{cjk:!0},lastUpdated:"2024-01-31 09:28:55",lastUpdatedTimestamp:1706664535e3},{title:"季度",frontmatter:{title:"季度",date:"2024-01-06T09:16:58.000Z",permalink:"/pages/quarter"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/10.%E5%AD%A3%E5%BA%A6.html",relativePath:"00.指南/01.工具库/10.季度.md",key:"v-7e0ed6b0",path:"/pages/quarter/",headers:[{level:2,title:"quarter2Chinese",slug:"quarter2chinese",normalizedTitle:"quarter2chinese",charIndex:16},{level:2,title:"getQuarter",slug:"getquarter",normalizedTitle:"getquarter",charIndex:118},{level:2,title:"getQuarterNum",slug:"getquarternum",normalizedTitle:"getquarternum",charIndex:200},{level:2,title:"formatQuarter",slug:"formatquarter",normalizedTitle:"formatquarter",charIndex:291},{level:2,title:"getQuarterBeginMonth",slug:"getquarterbeginmonth",normalizedTitle:"getquarterbeginmonth",charIndex:384},{level:2,title:"getQuarterEndMonth",slug:"getquarterendmonth",normalizedTitle:"getquarterendmonth",charIndex:488}],headersStr:"quarter2Chinese getQuarter getQuarterNum formatQuarter getQuarterBeginMonth getQuarterEndMonth",content:"提供季度相关的处理方法\n\n\n# quarter2Chinese\n\nquarter2Chinese(quarterNum: number): string\n\n将数字转为季度汉字，如 1 返回 一 ，用于根据季度编码得到季度名称。\n\n\n# getQuarter\n\ngetQuarter(date: string): string\n\n获取日期或月份的季度编码，如 202401 返回 2024Q1\n\n\n# getQuarterNum\n\ngetQuarterNum(month: number | string): number\n\n获取月份所在第几季度，如 202401 返回 1\n\n\n# formatQuarter\n\nformatQuarter(quarter: string): string\n\n根据季度编码获取季度名称，如 2024Q1 返回 2024第一季度\n\n\n# getQuarterBeginMonth\n\ngetQuarterBeginMonth(quarter: string): string\n\n获取季度所在的开始月份，如 2024Q1 返回 202401\n\n\n# getQuarterEndMonth\n\ngetQuarterEndMonth(quarter: string): string\n\n获取季度所在的结束月份，如 2024Q1 返回 202403",normalizedContent:"提供季度相关的处理方法\n\n\n# quarter2chinese\n\nquarter2chinese(quarternum: number): string\n\n将数字转为季度汉字，如 1 返回 一 ，用于根据季度编码得到季度名称。\n\n\n# getquarter\n\ngetquarter(date: string): string\n\n获取日期或月份的季度编码，如 202401 返回 2024q1\n\n\n# getquarternum\n\ngetquarternum(month: number | string): number\n\n获取月份所在第几季度，如 202401 返回 1\n\n\n# formatquarter\n\nformatquarter(quarter: string): string\n\n根据季度编码获取季度名称，如 2024q1 返回 2024第一季度\n\n\n# getquarterbeginmonth\n\ngetquarterbeginmonth(quarter: string): string\n\n获取季度所在的开始月份，如 2024q1 返回 202401\n\n\n# getquarterendmonth\n\ngetquarterendmonth(quarter: string): string\n\n获取季度所在的结束月份，如 2024q1 返回 202403",charsets:{cjk:!0},lastUpdated:"2024-01-24 08:25:00",lastUpdatedTimestamp:17060559e5},{title:"MIME types",frontmatter:{title:"MIME types",date:"2024-01-06T09:31:57.000Z",permalink:"/pages/mime-type"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/12.MIME%20types.html",relativePath:"00.指南/01.工具库/12.MIME types.md",key:"v-4d3d5367",path:"/pages/mime-type/",headersStr:null,content:"定义了一些常用文件类型的 MIME type，用于上传附件时限制可选文件类型等。\n\nexport const MIME_TYPE = {\n  aac: 'audio/aac',\n  abw: 'application/x-abiword',\n  arc: 'application/x-freearc',\n  avi: 'video/x-msvideo',\n  azw: 'application/vnd.amazon.ebook',\n  bin: 'application/octet-stream',\n  bmp: 'image/bmp',\n  bz: 'application/x-bzip',\n  bz2: 'application/x-bzip2',\n  csh: 'application/x-csh',\n  css: 'text/css',\n  csv: 'text/csv',\n  doc: 'application/msword',\n  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n  eot: 'application/vnd.ms-fontobject',\n  epub: 'application/epub+zip',\n  gif: 'image/gif',\n  html: 'text/html',\n  ico: 'image/vnd.microsoft.icon',\n  ics: 'text/calendar',\n  jar: 'application/java-archive',\n  jpg: 'image/jpeg',\n  jpeg: 'image/jpeg',\n  js: 'text/javascript',\n  json: 'application/json',\n  jsonld: 'application/ld+json',\n  mid: 'audio/midi',\n  midi: 'audio/x-midi',\n  mjs: 'text/javascript',\n  mp3: 'audio/mpeg',\n  mpeg: 'video/mpeg',\n  mpkg: 'application/vnd.apple.installer+xml',\n  odp: 'application/vnd.oasis.opendocument.presentation',\n  ods: 'application/vnd.oasis.opendocument.spreadsheet',\n  odt: 'application/vnd.oasis.opendocument.text',\n  oga: 'audio/ogg',\n  ogv: 'video/ogg',\n  ogx: 'application/ogg',\n  otf: 'font/otf',\n  png: 'image/png',\n  pdf: 'application/pdf',\n  ppt: 'application/vnd.ms-powerpoint',\n  pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n  rar: 'application/x-rar-compressed',\n  rtf: 'application/rtf',\n  sh: 'application/x-sh',\n  svg: 'image/svg+xml',\n  swf: 'application/x-shockwave-flash',\n  tar: 'application/x-tar',\n  tiff: 'image/tiff',\n  ttf: 'font/ttf',\n  txt: 'text/plain',\n  vsd: 'application/vnd.visio',\n  wav: 'audio/wav',\n  weba: 'audio/webm',\n  webm: 'video/webm',\n  webp: 'image/webp',\n  woff: 'font/woff',\n  woff2: 'font/woff2',\n  xhtml: 'application/xhtml+xml',\n  xls: 'application/vnd.ms-excel',\n  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  xul: 'application/vnd.mozilla.xul+xml',\n  zip: 'application/zip',\n  '7z': 'application/x-7z-compressed',\n}\n",normalizedContent:"定义了一些常用文件类型的 mime type，用于上传附件时限制可选文件类型等。\n\nexport const mime_type = {\n  aac: 'audio/aac',\n  abw: 'application/x-abiword',\n  arc: 'application/x-freearc',\n  avi: 'video/x-msvideo',\n  azw: 'application/vnd.amazon.ebook',\n  bin: 'application/octet-stream',\n  bmp: 'image/bmp',\n  bz: 'application/x-bzip',\n  bz2: 'application/x-bzip2',\n  csh: 'application/x-csh',\n  css: 'text/css',\n  csv: 'text/csv',\n  doc: 'application/msword',\n  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n  eot: 'application/vnd.ms-fontobject',\n  epub: 'application/epub+zip',\n  gif: 'image/gif',\n  html: 'text/html',\n  ico: 'image/vnd.microsoft.icon',\n  ics: 'text/calendar',\n  jar: 'application/java-archive',\n  jpg: 'image/jpeg',\n  jpeg: 'image/jpeg',\n  js: 'text/javascript',\n  json: 'application/json',\n  jsonld: 'application/ld+json',\n  mid: 'audio/midi',\n  midi: 'audio/x-midi',\n  mjs: 'text/javascript',\n  mp3: 'audio/mpeg',\n  mpeg: 'video/mpeg',\n  mpkg: 'application/vnd.apple.installer+xml',\n  odp: 'application/vnd.oasis.opendocument.presentation',\n  ods: 'application/vnd.oasis.opendocument.spreadsheet',\n  odt: 'application/vnd.oasis.opendocument.text',\n  oga: 'audio/ogg',\n  ogv: 'video/ogg',\n  ogx: 'application/ogg',\n  otf: 'font/otf',\n  png: 'image/png',\n  pdf: 'application/pdf',\n  ppt: 'application/vnd.ms-powerpoint',\n  pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n  rar: 'application/x-rar-compressed',\n  rtf: 'application/rtf',\n  sh: 'application/x-sh',\n  svg: 'image/svg+xml',\n  swf: 'application/x-shockwave-flash',\n  tar: 'application/x-tar',\n  tiff: 'image/tiff',\n  ttf: 'font/ttf',\n  txt: 'text/plain',\n  vsd: 'application/vnd.visio',\n  wav: 'audio/wav',\n  weba: 'audio/webm',\n  webm: 'video/webm',\n  webp: 'image/webp',\n  woff: 'font/woff',\n  woff2: 'font/woff2',\n  xhtml: 'application/xhtml+xml',\n  xls: 'application/vnd.ms-excel',\n  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  xul: 'application/vnd.mozilla.xul+xml',\n  zip: 'application/zip',\n  '7z': 'application/x-7z-compressed',\n}\n",charsets:{cjk:!0},lastUpdated:"2024-01-24 08:25:00",lastUpdatedTimestamp:17060559e5},{title:"Event",frontmatter:{title:"Event",date:"2024-01-06T09:18:53.000Z",permalink:"/pages/event"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/13.Event.html",relativePath:"00.指南/01.工具库/13.Event.md",key:"v-79192e6c",path:"/pages/event/",headers:[{level:2,title:"on",slug:"on",normalizedTitle:"on",charIndex:22},{level:2,title:"off",slug:"off",normalizedTitle:"off",charIndex:101},{level:2,title:"stopPropagation",slug:"stoppropagation",normalizedTitle:"stoppropagation",charIndex:182},{level:2,title:"preventDefault",slug:"preventdefault",normalizedTitle:"preventdefault",charIndex:245}],headersStr:"on off stopPropagation preventDefault",content:"监听 DOM 事件与取消监听的方法\n\n\n# on\n\non(element: Element, event: string, handler: Function): Function\n\n绑定事件\n\n\n# off\n\noff(element: Element, event: string, handler: Function): Function\n\n解绑事件\n\n\n# stopPropagation\n\nstopPropagation(event: Event)\n\n阻止事件的捕获或冒泡\n\n\n# preventDefault\n\npreventDefault(event: Event, isStopPropagation?: Boolean)\n\n阻止事件的默认行为\n\n# 参数\n\n * event: 事件名称\n * isStopPropagation: 是否同时阻止冒泡",normalizedContent:"监听 dom 事件与取消监听的方法\n\n\n# on\n\non(element: element, event: string, handler: function): function\n\n绑定事件\n\n\n# off\n\noff(element: element, event: string, handler: function): function\n\n解绑事件\n\n\n# stoppropagation\n\nstoppropagation(event: event)\n\n阻止事件的捕获或冒泡\n\n\n# preventdefault\n\npreventdefault(event: event, isstoppropagation?: boolean)\n\n阻止事件的默认行为\n\n# 参数\n\n * event: 事件名称\n * isstoppropagation: 是否同时阻止冒泡",charsets:{cjk:!0},lastUpdated:"2024-03-03 19:30:39",lastUpdatedTimestamp:1709465439e3},{title:"校验",frontmatter:{title:"校验",date:"2024-01-06T09:19:26.000Z",permalink:"/pages/validate"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/14.%E6%A0%A1%E9%AA%8C.html",relativePath:"00.指南/01.工具库/14.校验.md",key:"v-687254ec",path:"/pages/validate/",headers:[{level:2,title:"checkIdCard",slug:"checkidcard",normalizedTitle:"checkidcard",charIndex:13},{level:2,title:"checkPhone",slug:"checkphone",normalizedTitle:"checkphone",charIndex:107},{level:2,title:"checkTel",slug:"checktel",normalizedTitle:"checktel",charIndex:180},{level:2,title:"checkVehicleNo",slug:"checkvehicleno",normalizedTitle:"checkvehicleno",charIndex:246}],headersStr:"checkIdCard checkPhone checkTel checkVehicleNo",content:"提供一些校验方法\n\n\n# checkIdCard\n\ncheckIdCard(idcard: string): string\n\n校验身份证号。若校验成功，返回 验证通过! ；若不成功，返回具体的不通过原因。\n\n\n# checkPhone\n\ncheckPhone(str: string): boolean\n\n校验是否是手机号，目前只支持11位的手机号。\n\n\n# checkTel\n\ncheckTel(val: string): boolean\n\n校验是否是电话号码，支持座机和手机号。\n\n\n# checkVehicleNo\n\ncheckVehicleNo(str: string): boolean\n\n校验车牌号，支持新能源车和非新能源车，如 鲁B-6X810 和 鲁B-D60810",normalizedContent:"提供一些校验方法\n\n\n# checkidcard\n\ncheckidcard(idcard: string): string\n\n校验身份证号。若校验成功，返回 验证通过! ；若不成功，返回具体的不通过原因。\n\n\n# checkphone\n\ncheckphone(str: string): boolean\n\n校验是否是手机号，目前只支持11位的手机号。\n\n\n# checktel\n\nchecktel(val: string): boolean\n\n校验是否是电话号码，支持座机和手机号。\n\n\n# checkvehicleno\n\ncheckvehicleno(str: string): boolean\n\n校验车牌号，支持新能源车和非新能源车，如 鲁b-6x810 和 鲁b-d60810",charsets:{cjk:!0},lastUpdated:"2024-01-24 08:25:00",lastUpdatedTimestamp:17060559e5},{title:"表格",frontmatter:{title:"表格",date:"2024-01-06T09:19:38.000Z",permalink:"/pages/table"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/15.%E8%A1%A8%E6%A0%BC.html",relativePath:"00.指南/01.工具库/15.表格.md",key:"v-62a65d27",path:"/pages/table/",headers:[{level:2,title:"row2column - 行转列",slug:"row2column-行转列",normalizedTitle:"row2column - 行转列",charIndex:14},{level:2,title:"flattenRow2ColumnData - 解构行转列",slug:"flattenrow2columndata-解构行转列",normalizedTitle:"flattenrow2columndata - 解构行转列",charIndex:1543},{level:2,title:"类型",slug:"类型",normalizedTitle:"类型",charIndex:203},{level:3,title:"Column - 列定义",slug:"column-列定义",normalizedTitle:"column - 列定义",charIndex:2009},{level:3,title:"ColumnGroup - 动态列分组定义",slug:"columngroup-动态列分组定义",normalizedTitle:"columngroup - 动态列分组定义",charIndex:2122},{level:3,title:"row2columnOption - 转动态列的配置",slug:"row2columnoption-转动态列的配置",normalizedTitle:"row2columnoption - 转动态列的配置",charIndex:3845},{level:3,title:"row2columnResult - 返回数据格式",slug:"row2columnresult-返回数据格式",normalizedTitle:"row2columnresult - 返回数据格式",charIndex:4093}],headersStr:"row2column - 行转列 flattenRow2ColumnData - 解构行转列 类型 Column - 列定义 ColumnGroup - 动态列分组定义 row2columnOption - 转动态列的配置 row2columnResult - 返回数据格式",content:"提供表格的公共方法\n\n\n# row2column - 行转列\n\nrow2column(data: Array, columnGroups: Array<ColumnGroup>, rowKey: string, option?: row2columnOption): row2columnResult\n\n# 参数\n\n * data: 全量的行数据\n * columnGroups: 行转列的动态列分组定义，类型定义请看 ColumnGroup\n * rowKey: 行转列目标结果的行主键属性，将以这个属性将相关数据聚合到一行上\n * option: 一些行转列的配置参数，类型定义请看 row2columnOption\n\n# 返回\n\n * columns: 行转列后的列定义\n * data: 行转列后的表格数据\n\n# 样例\n\n<template>\n  <div class=\"language-json\">\n    <pre class=\"language-json\">\n      <code>{{ rlt }}</code>\n    </pre>\n  </div>\n</template>\n\n<script>\nimport { row2column } from '@indfnd/utils'\n\nconst allData = [\n  { comId: '11370101', comName: '济南', year: '2022', qty: 110, qtySame: 100, qtyIncPer: 10 },\n  { comId: '11370201', comName: '青岛', year: '2022', qty: 60, qtySame: 50, qtyIncPer: 20 },\n]\n\nconst columnGroups = [\n  {\n    keyProp: 'year',\n    titleProp: 'year',\n    titleFormatter: (title) => `${title}年`,\n    children: [\n      {\n        // 多层指标时，列定义的 key 会直接和下层的 key 拼在一起，中间没有分隔符\n        // 如 qty + Same = qtySame ，处理数据时会去找 qtySame 对应的值\n        // 当然，你也可以在这一层传空字符串，在最后一层传指标的完整属性名\n        // 但多种类型的指标都有本期同期增幅就无法复用了哦\n        key: 'qty',\n        title: '销量',\n        children: [\n          { key: '', title: '本期', width: 50, align: 'right' },\n          { key: 'Same', title: '同期', width: 50, align: 'right' },\n          { key: 'IncPer', title: '增幅(%)', width: 50, align: 'right' },\n        ],\n      },\n    ],\n  },\n]\n\nexport default {\n  data() {\n    return {\n      rlt: null,\n    }\n  },\n  created() {\n    this.rlt = row2column(allData, columnGroups, 'comId')\n  },\n}\n<\/script>\n\n\n# flattenRow2ColumnData - 解构行转列\n\nflattenRow2ColumnData(data: Array<any>, columns: Array<Column>, option: row2columnOption): Array<any>\n\n# 参数\n\n * data: 行转列的数据\n * columns: 使用 row2column 得到的列定义，或与之格式一致的列定义，如 comCode--11370101__itemCode--1111__qtyWx ，以 __ 分割不同层级的动态列，以 -- 分割属性名和属性值，最后一层是指标\n * option: 一些配置参数\n\n# 返回\n\n * data: 将行转列的数据解构得到的行数据\n\n提示\n\n需注意，动态列对应的 title 解构的大概率是不对的，因为列定义中只保留了动态列的 key ，而没有保留 title ，从当前列定义格式中，无法判断哪一个属性是 title 。\nkey 和指标数据是没问题的，因为列定义中指明了。\n\n\n# 类型\n\n\n# Column - 列定义\n\n属性          类型\nkey         string\ntitle       string\nchildren    Array<Column>\n...others   any\n\n\n# ColumnGroup - 动态列分组定义\n\n属性                类型                                                           说明\nkey               string                                                       和 title 配套使用，用于动态列中指标数据的列分组，如每个动态列都有的销量、销额等指标\ntitle             string                                                       和 key 配套使用\nkeyProp           string                                                       和 titleProp 配套使用，用于动态数据有关的列分组，如工业、卷烟等\ntitleProp         string                                                       和 keyProp 配套使用\ntitleFormatter    (title: string) => string                                    和 titleProp 配套使用，用于处理动态列数据的列名，如年度表头后加 “年”\nsortProp          string                                                       动态列转换为表头时排序的属性名，不指定则以数据的顺序排序\nsortOrder         boolean, 'asc', 'desc'                                       动态列转换表头时排序的顺序，默认 asc ，只当 sortProp 指定时才有效\nisLimitChildren   string                                                       是否限制下层表头的数据要基于本层的数据，如省份下的地市只有该省的地市\nheaderTooltip     boolean | string | ((data: any, parents: Array) => string)   鼠标悬浮文字；true时展示表头文字，string类型时展示传过来的对应文字，函数展示函数返回值\nkeyLastSuffix     string                                                       组织动态列列定义 key\n                                                                               的后缀，用于最后一层的动态列只有一个指标，此时大概率是不想展示两行表头的，这个配置维护对应的指标属性名即可\nchildren          Array<Column>                                                每层分组下继续分组，如第一行以时间分组，第二行以卷烟分组，第三行是销量指标等，则第二行是第一行的 children\n                                                                               ，第三行是第二行的 children ，之所以是数组，就是因为指标会有多个\n...others         any                                                          其它参数，会原封不动地组组织到最后的列定义中，如 width、align 等\n\n\n# row2columnOption - 转动态列的配置\n\n属性              类型       说明\nkeyPropName     string   表格列定义中 key 对应的属性名，如 view-design 为 key ，ag-grid 为 field\n                         (我们的表格做了兼容，传 key 就可以)，默认为 key\ntitlePropName   string   表格列定义中 title 对应属性名，默认 title\n\n\n# row2columnResult - 返回数据格式\n\n属性        类型\ncolumns   Array<Column>\ndata      Array<any>",normalizedContent:"提供表格的公共方法\n\n\n# row2column - 行转列\n\nrow2column(data: array, columngroups: array<columngroup>, rowkey: string, option?: row2columnoption): row2columnresult\n\n# 参数\n\n * data: 全量的行数据\n * columngroups: 行转列的动态列分组定义，类型定义请看 columngroup\n * rowkey: 行转列目标结果的行主键属性，将以这个属性将相关数据聚合到一行上\n * option: 一些行转列的配置参数，类型定义请看 row2columnoption\n\n# 返回\n\n * columns: 行转列后的列定义\n * data: 行转列后的表格数据\n\n# 样例\n\n<template>\n  <div class=\"language-json\">\n    <pre class=\"language-json\">\n      <code>{{ rlt }}</code>\n    </pre>\n  </div>\n</template>\n\n<script>\nimport { row2column } from '@indfnd/utils'\n\nconst alldata = [\n  { comid: '11370101', comname: '济南', year: '2022', qty: 110, qtysame: 100, qtyincper: 10 },\n  { comid: '11370201', comname: '青岛', year: '2022', qty: 60, qtysame: 50, qtyincper: 20 },\n]\n\nconst columngroups = [\n  {\n    keyprop: 'year',\n    titleprop: 'year',\n    titleformatter: (title) => `${title}年`,\n    children: [\n      {\n        // 多层指标时，列定义的 key 会直接和下层的 key 拼在一起，中间没有分隔符\n        // 如 qty + same = qtysame ，处理数据时会去找 qtysame 对应的值\n        // 当然，你也可以在这一层传空字符串，在最后一层传指标的完整属性名\n        // 但多种类型的指标都有本期同期增幅就无法复用了哦\n        key: 'qty',\n        title: '销量',\n        children: [\n          { key: '', title: '本期', width: 50, align: 'right' },\n          { key: 'same', title: '同期', width: 50, align: 'right' },\n          { key: 'incper', title: '增幅(%)', width: 50, align: 'right' },\n        ],\n      },\n    ],\n  },\n]\n\nexport default {\n  data() {\n    return {\n      rlt: null,\n    }\n  },\n  created() {\n    this.rlt = row2column(alldata, columngroups, 'comid')\n  },\n}\n<\/script>\n\n\n# flattenrow2columndata - 解构行转列\n\nflattenrow2columndata(data: array<any>, columns: array<column>, option: row2columnoption): array<any>\n\n# 参数\n\n * data: 行转列的数据\n * columns: 使用 row2column 得到的列定义，或与之格式一致的列定义，如 comcode--11370101__itemcode--1111__qtywx ，以 __ 分割不同层级的动态列，以 -- 分割属性名和属性值，最后一层是指标\n * option: 一些配置参数\n\n# 返回\n\n * data: 将行转列的数据解构得到的行数据\n\n提示\n\n需注意，动态列对应的 title 解构的大概率是不对的，因为列定义中只保留了动态列的 key ，而没有保留 title ，从当前列定义格式中，无法判断哪一个属性是 title 。\nkey 和指标数据是没问题的，因为列定义中指明了。\n\n\n# 类型\n\n\n# column - 列定义\n\n属性          类型\nkey         string\ntitle       string\nchildren    array<column>\n...others   any\n\n\n# columngroup - 动态列分组定义\n\n属性                类型                                                           说明\nkey               string                                                       和 title 配套使用，用于动态列中指标数据的列分组，如每个动态列都有的销量、销额等指标\ntitle             string                                                       和 key 配套使用\nkeyprop           string                                                       和 titleprop 配套使用，用于动态数据有关的列分组，如工业、卷烟等\ntitleprop         string                                                       和 keyprop 配套使用\ntitleformatter    (title: string) => string                                    和 titleprop 配套使用，用于处理动态列数据的列名，如年度表头后加 “年”\nsortprop          string                                                       动态列转换为表头时排序的属性名，不指定则以数据的顺序排序\nsortorder         boolean, 'asc', 'desc'                                       动态列转换表头时排序的顺序，默认 asc ，只当 sortprop 指定时才有效\nislimitchildren   string                                                       是否限制下层表头的数据要基于本层的数据，如省份下的地市只有该省的地市\nheadertooltip     boolean | string | ((data: any, parents: array) => string)   鼠标悬浮文字；true时展示表头文字，string类型时展示传过来的对应文字，函数展示函数返回值\nkeylastsuffix     string                                                       组织动态列列定义 key\n                                                                               的后缀，用于最后一层的动态列只有一个指标，此时大概率是不想展示两行表头的，这个配置维护对应的指标属性名即可\nchildren          array<column>                                                每层分组下继续分组，如第一行以时间分组，第二行以卷烟分组，第三行是销量指标等，则第二行是第一行的 children\n                                                                               ，第三行是第二行的 children ，之所以是数组，就是因为指标会有多个\n...others         any                                                          其它参数，会原封不动地组组织到最后的列定义中，如 width、align 等\n\n\n# row2columnoption - 转动态列的配置\n\n属性              类型       说明\nkeypropname     string   表格列定义中 key 对应的属性名，如 view-design 为 key ，ag-grid 为 field\n                         (我们的表格做了兼容，传 key 就可以)，默认为 key\ntitlepropname   string   表格列定义中 title 对应属性名，默认 title\n\n\n# row2columnresult - 返回数据格式\n\n属性        类型\ncolumns   array<column>\ndata      array<any>",charsets:{cjk:!0},lastUpdated:"2024-03-05 09:08:09",lastUpdatedTimestamp:1709600889e3},{title:"Excel",frontmatter:{title:"Excel",date:"2024-01-06T09:19:48.000Z",permalink:"/pages/excel"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/16.Excel.html",relativePath:"00.指南/01.工具库/16.Excel.md",key:"v-44c7acca",path:"/pages/excel/",headers:[{level:2,title:"exportJsonToExcel",slug:"exportjsontoexcel",normalizedTitle:"exportjsontoexcel",charIndex:24},{level:2,title:"importJsonFromExcel",slug:"importjsonfromexcel",normalizedTitle:"importjsonfromexcel",charIndex:96},{level:2,title:"getExcelColumnIdx",slug:"getexcelcolumnidx",normalizedTitle:"getexcelcolumnidx",charIndex:183},{level:2,title:"类型",slug:"类型",normalizedTitle:"类型",charIndex:323},{level:3,title:"ExcelData",slug:"exceldata",normalizedTitle:"exceldata",charIndex:72}],headersStr:"exportJsonToExcel importJsonFromExcel getExcelColumnIdx 类型 ExcelData",content:"表格导入、导出 Excel 的公共方法\n\n\n# exportJsonToExcel\n\nexportJsonToExcel(excelData: ExcelData)\n\n导出Excel\n\n\n# importJsonFromExcel\n\nimportJsonFromExcel(excelData: ExcelData): Promise\n\n导入Excel数据\n\n\n# getExcelColumnIdx\n\ngetExcelColumnIdx(number: number): string\n\n根据数组的下标返回 excel 对应的列号，目前是按照 .xlsx 的列规则计算的，下标从 0 开始计数，如 0 返回 A ，26 返回 AA 。\n\n\n# 类型\n\n\n# ExcelData\n\n# 参数\n\n * title: 导出的 excel 名字\n * columns: 列定义\n * columnWidth: 列宽，数组\n * columnAlign: 对齐方式，数组，可选值：\n   * center | left | right\n * columnType: 列数据格式，数组，可选值：\n   * s: String\n   * n: Number\n * datas: 数据\n * paramLeft: 标题下左侧提示，一般是时间，如： 查询时间：2022年01月-2022年05月\n * paramRight: 标题下右侧提示，一般是单位，如： 箱、万元\n * numberFormat: 数字格式，数组，如 '0.00'\n * rowSpanColumns: 需要跨行的列\n * rowSpanIndexCol: 根据哪一列进行跨行计算",normalizedContent:"表格导入、导出 excel 的公共方法\n\n\n# exportjsontoexcel\n\nexportjsontoexcel(exceldata: exceldata)\n\n导出excel\n\n\n# importjsonfromexcel\n\nimportjsonfromexcel(exceldata: exceldata): promise\n\n导入excel数据\n\n\n# getexcelcolumnidx\n\ngetexcelcolumnidx(number: number): string\n\n根据数组的下标返回 excel 对应的列号，目前是按照 .xlsx 的列规则计算的，下标从 0 开始计数，如 0 返回 a ，26 返回 aa 。\n\n\n# 类型\n\n\n# exceldata\n\n# 参数\n\n * title: 导出的 excel 名字\n * columns: 列定义\n * columnwidth: 列宽，数组\n * columnalign: 对齐方式，数组，可选值：\n   * center | left | right\n * columntype: 列数据格式，数组，可选值：\n   * s: string\n   * n: number\n * datas: 数据\n * paramleft: 标题下左侧提示，一般是时间，如： 查询时间：2022年01月-2022年05月\n * paramright: 标题下右侧提示，一般是单位，如： 箱、万元\n * numberformat: 数字格式，数组，如 '0.00'\n * rowspancolumns: 需要跨行的列\n * rowspanindexcol: 根据哪一列进行跨行计算",charsets:{cjk:!0},lastUpdated:"2024-01-24 08:25:00",lastUpdatedTimestamp:17060559e5},{title:"blob",frontmatter:{title:"blob",date:"2024-03-03T18:51:32.000Z",permalink:"/pages/31f744/"},regularPath:"/00.%E6%8C%87%E5%8D%97/01.%E5%B7%A5%E5%85%B7%E5%BA%93/17.blob.html",relativePath:"00.指南/01.工具库/17.blob.md",key:"v-769530ce",path:"/pages/31f744/",headers:[{level:2,title:"base64ToBlob",slug:"base64toblob",normalizedTitle:"base64toblob",charIndex:20}],headersStr:"base64ToBlob",content:"提供一些blob有关的公共方法\n\n\n# base64ToBlob\n\nbase64ToBlob(base64Data: string, contentType: string): Blob\n\n将 Base64 格式的字符串转换为 Blob 格式。\n\n# 参数\n\n * base64Data: Base64格式的数据，需注意应不包含表明数据类型的部分，如 data:image/png;base64,\n * contentType: 数据的MIME Type，可使用utils内提供的枚举 MIME types 得到完整无误的类型。",normalizedContent:"提供一些blob有关的公共方法\n\n\n# base64toblob\n\nbase64toblob(base64data: string, contenttype: string): blob\n\n将 base64 格式的字符串转换为 blob 格式。\n\n# 参数\n\n * base64data: base64格式的数据，需注意应不包含表明数据类型的部分，如 data:image/png;base64,\n * contenttype: 数据的mime type，可使用utils内提供的枚举 mime types 得到完整无误的类型。",charsets:{cjk:!0},lastUpdated:"2024-03-03 19:30:39",lastUpdatedTimestamp:1709465439e3},{title:"平台接口",frontmatter:{title:"平台接口",date:"2024-01-04T23:53:13.000Z",permalink:"/pages/7296bf/"},regularPath:"/00.%E6%8C%87%E5%8D%97/02.%E6%8E%A5%E5%8F%A3/01.%E5%B9%B3%E5%8F%B0%E6%8E%A5%E5%8F%A3.html",relativePath:"00.指南/02.接口/01.平台接口.md",key:"v-694ce7b5",path:"/pages/7296bf/",headers:[{level:2,title:"user",slug:"user",normalizedTitle:"user",charIndex:2},{level:3,title:"loginApi - 登录",slug:"loginapi-登录",normalizedTitle:"loginapi - 登录",charIndex:11},{level:3,title:"getUserInfoApi - 获取当前登录用户信息",slug:"getuserinfoapi-获取当前登录用户信息",normalizedTitle:"getuserinfoapi - 获取当前登录用户信息",charIndex:687},{level:3,title:"getGlobalPolicyApi",slug:"getglobalpolicyapi",normalizedTitle:"getglobalpolicyapi",charIndex:798},{level:3,title:"updatePasswordApi - 修改密码",slug:"updatepasswordapi-修改密码",normalizedTitle:"updatepasswordapi - 修改密码",charIndex:899},{level:3,title:"getCaptchaURL - 获取验证码图片",slug:"getcaptchaurl-获取验证码图片",normalizedTitle:"getcaptchaurl - 获取验证码图片",charIndex:1098},{level:3,title:"logoutApi - 退出登录",slug:"logoutapi-退出登录",normalizedTitle:"logoutapi - 退出登录",charIndex:1347},{level:2,title:"menu",slug:"menu",normalizedTitle:"menu",charIndex:1432},{level:3,title:"getPermissionApi - 获取有权限的功能资源",slug:"getpermissionapi-获取有权限的功能资源",normalizedTitle:"getpermissionapi - 获取有权限的功能资源",charIndex:1441},{level:3,title:"getMenuHistoryApi - 获取历史菜单",slug:"getmenuhistoryapi-获取历史菜单",normalizedTitle:"getmenuhistoryapi - 获取历史菜单",charIndex:1556},{level:3,title:"menuHistoryApi - 添加到历史菜单列表",slug:"menuhistoryapi-添加到历史菜单列表",normalizedTitle:"menuhistoryapi - 添加到历史菜单列表",charIndex:1667},{level:3,title:"deleteMenuHistoryApi - 从历史菜单列表删除",slug:"deletemenuhistoryapi-从历史菜单列表删除",normalizedTitle:"deletemenuhistoryapi - 从历史菜单列表删除",charIndex:1892},{level:3,title:"getMenuCollectApi - 获取收藏菜单",slug:"getmenucollectapi-获取收藏菜单",normalizedTitle:"getmenucollectapi - 获取收藏菜单",charIndex:2095},{level:3,title:"addMenuCollectApi - 添加到收藏菜单列表",slug:"addmenucollectapi-添加到收藏菜单列表",normalizedTitle:"addmenucollectapi - 添加到收藏菜单列表",charIndex:2206},{level:3,title:"deleteMenuCollectApi - 从收藏菜单列表删除",slug:"deletemenucollectapi-从收藏菜单列表删除",normalizedTitle:"deletemenucollectapi - 从收藏菜单列表删除",charIndex:2434},{level:3,title:"removeMenuCollectApi - 批量从收藏菜单列表删除 ?",slug:"removemenucollectapi-批量从收藏菜单列表删除",normalizedTitle:"removemenucollectapi - 批量从收藏菜单列表删除 ?",charIndex:2637},{level:3,title:"getAppListApi - 获取应用列表",slug:"getapplistapi-获取应用列表",normalizedTitle:"getapplistapi - 获取应用列表",charIndex:2875},{level:3,title:"getMaxTabNumValueApi - 获取最大展示页面标签数量",slug:"getmaxtabnumvalueapi-获取最大展示页面标签数量",normalizedTitle:"getmaxtabnumvalueapi - 获取最大展示页面标签数量",charIndex:2970},{level:2,title:"dict",slug:"dict",normalizedTitle:"dict",charIndex:3107},{level:3,title:"getDictMapApi  - 获取多个字典Id的数据",slug:"getdictmapapi-获取多个字典id的数据",normalizedTitle:"getdictmapapi  - 获取多个字典id的数据",charIndex:null},{level:3,title:"getDictsMapApi",slug:"getdictsmapapi",normalizedTitle:"getdictsmapapi",charIndex:3669},{level:3,title:"getDictApi - 获取指定字典Id的数据",slug:"getdictapi-获取指定字典id的数据",normalizedTitle:"getdictapi - 获取指定字典id的数据",charIndex:3898},{level:2,title:"oss",slug:"oss",normalizedTitle:"oss",charIndex:4261},{level:3,title:"getOssFileUrl - 获取文档中心文件的url",slug:"getossfileurl-获取文档中心文件的url",normalizedTitle:"getossfileurl - 获取文档中心文件的url",charIndex:4269},{level:3,title:"putOssFileUrl - 上传文档中心文件的url",slug:"putossfileurl-上传文档中心文件的url",normalizedTitle:"putossfileurl - 上传文档中心文件的url",charIndex:4462},{level:3,title:"getOssFileApi - 获取文档中心文件",slug:"getossfileapi-获取文档中心文件",normalizedTitle:"getossfileapi - 获取文档中心文件",charIndex:4580},{level:3,title:"putOssFileApi - 上传文档中心文件",slug:"putossfileapi-上传文档中心文件",normalizedTitle:"putossfileapi - 上传文档中心文件",charIndex:4999}],headersStr:"user loginApi - 登录 getUserInfoApi - 获取当前登录用户信息 getGlobalPolicyApi updatePasswordApi - 修改密码 getCaptchaURL - 获取验证码图片 logoutApi - 退出登录 menu getPermissionApi - 获取有权限的功能资源 getMenuHistoryApi - 获取历史菜单 menuHistoryApi - 添加到历史菜单列表 deleteMenuHistoryApi - 从历史菜单列表删除 getMenuCollectApi - 获取收藏菜单 addMenuCollectApi - 添加到收藏菜单列表 deleteMenuCollectApi - 从收藏菜单列表删除 removeMenuCollectApi - 批量从收藏菜单列表删除 ? getAppListApi - 获取应用列表 getMaxTabNumValueApi - 获取最大展示页面标签数量 dict getDictMapApi  - 获取多个字典Id的数据 getDictsMapApi getDictApi - 获取指定字典Id的数据 oss getOssFileUrl - 获取文档中心文件的url putOssFileUrl - 上传文档中心文件的url getOssFileApi - 获取文档中心文件 putOssFileApi - 上传文档中心文件",content:'# user\n\n\n# loginApi - 登录\n\n * url: ${config.authServerContext}/sso/auth/login\n * method: POST\n * Content-Type: form表单\n\n# 参数\n\n参数名                 类型       必传     说明\ndata                Object   true   \n├─ userName         string   true   登录账号\n├─ password         string   true   明文输入的密码，是否加密等由该方法统一处理\n├─ validCodeId      string   true   验证码图片的Id\n└─ validCodeInput   string   true   输入的验证码\n\n# 样例\n\nimport { loginApi } from \'@indfnd/utils\'\n\nasync doLogin() {\n  try {\n    const { data } = await loginApi({\n      userName: \'admin_ind\',\n      password: \'xxx\',\n      validCodeId: \'xxx\',\n      validCodeInput: \'xxx\'\n    })\n    console.log(data) // { code: 1, data: \'登录成功\' }\n  } catch(e) {}\n}\n\n\n\n# getUserInfoApi - 获取当前登录用户信息\n\n * url: ${config.authServerContext}/manage/user/getCurrentInfo\n * method: GET\n\n\n# getGlobalPolicyApi\n\n * url: ${config.authServerContext}/anon/user/getGlobalPolicy\n * method: GET\n\n\n# updatePasswordApi - 修改密码\n\n * url: ${config.authServerContext}/manage/user/updatePassword\n * method: POST\n\n# 参数\n\n参数名       类型       必传     说明\ndata      Object   true   \n├─ TODO                   \n\n\n# getCaptchaURL - 获取验证码图片\n\n * url: ${config.authServerContext}/anon/user/getCaptcha/${validCodeId}\n * method: GET\n\n返回的是个url，用于 <img> 的 src\n\n# 参数\n\n参数名           类型       必传     说明\nvalidCodeId   string   true   验证码图片Id，登录时要将这个Id和输入的用户名、密码、验证码一起传给后端\n\n\n# logoutApi - 退出登录\n\n * url: ${config.authServerContext}/sso/logout\n * method: POST\n\n\n# menu\n\n\n# getPermissionApi - 获取有权限的功能资源\n\n * url: ${config.authServerContext}/manage/menu/getAllPermission\n * method: GET\n\n\n# getMenuHistoryApi - 获取历史菜单\n\n * url: ${config.authServerContext}/manage/upmsMenuHistory/list\n * method: GET\n\n\n# menuHistoryApi - 添加到历史菜单列表\n\n * url: ${config.authServerContext}/manage/upmsMenuHistory/add\n * method: POST\n * Content-Type: form表单\n\n# 参数\n\n参数名       类型       必传     说明\ndata      Object   true   \n├─ TODO                   \n\n\n# deleteMenuHistoryApi - 从历史菜单列表删除\n\n * url: ${config.authServerContext}/manage/upmsMenuHistory/delete/${historyId}\n * method: GET\n\n# 参数\n\n参数名         类型       必传     说明\nhistoryId   string   true   菜单Id\n\n\n# getMenuCollectApi - 获取收藏菜单\n\n * url: ${config.authServerContext}/manage/upmsMenuCollect/list\n * method: GET\n\n\n# addMenuCollectApi - 添加到收藏菜单列表\n\n * url: ${config.authServerContext}/manage/upmsMenuCollect/add\n * method: POST\n * Content-Type: form表单\n\n# 参数\n\n参数名       类型       必传     说明\ndata      Object   true   \n├─ TODO                   \n\n\n# deleteMenuCollectApi - 从收藏菜单列表删除\n\n * url: ${config.authServerContext}/manage/upmsMenuCollect/delete/${collectId}\n * method: GET\n\n# 参数\n\n参数名         类型       必传     说明\ncollectId   string   true   菜单Id\n\n\n# removeMenuCollectApi - 批量从收藏菜单列表删除 ?\n\n * url: ${config.authServerContext}/manage/upmsMenuCollect/remove\n * method: POST\n * Content-Type: form表单\n\n# 参数\n\n参数名       类型       必传     说明\ndata      Object   true   \n├─ TODO                   \n\n\n# getAppListApi - 获取应用列表\n\n * url: ${config.authServerContext}/manage/app/list\n * method: GET\n\n\n# getMaxTabNumValueApi - 获取最大展示页面标签数量\n\n * url: ${config.authServerContext}/ipm/bc/basic/item/getMaxTabNum\n * method: GET\n\n从四川复制的，目前没有用\n\n\n# dict\n\n\n# getDictMapApi - 获取多个字典Id的数据\n\n虚拟接口，循环调用 getDictApi ，统一组织后返回\n\n# 参数\n\n参数名         类型              必传     说明\ndictIdArr   Array<string>   true   字典Id数组\n\n# 返回\n\n{\n  "字典Id1": {\n    "data": {\n      "字典Key1": "字典值1",\n      "字典Key2": "字典值2"\n    },\n    "renderData": [\n      { "K": "字典Key1", "V": "字典值1" }\n      { "K": "字典Key2", "V": "字典值2" }\n    ],\n  },\n  "字典Id2": { ... },\n  ...\n}\n\n\n# 样例\n\nimport { getDictMapApi } from \'@indfnd/utils\'\n\nasync initDictData() {\n  try {\n    const dicts = await getDictMapApi([\'IND_Y_N\'])\n    console.log(dicts)\n  } catch(e) {}\n}\n\n\n\n# getDictsMapApi\n\n * url: ${config.authServerContext}/anon/dict/getDictsMap\n * method: GET\n\n# 参数\n\n参数名      类型       必传     说明\ndictId   string   true   字典Id\n\n# 返回\n\n{\n  "code": 1,\n  "data": {\n    "字典Key1": "字典值1",\n    ...\n  }\n}\n\n\n\n# getDictApi - 获取指定字典Id的数据\n\n * url: ${config.authServerContext}/dict/getDicts\n * method: GET\n\n该接口请求到数据后，会将结果缓存到 sessionStorage 里，下次请求该dictId数据从缓存里取，减少请求次数。每次退出登录会清空 sessionStorage 的缓存。\n\n# 参数\n\n参数名      类型       必传     说明\ndictId   string   true   字典Id\n\n# 返回\n\n{\n  "code": 1,\n  "data": [\n    { "K": "字典Key1", "V": "字典值1" }\n    { "K": "字典Key2", "V": "字典值2" }\n  ]\n}\n\n\n\n# oss\n\n\n# getOssFileUrl - 获取文档中心文件的url\n\n返回的是下载文件的url，用于图片展示等只需要url即可的场景。\n\n# 参数\n\n参数名      类型       必传     说明\nfileId   string   true   文档中心文件Id\n\n# 返回\n\n${config.ossServerContext}/oss/file/get/${fileId}\n\n\n# putOssFileUrl - 上传文档中心文件的url\n\n返回的是上传文件的url，用于给第三方组件惨的等只需要url即可的场景。\n\n# 返回\n\n${config.ossServerContext}/oss/file/put\n\n\n# getOssFileApi - 获取文档中心文件\n\n * url: ${config.ossServerContext}/oss/file/get/${fileId}\n * method: GET\n\n返回的是文件的 blob 或 arraybuffer 等格式，用于需要对文件进行二次处理的场景。若是页面展示图片或点击下载，请使用 getOssFileUrl 和 <img> 、 <a> 配套使用。\n\n# 参数\n\n参数名            类型             必传      说明\nfileId         string         true    文档中心文件Id\nresponseType   ResponseType   false   要求响应返回的数据类型\n\n# ResponseType\n\narraybuffer | blob | document | json | text | stream\n\n\n# putOssFileApi - 上传文档中心文件\n\n * url: ${config.ossServerContext}/oss/file/put\n * method: POST\n * Content-Type: multi-form\n\n上传文档中心接口，需要文件名和Blob格式的文件数据。\n\n# 参数\n\n参数名        类型       必传     说明\nfilename   string   true   文件名，需带文件类型后缀(如.jpg)\nblob       Blob     true   文件数据',normalizedContent:'# user\n\n\n# loginapi - 登录\n\n * url: ${config.authservercontext}/sso/auth/login\n * method: post\n * content-type: form表单\n\n# 参数\n\n参数名                 类型       必传     说明\ndata                object   true   \n├─ username         string   true   登录账号\n├─ password         string   true   明文输入的密码，是否加密等由该方法统一处理\n├─ validcodeid      string   true   验证码图片的id\n└─ validcodeinput   string   true   输入的验证码\n\n# 样例\n\nimport { loginapi } from \'@indfnd/utils\'\n\nasync dologin() {\n  try {\n    const { data } = await loginapi({\n      username: \'admin_ind\',\n      password: \'xxx\',\n      validcodeid: \'xxx\',\n      validcodeinput: \'xxx\'\n    })\n    console.log(data) // { code: 1, data: \'登录成功\' }\n  } catch(e) {}\n}\n\n\n\n# getuserinfoapi - 获取当前登录用户信息\n\n * url: ${config.authservercontext}/manage/user/getcurrentinfo\n * method: get\n\n\n# getglobalpolicyapi\n\n * url: ${config.authservercontext}/anon/user/getglobalpolicy\n * method: get\n\n\n# updatepasswordapi - 修改密码\n\n * url: ${config.authservercontext}/manage/user/updatepassword\n * method: post\n\n# 参数\n\n参数名       类型       必传     说明\ndata      object   true   \n├─ todo                   \n\n\n# getcaptchaurl - 获取验证码图片\n\n * url: ${config.authservercontext}/anon/user/getcaptcha/${validcodeid}\n * method: get\n\n返回的是个url，用于 <img> 的 src\n\n# 参数\n\n参数名           类型       必传     说明\nvalidcodeid   string   true   验证码图片id，登录时要将这个id和输入的用户名、密码、验证码一起传给后端\n\n\n# logoutapi - 退出登录\n\n * url: ${config.authservercontext}/sso/logout\n * method: post\n\n\n# menu\n\n\n# getpermissionapi - 获取有权限的功能资源\n\n * url: ${config.authservercontext}/manage/menu/getallpermission\n * method: get\n\n\n# getmenuhistoryapi - 获取历史菜单\n\n * url: ${config.authservercontext}/manage/upmsmenuhistory/list\n * method: get\n\n\n# menuhistoryapi - 添加到历史菜单列表\n\n * url: ${config.authservercontext}/manage/upmsmenuhistory/add\n * method: post\n * content-type: form表单\n\n# 参数\n\n参数名       类型       必传     说明\ndata      object   true   \n├─ todo                   \n\n\n# deletemenuhistoryapi - 从历史菜单列表删除\n\n * url: ${config.authservercontext}/manage/upmsmenuhistory/delete/${historyid}\n * method: get\n\n# 参数\n\n参数名         类型       必传     说明\nhistoryid   string   true   菜单id\n\n\n# getmenucollectapi - 获取收藏菜单\n\n * url: ${config.authservercontext}/manage/upmsmenucollect/list\n * method: get\n\n\n# addmenucollectapi - 添加到收藏菜单列表\n\n * url: ${config.authservercontext}/manage/upmsmenucollect/add\n * method: post\n * content-type: form表单\n\n# 参数\n\n参数名       类型       必传     说明\ndata      object   true   \n├─ todo                   \n\n\n# deletemenucollectapi - 从收藏菜单列表删除\n\n * url: ${config.authservercontext}/manage/upmsmenucollect/delete/${collectid}\n * method: get\n\n# 参数\n\n参数名         类型       必传     说明\ncollectid   string   true   菜单id\n\n\n# removemenucollectapi - 批量从收藏菜单列表删除 ?\n\n * url: ${config.authservercontext}/manage/upmsmenucollect/remove\n * method: post\n * content-type: form表单\n\n# 参数\n\n参数名       类型       必传     说明\ndata      object   true   \n├─ todo                   \n\n\n# getapplistapi - 获取应用列表\n\n * url: ${config.authservercontext}/manage/app/list\n * method: get\n\n\n# getmaxtabnumvalueapi - 获取最大展示页面标签数量\n\n * url: ${config.authservercontext}/ipm/bc/basic/item/getmaxtabnum\n * method: get\n\n从四川复制的，目前没有用\n\n\n# dict\n\n\n# getdictmapapi - 获取多个字典id的数据\n\n虚拟接口，循环调用 getdictapi ，统一组织后返回\n\n# 参数\n\n参数名         类型              必传     说明\ndictidarr   array<string>   true   字典id数组\n\n# 返回\n\n{\n  "字典id1": {\n    "data": {\n      "字典key1": "字典值1",\n      "字典key2": "字典值2"\n    },\n    "renderdata": [\n      { "k": "字典key1", "v": "字典值1" }\n      { "k": "字典key2", "v": "字典值2" }\n    ],\n  },\n  "字典id2": { ... },\n  ...\n}\n\n\n# 样例\n\nimport { getdictmapapi } from \'@indfnd/utils\'\n\nasync initdictdata() {\n  try {\n    const dicts = await getdictmapapi([\'ind_y_n\'])\n    console.log(dicts)\n  } catch(e) {}\n}\n\n\n\n# getdictsmapapi\n\n * url: ${config.authservercontext}/anon/dict/getdictsmap\n * method: get\n\n# 参数\n\n参数名      类型       必传     说明\ndictid   string   true   字典id\n\n# 返回\n\n{\n  "code": 1,\n  "data": {\n    "字典key1": "字典值1",\n    ...\n  }\n}\n\n\n\n# getdictapi - 获取指定字典id的数据\n\n * url: ${config.authservercontext}/dict/getdicts\n * method: get\n\n该接口请求到数据后，会将结果缓存到 sessionstorage 里，下次请求该dictid数据从缓存里取，减少请求次数。每次退出登录会清空 sessionstorage 的缓存。\n\n# 参数\n\n参数名      类型       必传     说明\ndictid   string   true   字典id\n\n# 返回\n\n{\n  "code": 1,\n  "data": [\n    { "k": "字典key1", "v": "字典值1" }\n    { "k": "字典key2", "v": "字典值2" }\n  ]\n}\n\n\n\n# oss\n\n\n# getossfileurl - 获取文档中心文件的url\n\n返回的是下载文件的url，用于图片展示等只需要url即可的场景。\n\n# 参数\n\n参数名      类型       必传     说明\nfileid   string   true   文档中心文件id\n\n# 返回\n\n${config.ossservercontext}/oss/file/get/${fileid}\n\n\n# putossfileurl - 上传文档中心文件的url\n\n返回的是上传文件的url，用于给第三方组件惨的等只需要url即可的场景。\n\n# 返回\n\n${config.ossservercontext}/oss/file/put\n\n\n# getossfileapi - 获取文档中心文件\n\n * url: ${config.ossservercontext}/oss/file/get/${fileid}\n * method: get\n\n返回的是文件的 blob 或 arraybuffer 等格式，用于需要对文件进行二次处理的场景。若是页面展示图片或点击下载，请使用 getossfileurl 和 <img> 、 <a> 配套使用。\n\n# 参数\n\n参数名            类型             必传      说明\nfileid         string         true    文档中心文件id\nresponsetype   responsetype   false   要求响应返回的数据类型\n\n# responsetype\n\narraybuffer | blob | document | json | text | stream\n\n\n# putossfileapi - 上传文档中心文件\n\n * url: ${config.ossservercontext}/oss/file/put\n * method: post\n * content-type: multi-form\n\n上传文档中心接口，需要文件名和blob格式的文件数据。\n\n# 参数\n\n参数名        类型       必传     说明\nfilename   string   true   文件名，需带文件类型后缀(如.jpg)\nblob       blob     true   文件数据',charsets:{cjk:!0},lastUpdated:"2024-03-18 15:45:35",lastUpdatedTimestamp:1710747935e3},{title:"公共配置",frontmatter:{title:"公共配置",date:"2024-01-04T14:26:59.000Z",permalink:"/pages/config"},regularPath:"/00.%E6%8C%87%E5%8D%97/03.config/01.config.html",relativePath:"00.指南/03.config/01.config.md",key:"v-ee6a7e4c",path:"/pages/config/",headers:[{level:2,title:"介绍",slug:"介绍",normalizedTitle:"介绍",charIndex:2},{level:2,title:"useConfig",slug:"useconfig",normalizedTitle:"useconfig",charIndex:62},{level:2,title:"config",slug:"config",normalizedTitle:"config",charIndex:43},{level:2,title:"Config",slug:"config-2",normalizedTitle:"config",charIndex:65},{level:3,title:"baseConfig - 基础配置",slug:"baseconfig-基础配置",normalizedTitle:"baseconfig - 基础配置",charIndex:568},{level:3,title:"devConfig - 开发模式下覆盖的配置",slug:"devconfig-开发模式下覆盖的配置",normalizedTitle:"devconfig - 开发模式下覆盖的配置",charIndex:1456},{level:4,title:"showMenus",slug:"showmenus",normalizedTitle:"showmenus",charIndex:741},{level:3,title:"prodConfig - 生产模式下覆盖的配置",slug:"prodconfig-生产模式下覆盖的配置",normalizedTitle:"prodconfig - 生产模式下覆盖的配置",charIndex:1509},{level:4,title:"showMenus",slug:"showmenus-2",normalizedTitle:"showmenus",charIndex:741}],headersStr:"介绍 useConfig config Config baseConfig - 基础配置 devConfig - 开发模式下覆盖的配置 showMenus prodConfig - 生产模式下覆盖的配置 showMenus",content:"# 介绍\n\n集成各应用共用的一些配置，各应用的配置应该基于本配置，使用方法：\n\n// config.js\nimport { useConfig, config } from '@indfnd/utils'\n\nconst isDev = process.env.NODE_ENV === 'development'\n\nexport default {\n  ...useConfig(isDev),\n  subAppName: 'ilm', // 子应用名称\n}\n\n\n\n# useConfig\n\n * (isDev: boolean) => Config\n * 可以根据开发模式或生产模式使用不同的配置\n * 开发模式下会返回 devConfig 覆盖 baseConfig 后的配置\n * 生产模式下会返回 prodConfig 覆盖 baseConfig 后的配置\n\nimport { useConfig } from '@indfnd/utils'\n\nconst isDev = process.env.NODE_ENV === 'development'\nconst config = useConfig(isDev)\n\n\n\n# config\n\n * 返回 useConfig()，即生产模式下的配置\n\n\n# Config\n\n\n# baseConfig - 基础配置\n\n配置名                  配置值                   描述\ntitle                山东一体化营销系统             显示在浏览器标签的title\nicomId               20370001              工业公司编码\nshowMenus            false                 是否展示顶部及左侧菜单\nrouterBase           ind                   路由基础base路径\nloginRouteName       login                 登录页路由的name\nrootRouteName        root                  根路由的name\nhomeRouteName        home                  首页路由的name\nauthServerContext    /user-manage-server   登录应用上下文根\nossServerContext     /user-manage-server   文档中心上下文根\nismAmServerContext   /ind-ism-am-server    销售am应用上下文根\nismAcServerContext   /ind-ism-ac-server    销售ac应用上下文根\nismSqServerContext   /ind-ism-sq-server    销售分析应用上下文根\nilmServerContext     /ind-ilm-server       物流应用上下文根\nucExtServerContext   /ind-uc-ext-server    用户中心扩展应用上下文根\nerrorMessage         系统开小差了，请稍后尝试          报错统一提示，目前优先使用后端返回的message\n\n\n# devConfig - 开发模式下覆盖的配置\n\n# showMenus\n\n * 固定为 true\n\n\n# prodConfig - 生产模式下覆盖的配置\n\n# showMenus\n\n * 在 iframe(ibp) 环境下为 false\n * 非 iframe 环境下为 true",normalizedContent:"# 介绍\n\n集成各应用共用的一些配置，各应用的配置应该基于本配置，使用方法：\n\n// config.js\nimport { useconfig, config } from '@indfnd/utils'\n\nconst isdev = process.env.node_env === 'development'\n\nexport default {\n  ...useconfig(isdev),\n  subappname: 'ilm', // 子应用名称\n}\n\n\n\n# useconfig\n\n * (isdev: boolean) => config\n * 可以根据开发模式或生产模式使用不同的配置\n * 开发模式下会返回 devconfig 覆盖 baseconfig 后的配置\n * 生产模式下会返回 prodconfig 覆盖 baseconfig 后的配置\n\nimport { useconfig } from '@indfnd/utils'\n\nconst isdev = process.env.node_env === 'development'\nconst config = useconfig(isdev)\n\n\n\n# config\n\n * 返回 useconfig()，即生产模式下的配置\n\n\n# config\n\n\n# baseconfig - 基础配置\n\n配置名                  配置值                   描述\ntitle                山东一体化营销系统             显示在浏览器标签的title\nicomid               20370001              工业公司编码\nshowmenus            false                 是否展示顶部及左侧菜单\nrouterbase           ind                   路由基础base路径\nloginroutename       login                 登录页路由的name\nrootroutename        root                  根路由的name\nhomeroutename        home                  首页路由的name\nauthservercontext    /user-manage-server   登录应用上下文根\nossservercontext     /user-manage-server   文档中心上下文根\nismamservercontext   /ind-ism-am-server    销售am应用上下文根\nismacservercontext   /ind-ism-ac-server    销售ac应用上下文根\nismsqservercontext   /ind-ism-sq-server    销售分析应用上下文根\nilmservercontext     /ind-ilm-server       物流应用上下文根\nucextservercontext   /ind-uc-ext-server    用户中心扩展应用上下文根\nerrormessage         系统开小差了，请稍后尝试          报错统一提示，目前优先使用后端返回的message\n\n\n# devconfig - 开发模式下覆盖的配置\n\n# showmenus\n\n * 固定为 true\n\n\n# prodconfig - 生产模式下覆盖的配置\n\n# showmenus\n\n * 在 iframe(ibp) 环境下为 false\n * 非 iframe 环境下为 true",charsets:{cjk:!0},lastUpdated:"2024-01-24 18:07:31",lastUpdatedTimestamp:1706090851e3},{title:"sfc-demo",frontmatter:{title:"sfc-demo",date:"2024-01-10T22:37:19.000Z",permalink:"/pages/plugin-sfc-demo/"},regularPath:"/98.%E6%8F%92%E4%BB%B6%E6%B5%8B%E8%AF%95/01.sfc-demo.html",relativePath:"98.插件测试/01.sfc-demo.md",key:"v-5d5908ce",path:"/pages/plugin-sfc-demo/",headersStr:null,content:'<template>\n  <div>\n    测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {}\n  },\n}\n<\/script>\n\n<style lang="stylus">\n.test\n  border: 1px solid\n</style>',normalizedContent:'<template>\n  <div>\n    测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {}\n  },\n}\n<\/script>\n\n<style lang="stylus">\n.test\n  border: 1px solid\n</style>',charsets:{cjk:!0},lastUpdated:"2024-01-11 01:33:12",lastUpdatedTimestamp:1704907992e3},{title:"更新日志",frontmatter:{title:"更新日志",date:"2024-01-04T14:21:39.000Z",permalink:"/pages/release"},regularPath:"/99.%E6%9B%B4%E6%96%B0%E6%97%A5%E5%BF%97/01.release.html",relativePath:"99.更新日志/01.release.md",key:"v-285c42a3",path:"/pages/release/",headersStr:null,content:"# Changelog\n\nAll notable changes to this project will be documented in this file. See standard-version for commit guidelines.\n\n\n# 0.1.11 (2024-06-16)\n\n\n# Bug Fixes\n\n * number: 修改保留小数位数消除了两位数据的问题 (52c6e5a)\n\n\n# 0.1.10 (2024-05-27)\n\n\n# Bug Fixes\n\n * 更新依赖 (283e6b2)\n * enum: 表格enum处理支持多行表头 (d3256e6)\n * half-year: 半年公共方法支持全年 (6cab2e7)\n * table: row2column有的人不维护key (14b6968)\n\n\n# 0.1.9 (2024-04-30)\n\n\n# Bug Fixes\n\n * 修改permission的key (46aa0a4)\n\n\n# 0.1.8 (2024-04-28)\n\n\n# Features\n\n * label宽度修改 (e22e848)\n\n\n# 0.1.7 (2024-04-26)\n\n\n# Features\n\n * 兼容云庭 (efa008a)\n\n\n# 0.1.6 (2024-04-25)\n\n\n# Features\n\n * 修改label宽度 (8b56d81)\n * label宽度自适应 (daf83ea)\n\n\n# 0.1.5 (2024-04-16)\n\n\n# Features\n\n * 指标说明解耦 (93cda2a)\n\n\n# 0.1.4 (2024-04-13)\n\n\n# Features\n\n * 登录加密升级 (f3935fb)\n\n\n# Bug Fixes\n\n * 依赖 (3860882)\n\n\n# 0.1.3 (2024-04-06)\n\n\n# Features\n\n * 指标说明bug (2367d18)\n\n\n# 0.1.2 (2024-04-03)\n\n\n# Features\n\n * 指标描述 (70a8a6f)\n\n\n# Bug Fixes\n\n * 增加请求时长 (865aa04)\n\n\n# 0.1.1 (2024-04-01)\n\n\n# Features\n\n * package.json脚本修改 (1e545a2)\n * table: 支持合计列 (47505c7)\n\n\n# Bug Fixes\n\n * 车牌号校验修改，支持鲁AD1234 (9e8fc5b)\n\n\n# 0.1.0 (2024-03-18)\n\n\n# Bug Fixes\n\n * table: 修改headerTooltip (949db39)\n\n\n# 0.0.40 (2024-03-13)\n\n\n# Bug Fixes\n\n * 更新依赖 (8f9c236)\n * 文档中心获取图片并转base64 (13259ad)\n\n\n# 0.0.39 (2024-03-06)\n\n\n# Features\n\n * 登录加密参考云庭 (c9c6649)\n * 增加卷烟筛选条件接口 (85ae986)\n * 增加卷烟筛选条件接口 (445756b)\n * table: 行转列时可以设置headerTooltip (28d17cd)\n\n\n# Bug Fixes\n\n * 更新依赖 (6044adf)\n * 接口url修改 (fb0abd2)\n\n\n# 0.0.38 (2024-03-03)\n\n\n# Features\n\n * 增加指标描述功能 (268d147)\n\n\n# 0.0.37 (2024-03-02)\n\n\n# Bug Fixes\n\n * table: 行转列排序，上下层表头数据限制 (24c8959)\n\n\n# 0.0.36 (2024-03-01)\n\n\n# Features\n\n * api: 增加文档中心上传接口 (17a8174)\n * blob: 增加base64转blob方法 (9d860c1)\n * event: 增加阻止事件默认行为和冒泡的方法 (6f27ffa)\n * table: 行转列增加动态列排序相关配置 (e26e9c2)\n\n\n# 0.0.35 (2024-02-22)\n\n\n# Bug Fixes\n\n * request: 修改超时时的报错 (72f729b)\n\n\n# 0.0.34 (2024-02-22)\n\n\n# Bug Fixes\n\n * table: 修改列定义中没有key时报错的问题 (a37636e)\n\n\n# 0.0.33 (2024-01-31)\n\n\n# Features\n\n * uuid: 增加和后端uuid格式一致的方法 (3e6805e)\n\n\n# 0.0.32 (2024-01-27)\n\n\n# Features\n\n * request: 请求超时和状态码非200增加提示 (8742d1f)\n\n\n# Bug Fixes\n\n * date: 日期格式化函数判断格式 (bfa032d)\n\n\n# 0.0.31 (2024-01-24)\n\n\n# 0.0.30 (2024-01-24)\n\n\n# Bug Fixes\n\n * half-year: 编码使用H，不用Y (312c956)\n\n\n# 0.0.29 (2024-01-23)\n\n\n# Features\n\n * 增加获取半年的方法 (a31af87)\n\n\n# 0.0.28 (2024-01-18)\n\n\n# Features\n\n * validate: 增加车牌号校验 (6bd94a0)\n\n\n# 0.0.27 (2024-01-16)\n\n\n# Bug Fixes\n\n * excle导出0 (34d1f41)\n\n\n# 0.0.26 (2024-01-15)\n\n\n# Features\n\n * enum: 转换column支持select (f914ba2)\n\n\n# 0.0.25 (2024-01-15)\n\n\n# Features\n\n * 导出excle的0不能改成空 (ab17b1a)\n\n\n# 0.0.24 (2024-01-12)\n\n\n# Bug Fixes\n\n * enum: 处理定义enumData (3c3327b)\n\n\n# 0.0.23 (2024-01-11)\n\n\n# Bug Fixes\n\n * enum: 统一处理列定义不是响应式的，将处理后的返回 (85d2282)\n\n\n# 0.0.22 (2024-01-11)\n\n\n# Bug Fixes\n\n * api: 修复修改目录导致api找不到的bug (34ce5ac)\n\n\n# 0.0.21 (2024-01-11)\n\n\n# Bug Fixes\n\n * api: 删除无用的api (08548a4)\n * enum: 是否枚举换顺序 (2f53a08)\n * enum: 统一处理列定义不是响应式的，将处理后的返回 (829c127)\n * excle导入多加列的场景bug修复 (9598e90)\n\n\n# 0.0.20 (2024-01-04)\n\n\n# Bug Fixes\n\n * table: 表格行转列修改为key、title (bf17591)\n\n\n# 0.0.19 (2024-01-02)\n\n\n# 0.0.18 (2024-01-02)\n\n\n# 0.0.17 (2023-12-25)\n\n\n# Features\n\n * 增加树的api (9a0e448)\n\n\n# 0.0.16 (2023-12-22)\n\n\n# Features\n\n * 更新quarter的方法 (9989287)\n\n\n# 0.0.15 (2023-12-21)\n\n\n# Features\n\n * 增加UcEnum (8f6b53f)\n\n\n# 0.0.14 (2023-12-20)\n\n\n# Features\n\n * excel导入导出 (d78d1e2)\n\n\n# 0.0.13 (2023-12-19)\n\n\n# Features\n\n * 按要求导出exel模板 (5b18218)\n * 导出excel (d50f706)\n * 增加MIME Type (1f70dd4)\n\n\n# 0.0.12 (2023-12-09)\n\n\n# Features\n\n * request: response的拦截函数暴露出来 (0f21797)\n\n\n# 0.0.11 (2023-12-09)\n\n\n# Features\n\n * enum: 增加转换枚举列表公共方法 (165c1c0)\n\n\n# Bug Fixes\n\n * request: 修改判断文件流的逻辑 (9b5e7f5)\n\n\n# 0.0.10 (2023-12-04)\n\n\n# Features\n\n * date: 增加日期处理函数 (a26b7f0)\n\n\n# 0.0.9 (2023-12-04)\n\n\n# Features\n\n * 增加物流上下文根 (a0e78be)\n * 增加isPromise判断方法 (ffceaba)\n\n\n# 0.0.8 (2023-11-29)\n\n\n# Bug Fixes\n\n * config (51f34ce)\n\n\n# 0.0.7 (2023-11-29)\n\n\n# Bug Fixes\n\n * config (58b8f1d)\n\n\n# 0.0.6 (2023-11-29)\n\n\n# Bug Fixes\n\n * config (88cbe46)\n\n\n# 0.0.5 (2023-11-29)\n\n\n# Bug Fixes\n\n * 展示菜单逻辑修改 (733ae78)\n\n\n# 0.0.4 (2023-11-27)\n\n\n# Features\n\n * standard-version githooks (63f3fec)\n\n\n# Bug Fixes\n\n * git hooks (570f46d)\n\n\n# 0.0.3 (2023-11-27)\n\n\n# Features\n\n * standard-version githooks (63f3fec)\n\n\n# 0.0.2 (2023-11-27)\n\n\n# Features\n\n * 初始化入库 (2ea1501)\n * 登陆 (14e0379)\n * 更新utils (5a0cc63)\n * 缓存枚举值 (8a172d4)\n * 会话兼容云庭 (4663960)\n * 身份证校验规则 (30070cc)\n * 身份证校验规则 (8a078bd)\n * 是否的枚举 (6e1f7f9)\n * 修改后端服务上下文根 (fd1a435)\n * 修改user-manager地址 (72378bc)\n * 增加解构行转列数据方法 (ffe352d)\n * 增加类型判断工具方法 (51c8cf8)\n * 增加dayjs (2874231)\n * 增加standard-version (ad2943e)\n * 增加standard-version (b8c1a79)\n * add uc-ext (23546f5)\n * axios封装formPost (1191348)\n * config修改，支持开发模式和生产模式 (352b84b)\n * request封装修改，支持form表单 (6b34cd8)\n * token逻辑新增 (4a2a3a6)\n * update token (3964931)\n\n\n# Bug Fixes\n\n * 登录bug (a6d1b48)\n * 枚举bug (69c6549)\n * 权限 (f7ddd5a)\n * 修改之前获取缓存没有返回的bug，优化dict缓存代码 (5490bae)\n * fix bug (83d3f78)\n * fix enum bug (9e96c6f)\n * fix enum bug (df8db9e)\n * on、off立即执行的bug (8fb8d6f)\n * storage (c77cd96)\n\n",normalizedContent:"# changelog\n\nall notable changes to this project will be documented in this file. see standard-version for commit guidelines.\n\n\n# 0.1.11 (2024-06-16)\n\n\n# bug fixes\n\n * number: 修改保留小数位数消除了两位数据的问题 (52c6e5a)\n\n\n# 0.1.10 (2024-05-27)\n\n\n# bug fixes\n\n * 更新依赖 (283e6b2)\n * enum: 表格enum处理支持多行表头 (d3256e6)\n * half-year: 半年公共方法支持全年 (6cab2e7)\n * table: row2column有的人不维护key (14b6968)\n\n\n# 0.1.9 (2024-04-30)\n\n\n# bug fixes\n\n * 修改permission的key (46aa0a4)\n\n\n# 0.1.8 (2024-04-28)\n\n\n# features\n\n * label宽度修改 (e22e848)\n\n\n# 0.1.7 (2024-04-26)\n\n\n# features\n\n * 兼容云庭 (efa008a)\n\n\n# 0.1.6 (2024-04-25)\n\n\n# features\n\n * 修改label宽度 (8b56d81)\n * label宽度自适应 (daf83ea)\n\n\n# 0.1.5 (2024-04-16)\n\n\n# features\n\n * 指标说明解耦 (93cda2a)\n\n\n# 0.1.4 (2024-04-13)\n\n\n# features\n\n * 登录加密升级 (f3935fb)\n\n\n# bug fixes\n\n * 依赖 (3860882)\n\n\n# 0.1.3 (2024-04-06)\n\n\n# features\n\n * 指标说明bug (2367d18)\n\n\n# 0.1.2 (2024-04-03)\n\n\n# features\n\n * 指标描述 (70a8a6f)\n\n\n# bug fixes\n\n * 增加请求时长 (865aa04)\n\n\n# 0.1.1 (2024-04-01)\n\n\n# features\n\n * package.json脚本修改 (1e545a2)\n * table: 支持合计列 (47505c7)\n\n\n# bug fixes\n\n * 车牌号校验修改，支持鲁ad1234 (9e8fc5b)\n\n\n# 0.1.0 (2024-03-18)\n\n\n# bug fixes\n\n * table: 修改headertooltip (949db39)\n\n\n# 0.0.40 (2024-03-13)\n\n\n# bug fixes\n\n * 更新依赖 (8f9c236)\n * 文档中心获取图片并转base64 (13259ad)\n\n\n# 0.0.39 (2024-03-06)\n\n\n# features\n\n * 登录加密参考云庭 (c9c6649)\n * 增加卷烟筛选条件接口 (85ae986)\n * 增加卷烟筛选条件接口 (445756b)\n * table: 行转列时可以设置headertooltip (28d17cd)\n\n\n# bug fixes\n\n * 更新依赖 (6044adf)\n * 接口url修改 (fb0abd2)\n\n\n# 0.0.38 (2024-03-03)\n\n\n# features\n\n * 增加指标描述功能 (268d147)\n\n\n# 0.0.37 (2024-03-02)\n\n\n# bug fixes\n\n * table: 行转列排序，上下层表头数据限制 (24c8959)\n\n\n# 0.0.36 (2024-03-01)\n\n\n# features\n\n * api: 增加文档中心上传接口 (17a8174)\n * blob: 增加base64转blob方法 (9d860c1)\n * event: 增加阻止事件默认行为和冒泡的方法 (6f27ffa)\n * table: 行转列增加动态列排序相关配置 (e26e9c2)\n\n\n# 0.0.35 (2024-02-22)\n\n\n# bug fixes\n\n * request: 修改超时时的报错 (72f729b)\n\n\n# 0.0.34 (2024-02-22)\n\n\n# bug fixes\n\n * table: 修改列定义中没有key时报错的问题 (a37636e)\n\n\n# 0.0.33 (2024-01-31)\n\n\n# features\n\n * uuid: 增加和后端uuid格式一致的方法 (3e6805e)\n\n\n# 0.0.32 (2024-01-27)\n\n\n# features\n\n * request: 请求超时和状态码非200增加提示 (8742d1f)\n\n\n# bug fixes\n\n * date: 日期格式化函数判断格式 (bfa032d)\n\n\n# 0.0.31 (2024-01-24)\n\n\n# 0.0.30 (2024-01-24)\n\n\n# bug fixes\n\n * half-year: 编码使用h，不用y (312c956)\n\n\n# 0.0.29 (2024-01-23)\n\n\n# features\n\n * 增加获取半年的方法 (a31af87)\n\n\n# 0.0.28 (2024-01-18)\n\n\n# features\n\n * validate: 增加车牌号校验 (6bd94a0)\n\n\n# 0.0.27 (2024-01-16)\n\n\n# bug fixes\n\n * excle导出0 (34d1f41)\n\n\n# 0.0.26 (2024-01-15)\n\n\n# features\n\n * enum: 转换column支持select (f914ba2)\n\n\n# 0.0.25 (2024-01-15)\n\n\n# features\n\n * 导出excle的0不能改成空 (ab17b1a)\n\n\n# 0.0.24 (2024-01-12)\n\n\n# bug fixes\n\n * enum: 处理定义enumdata (3c3327b)\n\n\n# 0.0.23 (2024-01-11)\n\n\n# bug fixes\n\n * enum: 统一处理列定义不是响应式的，将处理后的返回 (85d2282)\n\n\n# 0.0.22 (2024-01-11)\n\n\n# bug fixes\n\n * api: 修复修改目录导致api找不到的bug (34ce5ac)\n\n\n# 0.0.21 (2024-01-11)\n\n\n# bug fixes\n\n * api: 删除无用的api (08548a4)\n * enum: 是否枚举换顺序 (2f53a08)\n * enum: 统一处理列定义不是响应式的，将处理后的返回 (829c127)\n * excle导入多加列的场景bug修复 (9598e90)\n\n\n# 0.0.20 (2024-01-04)\n\n\n# bug fixes\n\n * table: 表格行转列修改为key、title (bf17591)\n\n\n# 0.0.19 (2024-01-02)\n\n\n# 0.0.18 (2024-01-02)\n\n\n# 0.0.17 (2023-12-25)\n\n\n# features\n\n * 增加树的api (9a0e448)\n\n\n# 0.0.16 (2023-12-22)\n\n\n# features\n\n * 更新quarter的方法 (9989287)\n\n\n# 0.0.15 (2023-12-21)\n\n\n# features\n\n * 增加ucenum (8f6b53f)\n\n\n# 0.0.14 (2023-12-20)\n\n\n# features\n\n * excel导入导出 (d78d1e2)\n\n\n# 0.0.13 (2023-12-19)\n\n\n# features\n\n * 按要求导出exel模板 (5b18218)\n * 导出excel (d50f706)\n * 增加mime type (1f70dd4)\n\n\n# 0.0.12 (2023-12-09)\n\n\n# features\n\n * request: response的拦截函数暴露出来 (0f21797)\n\n\n# 0.0.11 (2023-12-09)\n\n\n# features\n\n * enum: 增加转换枚举列表公共方法 (165c1c0)\n\n\n# bug fixes\n\n * request: 修改判断文件流的逻辑 (9b5e7f5)\n\n\n# 0.0.10 (2023-12-04)\n\n\n# features\n\n * date: 增加日期处理函数 (a26b7f0)\n\n\n# 0.0.9 (2023-12-04)\n\n\n# features\n\n * 增加物流上下文根 (a0e78be)\n * 增加ispromise判断方法 (ffceaba)\n\n\n# 0.0.8 (2023-11-29)\n\n\n# bug fixes\n\n * config (51f34ce)\n\n\n# 0.0.7 (2023-11-29)\n\n\n# bug fixes\n\n * config (58b8f1d)\n\n\n# 0.0.6 (2023-11-29)\n\n\n# bug fixes\n\n * config (88cbe46)\n\n\n# 0.0.5 (2023-11-29)\n\n\n# bug fixes\n\n * 展示菜单逻辑修改 (733ae78)\n\n\n# 0.0.4 (2023-11-27)\n\n\n# features\n\n * standard-version githooks (63f3fec)\n\n\n# bug fixes\n\n * git hooks (570f46d)\n\n\n# 0.0.3 (2023-11-27)\n\n\n# features\n\n * standard-version githooks (63f3fec)\n\n\n# 0.0.2 (2023-11-27)\n\n\n# features\n\n * 初始化入库 (2ea1501)\n * 登陆 (14e0379)\n * 更新utils (5a0cc63)\n * 缓存枚举值 (8a172d4)\n * 会话兼容云庭 (4663960)\n * 身份证校验规则 (30070cc)\n * 身份证校验规则 (8a078bd)\n * 是否的枚举 (6e1f7f9)\n * 修改后端服务上下文根 (fd1a435)\n * 修改user-manager地址 (72378bc)\n * 增加解构行转列数据方法 (ffe352d)\n * 增加类型判断工具方法 (51c8cf8)\n * 增加dayjs (2874231)\n * 增加standard-version (ad2943e)\n * 增加standard-version (b8c1a79)\n * add uc-ext (23546f5)\n * axios封装formpost (1191348)\n * config修改，支持开发模式和生产模式 (352b84b)\n * request封装修改，支持form表单 (6b34cd8)\n * token逻辑新增 (4a2a3a6)\n * update token (3964931)\n\n\n# bug fixes\n\n * 登录bug (a6d1b48)\n * 枚举bug (69c6549)\n * 权限 (f7ddd5a)\n * 修改之前获取缓存没有返回的bug，优化dict缓存代码 (5490bae)\n * fix bug (83d3f78)\n * fix enum bug (9e96c6f)\n * fix enum bug (df8db9e)\n * on、off立即执行的bug (8fb8d6f)\n * storage (c77cd96)\n\n",charsets:{cjk:!0},lastUpdated:"2024-01-11 01:32:35",lastUpdatedTimestamp:1704907955e3},{title:"业务接口",frontmatter:{title:"业务接口",date:"2024-01-04T23:53:48.000Z",permalink:"/pages/d7f021/"},regularPath:"/00.%E6%8C%87%E5%8D%97/02.%E6%8E%A5%E5%8F%A3/02.%E4%B8%9A%E5%8A%A1%E6%8E%A5%E5%8F%A3.html",relativePath:"00.指南/02.接口/02.业务接口.md",key:"v-0fdd426f",path:"/pages/d7f021/",headers:[{level:2,title:"平台扩展",slug:"平台扩展",normalizedTitle:"平台扩展",charIndex:2},{level:3,title:"listUserTreeApi - 用户树",slug:"listusertreeapi-用户树",normalizedTitle:"listusertreeapi - 用户树",charIndex:11},{level:2,title:"商业公司",slug:"商业公司",normalizedTitle:"商业公司",charIndex:786},{level:3,title:"listComTreeApi - 公司树",slug:"listcomtreeapi-公司树",normalizedTitle:"listcomtreeapi - 公司树",charIndex:795},{level:2,title:"规格产品",slug:"规格产品",normalizedTitle:"规格产品",charIndex:930},{level:3,title:"listItemTreeApi - 卷烟树",slug:"listitemtreeapi-卷烟树",normalizedTitle:"listitemtreeapi - 卷烟树",charIndex:939},{level:3,title:"getPriceInfo - 获取卷烟各种价类、价位段等基础数据",slug:"getpriceinfo-获取卷烟各种价类、价位段等基础数据",normalizedTitle:"getpriceinfo - 获取卷烟各种价类、价位段等基础数据",charIndex:1076},{level:2,title:"统一指标定义",slug:"统一指标定义",normalizedTitle:"统一指标定义",charIndex:2631},{level:3,title:"listIndexDesc - 获取指标定义接口",slug:"listindexdesc-获取指标定义接口",normalizedTitle:"listindexdesc - 获取指标定义接口",charIndex:2642}],headersStr:"平台扩展 listUserTreeApi - 用户树 商业公司 listComTreeApi - 公司树 规格产品 listItemTreeApi - 卷烟树 getPriceInfo - 获取卷烟各种价类、价位段等基础数据 统一指标定义 listIndexDesc - 获取指标定义接口",content:'# 平台扩展\n\n\n# listUserTreeApi - 用户树\n\n * url: ${config.ucExtServerContext}/tree/uc-user/listUserTree\n * method: GET\n\n# 参数\n\n参数名               类型       必传      说明\nparams            Object   true    \n├─ orgCode        string   false   业务组织类型\n├─ manageUnitId   string   false   管理单元Id\n└─ isBindUser     string   false   是否筛选绑定了用户的组织节点; 1: 是，0: 否; 默认1\n\n# 返回\n\n{\n  "code": 1,\n  "data": [\n    {\n      "id": "组织节点Id或用户Id",\n      "title": "组织节点名称或用户名称",\n      "parentId": "上级节点Id",\n      "nodeLevel": "节点level，组织节点为 org_unit_${组织节点级别}，用户节点为user",\n      "leafNumber": 10, // 该节点下所有叶子节点的个数，包括非直接子节点\n      "children": [ ... ] // 子节点\n    },\n  ]\n}\n\n\n# 样例\n\nimport { listUserTreeApi } from \'@indfnd/utils\'\n\nasync getUserTree() {\n  try {\n    const { data } = await listUserTreeApi({})\n  } catch(e) {}\n}\n\n\n\n# 商业公司\n\n\n# listComTreeApi - 公司树\n\n * url: ${config.ismAmServerContext}/tree/com/listComTree\n * method: GET\n\n具体参数与返回格式请查看 demo ，可以更直观地感受不同参数的区别\n\n\n# 规格产品\n\n\n# listItemTreeApi - 卷烟树\n\n * url: ${config.ismAmServerContext}/tree/com/listItemTree\n * method: GET\n\n具体参数与返回格式请查看 demo ，可以更直观地感受不同参数的区别\n\n\n# getPriceInfo - 获取卷烟各种价类、价位段等基础数据\n\n * url: ${config.ismAmServerContext}/basic/getPriceInfo\n * method: GET\n\n# 返回\n\n{\n  "code": 1,\n  "data": {\n    // 价位段列表\n    "priceSegs": [\n      {\n        "priceSegCode": "价位段编码",\n        "priceSegName": "价位段名称",\n        "beginPriceVal": "起始价格",\n        "endPriceVal": "截止价格",\n      }\n    ],\n    // 档位列表\n    "gears": [\n      {\n        "gearCode": "档位编码",\n        "gearName": "档位名称",\n        "beginPriceVal": "起始价格",\n        "endPriceVal": "截止价格",\n      }\n    ],\n    // 大类列表\n    "kinds": [\n      {\n        "kindCode": "大类编码",\n        "kindName": "大类名称",\n        "beginPriceVal": "起始价格",\n        "endPriceVal": "截止价格",\n      }\n    ],\n    // 价类列表\n    "priceCodes": [\n      {\n        "priceCode": "细分价类编码",\n        "priceName": "细分价类名称",\n        "kindCode": "所属大类编码",\n        "priceType": "价格区间类型 10：不含税调拨价,20：含税调拨价,30：批发价,40：零售价",\n        "kindDetailCode": "所属档位编码",\n        "beginPriceVal": "起始价格",\n        "endPriceVal": "截止价格",\n      }\n    ],\n    // 品牌列表\n    "brands": [\n      {\n        "brandCode": "品牌编码",\n        "icomCode": "工业公司编码",\n        "brandName": "品牌名称",\n        "brandIsDoubleFifteen": "双十五品牌",\n        "brandIsKey": "重点品牌",\n        "isUse": "是否使用",\n        "seq": "排序",\n      }\n    ],\n    // 零售价段\n    "retailPrices": [\n      {\n        "priceCode": "细分价类编码",\n        "priceName": "细分价类名称",\n        "kindCode": "所属大类编码",\n        "priceType": "价格区间类型 10：不含税调拨价,20：含税调拨价,30：批发价,40：零售价",\n        "kindDetailCode": "所属档位编码",\n        "beginPriceVal": "起始价格",\n        "endPriceVal": "截止价格",\n      }\n    ],\n  }\n}\n\n\n\n# 统一指标定义\n\n\n# listIndexDesc - 获取指标定义接口\n\nTODO',normalizedContent:'# 平台扩展\n\n\n# listusertreeapi - 用户树\n\n * url: ${config.ucextservercontext}/tree/uc-user/listusertree\n * method: get\n\n# 参数\n\n参数名               类型       必传      说明\nparams            object   true    \n├─ orgcode        string   false   业务组织类型\n├─ manageunitid   string   false   管理单元id\n└─ isbinduser     string   false   是否筛选绑定了用户的组织节点; 1: 是，0: 否; 默认1\n\n# 返回\n\n{\n  "code": 1,\n  "data": [\n    {\n      "id": "组织节点id或用户id",\n      "title": "组织节点名称或用户名称",\n      "parentid": "上级节点id",\n      "nodelevel": "节点level，组织节点为 org_unit_${组织节点级别}，用户节点为user",\n      "leafnumber": 10, // 该节点下所有叶子节点的个数，包括非直接子节点\n      "children": [ ... ] // 子节点\n    },\n  ]\n}\n\n\n# 样例\n\nimport { listusertreeapi } from \'@indfnd/utils\'\n\nasync getusertree() {\n  try {\n    const { data } = await listusertreeapi({})\n  } catch(e) {}\n}\n\n\n\n# 商业公司\n\n\n# listcomtreeapi - 公司树\n\n * url: ${config.ismamservercontext}/tree/com/listcomtree\n * method: get\n\n具体参数与返回格式请查看 demo ，可以更直观地感受不同参数的区别\n\n\n# 规格产品\n\n\n# listitemtreeapi - 卷烟树\n\n * url: ${config.ismamservercontext}/tree/com/listitemtree\n * method: get\n\n具体参数与返回格式请查看 demo ，可以更直观地感受不同参数的区别\n\n\n# getpriceinfo - 获取卷烟各种价类、价位段等基础数据\n\n * url: ${config.ismamservercontext}/basic/getpriceinfo\n * method: get\n\n# 返回\n\n{\n  "code": 1,\n  "data": {\n    // 价位段列表\n    "pricesegs": [\n      {\n        "pricesegcode": "价位段编码",\n        "pricesegname": "价位段名称",\n        "beginpriceval": "起始价格",\n        "endpriceval": "截止价格",\n      }\n    ],\n    // 档位列表\n    "gears": [\n      {\n        "gearcode": "档位编码",\n        "gearname": "档位名称",\n        "beginpriceval": "起始价格",\n        "endpriceval": "截止价格",\n      }\n    ],\n    // 大类列表\n    "kinds": [\n      {\n        "kindcode": "大类编码",\n        "kindname": "大类名称",\n        "beginpriceval": "起始价格",\n        "endpriceval": "截止价格",\n      }\n    ],\n    // 价类列表\n    "pricecodes": [\n      {\n        "pricecode": "细分价类编码",\n        "pricename": "细分价类名称",\n        "kindcode": "所属大类编码",\n        "pricetype": "价格区间类型 10：不含税调拨价,20：含税调拨价,30：批发价,40：零售价",\n        "kinddetailcode": "所属档位编码",\n        "beginpriceval": "起始价格",\n        "endpriceval": "截止价格",\n      }\n    ],\n    // 品牌列表\n    "brands": [\n      {\n        "brandcode": "品牌编码",\n        "icomcode": "工业公司编码",\n        "brandname": "品牌名称",\n        "brandisdoublefifteen": "双十五品牌",\n        "brandiskey": "重点品牌",\n        "isuse": "是否使用",\n        "seq": "排序",\n      }\n    ],\n    // 零售价段\n    "retailprices": [\n      {\n        "pricecode": "细分价类编码",\n        "pricename": "细分价类名称",\n        "kindcode": "所属大类编码",\n        "pricetype": "价格区间类型 10：不含税调拨价,20：含税调拨价,30：批发价,40：零售价",\n        "kinddetailcode": "所属档位编码",\n        "beginpriceval": "起始价格",\n        "endpriceval": "截止价格",\n      }\n    ],\n  }\n}\n\n\n\n# 统一指标定义\n\n\n# listindexdesc - 获取指标定义接口\n\ntodo',charsets:{cjk:!0},lastUpdated:"2024-03-15 15:20:00",lastUpdatedTimestamp:17104872e5},{title:"博客文章",frontmatter:{archivesPage:!0,title:"博客文章",permalink:"/blog/",article:!1},regularPath:"/@pages/archivesPage.html",relativePath:"@pages/archivesPage.md",key:"v-44f6d96e",path:"/blog/",headersStr:null,content:"",normalizedContent:"",charsets:{},lastUpdated:"2024-01-04 14:58:51",lastUpdatedTimestamp:1704351531e3},{title:"Home",frontmatter:{home:!0,heroImage:"/img/logo.png",heroText:"@indfnd/utils",tagline:"工业营销一体化前端工具库",actionText:"快速开始 →",actionLink:"/pages/index/",bannerBg:"none",postList:"none"},regularPath:"/",relativePath:"index.md",key:"v-5f63254c",path:"/",headersStr:null,content:"",normalizedContent:"",charsets:{},lastUpdated:"2024-01-24 18:07:31",lastUpdatedTimestamp:1706090851e3}],themeConfig:{nav:[{text:"指南",link:"/pages/index/",items:[{text:"utils",link:"/pages/token/"},{text:"api",link:"/pages/7296bf/"},{text:"config",link:"/pages/config/"}]},{text:"更新日志",link:"/pages/release/"},{text:"插件测试",link:"/pages/plugin-sfc-demo/"},{text:"其它文档",items:[{text:"山东一体化开发必读",link:"http://10.110.23.208/big-front-end/notes/ind-x1/front.html"},{text:"PC组件库(努力编写文档ing)",link:"http://10.110.23.208/ind-common-doc/"},{text:"移动端组件库(规划中)",link:""}]},{text:"仓库地址",link:"http://git.inspur.com/imp-ec/ind-front/ind-utils.git"}],sidebarDepth:2,logo:"/img/logo.png",searchMaxSuggestions:10,lastUpdated:"上次更新",repo:"http://git.inspur.com/imp-ec/ind-front/ind-utils/-",docsDir:"docs/docs",docsBranch:"main",editLinks:!0,editLinkText:"编辑文档",sidebar:{"/00.指南/":[["00.概述.md","概述","/pages/index/"],{title:"工具库",collapsable:!1,children:[["01.工具库/01.Token.md","Token","/pages/token"],["01.工具库/02.Request.md","Request","/pages/request"],["01.工具库/03.缓存.md","缓存","/pages/cache"],["01.工具库/04.SM3.md","SM3","/pages/dcc8f2/"],["01.工具库/05.日期.md","日期","/pages/date"],["01.工具库/06.枚举.md","枚举","/pages/enum"],["01.工具库/07.类型判断.md","类型判断","/pages/is-type"],["01.工具库/08.数字.md","数字","/pages/number"],["01.工具库/09.半年.md","半年","/pages/b93470/"],["01.工具库/10.季度.md","季度","/pages/quarter"],["01.工具库/11.UUID.md","UUID","/pages/57e307/"],["01.工具库/12.MIME types.md","MIME types","/pages/mime-type"],["01.工具库/13.Event.md","Event","/pages/event"],["01.工具库/14.校验.md","校验","/pages/validate"],["01.工具库/15.表格.md","表格","/pages/table"],["01.工具库/16.Excel.md","Excel","/pages/excel"],["01.工具库/17.blob.md","blob","/pages/31f744/"]]},{title:"接口",collapsable:!1,children:[["02.接口/01.平台接口.md","平台接口","/pages/7296bf/"],["02.接口/02.业务接口.md","业务接口","/pages/d7f021/"]]},{title:"config",collapsable:!1,children:[["03.config/01.config.md","公共配置","/pages/config"]]}],catalogue:{},"/98.插件测试/":[["01.sfc-demo.md","sfc-demo","/pages/plugin-sfc-demo/"]],"/99.更新日志/":[["01.release.md","更新日志","/pages/release"]]},updateBar:{showToArticle:!1},titleBadge:!1,pageStyle:"line",category:!1,tag:!1,author:{name:"工业前端团队"},social:{icons:[]}}};var uc=t(93),pc=t(94),dc=t(11);var fc={computed:{$filterPosts(){return this.$site.pages.filter(n=>{const{frontmatter:{pageComponent:e,article:t,home:r}}=n;return!(e||!1===t||!0===r)})},$sortPosts(){return(n=this.$filterPosts).sort((n,e)=>{const t=n.frontmatter.sticky,r=e.frontmatter.sticky;return t&&r?t==r?Object(dc.a)(n,e):t-r:t&&!r?-1:!t&&r?1:Object(dc.a)(n,e)}),n;var n},$sortPostsByDate(){return(n=this.$filterPosts).sort((n,e)=>Object(dc.a)(n,e)),n;var n},$groupPosts(){return function(n){const e={},t={};for(let r=0,o=n.length;r<o;r++){const{frontmatter:{categories:o,tags:i}}=n[r];"array"===Object(dc.n)(o)&&o.forEach(t=>{t&&(e[t]||(e[t]=[]),e[t].push(n[r]))}),"array"===Object(dc.n)(i)&&i.forEach(e=>{e&&(t[e]||(t[e]=[]),t[e].push(n[r]))})}return{categories:e,tags:t}}(this.$sortPosts)},$categoriesAndTags(){return function(n){const e=[],t=[];for(let t in n.categories)e.push({key:t,length:n.categories[t].length});for(let e in n.tags)t.push({key:e,length:n.tags[e].length});return{categories:e,tags:t}}(this.$groupPosts)}}};Gt.component(uc.default),Gt.component(pc.default);function hc(n){return n.toString().padStart(2,"0")}t(243);Gt.component("Demo",()=>Promise.all([t.e(0),t.e(4)]).then(t.bind(null,376))),Gt.component("test-test",()=>Promise.all([t.e(0),t.e(5)]).then(t.bind(null,377))),Gt.component("table-row-column",()=>Promise.all([t.e(3),t.e(8)]).then(t.bind(null,374))),Gt.component("Badge",()=>Promise.all([t.e(0),t.e(6)]).then(t.bind(null,403))),Gt.component("CodeBlock",()=>Promise.resolve().then(t.bind(null,93))),Gt.component("CodeGroup",()=>Promise.resolve().then(t.bind(null,94)));t(244);var mc=[({Vue:n,options:e,router:t,siteData:r,isServer:o})=>{},({Vue:n,options:e,router:t,siteData:r})=>{r.pages.map(n=>{const{frontmatter:{date:e,author:t}}=n;"string"==typeof e&&"Z"===e.charAt(e.length-1)&&(n.frontmatter.date=function(n){n instanceof Date||(n=new Date(n));return`${n.getUTCFullYear()}-${hc(n.getUTCMonth()+1)}-${hc(n.getUTCDate())} ${hc(n.getUTCHours())}:${hc(n.getUTCMinutes())}:${hc(n.getUTCSeconds())}`}(e)),t?n.author=t:r.themeConfig.author&&(n.author=r.themeConfig.author)}),n.mixin(fc)},{},({Vue:n})=>{n.mixin({computed:{$dataBlock(){return this.$options.__data__block__}}})},{},{}],gc=[];class vc extends class{constructor(){this.store=new Gt({data:{state:{}}})}$get(n){return this.store.state[n]}$set(n,e){Gt.set(this.store.state,n,e)}$emit(...n){this.store.$emit(...n)}$on(...n){this.store.$on(...n)}}{}Object.assign(vc.prototype,{getPageAsyncComponent:ss,getLayoutAsyncComponent:cs,getAsyncComponent:ls,getVueComponent:us});var yc={install(n){const e=new vc;n.$vuepress=e,n.prototype.$vuepress=e}};function bc(n,e){const t=e.toLowerCase();return n.options.routes.some(n=>n.path.toLowerCase()===t)}var xc={props:{pageKey:String,slotKey:{type:String,default:"default"}},render(n){const e=this.pageKey||this.$parent.$page.key;return ds("pageKey",e),Gt.component(e)||Gt.component(e,ss(e)),Gt.component(e)?n(e):n("")}},_c={functional:!0,props:{slotKey:String,required:!0},render:(n,{props:e,slots:t})=>n("div",{class:["content__"+e.slotKey]},t()[e.slotKey])},wc={computed:{openInNewWindowTitle(){return this.$themeLocaleConfig.openNewWindowText||"(opens new window)"}}},Tc=(t(245),t(246),Object(ac.a)(wc,(function(){var n=this._self._c;return n("span",[n("svg",{staticClass:"icon outbound",attrs:{xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",x:"0px",y:"0px",viewBox:"0 0 100 100",width:"15",height:"15"}},[n("path",{attrs:{fill:"currentColor",d:"M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"}}),this._v(" "),n("polygon",{attrs:{fill:"currentColor",points:"45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"}})]),this._v(" "),n("span",{staticClass:"sr-only"},[this._v(this._s(this.openInNewWindowTitle))])])}),[],!1,null,null,null).exports),Ec={functional:!0,render(n,{parent:e,children:t}){if(e._isMounted)return t;e.$once("hook:mounted",()=>{e.$forceUpdate()})}};Gt.config.productionTip=!1,Gt.use(Va),Gt.use(yc),Gt.mixin(function(n,e,t=Gt){!function(n){n.locales&&Object.keys(n.locales).forEach(e=>{n.locales[e].path=e});Object.freeze(n)}(e),t.$vuepress.$set("siteData",e);const r=new(n(t.$vuepress.$get("siteData"))),o=Object.getOwnPropertyDescriptors(Object.getPrototypeOf(r)),i={};return Object.keys(o).reduce((n,e)=>(e.startsWith("$")&&(n[e]=o[e].get),n),i),{computed:i}}(n=>class{setPage(n){this.__page=n}get $site(){return n}get $themeConfig(){return this.$site.themeConfig}get $frontmatter(){return this.$page.frontmatter}get $localeConfig(){const{locales:n={}}=this.$site;let e,t;for(const r in n)"/"===r?t=n[r]:0===this.$page.path.indexOf(r)&&(e=n[r]);return e||t||{}}get $siteTitle(){return this.$localeConfig.title||this.$site.title||""}get $canonicalUrl(){const{canonicalUrl:n}=this.$page.frontmatter;return"string"==typeof n&&n}get $title(){const n=this.$page,{metaTitle:e}=this.$page.frontmatter;if("string"==typeof e)return e;const t=this.$siteTitle,r=n.frontmatter.home?null:n.frontmatter.title||n.title;return t?r?r+" | "+t:t:r||"VuePress"}get $description(){const n=function(n){if(n){const e=n.filter(n=>"description"===n.name)[0];if(e)return e.content}}(this.$page.frontmatter.meta);return n||(this.$page.frontmatter.description||this.$localeConfig.description||this.$site.description||"")}get $lang(){return this.$page.frontmatter.lang||this.$localeConfig.lang||"en-US"}get $localePath(){return this.$localeConfig.path||"/"}get $themeLocaleConfig(){return(this.$site.themeConfig.locales||{})[this.$localePath]||{}}get $page(){return this.__page?this.__page:function(n,e){for(let t=0;t<n.length;t++){const r=n[t];if(r.path.toLowerCase()===e.toLowerCase())return r}return{path:"",frontmatter:{}}}(this.$site.pages,this.$route.path)}},lc)),Gt.component("Content",xc),Gt.component("ContentSlotsDistributor",_c),Gt.component("OutboundLink",Tc),Gt.component("ClientOnly",Ec),Gt.component("Layout",cs("Layout")),Gt.component("NotFound",cs("NotFound")),Gt.prototype.$withBase=function(n){const e=this.$site.base;return"/"===n.charAt(0)?e+n.slice(1):n},window.__VUEPRESS__={version:"1.9.9",hash:"53da43b"},async function(n){const e="undefined"!=typeof window&&window.__VUEPRESS_ROUTER_BASE__?window.__VUEPRESS_ROUTER_BASE__:lc.routerBase||lc.base,t=new Va({base:e,mode:"history",fallback:!1,routes:cc,scrollBehavior:(n,e,t)=>t||(n.hash?!Gt.$vuepress.$get("disableScrollBehavior")&&{selector:decodeURIComponent(n.hash)}:{x:0,y:0})});!function(n){n.beforeEach((e,t,r)=>{if(bc(n,e.path))r();else if(/(\/|\.html)$/.test(e.path))if(/\/$/.test(e.path)){const t=e.path.replace(/\/$/,"")+".html";bc(n,t)?r(t):r()}else r();else{const t=e.path+"/",o=e.path+".html";bc(n,o)?r(o):bc(n,t)?r(t):r()}})}(t);const r={};try{await Promise.all(mc.filter(n=>"function"==typeof n).map(e=>e({Vue:Gt,options:r,router:t,siteData:lc,isServer:n})))}catch(n){console.error(n)}return{app:new Gt(Object.assign(r,{router:t,render:n=>n("div",{attrs:{id:"app"}},[n("RouterView",{ref:"layout"}),n("div",{class:"global-ui"},gc.map(e=>n(e)))])})),router:t}}(!1).then(({app:n,router:e})=>{e.onReady(()=>{n.$mount("#app")})})}]);