<template>
  <van-form @submit="handleSubmit">
    <div class="login-form" style="margin: 0 20px">
      <van-field
        label-width="4rem"
        v-model="form.userName"
        name="账号"
        label="账  号"
        placeholder="请输入账号"
        :rules="[{ required: true, message: '请填写账号' }]"
      />
      <van-field
        label-width="4rem"
        v-model="form.password"
        type="password"
        name="密码"
        label="密  码"
        placeholder="请输入密码"
        :rules="[{ required: true, message: '请填写密码' }]"
      />
      <van-field
        label-width="4rem"
        v-model="form.validCode"
        name="验证码"
        label="验证码"
        placeholder="请输入验证码"
        :rules="[{ required: true, message: '请填写验证码' }]"
        class="last-cell"
      >
        <template #button>
          <van-image
            style="display: block"
            type="contain"
            width="80"
            :src="captchaUrl"
            @click="getCaptcha"
          />
        </template>
      </van-field>
    </div>
    <div v-show="msg != ''" style="padding: 0 18px; color: red">{{ msg }}</div>
    <div style="margin: 30px 20px 20px 20px">
      <van-button :loading="loading" block type="info" native-type="submit" class="button-submit"
        >登录</van-button
      >
    </div>
  </van-form>
</template>

<script>
import { Form, Field, Button, Image } from 'vant'

import config from '@lambo-design-mobile/shared/config/config'
import {
  getLocalStorage,
  removeLocalStorage,
  setLocalStorage,
} from '@lambo-design-mobile/shared/utils/platform'
import { guid } from '@lambo-design-mobile/shared/utils/number'
import crypto from '@lambo-design-mobile/shared/utils/crypto'

export default {
  name: 'login-form',

  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [Image.name]: Image,
    [Button.name]: Button,
  },
  props: {
    loading: Boolean,
    userNameRules: {
      type: Array,
      default: () => [{ required: true, message: '账号不能为空', trigger: 'blur' }],
    },
    passwordRules: {
      type: Array,
      default: () => [{ required: true, message: '密码不能为空', trigger: 'blur' }],
    },
    validCodeRules: {
      type: Array,
      default: () => [{ required: true, message: '验证码不能为空', trigger: 'blur' }],
    },
    msg: {
      type: String,
      default: () => '',
    },
    captchaImgUrl: {
      type: String,
      default: null,
    },
    pasCryptor: {
      type: String,
      default: 'sm3',
    },
  },
  data() {
    return {
      form: { userName: '', password: '', validCode: '' },
      rememberMe: false,
      validCodeId: '',
    }
  },
  watch: {
    loading() {
      if (!this.loading) this.getCaptcha()
    },
  },
  computed: {
    rules() {
      return {
        userName: this.userNameRules,
        password: this.passwordRules,
        validCode: this.validCodeRules,
      }
    },
    captchaUrl() {
      if (this.captchaImgUrl) {
        let { captchaImgUrl } = this
        if (captchaImgUrl.indexOf('?') > -1) {
          captchaImgUrl += '&t=' + this.validCodeId
        } else {
          captchaImgUrl += '?t=' + this.validCodeId
        }
        return captchaImgUrl
      }
      return config.upmsServerContext + '/anon/user/getCaptcha/' + this.validCodeId
    },
  },
  created() {
    this.getCaptcha()
    this.getLocalStorage()
  },
  methods: {
    getCaptcha() {
      this.validCodeId = guid()
    },
    getCryptorPassword(password, validCode, cryptoMethod) {
      return crypto.encrypt(
        crypto.encrypt(password, cryptoMethod, true) + validCode,
        cryptoMethod,
        false,
      )
    },
    handleSubmit() {
      const params = {
        userName: this.form.userName,
        password: this.form.password,
        validCodeInput: this.form.validCode,
        validCodeId: this.validCodeId,
      }
      if (this.pasCryptor) {
        params.pasCryptor = this.pasCryptor
        params.pasEncode = this.getCryptorPassword(
          this.form.password,
          this.form.validCode,
          this.pasCryptor,
        )
      } else {
        // params.pasMd5 = this.getCryptorPassword(this.form.password,this.form.validCode,'md5');
        // params.pasSm3 = this.getCryptorPassword(this.form.password,this.form.validCode,'sm3');
      }
      this.$emit('on-success-valid', params)
      this.setLocalUserId(this.form.userName)
      if (this.rememberMe === true) {
        this.setLocalStorage(this.form.userName)
      }
      if (this.rememberMe === false) {
        this.clearLocalStorage()
      }
    },
    getLocalStorage() {
      this.form.userName = getLocalStorage('username')
    },
    setLocalStorage(name) {
      setLocalStorage('username', name)
    },
    setLocalUserId(name) {
      setLocalStorage('userId', name)
    },
    clearLocalStorage() {
      removeLocalStorage('username')
    },
  },
}
</script>
