(window.webpackJsonp=window.webpackJsonp||[]).push([[43],{578:function(t,v,_){"use strict";_.r(v);var e=_(15),a=Object(e.a)({},(function(){var t=this,v=t._self._c;return v("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[v("h2",{attrs:{id:"属性"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#属性"}},[t._v("#")]),t._v(" 属性")]),t._v(" "),v("table",[v("thead",[v("tr",[v("th",[t._v("属性")]),t._v(" "),v("th",[t._v("类型")]),t._v(" "),v("th",[t._v("默认值")]),t._v(" "),v("th",[t._v("说明")])])]),t._v(" "),v("tbody",[v("tr",[v("td",[v("code",[t._v("数据配置")])]),t._v(" "),v("td"),t._v(" "),v("td"),t._v(" "),v("td")]),t._v(" "),v("tr",[v("td",[t._v("value")]),t._v(" "),v("td",[t._v("Object")]),t._v(" "),v("td",[t._v("{}")]),t._v(" "),v("td",[t._v("表单的数据定义，推荐使用v-model")])]),t._v(" "),v("tr",[v("td",[t._v("fieldList")]),t._v(" "),v("td",[t._v("Array")]),t._v(" "),v("td",[t._v("[]")]),t._v(" "),v("td",[t._v("表单的一项定义，详见表单项属性")])]),t._v(" "),v("tr",[v("td",[t._v("gridColumns")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("3")]),t._v(" "),v("td",[t._v("一行展示表单的列数，不设置默认为3列，查询条件的表单仅支持4列")])]),t._v(" "),v("tr",[v("td",[t._v("labelWidth")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("200")]),t._v(" "),v("td",[t._v("表单的label宽度，单位为像素，一般不建议修改")])]),t._v(" "),v("tr",[v("td",[t._v("formType")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("form")]),t._v(" "),v("td",[t._v("search:查询条件表单 form:业务表单")])]),t._v(" "),v("tr",[v("td",[t._v("searchLoading")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("查询条件表单的业务场景下，查询按钮的禁用标志")])]),t._v(" "),v("tr",[v("td",[t._v("queryBtn")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("true")]),t._v(" "),v("td",[t._v("展示查询条件，有些个性化的查询不需要查询条件")])]),t._v(" "),v("tr",[v("td",[t._v("funId")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("$route.meta.permissionId")]),t._v(" "),v("td",[t._v("当前form表单的唯一功能主键，用于持久化预置查询条件的预置数据")])])])]),t._v(" "),v("h2",{attrs:{id:"表单项属性"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#表单项属性"}},[t._v("#")]),t._v(" 表单项属性")]),t._v(" "),v("table",[v("thead",[v("tr",[v("th",[t._v("属性")]),t._v(" "),v("th",[t._v("类型")]),t._v(" "),v("th",[t._v("默认值")]),t._v(" "),v("th",[t._v("说明")])])]),t._v(" "),v("tbody",[v("tr",[v("td",[t._v("title")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("表单标题")])]),t._v(" "),v("tr",[v("td",[t._v("formKey")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("表单元素的键，值存在v-model")])]),t._v(" "),v("tr",[v("td",[t._v("type")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("''")]),t._v(" "),v("td",[t._v("表单项的渲染类型，默认文本，"),v("code",[t._v("input \\| number \\| textarea \\| select \\| checkbox \\| radio \\| treeselect \\| datepicker \\| fileUpload \\| imgUpload")])])]),t._v(" "),v("tr",[v("td",[t._v("props")]),t._v(" "),v("td",[t._v("Object")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("传给表单项的渲染子组件的属性，与使用原组件的属性一致")])]),t._v(" "),v("tr",[v("td",[t._v("span")]),t._v(" "),v("td",[t._v("Number")]),t._v(" "),v("td",[t._v("8")]),t._v(" "),v("td",[t._v("栅格布局占据的空间树，默认即可，支持自定义 8 | 12 | 24")])]),t._v(" "),v("tr",[v("td",[t._v("enumKey")]),t._v(" "),v("td",[t._v("String")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("单选、多选、下拉 的数据源枚举值，系统配置的枚举值ID，组件自动完成枚举值的获取")])]),t._v(" "),v("tr",[v("td",[t._v("enumList")]),t._v(" "),v("td",[t._v("Array")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("单选、多选、下拉 的数据源选项，[{K: '', V: ''}]")])]),t._v(" "),v("tr",[v("td",[t._v("required")]),t._v(" "),v("td",[t._v("Boolean")]),t._v(" "),v("td",[t._v("false")]),t._v(" "),v("td",[t._v("控制是否必填选项")])]),t._v(" "),v("tr",[v("td",[t._v("validate")]),t._v(" "),v("td",[t._v("Function")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("自定义校验函数"),v("code",[t._v('(rule, value, callback) => { return callback(new Error("不能为空！")); }')])])]),t._v(" "),v("tr",[v("td",[t._v("render")]),t._v(" "),v("td",[t._v("Function")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("自定义渲染函数"),v("code",[t._v("(h, fieldDef, form)=>{}")])])]),t._v(" "),v("tr",[v("td",[t._v("disabled")]),t._v(" "),v("td",[t._v("Function | Boolean")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("用于控制表单元素只读，可设置为Boolean或者返回Boolean的函数"),v("code",[t._v("(form)=>{ return true }")])])]),t._v(" "),v("tr",[v("td",[t._v("condition")]),t._v(" "),v("td",[t._v("Function | Boolean")]),t._v(" "),v("td",[t._v("无")]),t._v(" "),v("td",[t._v("用于控制该表单项展示隐藏，可设置为Boolean或者返回Boolean的函数，否则仅在函数返回true的时候展示"),v("code",[t._v("(form)=>{ return true }")])])])])]),t._v(" "),v("h2",{attrs:{id:"api"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#api"}},[t._v("#")]),t._v(" Api")]),t._v(" "),v("ul",[v("li",[v("code",[t._v("validate")]),t._v(" 校验form表单当前输入值是否合法")]),t._v(" "),v("li",[v("code",[t._v("validateField")]),t._v(" 校验form表单表单项当前输入值是否合法，callback是回调函数，可不传 "),v("code",[t._v("validateField(key, callback)")])]),t._v(" "),v("li",[v("code",[t._v("resetFields")]),t._v(" 重置form校验")])]),t._v(" "),v("h2",{attrs:{id:"事件"}},[v("a",{staticClass:"header-anchor",attrs:{href:"#事件"}},[t._v("#")]),t._v(" 事件")]),t._v(" "),v("ul",[v("li",[v("code",[t._v("defaultCacheDone")]),t._v(" 取到预设查询条件默认值之后的回调函数")])]),t._v(" "),v("div",{staticClass:"language-js extra-class"},[v("pre",{pre:!0,attrs:{class:"language-js"}},[v("code",[v("span",{pre:!0,attrs:{class:"token function"}},[t._v("defaultCacheDone")]),v("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),v("span",{pre:!0,attrs:{class:"token parameter"}},[t._v("form")]),v("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),t._v(" "),v("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n  "),v("span",{pre:!0,attrs:{class:"token keyword"}},[t._v("this")]),v("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(".")]),t._v("form "),v("span",{pre:!0,attrs:{class:"token operator"}},[t._v("=")]),t._v(" form "),v("span",{pre:!0,attrs:{class:"token comment"}},[t._v("// 将取到的值赋给当前页面的查询条件")]),t._v("\n  "),v("span",{pre:!0,attrs:{class:"token keyword"}},[t._v("this")]),v("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(".")]),v("span",{pre:!0,attrs:{class:"token function"}},[t._v("query")]),v("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),v("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),t._v(" "),v("span",{pre:!0,attrs:{class:"token comment"}},[t._v("// 执行查询")]),t._v("\n"),v("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),v("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(",")]),t._v("\n")])])])])}),[],!1,null,null,null);v.default=a.exports}}]);