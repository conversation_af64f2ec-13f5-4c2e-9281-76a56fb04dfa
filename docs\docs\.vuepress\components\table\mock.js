export function getPreparationList(params) {
  return Promise.resolve({
    code: 1,
    message: 'success',
    data: {
      contractList: [
        {
          itemDayPlanCode: 'SCWP2023022100002000001751',
          pcomCode: '11510001',
          pcomSname: '四川',
          comCode: '11510101',
          contDelivWhseId: '203700010001001',
          comContReachWhseId: '450e235854378ffbbe7a72c3da7cb368',
          itemDayReqCode: 'SCWP2023022100002',
          comSname: '成都',
          contDelivWhseName: '青岛卷烟厂',
          comContReachWhseName: '四川省烟草公司成都市公司物流中心仓库',
          expecDelivDate: '20230301',
          createTime: '20240112154051',
          expecArrivalDate: '20230302',
          itemDayPlanType: '10',
          itemDayPlanStatus: '1',
          isMonopolyPuh: null,
          isCombine: null,
          itemDayPlanNote: null,
          itemDayPlanTrayType: '0',
          sumQtyPlan: 100,
          sumQty: 42,
          itemList: [
            {
              itemDayPlanId: '00bf1d38-b36f-11ed-a363-0242ac',
              itemCode: '6901028147828',
              itemName: '泰山(新东方)',
              itemDayPlanQty: null,
              qty: 20,
              rateDay: 0,
              preMeasurement: 0,
              distSize: null,
              marketPrice: null,
            },
            {
              itemDayPlanId: '00bf2d9b-b36f-11ed-a363-0242ac',
              itemCode: '6901028148764',
              itemName: '泰山(领秀)',
              itemDayPlanQty: null,
              qty: 22,
              rateDay: 0,
              preMeasurement: 0,
              distSize: null,
              marketPrice: null,
            },
          ],
          num: 2,
          preMeasurement: 0,
        },
        {
          itemDayPlanCode: 'SCWP2023022100002000002091',
          pcomCode: '11510001',
          pcomSname: '四川',
          comCode: '11510101',
          contDelivWhseId: '203700010002001',
          comContReachWhseId: '450e235854378ffbbe7a72c3da7cb368',
          itemDayReqCode: 'SCWP2023022100002',
          comSname: '成都',
          contDelivWhseName: '滕州卷烟厂',
          comContReachWhseName: '四川省烟草公司成都市公司物流中心仓库',
          expecDelivDate: '20230301',
          createTime: '20240112154051',
          expecArrivalDate: '20230302',
          itemDayPlanType: '10',
          itemDayPlanStatus: '1',
          isMonopolyPuh: null,
          isCombine: null,
          itemDayPlanNote: null,
          itemDayPlanTrayType: '0',
          sumQtyPlan: 100,
          sumQty: 18,
          itemList: [
            {
              itemDayPlanId: '00bf23f7-b36f-11ed-a363-0242ac',
              itemCode: '6901028148481',
              itemName: '泰山(好客)',
              itemDayPlanQty: null,
              qty: 18,
              rateDay: 0,
              preMeasurement: 0,
              distSize: null,
              marketPrice: null,
            },
          ],
          num: 1,
          preMeasurement: 0,
        },
        {
          itemDayPlanCode: 'SCWP2023022100002000001752',
          pcomCode: '11510001',
          pcomSname: '四川',
          comCode: '11510101',
          contDelivWhseId: '203700010001001',
          comContReachWhseId: '450e235854378ffbbe7a72c3da7cb368',
          itemDayReqCode: 'SCWP2023022100002',
          comSname: '成都',
          contDelivWhseName: '青岛卷烟厂',
          comContReachWhseName: '四川省烟草公司成都市公司物流中心仓库',
          expecDelivDate: '20230301',
          createTime: '20240112154051',
          expecArrivalDate: '20230302',
          itemDayPlanType: '10',
          itemDayPlanStatus: '1',
          isMonopolyPuh: null,
          isCombine: null,
          itemDayPlanNote: null,
          itemDayPlanTrayType: '0',
          sumQtyPlan: 100,
          sumQty: 13,
          itemList: [
            {
              itemDayPlanId: '00bf2b55-b36f-11ed-a363-0242ac',
              itemCode: '6901028148603',
              itemName: '泰山(拂光细支)',
              itemDayPlanQty: null,
              qty: 13,
              rateDay: 3,
              preMeasurement: 0,
              distSize: null,
              marketPrice: null,
            },
          ],
          num: 1,
          preMeasurement: 0,
        },
      ],
      itemList: [
        {
          itemCode: '6901028147828',
          itemName: '泰山(新东方)',
          qtyPlan: 0,
          qty: 0,
          priTax: null,
          itemStore: null,
          priNoTax: null,
          taryCapacity: null,
        },
        {
          itemCode: '6901028148764',
          itemName: '泰山(领秀)',
          qtyPlan: 0,
          qty: 0,
          priTax: null,
          itemStore: null,
          priNoTax: null,
          taryCapacity: null,
        },
        {
          itemCode: '6901028148481',
          itemName: '泰山(好客)',
          qtyPlan: 0,
          qty: 0,
          priTax: null,
          itemStore: null,
          priNoTax: null,
          taryCapacity: null,
        },
        {
          itemCode: '6901028148603',
          itemName: '泰山(拂光细支)',
          qtyPlan: 0,
          qty: 0,
          priTax: null,
          itemStore: null,
          priNoTax: null,
          taryCapacity: null,
        },
      ],
    },
  })
}

export function getPreparationTreeList(params) {
  return Promise.resolve({
    code: 1,
    message: 'success',
    data: {
      contractList: [
        {
          itemDayPlanCode: 'SCWP2023022100002000001751',
          pcomCode: '11510001',
          pcomSname: '四川',
          comCode: '11510101',
          contDelivWhseId: '203700010001001',
          comContReachWhseId: '450e235854378ffbbe7a72c3da7cb368',
          itemDayReqCode: 'SCWP2023022100002',
          comSname: '四川',
          contDelivWhseName: '青岛卷烟厂',
          comContReachWhseName: '四川省烟草公司成都市公司物流中心仓库',
          expecDelivDate: '20230301',
          createTime: '20240112154051',
          expecArrivalDate: '20230302',
          itemDayPlanType: '10',
          itemDayPlanStatus: '1',
          isMonopolyPuh: null,
          isCombine: null,
          itemDayPlanNote: null,
          itemDayPlanTrayType: '0',
          sumQtyPlan: 100,
          sumQty: 42,
          itemList: [
            {
              itemDayPlanId: '00bf1d38-b36f-11ed-a363-0242ac',
              itemCode: '6901028147828',
              itemName: '泰山(新东方)',
              itemDayPlanQty: null,
              qty: 20,
              rateDay: 0,
              preMeasurement: 0,
              distSize: null,
              marketPrice: null,
            },
            {
              itemDayPlanId: '00bf2d9b-b36f-11ed-a363-0242ac',
              itemCode: '6901028148764',
              itemName: '泰山(领秀)',
              itemDayPlanQty: null,
              qty: 22,
              rateDay: 0,
              preMeasurement: 0,
              distSize: null,
              marketPrice: null,
            },
          ],
          num: 2,
          preMeasurement: 0,
          children: [
            {
              itemDayPlanCode: 'SCWP2023022100002000001751',
              pcomCode: '11510001',
              pcomSname: '四川',
              comCode: '11510101',
              contDelivWhseId: '203700010001001',
              comContReachWhseId: '450e235854378ffbbe7a72c3da7cb368',
              itemDayReqCode: 'SCWP2023022100002',
              comSname: '成都',
              contDelivWhseName: '青岛卷烟厂',
              comContReachWhseName: '四川省烟草公司成都市公司物流中心仓库',
              expecDelivDate: '20230301',
              createTime: '20240112154051',
              expecArrivalDate: '20230302',
              itemDayPlanType: '10',
              itemDayPlanStatus: '1',
              isMonopolyPuh: null,
              isCombine: null,
              itemDayPlanNote: null,
              itemDayPlanTrayType: '0',
              sumQtyPlan: 100,
              sumQty: 42,
              itemList: [
                {
                  itemDayPlanId: '00bf1d38-b36f-11ed-a363-0242ac',
                  itemCode: '6901028147828',
                  itemName: '泰山(新东方)',
                  itemDayPlanQty: null,
                  qty: 20,
                  rateDay: 0,
                  preMeasurement: 0,
                  distSize: null,
                  marketPrice: null,
                },
                {
                  itemDayPlanId: '00bf2d9b-b36f-11ed-a363-0242ac',
                  itemCode: '6901028148764',
                  itemName: '泰山(领秀)',
                  itemDayPlanQty: null,
                  qty: 22,
                  rateDay: 0,
                  preMeasurement: 0,
                  distSize: null,
                  marketPrice: null,
                },
              ],
              num: 2,
              preMeasurement: 0,
            },
            {
              itemDayPlanCode: 'SCWP2023022100002000002091',
              pcomCode: '11510001',
              pcomSname: '四川',
              comCode: '11510101',
              contDelivWhseId: '203700010002001',
              comContReachWhseId: '450e235854378ffbbe7a72c3da7cb368',
              itemDayReqCode: 'SCWP2023022100002',
              comSname: '绵阳',
              contDelivWhseName: '滕州卷烟厂',
              comContReachWhseName: '四川省烟草公司成都市公司物流中心仓库',
              expecDelivDate: '20230301',
              createTime: '20240112154051',
              expecArrivalDate: '20230302',
              itemDayPlanType: '10',
              itemDayPlanStatus: '1',
              isMonopolyPuh: null,
              isCombine: null,
              itemDayPlanNote: null,
              itemDayPlanTrayType: '0',
              sumQtyPlan: 100,
              sumQty: 18,
              itemList: [
                {
                  itemDayPlanId: '00bf23f7-b36f-11ed-a363-0242ac',
                  itemCode: '6901028148481',
                  itemName: '泰山(好客)',
                  itemDayPlanQty: null,
                  qty: 18,
                  rateDay: 0,
                  preMeasurement: 0,
                  distSize: null,
                  marketPrice: null,
                },
              ],
              num: 1,
              preMeasurement: 0,
            },
            {
              itemDayPlanCode: 'SCWP2023022100002000001752',
              pcomCode: '11510001',
              pcomSname: '四川',
              comCode: '11510101',
              contDelivWhseId: '203700010001001',
              comContReachWhseId: '450e235854378ffbbe7a72c3da7cb368',
              itemDayReqCode: 'SCWP2023022100002',
              comSname: '雅安',
              contDelivWhseName: '青岛卷烟厂',
              comContReachWhseName: '四川省烟草公司成都市公司物流中心仓库',
              expecDelivDate: '20230301',
              createTime: '20240112154051',
              expecArrivalDate: '20230302',
              itemDayPlanType: '10',
              itemDayPlanStatus: '1',
              isMonopolyPuh: null,
              isCombine: null,
              itemDayPlanNote: null,
              itemDayPlanTrayType: '0',
              sumQtyPlan: 100,
              sumQty: 13,
              itemList: [
                {
                  itemDayPlanId: '00bf2b55-b36f-11ed-a363-0242ac',
                  itemCode: '6901028148603',
                  itemName: '泰山(拂光细支)',
                  itemDayPlanQty: null,
                  qty: 13,
                  rateDay: 3,
                  preMeasurement: 0,
                  distSize: null,
                  marketPrice: null,
                },
              ],
              num: 1,
              preMeasurement: 0,
            },
          ],
        },
      ],
      itemList: [
        {
          itemCode: '6901028147828',
          itemName: '泰山(新东方)',
          qtyPlan: 0,
          qty: 0,
          priTax: null,
          itemStore: null,
          priNoTax: null,
          taryCapacity: null,
        },
        {
          itemCode: '6901028148764',
          itemName: '泰山(领秀)',
          qtyPlan: 0,
          qty: 0,
          priTax: null,
          itemStore: null,
          priNoTax: null,
          taryCapacity: null,
        },
        {
          itemCode: '6901028148481',
          itemName: '泰山(好客)',
          qtyPlan: 0,
          qty: 0,
          priTax: null,
          itemStore: null,
          priNoTax: null,
          taryCapacity: null,
        },
        {
          itemCode: '6901028148603',
          itemName: '泰山(拂光细支)',
          qtyPlan: 0,
          qty: 0,
          priTax: null,
          itemStore: null,
          priNoTax: null,
          taryCapacity: null,
        },
      ],
    },
  })
}

export function getPreparationItemList() {
  return Promise.resolve({
    code: 1,
    message: 'success',
    data: [
      {
        itemCode: '6901028147828',
        itemName: '泰山(新东方)',
        qtyPlan: 0,
        qty: 0,
        price: 100,
        amtAll: 10000,
        agreeRemain: 100,
        qtySplit: 0,
      },
      {
        itemCode: '6901028148764',
        itemName: '泰山(领秀)',
        qtyPlan: 0,
        qty: 0,
        price: 100,
        amtAll: 10000,
        agreeRemain: 100,
        qtySplit: 0,
      },
      {
        itemCode: '6901028148481',
        itemName: '泰山(好客)',
        qtyPlan: 0,
        qty: 0,
        price: 100,
        amtAll: 10000,
        agreeRemain: 100,
        qtySplit: 0,
      },
      {
        itemCode: '6901028148603',
        itemName: '泰山(拂光细支)',
        qtyPlan: 0,
        qty: 0,
        price: 100,
        amtAll: 10000,
        agreeRemain: 100,
        qtySplit: 0,
      },
    ],
  })
}
