<template>
  <transition name="fade">
    <div v-if="visible" class="dialog-overlay" @click.self="handleCancel">
      <div class="dialog-content" @animationend="onAnimationEnd">
        <div class="title">
          <img v-show="resultImage" src="./imgs/succ.png" alt="" />
          <img v-show="!resultImage" src="./imgs/warn.png" alt="" />
          <div :style="{ color: resultColor }">{{ resultTitle }}</div>
        </div>
        <div class="content">{{ getMessage }}</div>
        <div class="dialog-buttons" v-if="isExpSignin">
          <button v-if="showCancel" @click="handleCancel">取消</button>
          <button @click="exceptionSignin">异常签到</button>
        </div>
        <div class="dialog-buttons" v-else>
          <button v-if="showCancel" @click="handleCancel">取消</button>
          <button @click="handleConfirm">确认</button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import dayjs from 'dayjs'

export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: '',
    },
    message: {
      type: String,
      default: '',
    },
    flag: {
      type: Boolean,
      default: true, // dan, shuang, wu
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    // 是否异常签到按钮
    isExpSignin: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  computed: {
    getMessage() {
      return this.message
    },
    resultClass() {
      return this.resultType === 'success' ? 'success' : 'failure'
    },
    resultImage() {
      return this.resultType === 'success' ? true : false
    },
    resultColor() {
      return this.resultType === 'success' ? '#2d8cf0' : '#e33d5c'
    },
    resultTitle() {
      return this.title
    },
    resultType() {
      return this.flag ? 'success' : 'failure'
    },
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm')
      this.$emit('update:visible', false)
    },
    exceptionSignin() {
      this.$emit('cancel')
      this.$emit('update:visible', false)

      const visitSignTime = dayjs().format('YYYYMMDDHHmmss')
      const visitDate = dayjs().format('YYYYMMDD')
      const info = {
        md06RtlCustVisitSignWay: '20',
        md06RtlCustVisitDate: visitDate,
        md06RtlCustVisitBeginTime: visitSignTime,
      }
      this.$emit('SignIn', info)
    },
    handleCancel() {
      this.$emit('cancel')
      this.$emit('update:visible', false)
    },
    onAnimationEnd() {
      // 可选：在动画结束时执行的操作
    },
  },
}
</script>

<style lang="less" scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.dialog-content {
  background: #fff;
  border-radius: 8px; /* 圆角 */
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transform: translateY(-20px);
  animation: slideIn 0.5s forwards;
  width: 271px; /* 固定宽度 */
  height: 210px; /* 固定高度 */
  max-width: 90%; /* 自适应小屏幕 */
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* 垂直排列 */
  text-align: center; /* 文字居中 */
  padding-top: 21px;
  position: relative;
  .title {
    position: absolute;
    width: 100%;
    height: auto;
    top: -10px;
    img {
      width: 83px;
    }
    div {
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 17px;
    }
  }
  .content {
    position: absolute;
    top: 98px;
    height: 76px;
    width: 100%;
    font-size: 15px;
    padding: 10px 36px 0px 36px;
    box-sizing: border-box;
  }
}
.dialog-buttons {
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
}
.dialog-buttons button {
  width: 100%;
  height: 40px;
  background: #f8fafb;
  color: white;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: background 0.3s;
  color: black;
}
.dialog-buttons button:first-child {
  border-bottom-left-radius: 8px;
  border-right: 1px solid #eeeeee;
}
.dialog-buttons button:last-child {
  border-bottom-right-radius: 8px;
  border-left: 1px solid #eeeeee;
}
.dialog-buttons button:hover {
  background: #e1e5e9;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.8s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
