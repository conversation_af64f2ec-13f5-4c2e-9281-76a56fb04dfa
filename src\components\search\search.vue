<template>
  <van-sticky :offset-top="offsetTop">
    <van-search
      v-model="searchVal"
      :show-action="showFilter"
      :placeholder="placeholder"
      @search="search"
    >
      <template #action v-if="showBtn">
        <div class="search" @click="onSearch">搜索</div>
      </template>
      <template #action v-else>
        <van-image
          v-if="isFiltering"
          :src="imgActive"
          width="20"
          height="20"
          @click="displayCondition = true"
        />
        <van-image v-else :src="img" width="20" height="20" @click="displayCondition = true" />
      </template>
    </van-search>
    <van-popup
      v-if="showFilter"
      v-model="displayCondition"
      position="bottom"
      :style="{ width: '100%', height: '60%' }"
      round
    >
      <van-form @submit="confirm" style="margin: 8px">
        <slot name="bottomFilter"></slot>
        <van-row gutter="5" class="row-button">
          <van-col offset="1" span="11">
            <van-button block round size="small" type="default" @click="cancel">取消</van-button>
          </van-col>
          <van-col span="11">
            <van-button block round size="small" type="info" native-type="submit">确认</van-button>
          </van-col>
        </van-row>
      </van-form>
    </van-popup>
  </van-sticky>
</template>

<script>
import _ from 'lodash'
import { Popup, Button, Cell, Row, Col, Search, Icon, Image } from 'vant'
import shaixuanImg from './img/shaixuan.png'
import shaixuanzhongImg from './img/shaixuanzhong.png'
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Search',
  components: {
    [Popup.name]: Popup,
    [Button.name]: Button,
    [Cell.name]: Cell,
    [Row.name]: Row,
    [Col.name]: Col,
    [Search.name]: Search,
    [Icon.name]: Icon,
    [Image.name]: Image,
  },
  props: {
    //是否展示搜索按钮
    showBtn: { type: Boolean, default: false },
    placeholder: { type: String, default: '请输入搜索关键词' },
    // 是否显示搜索框外右侧内容
    showFilter: { type: Boolean, default: false },
    offsetTop: {
      type: Number,
      default: 46,
    },
    value: Object,
    fieldList: Array,
  },
  data() {
    return {
      displayCondition: false,
      searchForm: {},
      searchVal: '',
      // img: require('./img/shaixuan.png'),
      // imgActive: require('./img/shaixuanzhong.png'),
      img: shaixuanImg,
      imgActive: shaixuanzhongImg,
    }
  },
  created() {
    this.searchForm = this.value
  },
  computed: {
    isFiltering() {
      return !_.isEmpty(this.searchForm)
    },
  },
  watch: {
    value: {
      handler: function () {
        this.searchForm = this.value
      },
      deep: true,
    },
  },
  methods: {
    showCondition() {
      this.displayCondition = true
    },
    onSearch() {
      this.$emit('doSearch', this.searchVal)
    },
    search() {
      if (this.showFilter && this.searchForm) {
        this.$emit('doSearch', this.searchVal, this.searchForm)
      } else {
        this.$emit('doSearch', this.searchVal)
      }
    },
    cancel() {
      this.displayCondition = false
      this.$emit('cancel')
    },
    confirm() {
      this.displayCondition = false
      if (this.showFilter && this.searchForm) {
        console.log('demand111 confirm this.searchForm传回去', this.searchForm)
        this.$emit('doSearch', this.searchVal, this.searchForm)
      } else {
        this.$emit('doSearch', this.searchVal)
      }
    },
  },
}
</script>
<style lang="less" scoped>
.search-btn {
  height: 20px;
  width: 40px;
  line-height: 20px;
  padding: 4px 10px;
  color: #fff;
  background: #0079fe;
  border-radius: 5px;
}
.search {
  font-weight: 400;
  font-size: 13px;
  color: #2d8cf0;
  padding-left: 10px;
  border-left: 1px solid #2d8cf0;
  line-height: 13px;
}
.row-button {
  margin: 10px;
  position: fixed;
  bottom: 10px;
  width: 90%;
}
</style>
