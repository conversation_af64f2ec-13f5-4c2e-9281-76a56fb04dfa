<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>基础 | @indfnd/common-mobile</title>
    <meta name="generator" content="VuePress 1.9.9">
    <link rel="icon" href="/ind-common-mobile-doc/img/favicon.ico">
    <meta name="description" content="工业营销一体化前端PC组件库">
    <meta name="theme-color" content="#11a8cd">
    
    <link rel="preload" href="/ind-common-mobile-doc/assets/css/0.styles.5e30afbe.css" as="style"><link rel="preload" href="/ind-common-mobile-doc/assets/js/app.ea3e7bd7.js" as="script"><link rel="preload" href="/ind-common-mobile-doc/assets/js/5.497b83b0.js" as="script"><link rel="preload" href="/ind-common-mobile-doc/assets/js/43.addd5047.js" as="script"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/1.f887c01b.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/10.c2f7dbc6.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/11.2d8ced3b.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/12.be193899.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/13.1adaeb18.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/14.a59f9f15.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/15.fdb4db63.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/16.c03d7b33.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/17.d218c707.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/18.a32369c7.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/19.e29739a2.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/2.f2d3b607.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/20.d8e8e499.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/21.9b7087d5.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/22.91c48ec9.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/23.e3a0c63b.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/24.6c1647d6.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/25.ec1a045d.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/26.03e2f49e.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/27.ec2b7445.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/28.adf304b5.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/29.40926cb5.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/3.ec1afd9a.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/30.227e65be.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/31.adc66a4a.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/32.ebdd110b.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/33.fa0b77dd.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/34.d71fceff.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/35.6fab15bf.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/36.0ea76172.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/37.6f6d7fd4.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/38.e28d96d5.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/39.5df428e7.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/40.1bb464e8.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/41.7476f9d7.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/42.afe7eef2.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/44.5a95c474.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/45.eb1c19ea.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/46.d2be3b44.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/47.6ceb750c.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/48.547ac81e.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/49.ab594200.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/50.267ced76.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/51.02715180.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/52.8f251bd7.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/53.9d3cf6b2.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/54.67594984.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/55.45babe73.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/56.90566112.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/57.d94f0e39.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/58.5b35ce87.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/59.f6bb8412.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/6.3d2394bd.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/60.266896d0.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/61.9f8cab45.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/62.5498d7bd.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/63.e112b97d.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/64.0e09ce0d.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/65.735c216a.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/7.76049480.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/8.d79108b0.js"><link rel="prefetch" href="/ind-common-mobile-doc/assets/js/9.b8b68c9a.js">
    <link rel="stylesheet" href="/ind-common-mobile-doc/assets/css/0.styles.5e30afbe.css">
  </head>
  <body class="theme-mode-light">
    <div id="app" data-server-rendered="true"><div class="theme-container sidebar-open have-rightmenu"><header class="navbar blur"><div title="目录" class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/ind-common-mobile-doc/" class="home-link router-link-active"><img src="/ind-common-mobile-doc/img/logo.png" alt="@indfnd/common-mobile" class="logo"> <span class="site-name can-hide">@indfnd/common-mobile</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/ind-common-mobile-doc/pages/a42973/" class="nav-link">概述</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/700a0d/" class="nav-link">组件</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/4212d3/" class="nav-link">指令</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/2ed36d/" class="nav-link">插件</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/fc57ff/" class="nav-link">样式</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/release/" class="nav-link">更新日志</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="其它文档" class="dropdown-title"><!----> <span class="title" style="display:;">其它文档</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="http://10.110.23.208/big-front-end/notes/ind-x1/front.html" target="_blank" rel="noopener noreferrer" class="nav-link external">
  山东一体化开发必读
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="http://10.110.23.208/ind-utils-doc/pages/index/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  工具库
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="/ind-common-mobile-doc/pages/2b4076/404" class="nav-link">移动端组件库(规划中)</a></li></ul></div></div><div class="nav-item"><a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front.git" target="_blank" rel="noopener noreferrer" class="nav-link external">
  仓库地址
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></div> <a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front/-" target="_blank" rel="noopener noreferrer" class="repo-link">
    Source
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <div class="sidebar-hover-trigger"></div> <aside class="sidebar" style="display:none;"><!----> <nav class="nav-links"><div class="nav-item"><a href="/ind-common-mobile-doc/pages/a42973/" class="nav-link">概述</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/700a0d/" class="nav-link">组件</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/4212d3/" class="nav-link">指令</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/2ed36d/" class="nav-link">插件</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/fc57ff/" class="nav-link">样式</a></div><div class="nav-item"><a href="/ind-common-mobile-doc/pages/release/" class="nav-link">更新日志</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="其它文档" class="dropdown-title"><!----> <span class="title" style="display:;">其它文档</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="http://10.110.23.208/big-front-end/notes/ind-x1/front.html" target="_blank" rel="noopener noreferrer" class="nav-link external">
  山东一体化开发必读
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="http://10.110.23.208/ind-utils-doc/pages/index/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  工具库
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="/ind-common-mobile-doc/pages/2b4076/404" class="nav-link">移动端组件库(规划中)</a></li></ul></div></div><div class="nav-item"><a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front.git" target="_blank" rel="noopener noreferrer" class="nav-link external">
  仓库地址
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></div> <a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front/-" target="_blank" rel="noopener noreferrer" class="repo-link">
    Source
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><section class="sidebar-group depth-0"><p class="sidebar-heading"><span>布局</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/700a0d/" class="sidebar-link">查询页 - IndPageView</a></li><li><a href="/ind-common-mobile-doc/pages/b4f8ff/" class="sidebar-link">明细页 - IndDetailView</a></li><li><a href="/ind-common-mobile-doc/pages/766a55/" class="sidebar-link">弹出页 - IndMPopupView</a></li></ul></section></li><li><section class="sidebar-group depth-0"><p class="sidebar-heading"><span>表格 - IndTable</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/7eaa51/" class="sidebar-link">基础</a></li><li><a href="/ind-common-mobile-doc/pages/53aeec/" class="sidebar-link">锁列</a></li><li><a href="/ind-common-mobile-doc/pages/67d86f/" class="sidebar-link">锁行</a></li><li><a href="/ind-common-mobile-doc/pages/1044e4/" class="sidebar-link">可编辑的列</a></li><li><a href="/ind-common-mobile-doc/pages/1ffe3e/" class="sidebar-link">树形表格</a></li><li><a href="/ind-common-mobile-doc/pages/771469/" class="sidebar-link">选中行</a></li><li><a href="/ind-common-mobile-doc/pages/09c3f0/" class="sidebar-link">表格的高度</a></li><li><a href="/ind-common-mobile-doc/pages/9062de/" class="sidebar-link">导出excel</a></li><li><a href="/ind-common-mobile-doc/pages/dde6ff/" class="sidebar-link">表格的数据类型</a></li><li><a href="/ind-common-mobile-doc/pages/aafd9b/" class="sidebar-link">表格的事件</a></li><li><a href="/ind-common-mobile-doc/pages/9bcc84/" class="sidebar-link">单元格的自定义样式</a></li><li><a href="/ind-common-mobile-doc/pages/cad969/" class="sidebar-link">单元格的格式化</a></li><li><a href="/ind-common-mobile-doc/pages/71bafb/" class="sidebar-link">单元格的宽度</a></li><li><a href="/ind-common-mobile-doc/pages/8bf102/" class="sidebar-link">单元格的对齐方式</a></li><li><a href="/ind-common-mobile-doc/pages/b1213e/" class="sidebar-link">表格排序</a></li><li><a href="/ind-common-mobile-doc/pages/cbb7d2/" class="sidebar-link">数据分组</a></li><li><a href="/ind-common-mobile-doc/pages/95980f/" class="sidebar-link">数据筛选</a></li><li><a href="/ind-common-mobile-doc/pages/0c4049/" class="sidebar-link">预置列定义</a></li><li><a href="/ind-common-mobile-doc/pages/1a0548/" class="sidebar-link">单位切换</a></li><li><a href="/ind-common-mobile-doc/pages/9919e0/" class="sidebar-link">合计行</a></li></ul></section></li><li><section class="sidebar-group depth-0"><p class="sidebar-heading open"><span>表单 - IndFormWrap</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/2b4076/" aria-current="page" class="active sidebar-link">基础</a><ul class="sidebar-sub-headers"><li class="sidebar-sub-header level2"><a href="/ind-common-mobile-doc/pages/2b4076/#属性" class="sidebar-link">属性</a></li><li class="sidebar-sub-header level2"><a href="/ind-common-mobile-doc/pages/2b4076/#表单项属性" class="sidebar-link">表单项属性</a></li><li class="sidebar-sub-header level2"><a href="/ind-common-mobile-doc/pages/2b4076/#api" class="sidebar-link">Api</a></li><li class="sidebar-sub-header level2"><a href="/ind-common-mobile-doc/pages/2b4076/#事件" class="sidebar-link">事件</a></li></ul></li><li><a href="/ind-common-mobile-doc/pages/1410c2/" class="sidebar-link">查询条件表单与业务表单</a></li><li><a href="/ind-common-mobile-doc/pages/69fadf/" class="sidebar-link">有联动的表单</a></li><li><a href="/ind-common-mobile-doc/pages/28cf9c/" class="sidebar-link">表单的校验</a></li><li><a href="/ind-common-mobile-doc/pages/866471/" class="sidebar-link">表单的布局</a></li><li><a href="/ind-common-mobile-doc/pages/fff3ca/" class="sidebar-link">获取表单的元素实例对象</a></li><li><a href="/ind-common-mobile-doc/pages/cc5e41/" class="sidebar-link">自定义渲染的表单元素</a></li></ul></section></li><li><section class="sidebar-group depth-0"><p class="sidebar-heading"><span>树 - IndTree</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/fda86b/" class="sidebar-link">基础</a></li><li><a href="/ind-common-mobile-doc/pages/2a5147/" class="sidebar-link">单选树与多选树</a></li><li><a href="/ind-common-mobile-doc/pages/74aafd/" class="sidebar-link">默认展开级别</a></li><li><a href="/ind-common-mobile-doc/pages/da923e/" class="sidebar-link">禁止点击的节点</a></li><li><a href="/ind-common-mobile-doc/pages/781c8c/" class="sidebar-link">树的事件</a></li><li><a href="/ind-common-mobile-doc/pages/19d553/" class="sidebar-link">获取选中的节点</a></li></ul></section></li><li><section class="sidebar-group depth-0"><p class="sidebar-heading"><span>业务组件</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/ind-common-mobile-doc/pages/ef4ce7/" class="sidebar-link">单位切换</a></li></ul></section></li></ul> </aside> <div><main class="page"><div class="theme-vdoing-wrapper "><div class="articleInfo-wrap" data-v-06225672><div class="articleInfo" data-v-06225672><ul class="breadcrumbs" data-v-06225672><li data-v-06225672><a href="/ind-common-mobile-doc/" title="首页" class="iconfont icon-home router-link-active" data-v-06225672></a></li> <li data-v-06225672><span data-v-06225672>组件</span></li><li data-v-06225672><span data-v-06225672>表单 - IndFormWrap</span></li></ul> <div class="info" data-v-06225672><div title="作者" class="author iconfont icon-touxiang" data-v-06225672><a href="javascript:;" data-v-06225672>工业前端团队</a></div> <div title="创建时间" class="date iconfont icon-riqi" data-v-06225672><a href="javascript:;" data-v-06225672>2024-01-13</a></div> <!----></div></div></div> <!----> <div class="content-wrapper"><div class="right-menu-wrapper"><div class="right-menu-margin"><div class="right-menu-title">目录</div> <div class="right-menu-content"></div></div></div> <h1><!---->基础<!----></h1> <!----> <div class="theme-vdoing-content content__default"><h2 id="属性"><a href="#属性" class="header-anchor">#</a> 属性</h2> <table><thead><tr><th>属性</th> <th>类型</th> <th>默认值</th> <th>说明</th></tr></thead> <tbody><tr><td><code>数据配置</code></td> <td></td> <td></td> <td></td></tr> <tr><td>value</td> <td>Object</td> <td>{}</td> <td>表单的数据定义，推荐使用v-model</td></tr> <tr><td>fieldList</td> <td>Array</td> <td>[]</td> <td>表单的一项定义，详见表单项属性</td></tr> <tr><td>gridColumns</td> <td>Number</td> <td>3</td> <td>一行展示表单的列数，不设置默认为3列，查询条件的表单仅支持4列</td></tr> <tr><td>labelWidth</td> <td>Number</td> <td>200</td> <td>表单的label宽度，单位为像素，一般不建议修改</td></tr> <tr><td>formType</td> <td>String</td> <td>form</td> <td>search:查询条件表单 form:业务表单</td></tr> <tr><td>searchLoading</td> <td>Boolean</td> <td>false</td> <td>查询条件表单的业务场景下，查询按钮的禁用标志</td></tr> <tr><td>queryBtn</td> <td>Boolean</td> <td>true</td> <td>展示查询条件，有些个性化的查询不需要查询条件</td></tr> <tr><td>funId</td> <td>String</td> <td>$route.meta.permissionId</td> <td>当前form表单的唯一功能主键，用于持久化预置查询条件的预置数据</td></tr></tbody></table> <h2 id="表单项属性"><a href="#表单项属性" class="header-anchor">#</a> 表单项属性</h2> <table><thead><tr><th>属性</th> <th>类型</th> <th>默认值</th> <th>说明</th></tr></thead> <tbody><tr><td>title</td> <td>String</td> <td>''</td> <td>表单标题</td></tr> <tr><td>formKey</td> <td>String</td> <td>''</td> <td>表单元素的键，值存在v-model</td></tr> <tr><td>type</td> <td>String</td> <td>''</td> <td>表单项的渲染类型，默认文本，<code>input \| number \| textarea \| select \| checkbox \| radio \| treeselect \| datepicker \| fileUpload \| imgUpload</code></td></tr> <tr><td>props</td> <td>Object</td> <td>无</td> <td>传给表单项的渲染子组件的属性，与使用原组件的属性一致</td></tr> <tr><td>span</td> <td>Number</td> <td>8</td> <td>栅格布局占据的空间树，默认即可，支持自定义 8 | 12 | 24</td></tr> <tr><td>enumKey</td> <td>String</td> <td>无</td> <td>单选、多选、下拉 的数据源枚举值，系统配置的枚举值ID，组件自动完成枚举值的获取</td></tr> <tr><td>enumList</td> <td>Array</td> <td>无</td> <td>单选、多选、下拉 的数据源选项，[{K: '', V: ''}]</td></tr> <tr><td>required</td> <td>Boolean</td> <td>false</td> <td>控制是否必填选项</td></tr> <tr><td>validate</td> <td>Function</td> <td>无</td> <td>自定义校验函数<code>(rule, value, callback) =&gt; { return callback(new Error(&quot;不能为空！&quot;)); }</code></td></tr> <tr><td>render</td> <td>Function</td> <td>无</td> <td>自定义渲染函数<code>(h, fieldDef, form)=&gt;{}</code></td></tr> <tr><td>disabled</td> <td>Function | Boolean</td> <td>无</td> <td>用于控制表单元素只读，可设置为Boolean或者返回Boolean的函数<code>(form)=&gt;{ return true }</code></td></tr> <tr><td>condition</td> <td>Function | Boolean</td> <td>无</td> <td>用于控制该表单项展示隐藏，可设置为Boolean或者返回Boolean的函数，否则仅在函数返回true的时候展示<code>(form)=&gt;{ return true }</code></td></tr></tbody></table> <h2 id="api"><a href="#api" class="header-anchor">#</a> Api</h2> <ul><li><code>validate</code> 校验form表单当前输入值是否合法</li> <li><code>validateField</code> 校验form表单表单项当前输入值是否合法，callback是回调函数，可不传 <code>validateField(key, callback)</code></li> <li><code>resetFields</code> 重置form校验</li></ul> <h2 id="事件"><a href="#事件" class="header-anchor">#</a> 事件</h2> <ul><li><code>defaultCacheDone</code> 取到预设查询条件默认值之后的回调函数</li></ul> <div class="language-js extra-class"><pre class="language-js"><code><span class="token function">defaultCacheDone</span><span class="token punctuation">(</span><span class="token parameter">form</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword">this</span><span class="token punctuation">.</span>form <span class="token operator">=</span> form <span class="token comment">// 将取到的值赋给当前页面的查询条件</span>
  <span class="token keyword">this</span><span class="token punctuation">.</span><span class="token function">query</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token comment">// 执行查询</span>
<span class="token punctuation">}</span><span class="token punctuation">,</span>
</code></pre></div></div></div> <!----> <div class="page-edit"><div class="edit-link"><a href="http://git.inspur.com/imp-ec/ind-front/ind-common-mobile-front/-/edit/main/docs/docs/02.组件/03.表单 - IndFormWrap/01.基础.md" target="_blank" rel="noopener noreferrer">编辑文档</a> <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></div> <!----> <div class="last-updated"><span class="prefix">上次更新:</span> <span class="time">2024-02-22 14:22:36</span></div></div> <div class="page-nav-wapper"><div class="page-nav-centre-wrap"><a href="/ind-common-mobile-doc/pages/9919e0/" class="page-nav-centre page-nav-centre-prev"><div class="tooltip">合计行</div></a> <a href="/ind-common-mobile-doc/pages/1410c2/" class="page-nav-centre page-nav-centre-next"><div class="tooltip">查询条件表单与业务表单</div></a></div> <div class="page-nav"><p class="inner"><span class="prev">
        ←
        <a href="/ind-common-mobile-doc/pages/9919e0/" class="prev">合计行</a></span> <span class="next"><a href="/ind-common-mobile-doc/pages/1410c2/">查询条件表单与业务表单</a>→
      </span></p></div></div></div> <!----></main></div> <div class="footer"><div class="icons"></div> 
  Theme by
  <a href="https://github.com/xugaoyi/vuepress-theme-vdoing" target="_blank" title="本站主题">Vdoing</a> <!----></div> <div class="buttons"><div title="返回顶部" class="button blur go-to-top iconfont icon-fanhuidingbu" style="display:none;"></div> <div title="去评论" class="button blur go-to-comment iconfont icon-pinglun" style="display:none;"></div> <div title="主题模式" class="button blur theme-mode-but iconfont icon-zhuti"><ul class="select-box" style="display:none;"><li class="iconfont icon-zidong">
          跟随系统
        </li><li class="iconfont icon-rijianmoshi">
          浅色模式
        </li><li class="iconfont icon-yejianmoshi">
          深色模式
        </li><li class="iconfont icon-yuedu">
          阅读模式
        </li></ul></div></div> <!----> <!----> <!----></div><div class="global-ui"></div></div>
    <script src="/ind-common-mobile-doc/assets/js/app.ea3e7bd7.js" defer></script><script src="/ind-common-mobile-doc/assets/js/5.497b83b0.js" defer></script><script src="/ind-common-mobile-doc/assets/js/43.addd5047.js" defer></script>
  </body>
</html>
