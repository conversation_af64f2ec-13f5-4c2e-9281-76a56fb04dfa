<template>
  <IndPageView style="height: 700px">
    <IndTable
      v-model="tableData"
      ref="table"
      :showTableOption="false"
      :disablePage="true"
      :columns="columns"
      :height="500"
      :headerAutoHeight="false"
      :loading="tableLoading"
    >
      <template #buttons> </template>
      <IndFormWrap
        slot="search"
        formType="search"
        v-model="searchForm"
        :fieldList="searchFieldList"
      />
    </IndTable>
  </IndPageView>
</template>

<script>
import _ from 'lodash'
import { searchFieldList, columnList, scale } from './utils'
import { getPreparationList } from './mock'

export default {
  data() {
    return {
      searchFieldList: _.cloneDeep(searchFieldList),
      searchForm: {
        coms: [],
        items: [],
        contDelivWhseId: [],
      },
      tableLoading: false,

      itemList: [], // 卷烟定义
      tableData: [], // 数据

      scale: scale,
    }
  },
  computed: {
    columns: function () {
      let leftColumns = _.cloneDeep(columnList)
      return [
        ...leftColumns,
        ...this.itemList.map((d) => {
          return {
            title: d.itemName,
            children: [
              {
                title: '计划量',
                field: d.itemCode + '_itemDayPlanQty',
                width: 100,
                align: 'right',
                isSum: true,
                isSwitchUnit: true,
                cellRendererParams: { scale: this.scale },
              },
              {
                title: '剩余量',
                field: d.itemCode + '_sumQty',
                width: 100,
                align: 'right',
                isSum: true,
                isSwitchUnit: true,
                cellRendererParams: { scale: this.scale },
              },
              {
                title: '存销比',
                field: d.itemCode + '_rateDay',
                width: 100,
                align: 'right',
                isSwitchUnit: true,
                cellRendererParams: { scale: 2 },
              },
            ],
          }
        }),
      ]
    },
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      this.loading = true
      try {
        const { data } = await getPreparationList(this.searchForm)
        const { contractList, itemList } = data
        contractList.forEach((d) => {
          d.itemList.map((dd) => {
            d[dd.itemCode + '_itemDayPlanQty'] = dd.itemDayPlanQty
            d[dd.itemCode + '_sumQty'] = dd.sumQty
            d[dd.itemCode + '_rateDay'] = dd.rateDay
          })
        })
        this.tableData = contractList
        this.itemList = itemList
        console.log('this.tableData', this.tableData, 'this.itemList', this.itemList)
      } catch (e) {
        console.log('getPreparationList e', e)
      }
      this.loading = false
    },
    doBusi() {
      let rows = this.$refs.table.getSelection()
      if (!rows || !rows.length) {
        this.$Message.warning({
          content: '请至少选择一条合同',
        })
      } else {
        console.log('rows is', rows)
        this.$router.push('/ism/am/contract/cont-preparation/preparation')
      }
    },
  },
}
</script>
