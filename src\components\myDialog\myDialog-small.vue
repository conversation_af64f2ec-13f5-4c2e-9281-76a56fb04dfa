<template>
  <transition name="fade">
    <div v-if="visible" class="dialog-overlay" @click.self="handleCancel">
      <div
        class="dialog-content-small"
        @animationend="onAnimationEnd"
        v-show="getFlag === 'loading' || getFlag === 'succ'"
      >
        <div class="title" v-show="getFlag === 'loading'">
          <div class="rotate-image">
            <img style="width: 44px; height: 57px" src="./imgs/loading.png" alt="Loading" />
          </div>
          <div>签退中</div>
        </div>
        <div class="title" v-show="getFlag === 'succ'">
          <img
            class="title-icon"
            style="width: 83x; height: 83px"
            src="./imgs/succ.png"
            alt="Success"
          />
          <div>签退成功</div>
        </div>
      </div>
      <div class="dialog-content-big" v-show="getFlag === 'warn'">
        <div class="warn-title">
          <img class="warn-icon" src="./imgs/warn.png" alt="Warning" />
          <div style="color: red">签退失败</div>
        </div>
        <div class="content" style="color: black">{{ message }}</div>
        <div class="dialog-buttons">
          <button @click="handleCancel">取消</button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    message: {
      type: String,
      default: '',
    },
    flag: {
      type: String,
      default: 'false',
    },
  },
  computed: {
    getFlag() {
      return this.flag
    },
  },
  methods: {
    handleCancel() {
      this.$emit('cancel')
      this.$emit('update:visible', false)
    },
    onAnimationEnd() {
      // 可选：在动画结束时执行的操作
      this.$emit('AnimationEnd')
    },
  },
}
</script>

<style lang="less" scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-content-small,
.dialog-content-big {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transform: translateY(-20px);
  animation: slideIn 0.5s forwards;
  max-width: 90%;
  text-align: center;
  padding-top: 21px;
  position: relative;
}

.dialog-content-small {
  width: 158px;
  height: 145px;
}

.dialog-content-big {
  width: 271px;
  height: 210px;
}

.title {
  position: absolute;
  width: 100%;

  .rotate-image {
    img {
      animation: rotate 2s infinite linear;
    }

    @keyframes rotate {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }

  .title-icon {
    width: 83px;
    height: 83px;
  }
  div {
    margin: 15px 0;
    font-weight: bold;
    font-size: 17px;
    color: #2d8cf0;
  }
}

.warn-title {
  position: absolute;
  width: 100%;
  top: -10px;
  .warn-icon {
    width: 83px;
    height: 83px;
  }
  div {
    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 17px;
    color: #2d8cf0;
  }
}
div {
  color: #e33d5c;
}

.content {
  position: absolute;
  top: 98px;
  height: 76px;
  width: 100%;
  font-size: 15px;
  padding: 10px 36px 0;
  box-sizing: border-box;
}

.dialog-buttons {
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
}

.dialog-buttons button {
  font-size: 15px;
  width: 100%;
  height: 40px;
  background: #f8fafb;
  color: black;
  border: none;
  cursor: pointer;
  transition: background 0.3s;
}

.dialog-buttons button:first-child {
  border-bottom-left-radius: 8px;
  border-right: 1px solid #eeeeee;
}

.dialog-buttons button:last-child {
  border-bottom-right-radius: 8px;
  border-left: 1px solid #eeeeee;
}
.dialog-buttons button:hover {
  background: #e1e5e9;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.8s; /* 过渡时间改为 0.5s */
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
