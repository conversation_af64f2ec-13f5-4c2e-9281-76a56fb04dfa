<template>
  <div class="map-component-sp" @click.stop="clickMap">
    <div :id="mapId" ref="mapRef" class="map"></div>
    <!-- <div v-if="showNavigator" class="navigator" @click="navigator"></div> -->
    <div v-if="showNavigator" class="navigator-box">
      <img :src="navIcon" class="navigator-icon" @click="navigator" />
    </div>
  </div>
</template>

<script>
import navIcon from './imgs/navigator-icon.png'
import curIcon from './imgs/cur-position-icon.png'
import geoViewport from 'ind-fnd-geo-viewport'
import { config } from '@/config/config'

export default {
  props: {
    //可画圈选择
    selectable: {
      type: Boolean,
      default: false,
    },
    //地图level
    mapLevel: {
      type: Number,
      default: 15,
    },
    curPosition: {
      type: Array,
      default: () => [0, 0],
    },
    zoomLevel: {
      type: Number,
      default: 0,
    },
    //显示控件
    controlBar: { type: Boolean, default: false },
    showNavigator: { type: Boolean, default: false },
    // unvisitedCustList: {
    //   type: Array,
    //   default: () => {return []}
    // },
    // visitingCustList: {
    //   type: Array,
    //   default: () => {return []}
    // },
    // visitedCustList: {
    //   type: Array,
    //   default: () => {return []}
    // },
  },
  data() {
    return {
      navIcon: navIcon,
      curIcon: curIcon,
      mapId: this.$route.path.replace(/\//g, '_'),
      map: null,
      curPoint: null,
      tempAxisList: [],
      isTouchStartBound: false,
    }
  },
  watch: {
    // selectable() {
    //   if (this.selectable) {
    //     this.map.disableDragging()
    //     // 监听地图的touchstart事件，用于开始绘制
    //     this.map.addEventListener('touchstart', this.touchEvent)
    //   } else {
    //     this.map.removeEventListener('touchstart', this.touchEvent)
    //     this.map.enableDragging()
    //     this.tempAxisList = []
    //     this.$emit('getAxisList', this.tempAxisList)
    //   }
    // },
    selectable(newVal) {
      if (newVal) {
        this.map.disableDragging()
        if (!this.isTouchStartBound) {
          this.map.addEventListener('touchstart', this.touchEvent)
          this.isTouchStartBound = true // 标记为已绑定
        }
      } else {
        if (this.isTouchStartBound) {
          this.map.removeEventListener('touchstart', this.touchEvent)
          this.isTouchStartBound = false // 标记为未绑定
        }
        this.map.enableDragging()
        this.tempAxisList = []
        this.$emit('getAxisList', this.tempAxisList)
      }
    },
    curPoint() {
      // this.$nextTick(() => {
      //   this.navigator()
      // })
    },
  },
  methods: {
    getBMapInstance() {
      return this.map
    },
    touchEvent(event) {
      if (event.touches.length > 0) {
        // var touch = event.touches[0];
        // 从触摸事件中获取视口坐标
        var x = event.touches[0].clientX
        var y = event.touches[0].clientY
        // 将视口坐标转换为地图的像素坐标
        var pixel = new BMap.Pixel(x, y)
        // 将像素坐标转换为经纬度坐标
        var point = this.map.pixelToPoint(pixel)
        this.tempAxisList.push(point)
        this.$emit('getAxisList', this.tempAxisList)
        // drawingManager.startDrawing(touch);
      }
    },
    //清除！全他妈清咯！
    clearOverlays() {
      this.map.clearOverlays()
    },
    clickMap() {
      this.$emit('clickMap')
    },
    // 移动到中心点
    navigator() {
      this.panTo(this.curPoint)
      this.$emit('navigator')
    },
    // 渲染地图
    renderMap(point, level = this.mapLevel) {
      this.map = new BMap.Map(this.mapId)
      this.map.enableScrollWheelZoom(true)

      this.curPoint = point
      if (!point) {
        const { longitude, latitude } = config.defaultLocation
        this.curPoint = new BMap.Point(longitude, latitude)
      }

      if (this.selectable) {
        this.$emit('getAxisList', this.tempAxisList)
      }
      setTimeout(() => {
        this.map.centerAndZoom(this.curPoint, level)
      }, 99)
      if (this.controlBar) {
        this.map.addControl(new BMap.NavigationControl())
        this.map.addControl(new BMap.ScaleControl())
      }
    },
    centerAndZoom(lng1, lat1, lng2, lat2) {
      const mapRef = this.$refs.mapRef
      const height = mapRef.clientHeight
      const width = mapRef.clientWidth

      let viewPort = geoViewport.viewport([lng1, lat1, lng2, lat2], [width, height])
      if (isNaN(viewPort.zoom)) {
        viewPort = geoViewport.viewport([lng2, lat2, lng1, lat1], [width, height])
      }
      const centerPoint = new BMap.Point(viewPort.center[0], viewPort.center[1])
      console.log('centerPoint', centerPoint, viewPort)
      this.map.centerAndZoom(centerPoint, viewPort.zoom + this.zoomLevel)
    },
    renderLocalSearch(options) {
      // let local =
      return new BMap.LocalSearch(this.map, options)
      // return local
    },
    // 更新我的位置
    updateMyPoint(point) {
      this.curPoint = point
    },
    // 更新地图中心点位置
    panTo(point) {
      if (point) {
        this.map.panTo(point)
      }
    },
    //添加当前位置覆盖物
    addCurPostionIcon(lng, lat) {
      let icon = new BMap.Icon(this.curIcon, new BMap.Size(31, 37))
      icon.setImageSize(new BMap.Size(31, 37))
      let points = new BMap.Point(lng, lat) //创建坐标点
      let marker = new BMap.Marker(points)
      marker.setZIndex(9999999999)
      marker.setIcon(icon)
      this.addOverlay([marker])
    },
    // 添加覆盖物
    addOverlay(overlays = []) {
      overlays.forEach((overlay) => this.map.addOverlay(overlay))
    },
    // 删除覆盖物
    removeOverlay(overlays = []) {
      overlays.forEach((overlay) => this.map.removeOverlay(overlay))
    },
    // 监听事件
    addEventListener() {
      this.map.addEventListener('click', this.clickMap)
    },
    // 监听事件 自定义方法
    setEventListener() {
      this.map.addEventListener(...arguments)
    },
    getBounds() {
      return this.map.getBounds()
    },
  },
}
</script>

<style lang="less" scoped>
// @navigator-color: @im-menu-color;

.map-component-sp {
  height: 100%;
  width: 100%;

  .map {
    height: 100%;
    width: 100%;
  }
  .navigator-box {
    position: absolute;
    bottom: 44%;
    left: 86%;
    padding: 5px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.8);
    .navigator-icon {
      width: 35px;
      height: 35px;
    }
  }
  .navigator {
    position: absolute;
    bottom: 44%;
    left: 86%;
    width: 35px;
    height: 35px;
    // background-color: #fff;
    border: 1px solid #f2f2f2;
    border-radius: 3px;
    // z-index: 999;

    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 220%;
      left: 90%;
      border-radius: 50%;
      transform: translateX(-50%) translateY(-50%);
    }

    &::before {
      height: 65%;
      width: 65%;
      // border: 3px solid @navigator-color;
      border: 3px solid blue;
    }

    &::after {
      height: 45%;
      width: 45%;
      // background-color: @navigator-color;
      background-color: blue;
    }
  }
}
</style>
