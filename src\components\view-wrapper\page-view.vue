<template>
  <div class="page-view">
    <van-sticky sticky>
      <van-nav-bar
        left-text="返回"
        left-arrow
        :placeholder="true"
        :safe-area-inset-top="true"
        :title="title || pageTitle"
        @click-left="onClickLeft"
        :right-text="rightText"
      >
        <template #right>
          <slot name="right"></slot>
        </template>
      </van-nav-bar>
    </van-sticky>
    <slot name="default"></slot>
    <!-- <div :style="{ 'padding-top': navBarHeight + 'px' }"> -->
    <!-- </div> -->
  </div>
</template>

<script>
import config from '@/config/config'

export default {
  props: {
    rightText: {
      type: String,
      default: '',
    },
    //页面标题 不传默认显示云庭配置的
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      pageTitle: '测试标题',
      navBarHeight: config.navBarHeight,
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler(to) {
        this.pageTitle = to.name
      },
    },
  },
  created() {},
  methods: {
    onChange() {},
    onClickLeft() {
      this.$router.go(-1)
    },
    onClickRight() {},
  },
}
</script>

<style lang="less" scoped>
.page-view {
  // height: 100vh;
  width: 100%;
}
</style>
