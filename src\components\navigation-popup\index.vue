<template>
  <div class="popup-box">
    <!-- <van-popup round v-model="showPopup" position="bottom" @close="closePopup">
      <div class="item" @click="openBaiduMap">百度地图</div>
      <div class="item">高德地图</div>
    </van-popup> -->
    <van-action-sheet v-model="showPopup" :actions="actions" @select="onSelect" />
  </div>
</template>
<script>
import { getLocation } from '@/utils/location'
import config from '@/config/config'
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    currPosition: {
      type: Array,
    },
    destination: {
      type: String,
      default: '',
    },
  },
  watch: {
    show: {
      deep: true,
      handler() {
        if (this.show) {
          this.showPopup = true
        } else {
          this.showPopup = false
        }
      },
    },
  },
  data() {
    return {
      myLat: 0,
      myLong: 0,
      showPopup: false,
      // actions: [{ name: '百度地图' }, { name: '高德地图' }],
      actions: [{ name: '高德地图' }],
      platform: '',
    }
  },
  computed: {
    getPosition() {
      return this.$props.currPosition
    },
  },
  created() {
    this.getCurLocation()
  },
  methods: {
    // 获取我的位置
    async getCurLocation() {
      const result = await getLocation()
      this.myLat = result.latitudeBD09
      this.myLong = result.longitudeBD09
    },
    onSelect(item) {
      console.log(item)
      if (item.name == '百度地图') {
        this.openBaiduMap()
      } else if (item.name == '高德地图') {
        this.openGaodeMap()
      }
      this.showPopup = false
    },
    closePopup() {
      this.$emit('close', this.showPopup)
    },
    openBaiduMap() {
      const location = this.getPosition // 位置信息，示例为纬度和经度坐标
      console.log('location', location)
      console.log(' config.device', config.device)
      console.log(' config.isQywx', config.isQywx)
      console.log('destination', this.destination)
      const userAgent = navigator.userAgent.toLowerCase()
      const isAndroid = 'android' == userAgent.match(/android/i)
      console.log('isAndroid----------------------------------', isAndroid)
      const device = {
        platform: isAndroid ? 'Android' : 'iOS',
      }
      // this.platform = config.device.platform || ''
      this.platform = device.platform || ''
      console.log('platform', this.platform)
      var url =
        (this.platform == 'iOS' ? 'baidumap' : 'bdapp') +
        '://map/direction?origin=' +
        encodeURIComponent('name:我的位置|latlng:' + this.myLong + ',' + this.myLat) +
        '&destination=' +
        encodeURIComponent(
          'name:' + this.destination + '|latlng:' + location[1] + ',' + location[0],
        ) +
        '&mode=riding'
      // window.location.href = url
      console.log('navigation-url----------------------------------------------', url)
      wx.invoke(
        'openUrl',
        {
          type: 1, //0或不填表示使用内部浏览器新窗口打开，1表示用系统浏览器打开
          url: url, //url地址
        },
        function (res) {
          if (res.err_msg != 'openUrl:ok') {
            //错误处理
          }
        },
      )
      // const location = this.getPosition // 位置信息，示例为纬度和经度坐标
      // let url = `http://api.map.baidu.com/marker?location=${location[0]},${location[1]}&title=${this.destination}&content=目标地点&mode=bicyclingoutput=html`
      // window.location.href = url
    },
    openGaodeMap() {
      const location = this.getPosition // 位置信息，示例为纬度和经度坐标
      window.location.href = `https://uri.amap.com/navigation?from=${this.myLong},${this.myLat},我的位置&to=${location[1]},${location[0]},${this.destination}&mode=riding&policy=1&src=mypage&coordinate=gaode&callnative=1`
    },
  },
}
</script>
<style lang="less" scoped>
.item {
  text-align: center;
  padding: 25px 0;
  font-size: 16px;
  border-bottom: 1px solid #f3f3f3;
}
</style>
