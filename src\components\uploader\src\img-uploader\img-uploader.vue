<template>
  <div class="img-uploader">
    <div class="img-uploader-main" @click="chooseFile">
      <van-image-oss
        v-if="fileId"
        class="img-uploader-image"
        :preview="false"
        :src="fileId"
        :fit="imageFit"
      />
      <template v-else>
        <slot>
          <div class="img-uploader-image-empty">
            <van-icon class="img-uploader-image-empty-icon" name="photograph" />
          </div>
        </slot>
      </template>
    </div>
    <van-action-sheet v-model="actionSheetShow" :actions="actions" @select="onSelect" />
    <Uploader
      ref="uploader"
      :max-size="maxSize"
      :max-width="maxWidth"
      :max-height="maxHeight"
    ></Uploader>
  </div>
</template>

<script>
import { Toast } from 'vant'
import Uploader from '../components/uploader.vue'
import { uploadFile } from '@/api/ics/common/index'
import config from '@/config/config'
import { axios } from '@/utils/request'

// const sourceTypeList = ['album', 'camera']
const sourceTypeList = ['album', 'camera']

export default {
  name: 'img-uploader',
  components: {
    Uploader,
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    sourceType: {
      type: Array, // 'album', 'camera'
      default: () => sourceTypeList,
    },
    maxSize: {
      type: [Number, String, Function], // number | string | (file: File) => boolean
    },
    maxWidth: {
      type: [Number],
    },
    maxHeight: {
      type: [Number],
    },
    imageFit: {
      type: String,
      default: 'cover',
    },
  },
  data() {
    return {
      fileId: '',
      url: '',
      actionSheetShow: false,
      actions: [
        {
          name: '拍照',
          key: 'camera',
        },
        {
          name: '相册',
          key: 'album',
        },
      ],
    }
  },
  created() {},
  watch: {
    value: {
      handler() {
        this.fileId = this.value
      },
      immediate: true,
    },
    fileId() {
      this.$emit('input', this.fileId)
    },
  },

  methods: {
    async uploadFile(file) {
      Toast.loading('正在上传')
      const response = await uploadFile(file)
      const url = config.dddIcustServiceManageServer + '/oss/file/put'
      console.log('url:::>', url)
      Toast.clear()
      if (response.code === 1) {
        const result = response.data
        if (result.length > 0) {
          this.fileId = result[0].fileId
          console.log('this.fileId', this.fileId)
          return this.fileId
        } else {
          const imsysErrorLogsREQ = {
            imsysLogsDesc: '上传失败',
            imsysLogsFunction: 'uploadFile',
            imsysLogsInput: '',
            imsysLogsModule: '上传',
            imsysLogsRepair: '',
            imsysLogsTitle: '网络请求错误',
            imsysLogsType: '1',
            imsysLogsUrl: url,
          }
          axios
            .post(`/ddd-imsys-manage-server/manage/error-log/saveErrorLog`, imsysErrorLogsREQ)
            .then((resp) => {
              if (resp.data.code === 1) {
                /* empty */
              } else {
                /* empty */
              }
            })
          console.error('File array is empty.')
          Toast.fail('图片上传失败')
        }
      } else {
        const imsysErrorLogsREQ = {
          imsysLogsDesc: '上传失败',
          imsysLogsFunction: 'uploadFile',
          imsysLogsInput: '',
          imsysLogsModule: '上传',
          imsysLogsRepair: '',
          imsysLogsTitle: '网络请求错误',
          imsysLogsType: '1',
          imsysLogsUrl: url,
        }
        axios
          .post(`/ddd-imsys-manage-server/manage/error-log/saveErrorLog`, imsysErrorLogsREQ)
          .then((resp) => {
            if (resp.data.code === 1) {
              /* empty */
            } else {
              /* empty */
            }
          })
        console.error(response.data.message)
        Toast.fail('图片上传失败')
      }
    },
    async onSelect(item) {
      this.actionSheetShow = false
      const file = await this.$refs.uploader.chooseFile(item.key)
      this.uploadFile(file)
    },
    async toUpload() {
      const options = {
        ossServerContext: config.dddIcustServiceManageServer,
        ossImgPutUrl: '/oss/file/put',
        ossImgGetUrl: '/oss/file/get/',
        outputType: 'oss',
        count: 1,
        sourceType: this.sourceType,
      }
      console.log('toUpload options')
      if (config.isQywx) {
        const photos = await window.$lamboJsBridge.takePhoto(options)
        // this.photoResult += `${JSON.stringify(photos)}\n`
        console.log('企业微信 Photo result:', photos) // 调试输出
        this.fileId = photos.imageOss.fileId
      }
    },
    async chooseFile() {
      if (!this.disabled && config.isQywx) {
        await this.toUpload()
        return
      }
      console.log('img-uploader-------choosefile')
      if (!this.disabled) {
        console.log('img-uploader-------disabled')
        const sourceType = this.sourceType.filter((item) => sourceTypeList.includes(item))
        if (sourceType.length > 1) {
          this.actionSheetShow = true
        } else if (sourceType.length === 1) {
          const file = await this.$refs.uploader.chooseFile(this.sourceType[0])
          await this.uploadFile(file)
        }
      }
    },
  },
}
</script>

<style lang="less">
.img-uploader {
  width: 100%;
  height: 100%;
  .img-uploader-main {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .img-uploader-image {
      width: 100%;
      height: 100%;
    }
    .img-uploader-image-empty {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f7f8fa;
      .img-uploader-image-empty-icon {
        color: #dcdee0;
        font-size: 24px;
      }
    }
  }
}
</style>
