<template>
  <div class="custInfo-card">
    <div class="custInfo-card-top">
      <van-image-oss
        class="avater"
        radius="6"
        v-if="getcustInfo.md04MgRtlCustStoreImage"
        :src="getcustInfo.md04MgRtlCustStoreImage"
      />
      <img v-else class="avater" src="./imgs/avater.png" alt="" />
      <div class="cust-name">
        {{
          getcustInfo.bbRtlCustShopName || getcustInfo.bbRetailCustomerName
            ? getcustInfo.bbRtlCustShopName || getcustInfo.bbRetailCustomerName
            : '--'
        }}
      </div>
      <div class="cust-label">
        <div v-for="(item, index) in tagNameList" :key="index" class="cust-label-item">
          {{ item }}
        </div>
      </div>
      <div></div>
      <div class="permit-number">
        <div class="permit-number-img">
          <img src="./imgs/percard.png" alt="" />
        </div>
        <div class="permit-number-num">
          {{ isShowMask == '1' ? custInfo.bbRtlCustLicNoMask : custInfo.bbRtlCustLicNo }}
        </div>
      </div>
      <div class="line"></div>
      <div class="name-phone">
        <div class="name">
          <img style="width: 9px; height: 10px" src="./imgs/people.png" />
          <span class="name-in">{{
            getcustInfo.bbRtlCustShopName || getcustInfo.bbRetailCustomerName
          }}</span>
        </div>
        <div class="phone" @click="showPhonePopup">
          <img style="width: 13px; height: 13px" src="./imgs/phone.png" />
          <span class="phone-in">电话</span>
        </div>
      </div>
      <div class="addressAnav">
        <div class="address">
          <img style="width: 8px; height: 10px" src="./imgs/dizhi.png" alt="" />
          <div class="name">
            {{
              (getcustInfo.mc04ProvOrgName ? getcustInfo.mc04ProvOrgName : '') +
              (getcustInfo.mc04CityOrgName ? '-' + getcustInfo.mc04CityOrgName : '') +
              (getcustInfo.bbRtlCustBusinessAddr ? getcustInfo.bbRtlCustBusinessAddr : '')
            }}
          </div>
        </div>
        <div class="navigation" @click="showNavPopup">
          <img style="width: 13px; height: 13px" src="./imgs/daohang.png" alt="" />
          <div class="name">导航</div>
        </div>
      </div>
    </div>
    <div v-if="getFlag == 1">
      <div class="custInfo-card-bottom-f" v-show="getcustInfo.mf02RtlInfoCompletePercent < 100">
        <img src="./imgs/warning.png" alt="" />
        <span class="text">该零售户档案信息不完善，请签到后及时维护</span>
      </div>
    </div>
    <div v-else>
      <div class="custInfo-card-bottom-s">
        <div class="bottom">
          <!-- <div class="bottom-item" v-for="(item, index) in items" :key="index">
            <img :src="getImagePath(index)" alt="" />
            <div class="text">{{ item.name }}</div>
          </div> -->
          <div class="response-item" v-if="cigaretteResponse">
            <img class="response-icon" :src="cigaretteImg" />
            <div class="response-text">{{ cigaretteResponse }}</div>
          </div>
          <div class="response-item" v-if="cigarResponse">
            <img class="response-icon" :src="cigarImg" />
            <div class="response-text">{{ cigarResponse }}</div>
          </div>
        </div>
        <div class="todetail" v-if="showToDetail" @click="toDetail">
          <img src="./imgs/icon1.png" alt="" />
          <div class="text">查看档案明细</div>
          <van-icon name="arrow" color="#FFFFFF" />
        </div>
      </div>
    </div>
    <NavigationPopup
      :currPosition="getPosition"
      :show="navPopupShow"
      :destination="custInfo.custBusinessAddress || custInfo.bbRtlCustBusinessAddr"
      @close="closeNavPopup"
    ></NavigationPopup>
    <PhonePopup
      :phoneList="getPhoneList"
      :show="phonePopupShow"
      @close="closePhonePopup"
    ></PhonePopup>
    <van-popup v-model="showCancelDocumentation"></van-popup>
  </div>
</template>
<script>
import NavigationPopup from '@/components/navigation-popup/index'
import PhonePopup from '@/components/phone-popup/index'
import cigaretteImg from './imgs/cigarette.png'
import cigarImg from './imgs/cigar.png'
import { getValuationLabel } from '@/api/ics'
import { openLocation } from '@/utils/openLocation'
import _ from 'lodash'

export default {
  components: {
    NavigationPopup,
    PhonePopup,
  },
  // props: ['cust-Info', 'flag'],
  props: {
    showToDetail: {
      type: Boolean,
      default: true,
    },
    custInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
    flag: {
      type: String,
      default: '',
    },
    isNotMixXj: {
      type: String,
      default: '0',
    },
  },
  data() {
    return {
      navPopupShow: false,
      phonePopupShow: false,
      showCancelDocumentation: false,
      lable: [],
      cigarImg: cigarImg,
      cigaretteImg: cigaretteImg,
      valuationLabelList: [],
    }
  },
  computed: {
    //价值标签name列表
    valuationLabelNameList() {
      let tempList = _.cloneDeep(this.valuationLabelList)
      console.log('this.custInfo.custSaleTags', this.custInfo.custSaleTags)
      if (this.custInfo.custSaleTags && this.custInfo.custSaleTags.length > 0) {
        console.log('this.valuationLabelList', tempList)
        console.log('this.custInfo.custSaleTags', this.custInfo.custSaleTags)
        let tempNameList = []
        for (const item of this.valuationLabelList) {
          if (this.custInfo.custSaleTags[0][item.K] == '1') {
            tempNameList.push(item.V)
          }
        }
        console.log('final ------- valuenamelist', tempNameList)
        return tempNameList
      } else {
        return []
      }
    },
    //客户标签name列表
    tagNameList() {
      let tempList = []
      if (this.custInfo.bbRtlCustBusinessTypeName) {
        tempList.push(this.custInfo.bbRtlCustBusinessTypeName)
      }
      if (this.custInfo.bbRtlCustMarketTypeName) {
        tempList.push(this.custInfo.bbRtlCustMarketTypeName)
      }
      if (this.custInfo.bbRtlCustCgtOperateScopeName) {
        tempList.push(this.custInfo.bbRtlCustCgtOperateScopeName)
      }
      if (this.isNotMixXj == '1') {
        // 雪茄的时候展示
        if (this.custInfo.bbRtlCustCigaCustGradeName) {
          // 雪茄档位
          tempList.push(this.custInfo.bbRtlCustCigaCustGradeName)
        }
        if (this.custInfo.mc04CigarTerminalTypeName) {
          // 雪茄终端类型
          tempList.push(this.custInfo.mc04CigarTerminalTypeName)
        }
      } else {
        // 卷烟的时候展示
        if (this.custInfo.bbRtlCustGradeName) {
          tempList.push(this.custInfo.bbRtlCustGradeName)
        }
      }
      if (this.custInfo.custTypes && this.custInfo.custTypes.length > 0) {
        for (const item of this.custInfo.custTypes) {
          tempList.push(item.mc04CustTypeName)
        }
      }
      if (
        this.custInfo.mc04IsCustMarketSubmitCapt &&
        this.custInfo.mc04IsCustMarketSubmitCapt == '1'
      ) {
        tempList.push('信息点')
      }
      if (this.custInfo.mc04IsCustDirectCapt && this.custInfo.mc04IsCustDirectCapt === '1') {
        tempList.push('直报信息点')
      }
      if (this.valuationLabelNameList && this.valuationLabelNameList.length > 0) {
        for (const item of this.valuationLabelNameList) {
          tempList.push(item)
        }
      }
      return tempList
    },
    cigaretteResponse() {
      if (this.custInfo.custPositions && this.custInfo.custPositions.length > 0) {
        const tempList = _.filter(this.custInfo.custPositions, (item) => {
          return item.mc04CustPostRelationTypeCode == '01'
        })
        console.log('cigaretteResponse templist', tempList)
        return tempList.map((item) => item.positionShortName).join(',')
      } else {
        return ''
      }
    },
    cigarResponse() {
      if (this.custInfo.custPositions && this.custInfo.custPositions.length > 0) {
        const tempList = _.filter(this.custInfo.custPositions, (item) => {
          return item.mc04CustPostRelationTypeCode == '02'
        })
        console.log('cigarResponse templist', tempList)
        return tempList.map((item) => item.positionShortName).join(',')
      } else {
        return ''
      }
    },
    getcustInfo() {
      console.log('this.$props.custInfo', this.$props.custInfo)
      return this.$props.custInfo
    },
    getPhoneList() {
      const personList = _.cloneDeep(this.getcustInfo.custPersons)
      const tempList = _.map(personList, 'bbTelephone')
      let rlt = tempList.filter(function (item) {
        return item != null && item != undefined && item
      })
      return rlt
    },
    getFlag() {
      return this.$props.flag
    },
    getPosition() {
      const arr = [this.getcustInfo.bbRtlCustGisLongitude, this.getcustInfo.bbRtlCustGisLatitude]
      return arr
    },
    // 是否脱敏展示许可证
    isShowMask() {
      return localStorage.getItem('isShowMask') || ''
    },
  },
  async created() {
    await this.getValuationLabel()
  },
  methods: {
    toDetail() {
      this.$router.push({
        path: '/ics/cust-maintain/index',
        // name: '客户档案维护',
        query: {
          isEdit: '0',
          isDetail: '1',
          custCode: this.custInfo.bbRetailCustomerCode || '',
          baCityOrgCode: this.custInfo.baCityOrgCode || '',
          baProvOrgCode: this.custInfo.baProvOrgCode || '',
          isNotMixXj: this.isNotMixXj,
        },
      })
    },
    //获取价值标签
    async getValuationLabel() {
      try {
        const result = await getValuationLabel()
        if (result.code == 1) {
          // this.valuationLabelList = result.data
          const tempList = result.data
          for (const item of tempList) {
            this.valuationLabelList.push({
              K: item.mc04CustSaleTagTypeCode,
              V: item.mc04CustSaleTagTypeName,
            })
          }
        }
      } catch (e) {
        console.log(e)
      }
    },
    // 使用 require 动态加载图片路径
    getImagePath(index) {
      // 这里你可以根据索引返回不同的图片路径
      const imagePaths = [
        cigaretteImg,
        cigarImg,
        // 添加更多图片路径
      ]
      return imagePaths[index % imagePaths.length] // 根据索引循环选择图片
    },

    showNavPopup() {
      this.navPopupShow = true
      openLocation({
        ...this.custInfo,
        // mc04RtlCustGisLatitudeBd: this.custInfo.bbRtlCustGisLatitude,
        // mc04RtlCustGisLongitudeBd: this.custInfo.bbRtlCustGisLongitude,
      })
    },
    closeNavPopup(showPopup) {
      this.navPopupShow = showPopup
    },
    showPhonePopup() {
      if (this.getPhoneList.length > 0 && this.getPhoneList[0]) {
        this.phonePopupShow = true
      } else {
        this.$toast('当前零售户未查询到联系人电话信息')
      }
    },
    closePhonePopup(showPopup) {
      this.phonePopupShow = showPopup
    },
  },
  mounted() {
    console.log(this.$props)
    console.log(`🚀✨🌟🌀🌈⚡ -- mounted -- this.$props4455566:`, this.$props)
  },
}
</script>
<style lang="less" scoped>
.custInfo-card {
  width: calc(100%- 19px);
  height: 175px;
  border-radius: 10px;
  margin: 0px 10px;
  position: relative;
  .custInfo-card-top {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 145px;
    z-index: 9;
    background-image: url(./imgs/page1.png), linear-gradient(90deg, #49a4f1 0%, #2d8cf0 100%);
    background-size: cover;
    background-position: center;
    box-shadow: 0px 4px 8px 0px rgba(20, 63, 111, 0.15);
    border-radius: 10px;
    .avater {
      position: absolute;
      width: 60px;
      height: 60px;
      left: 10px;
      top: 15px;
      border-radius: 6px;
      overflow: hidden;
    }
    .cust-name {
      height: 17px;
      position: absolute;
      left: 83px;
      top: 22px;
      font-size: 17px;
      font-weight: bold;
      color: white;
    }
    .cust-label {
      position: absolute;
      top: 50px;
      left: 80px;
      font-size: 10px;
      width: 75%;
      display: flex;
      flex-wrap: wrap;
      max-height: 42px;
      overflow-y: scroll;
      .cust-label-item {
        color: #ffffff;
        border: 1px solid #ffffff;
        border-radius: 3px;
        white-space: nowrap;
        padding: 2px 4px;
        margin-right: 5px;
        margin-bottom: 2px;
      }
    }
    .permit-number {
      position: absolute;
      width: 138px;
      height: 19px;
      background-color: rgba(255, 255, 255, 0.2);
      right: 0px;
      top: 0px;
      border-radius: 0px 10px 0px 8px;
      display: flex;
      .permit-number-img {
        width: 50px;
        height: 19px;
        img {
          width: 50px;
          height: 19px;
        }
      }
      .permit-number-num {
        width: 88px;
        height: 19px;
        padding: 5px 6px;
        font-size: 10px;
        color: white;
      }
    }
    .line {
      width: calc(100% - 20px);
      height: 1px;
      background-color: rgba(255, 255, 255, 0.2);
      position: absolute;
      left: 10px;
      top: 90px;
    }
    .name-phone {
      position: absolute;
      left: 15px;
      top: 101px;
      height: 13px;
      width: calc(100% - 30px);
      font-size: 11px;
      color: white;
      display: flex;
      .name {
        flex: 1;
        display: flex;
        .name-in {
          height: auto;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 11px;
          color: #ffffff;
          line-height: 11px;
          margin-left: 5px;
        }
      }
      .phone {
        flex: 1;
        display: flex;
        justify-content: end;
        .phone-in {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 11px;
          color: #ffffff;
          line-height: 13px;
          margin-left: 5px;
        }
      }
    }
    .addressAnav {
      position: absolute;
      left: 15px;
      top: 122px;
      height: 13px;
      width: calc(100% - 30px);
      font-size: 11px;
      color: white;
      display: flex;
      .address {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .name {
          height: auto;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 11px;
          color: #ffffff;
          line-height: 13px;
          margin-left: 5px;
        }
      }
      .navigation {
        display: flex;
        justify-content: flex-end;
        .name {
          height: 11px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 11px;
          color: #ffffff;
          line-height: 13px;
          margin-left: 5px;
        }
      }
    }
    .van-tag--plain {
      background-color: rgba(252, 247, 247, 0.1);
      border-color: #fff;
      color: #ffffff;
    }
  }
  .custInfo-card-bottom-f {
    position: absolute;
    width: 100%;
    height: 172px;
    background: rgba(250, 213, 219, 0.8);
    box-shadow: 0px 4px 8px 0px rgba(20, 63, 111, 0.15);
    border-radius: 10px;
    position: absolute;
    left: 0px;
    top: 0px;
    padding-top: 153px;
    padding-left: 10px;
    box-sizing: border-box;
    img {
      width: 10px;
      height: 10px;
      margin-right: 5px;
    }
    .text {
      height: 11px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 11px;
      color: #e33d5c;
      line-height: 11px;
    }
  }
  .custInfo-card-bottom-s {
    position: absolute;
    width: 100%;
    height: 177px;
    background: linear-gradient(-90deg, #5e8cc7 0%, #3f69a0 100%);
    box-shadow: 0px 4px 8px 0px rgba(20, 63, 111, 0.15);
    border-radius: 10px;
    position: absolute;
    left: 0px;
    top: 0px;
    padding-top: 153px;
    padding-left: 10px;
    box-sizing: border-box;
    .bottom {
      height: 15px;
      width: 70%;
      display: flex;
      .bottom-item {
        height: 15px;
        width: auto;
        display: flex;
        img {
          width: 53px;
          height: 15px;
          margin-right: 5px;
        }
        .text {
          height: 13px;
          width: 50px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 13px;
          color: #ffffff;
          line-height: 15px;
        }
      }
      .response-item {
        display: flex;
        align-items: center;
        margin-right: 12px;
        .response-icon {
          width: 53px;
          height: 15px;
        }
        .response-text {
          font-weight: bold;
          font-size: 13px;
          color: #ffffff;
          line-height: 23px;
          margin-left: 5px;
        }
      }
    }
    .todetail {
      position: absolute;
      right: 10px;
      bottom: 10px;
      width: auto;
      height: 11px;
      display: flex;
      img {
        width: 11px;
        height: 10px;
        margin-right: 3px;
      }
      .text {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 11px;
        color: #ffffff;
        line-height: 11px;
        opacity: 0.8;
        margin: 0px 3px;
      }
    }
  }
}
</style>
